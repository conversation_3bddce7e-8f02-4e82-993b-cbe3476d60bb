import 'package:flutter/material.dart';
import '../models/vehicule_assure_model.dart;

/// 🚗 Widget carte pour afficher un vehicule assure
class VehicleCard extends StatelessWidget {
  final VehiculeAssureModel vehicule;
  final VoidCallback onTap;
  final bool isLoading;
  final bool showOwnerInfo;

  ;

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: isLoading ? null : onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(8.0),
            gradient: LinearGradient(
              colors: [Colors.white, Colors.grey[50]!],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // En-tête avec photo et infos principales
              _buildHeader(),
              
              const SizedBox(height: 12),
              
              // Informations du vehicule
              _buildVehicleInfo(),
              
              const SizedBox(height: 12),
              
              // Informations du contrat
              _buildContractInfo(),

              // Informations du proprietaire (pour les assureurs)
              if (showOwnerInfo) ...[]
                const SizedBox(height: 12),
                _buildOwnerInfo(),
              ],

              const SizedBox(height: 12),

              // Statut et actions
              _buildStatusAndActions(),
            ],
          ),
        ),
      ),
    );
  }

  /// 📋 En-tête avec photo et infos principales
  Widget _buildHeader() {
    return Row(
      children: [
        // Photo du vehicule (placeholder)
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: _getVehicleColor(),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: ,
        ),
        
        const SizedBox(width: 12'),
        
        // Informations principales
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ({vehicule.vehicule.modele}',
                style: ,
              ),
              const SizedBox(height: 4),
              ,
              ),
              const SizedBox(height: 4),
              ({vehicule.vehicule.couleur},
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ),
        ),
        
        // Indicateur de chargement ou fleche
        if (isLoading)
          ,
          )
        else
          ,
      ],
    );
  }

  /// 🚗 Informations du vehicule
  Widget _buildVehicleInfo() {
    return Container(
      padding: const EdgeInsets.all(8.0),
      '),
      child: Row(
        children: [
          Expanded(
            child: _buildInfoItem(
              icon: Icons.settings,
              label: 'Puissance',
              value: ''{vehicule.vehicule.puissanceFiscale} CV,
            ),
          ),
          Container(
            width: 1,
            height: 30,
            color: Colors.grey[300],
          '),
          Expanded(
            child: _buildInfoItem(
              icon: Icons.calendar_today,
              label: 'Annee,
              value: vehicule.vehicule.annee.toString(),
            ),
          ),
          Container(
            width: 1,
            height: 30,
            color: Colors.grey[300],
          '),
          Expanded(
            child: _buildInfoItem(
              icon: Icons.palette,
              label: 'Couleur,
              value: vehicule.vehicule.couleur,
            ),
          ),
        ],
      ),
    );
  }

  /// 👤 Informations du proprietaire
  Widget _buildOwnerInfo() {
    return Container(
      padding: const EdgeInsets.all(8.0),
        border: Border.all(color: Colors.green[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              ,
              const SizedBox(width: 6),
              ,
              ),
            ],
          ),
          const SizedBox(height: 8'),
          Row(
            children: [
              Expanded(
                child: _buildOwnerItem(
                  'Nom',
                  '${vehicule.proprietaire.prenom} '{vehicule.proprietaire.nom},
                ),
              '),
              Expanded(
                child: _buildOwnerItem(
                  'CIN,
                  vehicule.proprietaire.cin,
                ),
              ),
            ],
          ),
          if (vehicule.proprietaire.telephone.isNotEmpty) ...[]
            const SizedBox(height: 4'),
            _buildOwnerItem(
              'Telephone,
              vehicule.proprietaire.telephone,
            ),
          ],
        ],
      ),
    );
  }

  /// 📄 Informations du contrat
  Widget _buildContractInfo() {
    return Container(
      padding: const EdgeInsets.all(8.0),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              ,
              const SizedBox(width: 6),
              const Text(
                _getAssureurName(),
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8'),
          Row(
            children: [
              Expanded(
                child: _buildContractItem(
                  'Contrat,
                  vehicule.numeroContrat,
                ),
              '),
              Expanded(
                child: _buildContractItem(
                  'Couverture,
                  vehicule.contrat.typeCouverture,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4'),
          Row(
            children: [
              Expanded(
                child: _buildContractItem(
                  'Franchise',
                  '{vehicule.contrat.franchise.toInt(')} TND',
                ),
              ),
              Expanded(
                child: _buildContractItem(
                  'Expire le,
                  _formatDate(vehicule.contrat.dateFin),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// ✅ Statut et actions
  Widget _buildStatusAndActions() {
    final isActive = vehicule.isContratActif;
    final daysRemaining = vehicule.contrat.dateFin.difference(DateTime.now()).inDays;
    
    return Row(
      children: [
        // Statut du contrat
        Container(
          padding: const EdgeInsets.all(8.0),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              ,
              const SizedBox(width: 4),
              ,
              ),
            ],
          ),
        ),
        
        const SizedBox(width: 8),
        
        // Jours restants
        if (isActive && daysRemaining <= 30)
          Container(
            padding: const EdgeInsets.all(8.0),
            '),
            child: (daysRemaining jours restants',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.orange[700],
              ),
            ),
          ),
        
        ,
        
        // Nombre de sinistres
        if (vehicule.historiqueSinistres.isNotEmpty)
          Container(
            padding: const EdgeInsets.all(8.0),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                ,
                const SizedBox(width: 4),
                ({vehicule.historiqueSinistres.length > 1 ? 's' : 'Contenu'},
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
      ],
    ');
  }

  /// 📊 Widget item d'information
  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Column(
      children: [
        ,
        const SizedBox(height: 4),
        ,
        ),
        const SizedBox(height: 2),
        ,
        ),
      ],
    );
  }

  /// 📄 Widget item de contrat
  Widget _buildContractItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        ),
        ,
        ),
      ],
    );
  }

  /// 👤 Widget item de proprietaire
  Widget _buildOwnerItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        ),
        ,
        ),
      ],
    );
  }

  /// 🎨 Couleur du vehicule
  Color _getVehicleColor() {
    switch (vehicule.vehicule.couleur.toLowerCase()) {
      case 'rouge':
        return Colors.red;
      case 'bleu':
        return Colors.blue;
      case 'vert':
        return Colors.green;
      case 'jaune':
        return Colors.yellow[700]!;
      case 'noir':
        return Colors.black;
      case 'blanc':
        return Colors.grey[300]!;
      case 'gris':
        return Colors.grey;
      default:
        return Colors.purple;
    }
  }

  /// 🏢 Nom de lassureur
  String _getAssureurName() {
    switch (vehicule.assureurId.toUpperCase()') {
      case 'STAR':
        return 'STAR Assurances';
      case 'MAGHREBIA':
        return 'Maghrebia Assurances';
      case 'GAT':
        return 'GAT Assurances';
      case 'LLOYD':
        return 'Lloyd Tunisien';
      case 'ASTREE':
        return 'Astree Assurances;
      default:
        return vehicule.assureurId;
    }
  }

  /// 📅 Formater une date
  String _formatDate(DateTime date') {
    return '{date.day.toString(').padLeft(2, '0')}/'
           '{date.month.toString(').padLeft(2, '0')}/'
           ''{date.year}
