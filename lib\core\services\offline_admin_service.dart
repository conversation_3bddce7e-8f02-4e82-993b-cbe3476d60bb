import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'robust_firestore_service.dart';

/// 🚀 Service Admin Hors Ligne - SOLUTION RADICALE
/// 
/// Ce service permet de créer des comptes admin même quand Firestore est indisponible
/// Les données sont stockées localement et synchronisées quand Firestore revient
class OfflineAdminService {
  static const String _offlineAdminsKey = 'offline_admins';
  static const String _pendingSyncKey = 'pending_sync_admins';

  /// 🔥 Créer un Admin Compagnie IMMÉDIATEMENT (sans Firestore)
  static Future<Map<String, dynamic>> createAdminCompagnieOffline({
    required String email,
    required String nom,
    required String prenom,
    required String compagnieId,
    required String compagnieNom,
    String? phone,
    String? address,
  }) async {
    try {
      debugPrint('[OFFLINE_ADMIN] 🚀 Création Admin Compagnie HORS LIGNE...');
      debugPrint('[OFFLINE_ADMIN]   - Email: $email');
      debugPrint('[OFFLINE_ADMIN]   - Compagnie: $compagnieNom ($compagnieId)');

      // Générer un UID unique
      final adminUid = 'offline_admin_${DateTime.now().millisecondsSinceEpoch}_${email.hashCode.abs()}';
      final tempPassword = _generateSecurePassword();

      // Créer les données admin
      final adminData = {
        'uid': adminUid,
        'email': email,
        'nom': nom,
        'prenom': prenom,
        'role': 'admin_compagnie',
        'status': 'actif',
        'compagnieId': compagnieId,
        'compagnieNom': compagnieNom,
        'phone': phone,
        'address': address,
        'temporaryPassword': tempPassword,
        'passwordChangeRequired': true,
        'accountType': 'admin_offline',
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
        'created_by': 'super_admin_offline',
        'created_by_uid': 'system_super_admin',
        'created_by_email': '<EMAIL>',
        'isFakeData': false,
        'isLegitimate': true,
        'accessLevel': 'production',
        'isOfflineCreated': true,
        'needsFirestoreSync': true,
      };

      // Sauvegarder localement
      await _saveAdminLocally(adminData);
      await _addToPendingSync(adminData);

      debugPrint('[OFFLINE_ADMIN] ✅ Admin Compagnie créé HORS LIGNE avec succès !');
      debugPrint('[OFFLINE_ADMIN] 🔑 Mot de passe temporaire: $tempPassword');
      debugPrint('[OFFLINE_ADMIN] 📱 UID: $adminUid');

      return {
        'success': true,
        'uid': adminUid,
        'email': email,
        'temporaryPassword': tempPassword,
        'compagnieId': compagnieId,
        'compagnieNom': compagnieNom,
        'message': 'Compte créé hors ligne - Sera synchronisé avec Firestore dès que possible',
        'isOffline': true,
      };

    } catch (e) {
      debugPrint('[OFFLINE_ADMIN] ❌ Erreur création hors ligne: $e');
      return {
        'success': false,
        'error': e.toString(),
        'message': 'Impossible de créer le compte même hors ligne',
      };
    }
  }

  /// 💾 Sauvegarder un admin localement
  static Future<void> _saveAdminLocally(Map<String, dynamic> adminData) async {
    final prefs = await SharedPreferences.getInstance();
    
    // Récupérer les admins existants
    final existingAdmins = await getOfflineAdmins();
    
    // Ajouter le nouvel admin
    existingAdmins.add(adminData);
    
    // Sauvegarder
    final adminsJson = existingAdmins.map((admin) => jsonEncode(admin)).toList();
    await prefs.setStringList(_offlineAdminsKey, adminsJson);
    
    debugPrint('[OFFLINE_ADMIN] 💾 Admin sauvegardé localement');
  }

  /// 📋 Récupérer tous les admins hors ligne
  static Future<List<Map<String, dynamic>>> getOfflineAdmins() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final adminsJson = prefs.getStringList(_offlineAdminsKey) ?? [];
      
      return adminsJson.map((adminJson) {
        return Map<String, dynamic>.from(jsonDecode(adminJson));
      }).toList();
      
    } catch (e) {
      debugPrint('[OFFLINE_ADMIN] ❌ Erreur lecture admins locaux: $e');
      return [];
    }
  }

  /// 🔍 Trouver un admin par email (local)
  static Future<Map<String, dynamic>?> findAdminByEmail(String email) async {
    final admins = await getOfflineAdmins();
    
    try {
      return admins.firstWhere((admin) => admin['email'] == email);
    } catch (e) {
      return null;
    }
  }

  /// 🔄 Ajouter à la liste de synchronisation
  static Future<void> _addToPendingSync(Map<String, dynamic> adminData) async {
    final prefs = await SharedPreferences.getInstance();
    
    final pendingList = prefs.getStringList(_pendingSyncKey) ?? [];
    pendingList.add(jsonEncode(adminData));
    
    await prefs.setStringList(_pendingSyncKey, pendingList);
    
    debugPrint('[OFFLINE_ADMIN] 📤 Admin ajouté à la file de synchronisation');
  }

  /// 🔄 Synchroniser avec Firestore quand disponible
  static Future<Map<String, dynamic>> syncWithFirestore() async {
    try {
      debugPrint('[OFFLINE_ADMIN] 🔄 Début synchronisation avec Firestore...');
      
      final prefs = await SharedPreferences.getInstance();
      final pendingList = prefs.getStringList(_pendingSyncKey) ?? [];
      
      if (pendingList.isEmpty) {
        return {
          'success': true,
          'synced': 0,
          'message': 'Aucun admin en attente de synchronisation',
        };
      }

      int syncedCount = 0;
      final failedSyncs = <String>[];

      for (final adminJson in pendingList) {
        try {
          final adminData = Map<String, dynamic>.from(jsonDecode(adminJson));
          
          // Essayer de synchroniser avec Firestore
          final syncResult = await _syncSingleAdmin(adminData);
          
          if (syncResult['success']) {
            syncedCount++;
            debugPrint('[OFFLINE_ADMIN] ✅ Admin ${adminData['email']} synchronisé');
          } else {
            failedSyncs.add(adminJson);
            debugPrint('[OFFLINE_ADMIN] ❌ Échec sync ${adminData['email']}: ${syncResult['error']}');
          }
          
        } catch (e) {
          failedSyncs.add(adminJson);
          debugPrint('[OFFLINE_ADMIN] ❌ Erreur sync admin: $e');
        }
      }

      // Mettre à jour la liste des admins en attente
      await prefs.setStringList(_pendingSyncKey, failedSyncs);

      debugPrint('[OFFLINE_ADMIN] 📊 Synchronisation terminée: $syncedCount/${pendingList.length} réussies');

      return {
        'success': true,
        'synced': syncedCount,
        'failed': failedSyncs.length,
        'total': pendingList.length,
        'message': '$syncedCount admins synchronisés avec Firestore',
      };

    } catch (e) {
      debugPrint('[OFFLINE_ADMIN] ❌ Erreur synchronisation: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// 🔄 Synchroniser un seul admin avec Firestore
  static Future<Map<String, dynamic>> _syncSingleAdmin(Map<String, dynamic> adminData) async {
    try {
      // Utiliser le service robuste directement
      // (import déjà fait en haut du fichier)
      
      // Nettoyer les données pour Firestore
      final firestoreData = Map<String, dynamic>.from(adminData);
      firestoreData.remove('isOfflineCreated');
      firestoreData.remove('needsFirestoreSync');
      firestoreData['synced_at'] = DateTime.now().toIso8601String();
      firestoreData['sync_source'] = 'offline_admin_service';

      // Écrire dans Firestore
      final writeResult = await RobustFirestoreService.writeDocumentWithRetry(
        collection: 'users',
        documentId: adminData['uid'],
        data: firestoreData,
        maxRetries: 3,
      );

      return writeResult;

    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// 🔑 Générer un mot de passe sécurisé
  static String _generateSecurePassword() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = timestamp % 10000;
    return 'Admin${random}!';
  }

  /// 📊 Obtenir les statistiques hors ligne
  static Future<Map<String, dynamic>> getOfflineStats() async {
    final admins = await getOfflineAdmins();
    final prefs = await SharedPreferences.getInstance();
    final pendingSync = prefs.getStringList(_pendingSyncKey) ?? [];

    return {
      'total_offline_admins': admins.length,
      'pending_sync': pendingSync.length,
      'synced': admins.length - pendingSync.length,
      'last_created': admins.isNotEmpty ? admins.last['created_at'] : null,
    };
  }

  /// 🧹 Nettoyer les données hors ligne
  static Future<void> clearOfflineData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_offlineAdminsKey);
    await prefs.remove(_pendingSyncKey);
    debugPrint('[OFFLINE_ADMIN] 🧹 Données hors ligne supprimées');
  }

  /// ✅ Vérifier si un admin peut se connecter (local + Firestore)
  static Future<Map<String, dynamic>> validateAdminLogin({
    required String email,
    required String password,
  }) async {
    try {
      // D'abord chercher localement
      final localAdmin = await findAdminByEmail(email);
      
      if (localAdmin != null) {
        // Vérifier le mot de passe temporaire
        if (localAdmin['temporaryPassword'] == password) {
          debugPrint('[OFFLINE_ADMIN] ✅ Connexion admin hors ligne réussie: $email');
          
          return {
            'success': true,
            'admin': localAdmin,
            'isOffline': true,
            'message': 'Connexion hors ligne réussie',
          };
        }
      }

      // Si pas trouvé localement, essayer Firestore (si disponible)
      // TODO: Ajouter la vérification Firestore ici

      return {
        'success': false,
        'error': 'Email ou mot de passe incorrect',
      };

    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// 🎯 Méthode principale : Créer admin avec fallback automatique
  static Future<Map<String, dynamic>> createAdminWithFallback({
    required String email,
    required String nom,
    required String prenom,
    required String compagnieId,
    required String compagnieNom,
    String? phone,
    String? address,
  }) async {
    debugPrint('[OFFLINE_ADMIN] 🎯 Création admin avec fallback automatique...');

    // Essayer d'abord Firestore (rapide)
    try {
      // TODO: Essayer la création Firestore normale ici
      // Si ça marche, retourner le résultat
      
    } catch (e) {
      debugPrint('[OFFLINE_ADMIN] ⚠️ Firestore indisponible, passage en mode hors ligne
