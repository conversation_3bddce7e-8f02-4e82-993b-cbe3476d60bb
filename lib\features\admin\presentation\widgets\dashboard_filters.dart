import 'package:flutter/material.dart';
import 'package:constat_tunisie/features/admin/services/dashboard_filter_service.dart';

/// 🎛️ Widgets de filtres pour le Dashboard BI
class DashboardFilters {
  
  /// 📅 Sélecteur de période
  static Widget buildPeriodSelector({
    required String selectedPeriod,
    required Function(String) onPeriodChanged,
    bool showCustomRange = true,
  }) {
    return Container(
      padding: ,
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: selectedPeriod,
          icon: const Icon(Icons.info),
          items: [
            ...DashboardFilterService.predefinedPeriods.keys.map((period) {
              return DropdownMenuItem(
                value: period,
                child: const Text(
                  period,
                  style: const TextStyle(fontSize: 14),
                ),
              );
            },
            if (showCustomRange)
              const DropdownMenuItem(
                value: 'Personnalisé',
                child: const Text(
                  'Personnalisé',
                  style: TextStyle(fontSize: 14),
                ),
              ),
          ],
          onChanged: (value) {
            if (value != null) {
              onPeriodChanged(value);
            }
          },
        ),
      ),
    );
  }

  /// 🎯 Filtre par rôle
  static Widget buildRoleFilter({
    required String selectedRole,
    required Function(String) onRoleChanged,
  }) {
    return Container(
      padding: ,
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: selectedRole,
          icon: const Icon(Icons.info),
          items: DashboardFilterService.filterTypes.map((role) {
            return DropdownMenuItem(
              value: role,
              child: const Text(
                role,
                style: const TextStyle(fontSize: 14),
              ),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              onRoleChanged(value);
            }
          },
        ),
      ),
    );
  }

  /// 📊 Filtre par statut
  static Widget buildStatusFilter({
    required String selectedStatus,
    required Function(String) onStatusChanged,
  }) {
    return Container(
      padding: ,
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: selectedStatus,
          icon: const Icon(Icons.info),
          items: DashboardFilterService.statusFilters.map((status) {
            return DropdownMenuItem(
              value: status,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: _getStatusColor(status),
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    status,
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              ),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              onStatusChanged(value);
            }
          },
        ),
      ),
    );
  }

  /// 🏢 Filtre par compagnie
  static Widget buildCompanyFilter({
    required String selectedCompany,
    required List<String> companies,
    required Function(String) onCompanyChanged,
  }) {
    final allCompanies = ['Toutes', ...companies];
    
    return Container(
      padding: ,
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: selectedCompany,
          icon: const Icon(Icons.info),
          items: allCompanies.map((company) {
            return DropdownMenuItem(
              value: company,
              child: const Text(
                company,
                style: const TextStyle(fontSize: 14),
                overflow: TextOverflow.ellipsis,
              ),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              onCompanyChanged(value);
            }
          },
        ),
      ),
    );
  }

  /// 🏛️ Barre de filtres complète
  static Widget buildFilterBar({
    required String selectedPeriod,
    required String selectedRole,
    required String selectedStatus,
    required String selectedCompany,
    required List<String> companies,
    required Function(String) onPeriodChanged,
    required Function(String) onRoleChanged,
    required Function(String) onStatusChanged,
    required Function(String) onCompanyChanged,
    VoidCallback? onResetFilters,
    VoidCallback? onExportData,
  }) {
    return Container(
      padding: ,
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              ,
              const SizedBox(width: 8),
              Expanded(
                child: ,
                ),
              ),
              if (onResetFilters != null)
                TextButton.const Icon(
                  onPressed: onResetFilters,
                  icon: const Icon(Icons.info),
                  label: const Text('Reset', style: TextStyle(fontSize: 12)),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.grey.shade600,
                    padding: ,
                ),
              if (onExportData != null) ...[
                const SizedBox(width: 4),
                ElevatedButton.const Icon(
                  onPressed: onExportData,
                  icon: const Icon(Icons.info),
                  label: const Text('PDF', style: TextStyle(fontSize: 12)),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF667eea),
                    foregroundColor: Colors.white,
                    padding: ,
                ),
              ],
            ],
          ),
          const SizedBox(height: 16),
          // Utiliser une grille responsive au lieu de Wrap
          LayoutBuilder(
            builder: (context, constraints) {
              final isSmallScreen = constraints.maxWidth < 600;

              if (isSmallScreen) {
                // Affichage vertical pour petits écrans
                return Column(
                  children: [
                    _buildFilterItem('Période', buildPeriodSelector(
                      selectedPeriod: selectedPeriod,
                      onPeriodChanged: onPeriodChanged,
                    )),
                    const SizedBox(height: 12),
                    _buildFilterItem('Rôle', buildRoleFilter(
                      selectedRole: selectedRole,
                      onRoleChanged: onRoleChanged,
                    )),
                    const SizedBox(height: 12),
                    _buildFilterItem('Statut', buildStatusFilter(
                      selectedStatus: selectedStatus,
                      onStatusChanged: onStatusChanged,
                    )),
                    if (companies.isNotEmpty) ...[
                      const SizedBox(height: 12),
                      _buildFilterItem('Compagnie', buildCompanyFilter(
                        selectedCompany: selectedCompany,
                        companies: companies,
                        onCompanyChanged: onCompanyChanged,
                      )),
                    ],
                  ],
                );
              } else {
                // Affichage horizontal pour grands écrans
                return Wrap(
                  spacing: 12,
                  runSpacing: 12,
                  children: [
                    _buildFilterItem('Période', buildPeriodSelector(
                      selectedPeriod: selectedPeriod,
                      onPeriodChanged: onPeriodChanged,
                    )),
                    _buildFilterItem('Rôle', buildRoleFilter(
                      selectedRole: selectedRole,
                      onRoleChanged: onRoleChanged,
                    )),
                    _buildFilterItem('Statut', buildStatusFilter(
                      selectedStatus: selectedStatus,
                      onStatusChanged: onStatusChanged,
                    )),
                    if (companies.isNotEmpty)
                      _buildFilterItem('Compagnie', buildCompanyFilter(
                        selectedCompany: selectedCompany,
                        companies: companies,
                        onCompanyChanged: onCompanyChanged,
                      )),
                  ],
                );
              }
            },
          ),
        ],
      ),
    );
  }

  /// 🚨 Badges d'alerte
  static Widget buildAlertBadges(List<Map<String, dynamic>> badges) {
    if (badges.isEmpty) {
      return ,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              ,
              const SizedBox(width: 8),
              ,
              ),
            ],
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: badges.map((badge) {
              final level = badge['level'] as String;
              final color = DashboardFilterService.getAlertColor(level);
              
              return Container(
                padding: ,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: color.withValues(alpha: 0.3)),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      badge['icon'] as IconData,
                      size: 16,
                      color: color,
                    ),
                    const SizedBox(width: 6),
                    ,
                    ),
                    const SizedBox(width: 4),
                    Container(
                      padding: ,
                      ),
                      child: ({badge['count']}',
                        style: ,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  /// 🎨 Obtenir la couleur du statut
  static Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'actif':
        return const Color(0xFF10B981);
      case 'inactif':
        return const Color(0xFF6B7280);
      case 'bloqué':
        return const Color(0xFFEF4444);
      case 'désactivé
