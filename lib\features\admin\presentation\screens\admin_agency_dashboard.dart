import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/admin_providers.dart';
import '../../models/admin_models.dart';
import '../widgets/admin_stats_card.dart';
import '../widgets/admin_data_table.dart';
import '../widgets/admin_action_button.dart';
import 'agents_management_screen.dart';

/// 🏬 Admin Agence Dashboard - Gestion des données de l'agence
class AdminAgencyDashboard extends ConsumerStatefulWidget {
  final String agenceId;
  
  ;

  @override
  ConsumerState<AdminAgencyDashboard> createState() => _AdminAgencyDashboardState();
}

class _AdminAgencyDashboardState extends ConsumerState<AdminAgencyDashboard>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildHomeTab(),
                _buildAgentsTab(),
                _buildContractsTab(),
                _buildSinistresTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 📱 AppBar avec informations de l'agence
  PreferredSizeWidget _buildAppBar() {
    final agenceAsync = ref.watch(agenceProvider(widget.agenceId));
    
    return AppBar(
      elevation: 0,
      backgroundColor: const Color(0xFF7C3AED),
      title: agenceAsync.when(
        data: (agence) => Row(
          children: [
            ,
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ,
                ),
                ,
                ),
              ],
            ),
          ],
        ),
        loading: () => Row(
          children: [
            ,
            const SizedBox(width: 12),
            ,
            ),
          ],
        ),
        error: (error, stack) => Row(
          children: [
            ,
            const SizedBox(width: 12),
            ,
            ),
          ],
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.info),
          onPressed: () => _showNotifications(),
        ),
        IconButton(
          icon: const Icon(Icons.info),
          onPressed: () => _logout(),
        ),
        const SizedBox(width: 16),
      ],
    );
  }

  /// 📋 TabBar pour la navigation
  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        indicatorColor: const Color(0xFF7C3AED),
        labelColor: const Color(0xFF7C3AED),
        unselectedLabelColor: const Color(0xFF64748B),
        labelStyle: ,
        tabs: const [ text: 'Tableau de Bord'),
          Tab(icon: const Icon(Icons.info), text: 'Mes Agents'),
          Tab(icon: const Icon(Icons.info), text: 'Mes Contrats'),
          Tab(icon: const Icon(Icons.info), text: 'Mes Sinistres'),
        ],
      ),
    );
  }

  /// 🏠 Onglet Tableau de Bord
  Widget _buildHomeTab() {
    final statsAsync = ref.watch(agenceStatsProvider(widget.agenceId));
    final agentsStatsAsync = ref.watch(agenceAgentsStatsProvider(widget.agenceId));

    return statsAsync.when(
      data: (stats) => SingleChildScrollView(
        padding: ,
              ),
            ),
            const SizedBox(height: 24),

            // Cartes de statistiques
            GridView.count(
              shrinkWrap: true,
              physics: ,
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 1.5,
              children: [
                // Statistiques des agents avec données en temps réel
                agentsStatsAsync.when(
                  data: (agentsStats) => AdminStatsCard(
                    title: 'Mes Agents',
                    value: agentsStats['totalAgents']?.toString() ?? '0',
                    icon: Icons.people,
                    color: const Color(0xFF7C3AED),
                  ),
                  loading: () => ,
                  ),
                  error: (_, __) => AdminStatsCard(
                    title: 'Mes Agents',
                    value: stats['users']?.toString() ?? '0',
                    icon: Icons.people,
                    color: const Color(0xFF7C3AED),
                  ),
                ),
                AdminStatsCard(
                  title: 'Mes Contrats',
                  value: stats['contracts']?.toString() ?? '0',
                  icon: Icons.description,
                  color: const Color(0xFF3B82F6),
                ),
                AdminStatsCard(
                  title: 'Mes Sinistres',
                  value: stats['sinistres']?.toString() ?? '0',
                  icon: Icons.report_problem,
                  color: const Color(0xFFEF4444),
                ),
                // Agents actifs avec données en temps réel
                agentsStatsAsync.when(
                  data: (agentsStats) => AdminStatsCard(
                    title: 'Agents Actifs',
                    value: agentsStats['agentsActifs']?.toString() ?? '0',
                    icon: Icons.check_circle,
                    color: const Color(0xFF10B981),
                  ),
                  loading: () => ,
                  ),
                  error: (_, __) => ,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 32),
            
            // Actions  ,
              ),
            ),
            const SizedBox(height: 16),
            
            Wrap(
              spacing: 16,
              runSpacing: 16,
              children: [
                AdminActionButton(
                  title: 'Nouveau Contrat',
                  icon: Icons.add_circle,
                  color: const Color(0xFF7C3AED),
                  onPressed: () => _createContract(),
                ),
                AdminActionButton(
                  title: 'Rapport Agence',
                  icon: Icons.analytics,
                  color: const Color(0xFF3B82F6),
                  onPressed: () => _generateAgencyReport(),
                ),
                AdminActionButton(
                  title: 'Gérer Agents',
                  icon: Icons.people_alt,
                  color: const Color(0xFF10B981),
                  onPressed: () => _tabController.animateTo(1),
                ),
                AdminActionButton(
                  title: 'Voir Sinistres',
                  icon: Icons.warning,
                  color: const Color(0xFFEF4444),
                  onPressed: () => _tabController.animateTo(3),
                ),
              ],
            ),
          ],
        ),
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => (error')),
    );
  }

  /// 👨‍💼 Onglet Agents
  Widget _buildAgentsTab() {
    return AgentsManagementScreen(
      agenceId: widget.agenceId,
      compagnieId: _getCompagnieId(),
    );
  }

  /// 📋 Onglet Contrats
  Widget _buildContractsTab() {
    final contractsAsync = ref.watch(contractsByAgenceProvider(widget.agenceId));

    return contractsAsync.when(
      data: (contracts) => (1),
                  ),
                ),
                ElevatedButton.const Icon(
                  onPressed: () => _createContract(),
                  icon: const Icon(Icons.info),
                  label: const Text('Nouveau Contrat'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF7C3AED),
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            Expanded(
              child: AdminDataTable<ContractAssurance>(
                data: contracts,
                columns: const [
                  'N° Contrat',
                  'Conducteur',
                  'Véhicule',
                  'Type',
                  'Montant',
                  'Début',
                  'Fin',
                  'Statut',
                  'Actions',
                ],
                buildRow: (contract) => [
                  contract.numeroContrat,
                  contract.conducteurId,
                  contract.vehiculeId ?? 'N/A',
                  contract.typeAssurance,
                  '${contract.montantPrime} DT',
                  _formatDate(contract.dateDebut),
                  _formatDate(contract.dateFin),
                  _buildStatusChip(contract.isValid ? 'Valide' : 'Expiré'),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: ),
                        onPressed: () => _viewContract(contract),
                      ),
                      IconButton(
                        icon: ),
                        onPressed: () => _editContract(contract),
                      ),
                      IconButton(
                        icon: ),
                        onPressed: () => _printContract(contract),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => (error')),
    );
  }

  /// 🚧 Onglet Sinistres
  Widget _buildSinistresTab() {
    final sinistresAsync = ref.watch(sinistresByAgenceProvider(widget.agenceId));

    return sinistresAsync.when(
      data: (sinistres) => (1),
              ),
            ),
            const SizedBox(height: 24),

            Expanded(
              child: AdminDataTable<SinistreAssurance>(
                data: sinistres,
                columns: const [
                  'N° Sinistre',
                  'Date',
                  'Lieu',
                  'Conducteur',
                  'Expert',
                  'Montant Estimé',
                  'Statut',
                  'Actions',
                ],
                buildRow: (sinistre) => [
                  sinistre.numeroSinistre,
                  _formatDate(sinistre.dateAccident),
                  sinistre.lieuAccident,
                  sinistre.conducteurAId,
                  sinistre.expertId ?? 'Non assigné',
                  sinistre.montantEstime != null ? '${sinistre.montantEstime} DT' : 'N/A',
                  _buildStatusChip(sinistre.statut),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: ),
                        onPressed: () => _viewSinistre(sinistre),
                      ),
                      IconButton(
                        icon: ),
                        onPressed: () => _assignExpert(sinistre),
                      ),
                      IconButton(
                        icon: ),
                        onPressed: () => _viewPhotos(sinistre),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => (error')),
    );
  }

  // Helpers
  String? _getCompagnieId() {
    final agenceAsync = ref.read(agenceProvider(widget.agenceId));
    return agenceAsync.value?.compagnieId;
  }

  // Actions
  void _showNotifications() {
    // TODO: Implémenter les notifications
  }

  void _logout() {
    // TODO: Implémenter la déconnexion
  }

  void _createContract() {
    // TODO: Implémenter la création de contrat
  }

  void _generateAgencyReport() {
    // TODO: Implémenter la génération de rapport d'agence
  }

  // Méthodes utilitaires
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Widget _buildStatusChip(String status) {
    Color color;
    String label;

    switch (status.toLowerCase()) {
      case 'active':
      case 'valide':
      case 'paye':
        color = const Color(0xFF10B981);
        label = status;
        break;
      case 'suspended':
      case 'expiré':
      case 'impaye':
        color = const Color(0xFFEF4444);
        label = status;
        break;
      case 'pending':
      case 'en_cours':
        color = const Color(0xFFF59E0B);
        label = status;
        break;
      default:
        color = const Color(0xFF64748B);
        label = status;
    }

    return Container(
      padding: ,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: ,
      ),
    );
  }

  // Nouvelles méthodes d'action
  void _viewContract(ContractAssurance contract) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: ({contract.numeroContrat} - À implémenter')),
    );
  }

  void _editContract(ContractAssurance contract) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: ({contract.numeroContrat} - À implémenter')),
    );
  }

  void _printContract(ContractAssurance contract) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: ({contract.numeroContrat} - À implémenter')),
    );
  }

  void _viewSinistre(SinistreAssurance sinistre) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: ({sinistre.numeroSinistre} - À implémenter')),
    );
  }

  void _assignExpert(SinistreAssurance sinistre) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: ({sinistre.numeroSinistre} - À implémenter')),
    );
  }

  void _viewPhotos(SinistreAssurance sinistre) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: ({sinistre.numeroSinistre} - À implémenter
