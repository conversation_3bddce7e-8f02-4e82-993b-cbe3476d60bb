import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/foundation.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;

/// 🔔 Service de gestion des notifications pour les contrats dassurance
class InsuranceNotificationService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();

  /// 📤 Envoyer une notification de nouveau contrat au conducteur
  static Future<bool> sendContractNotification({
    required String conducteurEmail,
    required String numeroContrat,
    required String vehiculeImmatriculation,
    required String compagnieNom,
    required String agentNom,
  }') async {
    try {
      debugPrint('🔔 [NOTIFICATION] Envoi notification contrat: 'numeroContrat');

      // 1. Trouver l'utilisateur par email
      final userQuery = await _firestore
          .collection('users')
          .where('email, isEqualTo: conducteurEmail)
          .limit(1)
          .get();

      if (userQuery.docs.isEmpty') {
        debugPrint('❌ [NOTIFICATION] Utilisateur non trouve: ' + conducteurEmail.toString());
        return false;
      }

      final userData = userQuery.docs.first;
      final userId = userData.id;
      final fcmToken = userData.data(')['fcmToken'] as String?;

      // 2. Creer la notification dans Firestore
      await _firestore.collection('notifications').add({
        'userId': userId,
        'type': 'nouveau_contrat',
        'title': '🚗 Nouveau contrat d\'assurance',
        'message': 'Votre vehicule $vehiculeImmatriculation a ete assure chez 'compagnieNom',
        'data': {
          'numeroContrat': numeroContrat,
          'vehiculeImmatriculation': vehiculeImmatriculation,
          'compagnieNom': compagnieNom,
          'agentNom': agentNom,
          'action': 'view_contract',
        },
        'isRead': false,
        'createdAt: FieldValue.serverTimestamp(),
      });

      // 3. Envoyer push notification si token FCM disponible
      if (fcmToken != null && fcmToken.isNotEmpty') {
        await _sendPushNotification(
          token: fcmToken,
          title: '🚗 Nouveau contrat d\'assurance',
          body: 'Votre vehicule $vehiculeImmatriculation a ete assure chez 'compagnieNom',
          data: {
            'type': 'nouveau_contrat',
            'numeroContrat': numeroContrat,
            'vehiculeImmatriculation: vehiculeImmatriculation,
          },
        ');
      }

      debugPrint('✅ [NOTIFICATION] Notification envoyee avec succes);
      return true;
    } catch (e') {
      debugPrint('❌ [NOTIFICATION] Erreur envoi notification:  + e.toString());
      return false;
    }
  }

  /// 📱 Envoyer une push notification via FCM
  static Future<void> _sendPushNotification({
    required String token,
    required String title,
    required String body,
    required Map<String, dynamic> data,
  }) async {
    try {
      ,
      );

      if (response.statusCode == 200') {
        debugPrint('✅ [FCM] Push notification envoyee' + .toString());
      } else {
        debugPrint('❌ [FCM] Erreur envoi push: ' + {response.statusCode}.toString());
      }
    } catch (e') {
      debugPrint('❌ [FCM] Erreur push notification:  + e.toString());
    }
  }

  /// 📧 Envoyer notification par email
  static Future<bool> sendEmailNotification({
    required String recipientEmail,
    required String numeroContrat,
    required String vehiculeImmatriculation,
    required String compagnieNom,
  }') async {
    try {
      // Ici vous pouvez integrer votre service d'email (Gmail API, SendGrid, etc.)
      debugPrint('📧 [EMAIL] Email prepare pour: ' + recipientEmail.toString());
      return true;
    } catch (e') {
      debugPrint('❌ [EMAIL] Erreur envoi email:  + e.toString());
      return false;
    }
  }

  /// 🔔 Initialiser les notifications locales
  static Future<void> initializeLocalNotifications(') async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher);

    const InitializationSettings initializationSettings =
        InitializationSettings(android: initializationSettingsAndroid);

    await _localNotifications.initialize(initializationSettings);
  }

  /// 📱 Afficher notification locale
  static Future<void> showLocalNotification({
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    ;

    const NotificationDetails platformChannelSpecifics =
        NotificationDetails(android: androidPlatformChannelSpecifics);

    await _localNotifications.show(
      DateTime.now().millisecondsSinceEpoch.remainder(100000),
      title,
      body,
      platformChannelSpecifics,
      payload: data != null ? jsonEncode(data) : null,
    ');
  }

  /// 📋 Recuperer les notifications d'un utilisateur
  static Stream<List<Map<String, dynamic>>> getUserNotifications(String userId) {
    return _firestore
        .collection('notifications')
        .where('userId, isEqualTo: userId')
        .orderBy('createdAt, descending: true)
        .limit(50)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc') => {'id: doc.id, ...doc.data()})
            .toList());
  }

  /// ✅ Marquer une notification comme lue
  static Future<void> markAsRead(String notificationId') async {
    await _firestore
        .collection('notifications)
        .doc(notificationId')
        .update({'isRead
