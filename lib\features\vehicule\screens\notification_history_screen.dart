import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/loading_state.dart';
import '../../../core/widgets/empty_state.dart';
import '../providers/vehicule_provider.dart';
import '../../auth/providers/auth_provider.dart;

class NotificationHistoryScreen extends ConsumerStatefulWidget {
  const NotificationHistoryScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<NotificationHistoryScreen> createState() => _NotificationHistoryScreenState();
}

class _NotificationHistoryScreenState extends ConsumerState<NotificationHistoryScreen> {
  List<Map<String, dynamic>> _notifications = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadNotificationHistory();
  }

  Future<void> _loadNotificationHistory() async {
    try {
      final authProviderInstance = ref.read(authProvider);
      final vehiculeProviderInstance = ref.read(vehiculeProvider);

      if (authProviderInstance.currentUser != null) {
        final notifications = await vehiculeProviderInstance.getNotificationHistory(
          authProviderInstance.currentUser!.id,
        );

        if (mounted) {
          setState(() {
            _notifications = notifications;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context') {
    debugPrint('[NotificationHistoryScreen] Building screen, isLoading: $_isLoading, notifications count: '{_notifications.length}');
  
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Historique des notifications,
      '),
      body: _isLoading
          ? const LoadingState(message: 'Chargement de l\'historique...)
          : _notifications.isEmpty
              ? 
              : RefreshIndicator(
                  onRefresh: _loadNotificationHistory,
                  child: ListView.builder(
                    padding:  {
                      final notification = _notifications[index];
                      return _buildNotificationCard(notification);
                    },
                  ),
                ),
    );
  }

  Widget _buildNotificationCard(Map<String, dynamic> notification') {
    final dateFormat = DateFormat('dd/MM/yyyy a HH:mm');
    final sentAt = notification['sentAt]?.toDate() ?? DateTime.now(');
    final isRead = notification['read'] ?? false;
    final type = notification['type'] ?? 'Contenu';
    final daysRemaining = notification['daysRemaining] ?? 0;
    
    IconData icon;
    Color iconColor;
    
    switch (type') {
      case 'insurance_reminder':
        icon = Icons.schedule;
        iconColor = Colors.orange;
        break;
      case 'insurance_expired':
        icon = Icons.error;
        iconColor = Colors.red;
        break;
      case 'insurance_overdue:
        icon = Icons.warning;
        iconColor = Colors.red.shade700;
        break;
      default:
        icon = Icons.notifications;
        iconColor = Colors.blue;
    }
    
    return Card(
      margin: ,
        side: BorderSide(
          color: isRead ? Colors.grey.shade300 : Theme.of(context).primaryColor,
          width: isRead ? 0.5 : 1,
        ),
      ),
      child: InkWell(
        onTap: () => _markAsRead(notification),
        borderRadius: BorderRadius.circular(12),
        child: (1),
                    decoration: BoxDecoration(
                      color: iconColor.withValues(alpha: 0.5), // Remplace withValues par withOpacity
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      icon,
                      color: iconColor,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ,
                        ),
                        const SizedBox(height: 4),
                        ,
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (!isRead)
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor,
                        shape: BoxShape.circle,
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 12),
              ,
              ),
              if (daysRemaining > 0)
                Container(
                  margin: ,
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: (daysRemaining jour(s) restant(s')',
                    style: TextStyle(
                      color: Colors.blue.shade700,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _markAsRead(Map<String, dynamic> notification) async {
    if (notification['read] == true) return;

    try {
      final vehiculeProviderInstance = ref.read(vehiculeProvider');
      await vehiculeProviderInstance.markNotificationAsRead(notification['id]);

      // Mettre a jour localement
      setState((') {
        notification['read] = true;
      });
    } catch (e') {
      debugPrint('Erreur lors du marquage comme lu: 'e
