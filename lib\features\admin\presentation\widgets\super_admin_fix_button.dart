import 'package:flutter/material.dart';
import '../../services/super_admin_fix_service.dart';

/// 🔧 Bouton pour corriger les problèmes du Super Admin
class SuperAdminFixButton extends StatefulWidget {
  final VoidCallback? onFixComplete;
  
  ;

  @override
  State<SuperAdminFixButton> createState() => _SuperAdminFixButtonState();
}

class _SuperAdminFixButtonState extends State<SuperAdminFixButton> {
  bool _isFixing = false;
  bool _isDiagnosing = false;

  /// 🔧 Exécuter la correction
  Future<void> _runFix() async {
    setState(() => _isFixing = true);

    try {
      debugPrint('[SUPER_ADMIN_FIX_BUTTON] 🔧 Démarrage correction...');
      
      final result = await SuperAdminFixService.fixSuperAdminDocument();
      
      if (mounted) {
        if (result['success'] == true) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: ({result['message'] ?? 'Super Admin corrigé avec succès !'}'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
            ),
          );
          
          // Appeler le callback si fourni
          widget.onFixComplete?.call();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: ({result['error']}'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 5),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isFixing = false);
      }
    }
  }

  /// 🔍 Exécuter le diagnostic
  Future<void> _runDiagnosis() async {
    setState(() => _isDiagnosing = true);

    try {
      debugPrint('[SUPER_ADMIN_FIX_BUTTON] 🔍 Démarrage diagnostic...');
      
      // Afficher le diagnostic dans les logs
      await SuperAdminFixService.printFullDiagnosis();
      
      // Obtenir le diagnostic pour l'affichage
      final diagnosis = await SuperAdminFixService.diagnoseSuperAdminIssue();
      
      if (mounted) {
        _showDiagnosisDialog(diagnosis);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isDiagnosing = false);
      }
    }
  }

  /// 📊 Afficher le dialog de diagnostic
  void _showDiagnosisDialog(Map<String, dynamic> diagnosis) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            ,
            const SizedBox(width: 8),
            const Text('🔍 Diagnostic Super Admin'),
          ],
        ),
        content: (1) 
                        ? Colors.green.shade100 
                        : Colors.red.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        (diagnosis['can_create_users'] == true) 
                            ? Icons.check_circle 
                            : Icons.error,
                        color: (diagnosis['can_create_users'] == true) 
                            ? Colors.green 
                            : Colors.red,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: const Text(
                          (diagnosis['can_create_users'] == true)
                              ? '✅ Super Admin configuré correctement'
                              : '❌ Problèmes détectés avec Super Admin',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: (diagnosis['can_create_users'] == true) 
                                ? Colors.green.shade700 
                                : Colors.red.shade700,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Firebase  ,
                ),
                const SizedBox(height: 8),
                _buildInfoCard(diagnosis['firebase_auth'] as Map<String, dynamic>? ?? {},
                
                const SizedBox(height: 16),
                
                // Firestore  ,
                ),
                const SizedBox(height: 8),
                _buildInfoCard(diagnosis['firestore_document'] as Map<String, dynamic>? ?? {},
                
                // Problèmes
                ..._buildIssuesSection(diagnosis),

                // Recommandations
                ..._buildRecommendationsSection(diagnosis),
              ],
            ),
          ),
        ),
        actions: [
          if ((diagnosis['issues'] as List<dynamic>? ?? []).isNotEmpty)
            ElevatedButton.const Icon(
              onPressed: () {
                Navigator.of(context).pop();
                _runFix();
              },
              icon: const Icon(Icons.info),
              label: const Text('Corriger'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
            ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  /// 🎨 Construire une carte d'information
  Widget _buildInfoCard(Map<String, dynamic> data) {
    return Container(
      padding: ,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: data.entries.map((entry) {
          if (entry.key == 'data') return ,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ({entry.key}:',
                    style: ,
                  ),
                ),
                Expanded(
                  child:  ?? 'N/A',
                    style: TextStyle(
                      color: entry.value == true
                          ? Colors.green
                          : entry.value == false
                              ? Colors.red
                              : null,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  /// ❌ Construire la section des problèmes
  List<Widget> _buildIssuesSection(Map<String, dynamic> diagnosis) {
    final issues = diagnosis['issues'] as List<dynamic>? ?? [];
    if (issues.isEmpty) return [];

    return [
      const SizedBox(height: 16),
      ,
      ),
      const SizedBox(height: 8),
      ...issues.map((issue) => (1)),
            Expanded(child: )),
          ],
        ),
      )),
    ];
  }

  /// 💡 Construire la section des recommandations
  List<Widget> _buildRecommendationsSection(Map<String, dynamic> diagnosis) {
    final recommendations = diagnosis['recommendations'] as List<dynamic>? ?? [];
    if (recommendations.isEmpty) return [];

    return [
      const SizedBox(height: 16),
      ,
      ),
      const SizedBox(height: 8),
      ...recommendations.map((rec) => (1)),
            Expanded(child: )),
          ],
        ),
      )),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Bouton de diagnostic
        ElevatedButton.const Icon(
          onPressed: _isDiagnosing ? null : _runDiagnosis,
          icon: _isDiagnosing 
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : ,
          label: const Text('Diagnostic'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
          ),
        ),
        
        const SizedBox(width: 8),
        
        // Bouton de correction
        ElevatedButton.const Icon(
          onPressed: _isFixing ? null : _runFix,
          icon: _isFixing 
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : ,
          label: const Text('Corriger Super Admin
