import 'package:flutter/material.dart';
import '../../services/super_admin_diagnostic_service.dart';

/// 🔍 Bouton de diagnostic pour le Super Admin
class SuperAdminDiagnosticButton extends StatefulWidget {
  const Text(\;

  @override
  State<SuperAdminDiagnosticButton> createState() => _SuperAdminDiagnosticButtonState();
}

class _SuperAdminDiagnosticButtonState extends State<SuperAdminDiagnosticButton> {
  bool _isRunning = false;
  Map<String, dynamic>? _lastDiagnostic;

  /// 🔍 Exécuter le diagnostic
  Future<void> _runDiagnostic() async {
    setState(() => _isRunning = true);

    try {
      final diagnostic = await SuperAdminDiagnosticService.fullDiagnostic();
      setState(() => _lastDiagnostic = diagnostic);
      
      if (mounted) {
        _showDiagnosticDialog(diagnostic);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isRunning = false);
    }
  }

  /// 🔧 Corriger automatiquement les problèmes
  Future<void> _autoFix() async {
    setState(() => _isRunning = true);

    try {
      final created = await SuperAdminDiagnosticService.createSuperAdminFirestoreDocument();
      
      if (created) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: const Text('✅ Document Super Admin créé avec succès !'),
              backgroundColor: Colors.green,
            ),
          );
        }
        
        // Re-exécuter le diagnostic
        await _runDiagnostic();
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: const Text('❌ Échec de la correction automatique'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isRunning = false);
    }
  }

  /// 📊 Afficher le dialog de diagnostic
  void _showDiagnosticDialog(Map<String, dynamic> diagnostic) {
    final auth = diagnostic['firebase_auth'] as Map<String, dynamic>? ?? {};
    final firestore = diagnostic['firestore_user'] as Map<String, dynamic>? ?? {};
    final permissions = diagnostic['permissions'] as Map<String, dynamic>? ?? {};
    final recommendations = diagnostic['recommendations'] as List<dynamic>? ?? [];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            ,
            const SizedBox(width: 8),
            const Text('🔍 Diagnostic Super Admin'),
          ],
        ),
        content: ({auth['user_uid'] ?? 'N/A'}',
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // Firestore
                _buildSection(
                  '📄 Firestore',
                  [
                    'Document existe: ${firestore['document_exists'] == true ? '✅' : '❌'}',
                    if (firestore['document_exists'] == true) ...[
                      'Rôle: ${firestore['role'] ?? 'N/A'}',
                      'Super Admin: ${firestore['is_super_admin_role'] == true ? '✅' : '❌'}',
                      'Status: ${firestore['status'] ?? 'N/A'}',
                    ],
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // Permissions
                _buildSection(
                  '🔑 Permissions',
                  [
                    'Créer Admin Compagnie: ${permissions['can_create_admin_compagnie'] == true ? '✅' : '❌'}',
                    'Créer Admin Agence: ${permissions['can_create_admin_agence'] == true ? '✅' : '❌'}',
                    'Dashboard Super Admin: ${permissions['can_access_super_admin_dashboard'] == true ? '✅' : '❌'}',
                  ],
                ),
                
                // Recommandations
                if (recommendations.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  _buildSection(
                    '💡 Recommandations',
                    recommendations.map((r) => r.toString()).toList(),
                  ),
                ],
              ],
            ),
          ),
        ),
        actions: [
          if (firestore['document_exists'] != true)
            TextButton.const Icon(
              onPressed: _isRunning ? null : _autoFix,
              icon: const Icon(Icons.info),
              label: const Text('🔧 Corriger'),
            ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  /// 📋 Construire une section du diagnostic
  Widget _buildSection(String title, List<String> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        ),
        const SizedBox(height: 8),
        ...items.map((item) => (1),
          ),
        )),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Bouton diagnostic
        ElevatedButton.const Icon(
          onPressed: _isRunning ? null : _runDiagnostic,
          icon: _isRunning 
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : ,
          label: const Text('🔍 Diagnostic'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
          ),
        ),
        
        // Indicateur de statut
        if (_lastDiagnostic != null) ...[
          const SizedBox(width: 8),
          Container(
            padding: ,
              borderRadius: BorderRadius.circular(12),
            ),
            child: (1),
              style: ,
            ),
          ),
        ],
      ],
    );
  }

  /// 🎨 Obtenir la couleur du statut
  Color _getStatusColor() {
    if (_lastDiagnostic == null) return Colors.grey;
    
    final permissions = _lastDiagnostic!['permissions'] as Map<String, dynamic>? ?? {};
    final canCreate = permissions['can_create_admin_compagnie'] == true;
    
    return canCreate ? Colors.green : Colors.red;
  }

  /// 📝 Obtenir le texte du statut
  String _getStatus {
    if (_lastDiagnostic == null) return 'Non testé';
    
    final permissions = _lastDiagnostic!['permissions'] as Map<String, dynamic>? ?? {};
    final canCreate = permissions['can_create_admin_compagnie'] == true;
    
    return canCreate ? 'OK' : 'Problème
