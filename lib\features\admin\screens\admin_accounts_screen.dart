import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../services/admin_accounts_manager.dart';

/// 👨‍💼 Écran de gestion des comptes administrateurs
class AdminAccountsScreen extends StatefulWidget {
  const AdminAccountsScreen({Key? key}) ) : super(key: key);

  @override
  State<AdminAccountsScreen> createState() => _AdminAccountsScreenState();
}

class _AdminAccountsScreenState extends State<AdminAccountsScreen> {
  bool _isLoading = false;
  Map<String, dynamic>? _creationResults;
  Map<String, dynamic>? _stats;

  @override
  void initState() {
    super.initState();
    _loadStats();
  }

  /// 📊 Charger les statistiques
  Future<void> _loadStats() async {
    final stats = await AdminAccountsManager.getAdminStats();
    setState(() => _stats = stats);
  }

  /// 🔧 Créer tous les comptes admin
  Future<void> _createAllAccounts() async {
    setState(() => _isLoading = true);

    try {
      final results = await AdminAccountsManager.createAllAdminAccounts();
      setState(() => _creationResults = results);
      await _loadStats(); // Recharger les stats
      
      _showResultsDialog(results);
    } catch (e) {
      _showErrorDialog('Erreur lors de la création des comptes: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 🧪 Tester un compte admin
  Future<void> _testAccount(String email, String password) async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            const SizedBox(width: 16),
            const Text('Test de connexion...'),
          ],
        ),
      ),
    );

    final result = await AdminAccountsManager.testAdminLogin(email, password);
    
    if (mounted) {
      Navigator.of(context).pop(); // Fermer le dialog de chargement
      
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text(result['success'] ? '✅ Test Réussi' : '❌ Test Échoué'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              (email'),
              const SizedBox(height: 8),
              ({result['message']}'),
              if (result['success']) ...[
                const SizedBox(height: 8),
                ({result['adminType']}'),
                ({result['nom']}'),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        ),
      );
    }
  }

  /// 📋 Afficher les résultats de création
  void _showResultsDialog(Map<String, dynamic> results) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🎉 Résultats de Création'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ({results['success'].length}'),
              ({results['existing'].length}'),
              ({results['errors'].length}'),
              
              if (results['success'].isNotEmpty) ...[
                const SizedBox(height: 16),
                ),
                for (final email in results['success'])
                  (email', style: const TextStyle()),
              ],
              
              if (results['errors'].isNotEmpty) ...[
                const SizedBox(height: 16),
                ),
                for (final error in results['errors'])
                  (error', style: const TextStyle()),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// ⚠️ Afficher dialog d'erreur
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('❌ Erreur'),
        content: const Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// 📋 Copier email et mot de passe
  void _copyCredentials(String email, String password) {
    Clipboard.setData(ClipboardData(text: 'Email: $email\nMot de passe: $password'));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: const Text('📋 Identifiants copiés dans le presse-papiers'),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('👨‍💼 Comptes Administrateurs'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.info),
            onPressed: _loadStats,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding:  _buildStatsCard(),
            
            const SizedBox(height: 16),
            
            // Boutons d'action
            Row(
              children: [
                Expanded(child: _buildCreateButton()),
                const SizedBox(width: 12),
                Expanded(child: _buildSyncButton()),
              ],
            ),

            const SizedBox(height: 24),
            
            // Liste des comptes
            _buildAccountsList(),
          ],
        ),
      ),
    );
  }

  /// 📊 Card des statistiques
  Widget _buildStatsCard() {
    final stats = _stats!['stats'] as Map<String, int>? ?? {};
    
    return Card(
      child: (1),
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem('Total', stats['total'] ?? 0, Colors.blue),
                _buildStatItem('Super Admin', stats['super_admin'] ?? 0, Colors.purple),
                _buildStatItem('Compagnies', stats['admin_compagnie'] ?? 0, Colors.orange),
                _buildStatItem('Agences', stats['admin_agence'] ?? 0, Colors.green),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 📈 Item de statistique
  Widget _buildStatItem(String label, int value, Color color) {
    return Column(
      children: [
        ,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const Text(
          label,
          style: const TextStyle(fontSize: 12),
        ),
      ],
    );
  }

  /// 🔧 Bouton de création
  Widget _buildCreateButton() {
    return ElevatedButton.const Icon(
      onPressed: _isLoading ? null : _createAllAccounts,
      icon: _isLoading
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : ,
      label: const Text(_isLoading ? 'Création...' : 'Créer Comptes'),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        padding: ,
    );
  }

  /// 🔄 Bouton de synchronisation
  Widget _buildSyncButton() {
    return ElevatedButton.const Icon(
      onPressed: _isLoading ? null : _navigateToSync,
      icon: const Icon(Icons.info),
      label: const Text('Synchroniser'),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        padding: ,
    );
  }

  /// 🔄 Naviguer vers l'écran de synchronisation
  void _navigateToSync() {
    // TODO: Implémenter la synchronisation
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: const Text('🚧 Synchronisation à implémenter')),
    );
  }

  /// 📋 Liste des comptes
  Widget _buildAccountsList() {
    final accounts = AdminAccountsManager.getFormattedAdminEmails();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        ),
        const SizedBox(height: 16),
        
        for (final category in accounts.entries)
          _buildCategoryCard(category.key, category.value),
      ],
    );
  }

  /// 📂 Card de catégorie
  Widget _buildCategoryCard(String category, List<String> emails) {
    return Card(
      margin: ,
        ),
        children: emails.map((emailWithPassword) {
          final parts = emailWithPassword.split(' (');
          final email = parts[0];
          final password = parts[1].replaceAll(')', 'Contenu');
          
          return ListTile(
            leading: ,
            title: const Text(email),
            subtitle: (password'),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.info),
                  onPressed: () => _copyCredentials(email, password),
                  tooltip: 'Copier les identifiants',
                ),
                IconButton(
                  icon: const Icon(Icons.info),
                  onPressed: () => _testAccount(email, password),
                  tooltip: 'Tester la connexion
