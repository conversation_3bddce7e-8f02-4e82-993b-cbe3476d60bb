import 'package:flutter/material.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/config/app_routes.dart'; // Ajout de l'import manquant

class ConducteurAccidentsScreen extends StatelessWidget {
  const ConducteurAccidentsScreen({Key? key}) ) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Historique des constats',
      ),
      body: ,
            const SizedBox(height: 24),
            ,
            ),
            const SizedBox(height: 16),
            ,
            ),
            const SizedBox(height: 32),
            ElevatedButton.const Icon(
              onPressed: () {
                // Naviguer vers l'écran de création de constat
                Navigator.pushNamed(context, AppRoutes.conducteurDeclaration);
              },
              icon: const Icon(Icons.info),
              label: const Text('Créer un constat
