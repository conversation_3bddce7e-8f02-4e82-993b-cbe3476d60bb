import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/admin_providers.dart';
import '../../models/admin_models.dart';
import '../widgets/admin_stats_card.dart';
import '../widgets/admin_data_table.dart';
import '../widgets/admin_action_button.dart';

/// 🏢 Admin Compagnie Dashboard - Gestion des données de la compagnie
class AdminCompanyDashboard extends ConsumerStatefulWidget {
  final String compagnieId;
  
  ;

  @override
  ConsumerState<AdminCompanyDashboard> createState() => _AdminCompanyDashboardState();
}

class _AdminCompanyDashboardState extends ConsumerState<AdminCompanyDashboard>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildHomeTab(),
                _buildAgencesTab(),
                _buildUsersTab(),
                _buildContractsTab(),
                _buildSinistresTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 📱 AppBar avec informations de la compagnie
  PreferredSizeWidget _buildAppBar() {
    final compagnieAsync = ref.watch(compagnieProvider(widget.compagnieId));
    
    return AppBar(
      elevation: 0,
      backgroundColor: const Color(0xFF059669),
      title: compagnieAsync.when(
        data: (compagnie) => Row(
          children: [
            ,
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ,
                ),
                ,
                ),
              ],
            ),
          ],
        ),
        loading: () => Row(
          children: [
            ,
            const SizedBox(width: 12),
            ,
            ),
          ],
        ),
        error: (error, stack) => Row(
          children: [
            ,
            const SizedBox(width: 12),
            ,
            ),
          ],
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.info),
          onPressed: () => _showNotifications(),
        ),
        IconButton(
          icon: const Icon(Icons.info),
          onPressed: () => _logout(),
        ),
        const SizedBox(width: 16),
      ],
    );
  }

  /// 📋 TabBar pour la navigation
  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        indicatorColor: const Color(0xFF059669),
        labelColor: const Color(0xFF059669),
        unselectedLabelColor: const Color(0xFF64748B),
        labelStyle: ,
        tabs: const [ text: 'Tableau de Bord'),
          Tab(icon: const Icon(Icons.info), text: 'Mes Agences'),
          Tab(icon: const Icon(Icons.info), text: 'Mes Utilisateurs'),
          Tab(icon: const Icon(Icons.info), text: 'Mes Contrats'),
          Tab(icon: const Icon(Icons.info), text: 'Mes Sinistres'),
        ],
      ),
    );
  }

  /// 🏠 Onglet Tableau de Bord
  Widget _buildHomeTab() {
    final statsAsync = ref.watch(compagnieStatsProvider(widget.compagnieId));
    
    return statsAsync.when(
      data: (stats) => SingleChildScrollView(
        padding: ,
              ),
            ),
            const SizedBox(height: 24),
            
            // Cartes de statistiques
            GridView.count(
              shrinkWrap: true,
              physics: ,
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 1.5,
              children: [
                AdminStatsCard(
                  title: 'Mes Agences',
                  value: stats['agences']?.toString() ?? '0',
                  icon: Icons.store,
                  color: const Color(0xFF059669),
                ),
                AdminStatsCard(
                  title: 'Mes Utilisateurs',
                  value: stats['users']?.toString() ?? '0',
                  icon: Icons.people,
                  color: const Color(0xFF3B82F6),
                ),
                AdminStatsCard(
                  title: 'Mes Contrats',
                  value: stats['contracts']?.toString() ?? '0',
                  icon: Icons.description,
                  color: const Color(0xFFF59E0B),
                ),
                AdminStatsCard(
                  title: 'Mes Sinistres',
                  value: stats['sinistres']?.toString() ?? '0',
                  icon: Icons.report_problem,
                  color: const Color(0xFFEF4444),
                ),
              ],
            ),
            
            const SizedBox(height: 32),
            
            // Actions  ,
              ),
            ),
            const SizedBox(height: 16),
            
            Wrap(
              spacing: 16,
              runSpacing: 16,
              children: [
                AdminActionButton(
                  title: 'Nouvelle Agence',
                  icon: Icons.add_location,
                  color: const Color(0xFF059669),
                  onPressed: () => _createAgence(),
                ),
                AdminActionButton(
                  title: 'Rapport Mensuel',
                  icon: Icons.analytics,
                  color: const Color(0xFF3B82F6),
                  onPressed: () => _generateMonthlyReport(),
                ),
                AdminActionButton(
                  title: 'Gérer Agents',
                  icon: Icons.people_alt,
                  color: const Color(0xFF8B5CF6),
                  onPressed: () => _tabController.animateTo(2),
                ),
                AdminActionButton(
                  title: 'Voir Sinistres',
                  icon: Icons.warning,
                  color: const Color(0xFFEF4444),
                  onPressed: () => _tabController.animateTo(4),
                ),
              ],
            ),
          ],
        ),
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => (error')),
    );
  }

  /// 🏬 Onglet Agences
  Widget _buildAgencesTab() {
    final agencesAsync = ref.watch(agencesByCompagnieProvider(widget.compagnieId));
    
    return agencesAsync.when(
      data: (agences) => (1),
                  ),
                ),
                ElevatedButton.const Icon(
                  onPressed: () => _createAgence(),
                  icon: const Icon(Icons.info),
                  label: const Text('Nouvelle Agence'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF059669),
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            Expanded(
              child: AdminDataTable<AgenceAssurance>(
                data: agences,
                columns: const [
                  'Nom',
                  'Code',
                  'Ville',
                  'Gouvernorat',
                  'Responsable',
                  'Téléphone',
                  'Actions',
                ],
                buildRow: (agence) => [
                  agence.nom,
                  agence.code,
                  agence.ville,
                  agence.gouvernorat,
                  agence.responsable ?? 'N/A',
                  agence.telephone ?? 'N/A',
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: ),
                        onPressed: () => _viewAgence(agence),
                      ),
                      IconButton(
                        icon: ),
                        onPressed: () => _editAgence(agence),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => (error')),
    );
  }

  /// 👤 Onglet Utilisateurs
  Widget _buildUsersTab() {
    final usersAsync = ref.watch(usersByCompagnieProvider(widget.compagnieId));

    return usersAsync.when(
      data: (users) => (1),
              ),
            ),
            const SizedBox(height: 24),

            Expanded(
              child: AdminDataTable<UserProfile>(
                data: users,
                columns: const [
                  'Nom',
                  'Email',
                  'Rôle',
                  'Agence',
                  'Téléphone',
                  'Statut',
                  'Actions',
                ],
                buildRow: (user) => [
                  user.fullName,
                  user.email,
                  _getRoleDisplayName(user.role),
                  user.agenceId ?? 'N/A',
                  user.phone,
                  _buildStatusChip(user.status),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: ),
                        onPressed: () => _viewUser(user),
                      ),
                      IconButton(
                        icon: ),
                        onPressed: () => _editUser(user),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => (error')),
    );
  }

  /// 📋 Onglet Contrats
  Widget _buildContractsTab() {
    final contractsAsync = ref.watch(contractsByCompagnieProvider(widget.compagnieId));

    return contractsAsync.when(
      data: (contracts) => (1),
              ),
            ),
            const SizedBox(height: 24),

            Expanded(
              child: AdminDataTable<ContractAssurance>(
                data: contracts,
                columns: const [
                  'N° Contrat',
                  'Conducteur',
                  'Agence',
                  'Type',
                  'Montant',
                  'Validité',
                  'Statut',
                  'Actions',
                ],
                buildRow: (contract) => [
                  contract.numeroContrat,
                  contract.conducteurId,
                  contract.agenceId,
                  contract.typeAssurance,
                  '${contract.montantPrime} DT',
                  _formatDate(contract.dateFin),
                  _buildStatusChip(contract.isValid ? 'Valide' : 'Expiré'),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: ),
                        onPressed: () => _viewContract(contract),
                      ),
                      IconButton(
                        icon: ),
                        onPressed: () => _editContract(contract),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => (error')),
    );
  }

  /// 🚧 Onglet Sinistres
  Widget _buildSinistresTab() {
    final sinistresAsync = ref.watch(sinistresByCompagnieProvider(widget.compagnieId));

    return sinistresAsync.when(
      data: (sinistres) => (1),
              ),
            ),
            const SizedBox(height: 24),

            Expanded(
              child: AdminDataTable<SinistreAssurance>(
                data: sinistres,
                columns: const [
                  'N° Sinistre',
                  'Date',
                  'Lieu',
                  'Conducteur',
                  'Expert',
                  'Statut',
                  'Actions',
                ],
                buildRow: (sinistre) => [
                  sinistre.numeroSinistre,
                  _formatDate(sinistre.dateAccident),
                  sinistre.lieuAccident,
                  sinistre.conducteurAId,
                  sinistre.expertId ?? 'Non assigné',
                  _buildStatusChip(sinistre.statut),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: ),
                        onPressed: () => _viewSinistre(sinistre),
                      ),
                      IconButton(
                        icon: ),
                        onPressed: () => _assignExpert(sinistre),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => (error')),
    );
  }

  // Actions
  void _showNotifications() {
    // TODO: Implémenter les notifications
  }

  void _logout() {
    // TODO: Implémenter la déconnexion
  }

  void _createAgence() {
    // TODO: Implémenter la création d'agence
  }

  void _generateMonthlyReport() {
    // TODO: Implémenter la génération de rapport mensuel
  }

  void _viewAgence(AgenceAssurance agence) {
    // TODO: Implémenter la vue détaillée de l'agence
  }

  void _editAgence(AgenceAssurance agence) {
    // TODO: Implémenter l'édition d'agence
  }

  // Méthodes utilitaires
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _getRoleDisplayName(String role) {
    switch (role) {
      case 'admin_agence':
        return 'Admin Agence';
      case 'agent_agence':
        return 'Agent';
      case 'expert_auto':
        return 'Expert Auto';
      case 'conducteur':
        return 'Conducteur';
      default:
        return role;
    }
  }

  Widget _buildStatusChip(String status) {
    Color color;
    String label;

    switch (status.toLowerCase()) {
      case 'active':
      case 'valide':
      case 'paye':
        color = const Color(0xFF10B981);
        label = status;
        break;
      case 'suspended':
      case 'expiré':
      case 'impaye':
        color = const Color(0xFFEF4444);
        label = status;
        break;
      case 'pending':
      case 'en_cours':
        color = const Color(0xFFF59E0B);
        label = status;
        break;
      default:
        color = const Color(0xFF64748B);
        label = status;
    }

    return Container(
      padding: ,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: ,
      ),
    );
  }

  // Nouvelles méthodes d'action
  void _viewUser(UserProfile user) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: ({user.fullName} - À implémenter')),
    );
  }

  void _editUser(UserProfile user) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: ({user.fullName} - À implémenter')),
    );
  }

  void _viewContract(ContractAssurance contract) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: ({contract.numeroContrat} - À implémenter')),
    );
  }

  void _editContract(ContractAssurance contract) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: ({contract.numeroContrat} - À implémenter')),
    );
  }

  void _viewSinistre(SinistreAssurance sinistre) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: ({sinistre.numeroSinistre} - À implémenter')),
    );
  }

  void _assignExpert(SinistreAssurance sinistre) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: ({sinistre.numeroSinistre} - À implémenter
