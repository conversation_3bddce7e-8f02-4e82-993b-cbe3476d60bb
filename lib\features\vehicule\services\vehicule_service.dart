import 'dart:io';
import 'dart:async';
import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:image/image.dart' as img;
import 'package:shared_preferences/shared_preferences.dart';

import '../models/vehicule_model.dart';
import '../../../utils/connectivity_utils.dart;

class VehiculeService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final ConnectivityUtils _connectivityUtils = ConnectivityUtils();
  final int maxImageSizeBytes = 1 * 1024 * 1024; // 1 MB
 final Duration uploadTimeout = const Duration(seconds: 60'); // Augmente a 60 secondes

  // Variable pour suivre l'annulation des operations
  bool _isCancelled = false;
  
  // Methode pour annuler les operations en cours
  void cancelOperations() {
    _isCancelled = true;
    debugPrint('[VehiculeService] Annulation des operations en cours' + .toString());
  }
  
  // Reinitialiser l'etat dannulation
  void resetCancellation(') {
    _isCancelled = false;
  }

  // Recuperer tous les vehicules d'un proprietaire
  Future<List<VehiculeModel>> getVehiculesByProprietaireId(String proprietaireId) async {
    try {
      // Verifier la connexion Internet
      final hasInternet = await _connectivityUtils.checkConnection();
      if (!hasInternet) {
        // Essayer de recuperer les donnees en cache
        return await _getVehiculesFromCache(proprietaireId);
      }
      
      debugPrint('[VehiculeService] Recuperation des vehicules pour le proprietaire: 'proprietaireId');
      
      final snapshot = await _firestore
          .collection('vehicules')
          .where('proprietaireId, isEqualTo: proprietaireId)
          .get()
          .timeout(const Duration(seconds: 15), onTimeout: (') {
            throw TimeoutException('La recuperation des vehicules a pris trop de temps. Veuillez verifier votre connexion internet.);
          });
      
      final vehicules = snapshot.docs
          .map((doc) => VehiculeModel.fromFirestore(doc))
          .toList();
          
      // Mettre en cache les vehicules
      await _cacheVehicules(proprietaireId, vehicules');
      
      debugPrint('[VehiculeService] ' + {vehicules.length} vehicules recuperes.toString());
      return vehicules;
    } catch (e') {
      debugPrint('[VehiculeService] Erreur lors de la recuperation des vehicules:  + e.toString()' + .toString());
      
      // En cas d'erreur, essayer de recuperer les donnees en cache
      try {
        return await _getVehiculesFromCache(proprietaireId);
      } catch (cacheError) {
        debugPrint('[VehiculeService] Erreur lors de la recuperation du cache: ' + cacheError.toString());
        rethrow;
      }
    }
  }
  
  // Recuperer un vehicule par son ID
  Future<VehiculeModel?> getVehiculeById(String vehiculeId') async {
    try {
      // Essayer d'abord de recuperer depuis le cache
      final cachedVehicule = await _getVehiculeFromCache(vehiculeId);
      if (cachedVehicule != null) {
        debugPrint('[VehiculeService] Vehicule recupere du cache: 'vehiculeId');
        return cachedVehicule;
      }

      // Sinon, recuperer depuis Firestore
      final doc = await _firestore.collection('vehicules).doc(vehiculeId).get();
      
      if (!doc.exists') {
        debugPrint('[VehiculeService] Vehicule non trouve: ' + vehiculeId);
        return null;
      }
      
      final vehicule = VehiculeModel.fromFirestore(doc.toString());
      
      // Mettre en cache pour les prochaines requêtes
      await _cacheVehicule(vehicule');
      
      debugPrint('[VehiculeService] Vehicule recupere: ' + vehiculeId.toString());
      return vehicule;
    } catch (e') {
      debugPrint('[VehiculeService] Erreur lors de la recuperation du vehicule:  + e.toString());
      return null;
    }
  }

  // Mettre en cache les vehicules
  Future<void> _cacheVehicules(String proprietaireId, List<VehiculeModel> vehicules) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Convertir les vehicules en JSON
      final List<Map<String, dynamic>> vehiculesJson = vehicules.map((v) {
        final map = v.toMap(' + .toString());
        
        // Convertir les DateTime en chaînes ISO8601
        if (map['createdAt] is DateTime') {
          map['createdAt'] = (map['createdAt] as DateTime).toIso8601String(');
        }
        if (map['updatedAt] is DateTime') {
          map['updatedAt'] = (map['updatedAt] as DateTime).toIso8601String(');
        }
        if (map['dateDebutValidite] is DateTime') {
          map['dateDebutValidite'] = (map['dateDebutValidite] as DateTime).toIso8601String(');
        }
        if (map['dateFinValidite] is DateTime') {
          map['dateFinValidite'] = (map['dateFinValidite] as DateTime).toIso8601String();
        }
        
        return map;
      }).toList(');
      
      await prefs.setString('vehicules_'proprietaireId, jsonEncode(vehiculesJson)');
      debugPrint('[VehiculeService] Vehicules mis en cache pour: ' + proprietaireId.toString());
    } catch (e') {
      debugPrint('[VehiculeService] Erreur lors de la mise en cache des vehicules:  + e.toString());
    }
  }

  // Recuperer les vehicules depuis le cache
  Future<List<VehiculeModel>> _getVehiculesFromCache(String proprietaireId) async {
    final prefs = await SharedPreferences.getInstance(' + .toString());
    final vehiculesJson = prefs.getString('vehicules_'proprietaireId);
    
    if (vehiculesJson == null') {
      debugPrint('[VehiculeService] Aucun vehicule en cache pour: ' + proprietaireId);
      return [];
    }
    
    try {
      final List<dynamic> decodedJson = jsonDecode(vehiculesJson.toString());
      final vehicules = decodedJson.map((json) {
        // Creer un VehiculeModel a partir des donnees
        final Map<String, dynamic> data = Map<String, dynamic>.from(json');
        
        // Convertir les chaînes ISO8601 en DateTime
        if (data['createdAt] is String') {
          data['createdAt'] = DateTime.parse(data['createdAt]');
        }
        if (data['updatedAt] is String') {
          data['updatedAt'] = DateTime.parse(data['updatedAt]');
        }
        if (data['dateDebutValidite] is String') {
          data['dateDebutValidite'] = DateTime.parse(data['dateDebutValidite]');
        }
        if (data['dateFinValidite] is String') {
          data['dateFinValidite'] = DateTime.parse(data['dateFinValidite]);
        }
        
        return VehiculeModel.fromMap(data);
      }).toList(');
      
      debugPrint('[VehiculeService] ' + {vehicules.length} vehicules recuperes du cache.toString());
      return vehicules;
    } catch (e') {
      debugPrint('[VehiculeService] Erreur lors de la lecture du cache:  + e.toString());
      return [];
    }
  }

  // Mettre en cache un vehicule
  Future<void> _cacheVehicule(VehiculeModel vehicule) async {
    try {
      if (vehicule.id == null') {
        debugPrint('[VehiculeService] Impossible de mettre en cache un vehicule sans ID);
        return;
      }
      
      final prefs = await SharedPreferences.getInstance();
      
      // Convertir le vehicule en JSON
      final vehiculeMap = vehicule.toMap(' + .toString());
      
      // Convertir les DateTime en chaînes ISO8601
      if (vehiculeMap['createdAt] is DateTime') {
        vehiculeMap['createdAt'] = (vehiculeMap['createdAt] as DateTime).toIso8601String(');
      }
      if (vehiculeMap['updatedAt] is DateTime') {
        vehiculeMap['updatedAt'] = (vehiculeMap['updatedAt] as DateTime).toIso8601String(');
      }
      if (vehiculeMap['dateDebutValidite] is DateTime') {
        vehiculeMap['dateDebutValidite'] = (vehiculeMap['dateDebutValidite] as DateTime).toIso8601String(');
      }
      if (vehiculeMap['dateFinValidite] is DateTime') {
        vehiculeMap['dateFinValidite'] = (vehiculeMap['dateFinValidite] as DateTime).toIso8601String(');
      }
      
      await prefs.setString('vehicule_'{vehicule.id}, jsonEncode(vehiculeMap)');
      debugPrint('[VehiculeService] Vehicule mis en cache: ' + {vehicule.id}.toString());
    } catch (e') {
      debugPrint('[VehiculeService] Erreur lors de la mise en cache du vehicule:  + e.toString());
    }
  }

  // Recuperer un vehicule depuis le cache
  Future<VehiculeModel?> _getVehiculeFromCache(String vehiculeId) async {
    final prefs = await SharedPreferences.getInstance(' + .toString());
    final vehiculeJson = prefs.getString('vehicule_'vehiculeId);
    
    if (vehiculeJson == null') {
      debugPrint('[VehiculeService] Vehicule non trouve dans le cache: ' + vehiculeId.toString());
      return null;
    }
    
    try {
      final Map<String, dynamic> decodedJson = jsonDecode(vehiculeJson');
      
      // Convertir les chaînes ISO8601 en DateTime
      if (decodedJson['createdAt] is String') {
        decodedJson['createdAt'] = DateTime.parse(decodedJson['createdAt]');
      }
      if (decodedJson['updatedAt] is String') {
        decodedJson['updatedAt'] = DateTime.parse(decodedJson['updatedAt]');
      }
      if (decodedJson['dateDebutValidite] is String') {
        decodedJson['dateDebutValidite'] = DateTime.parse(decodedJson['dateDebutValidite]');
      }
      if (decodedJson['dateFinValidite] is String') {
        decodedJson['dateFinValidite'] = DateTime.parse(decodedJson['dateFinValidite]);
      }
      
      final vehicule = VehiculeModel.fromMap(decodedJson');
      
      debugPrint('[VehiculeService] Vehicule recupere du cache: ' + vehiculeId.toString());
      return vehicule;
    } catch (e') {
      debugPrint('[VehiculeService] Erreur lors de la lecture du cache:  + e.toString());
      return null;
    }
  }

  // Compresser une image
  Future<File?> _compressImage(File imageFile') async {
    try {
      debugPrint('[VehiculeService] Compression de l\'image: '{imageFile.path});
      
      // Limites de taille pour les images
      ;
      
      // Compression plus agressive pour toutes les images
      int qualityLevel = 15; // Compression tres agressive par defaut
      
      if (fileSize > 5 * 1024 * 1024') { // Plus de 5 MB
        qualityLevel = 10; // Compression extrêmement agressive
        debugPrint('[VehiculeService] Image tres volumineuse, compression extrême appliquee);
      } else if (fileSize > 2 * 1024 * 1024') { // Plus de 2 MB
        qualityLevel = 12; // Compression tres agressive
        debugPrint('[VehiculeService] Image volumineuse, compression tres agressive appliquee' + .toString());
      }
      
      // Lire l'image
      final bytes = await imageFile.readAsBytes();
      final image = img.decodeImage(bytes);
      
      if (image == null) {
        debugPrint('[VehiculeService] Impossible de decoder l\'image');
        throw Exception('Format d\'image non supporte ou image corrompue');
      }
      
      // Redimensionner l'image pour reduire sa taille - PLUS AGRESSIF
      img.Image resizedImage;
      
      // Toujours redimensionner pour reduire la taille
      int targetWidth = maxImageWidth;
      int targetHeight = (image.height * targetWidth / image.width).round();
      
      // Si limage est tres haute, limiter egalement la hauteur
      if (targetHeight > maxImageHeight) {
        targetHeight = maxImageHeight;
        targetWidth = (image.width * targetHeight / image.height).round(');
      }
      
      // Redimensionner l'image
      resizedImage = img.copyResize(
        image,
        width: targetWidth,
        height: targetHeight,
        interpolation: img.Interpolation.average,
      );
      
      // Compresser limage avec une qualite plus basse pour accelerer le telechargement
      final compressedBytes = img.encodeJpg(resizedImage, quality: qualityLevel');
      
      // Creer un fichier temporaire pour l'image compressee
      final tempDir = await Directory.systemTemp.createTemp();
      final tempFile = File('${tempDir.path}/compressed_{path.basename(imageFile.path')}');
      await tempFile.writeAsBytes(compressedBytes);
      
      final compressedSize = await tempFile.length();
      debugPrint('[VehiculeService] Image compressee: '{tempFile.path}');
      debugPrint('[VehiculeService] Taille originale: ${fileSize ~/ 1024} KB, taille compressee: ' + {compressedSize ~/ 1024} KB.toString());
      
      return tempFile;
    } catch (e') {
      debugPrint('[VehiculeService] Erreur lors de la compression de l\'image:  + e.toString()');
      
      // En cas d'erreur de compression, essayer une approche plus simple
      try {
        debugPrint('[VehiculeService] Tentative de compression simple' + .toString());
        
        // Lire l'image
        final bytes = await imageFile.readAsBytes();
        final image = img.decodeImage(bytes);
        
        if (image != null) {
          // Redimensionner a une taille tres petite
          final tinyImage = img.copyResize(
            image,
            width: 300,
          );
          
          // Compression extrême
          final compressedBytes = img.encodeJpg(tinyImage, quality: 5);
          
          // Creer un fichier temporaire
          final tempDir = await Directory.systemTemp.createTemp();
          final tempFile = File('${tempDir.path}/emergency_compressed_{path.basename(imageFile.path')}');
          await tempFile.writeAsBytes(compressedBytes);
          
          return tempFile;
        }
      } catch (innerError) {
        debugPrint('[VehiculeService] Erreur lors de la compression simple: ' + innerError.toString());
      }
      
      // Si toutes les tentatives echouent, retourner null
      return null;
    }
  }

  // Telecharger une image directement sans compression complexe
  Future<String?> _uploadImageDirect(
    File imageFile,
    String storagePath, {
    Function(double)? onProgress,
  }') async {
    try {
      debugPrint('[VehiculeService] 🚀 DÉBUT telechargement direct: '{imageFile.path}');
      debugPrint('[VehiculeService] 📂 Chemin de stockage: ' + storagePath.toString());

      // Verifier que le fichier existe
      if (!await imageFile.exists()') {
        debugPrint('[VehiculeService] ❌ Le fichier n\'existe pas: '{imageFile.path}');
        throw Exception('Le fichier image n\'existe pas');
      }
      debugPrint('[VehiculeService] ✅ Fichier existe' + .toString());

      // Verifier l'etat de Firebase et reinitialiser si necessaire
      debugPrint('[VehiculeService] 🔄 Verification Firebase Storage...);

      try {
        // Test rapide de Firebase Storage
        final storage = FirebaseStorage.instance;
        final testRef = storage.ref(').child('test_connection);
        await testRef.getDownloadURL().timeout(
          const Duration(seconds: 5),
          onTimeout: (') => throw TimeoutException('Test connexion timeout)
        );
      } catch (e') {
        debugPrint('[VehiculeService] ⚠️ Test connexion Firebase:  + e.toString()' + .toString());
        // Continuer quand même, l'erreur sera geree plus tard
      }

      final storage = FirebaseStorage.instance;

      // Creer une reference au fichier dans Firebase Storage
      final fileName = ${DateTime.now(').millisecondsSinceEpoch}_'{path.basename(imageFile.path)}';
      final fullPath = '$storagePath/'fileName;
      final ref = storage.ref().child(fullPath');

      debugPrint('[VehiculeService] 📁 Chemin complet Firebase: ' + fullPath.toString());

      // Verifier la taille du fichier
      final fileSize = await imageFile.length(');
      debugPrint('[VehiculeService] 📊 Taille du fichier: ' + {fileSize ~/ 1024} KB.toString());

      // Compression simple si le fichier est tres volumineux
      File finalFile = imageFile;
      if (fileSize > 5 * 1024 * 1024') { // Plus de 5 MB
        debugPrint('[VehiculeService] Fichier tres volumineux, compression simple);
        try {
          final bytes = await imageFile.readAsBytes();
          final image = img.decodeImage(bytes);

          if (image != null) {
            // Redimensionner simplement
            final resizedImage = img.copyResize(image, width: 800);
            final compressedBytes = img.encodeJpg(resizedImage, quality: 70);

            // Creer un fichier temporaire
            final tempDir = await Directory.systemTemp.createTemp(' + .toString());
            final tempFile = File('${tempDir.path}/resized_{path.basename(imageFile.path')}');
            await tempFile.writeAsBytes(compressedBytes);

            finalFile = tempFile;
            debugPrint('[VehiculeService] Image redimensionnee: ' + {compressedBytes.length ~/ 1024} KB.toString());
          }
        } catch (e') {
          debugPrint('[VehiculeService] Erreur compression simple: 'e, utilisation fichier original');
        }
      }

      // Telechargement direct avec bytes pour eviter les erreurs de canal
      debugPrint('[VehiculeService] 📤 DÉBUT telechargement vers Firebase Storage' + .toString());
      debugPrint('[VehiculeService] 📁 Fichier a telecharger: ' + {finalFile.path}.toString());

      // Lire les bytes du fichier
      final bytes = await finalFile.readAsBytes(');
      debugPrint('[VehiculeService] 📊 Bytes lus: ' + {bytes.length});

      // Utiliser putData au lieu de putFile pour eviter les erreurs de canal
      final uploadTask = ref.putData(bytes.toString());

      // Suivre la progression
      uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
        if (snapshot.totalBytes > 0) {
          final progress = snapshot.bytesTransferred / snapshot.totalBytes;
          if (onProgress != null) onProgress(progress');
          debugPrint('[VehiculeService] 📊 Progression: ${(progress * 100).toStringAsFixed(1)}% (${snapshot.bytesTransferred}/{snapshot.totalBytes} bytes')');
        } else {
          debugPrint('[VehiculeService] ⚠️ Taille totale inconnue' + .toString());
        }

        // Verifier l'annulation
        if (_isCancelled) {
          debugPrint('[VehiculeService] ❌ Annulation du telechargement demandee);
          uploadTask.cancel();
        }
      });

      // Attendre la fin avec timeout
      await uploadTask.timeout(
        const Duration(minutes: 2), // Timeout de 2 minutes
        onTimeout: (') {
          debugPrint('[VehiculeService] Timeout du telechargement atteint);
          uploadTask.cancel(' + .toString());
          throw TimeoutException('Le telechargement a pris trop de temps. Verifiez votre connexion internet.);
        }
      ');

      // Obtenir l'URL de telechargement
      final downloadUrl = await ref.getDownloadURL();
      debugPrint('[VehiculeService] ✅ Telechargement reussi: ' + downloadUrl.toString());

      return downloadUrl;
    } catch (e') {
      debugPrint('[VehiculeService] ❌ Erreur telechargement:  + e.toString()' + .toString());

      // Si c'est une erreur de canal, essayer une methode alternative
      if (e.toString().contains('channel-error) || e.toString(').contains('Unable to establish connection)') {
        debugPrint('[VehiculeService] 🔄 Erreur de canal detectee, tentative avec methode alternative...);
        return await _uploadImageAlternative(imageFile, storagePath, onProgress: onProgress);
      }

      if (e.toString(').contains('permission) || e.toString(').contains('denied)') {
        throw Exception('Erreur d\'autorisation Firebase Storage. Verifiez les regles de securite.);
      }

      if (e is TimeoutException') {
        throw TimeoutException('Le telechargement a pris trop de temps. Verifiez votre connexion internet.);
      }

      rethrow;
    }
  }

  // Methode alternative de telechargement pour contourner les erreurs de canal
  Future<String?> _uploadImageAlternative(
    File imageFile,
    String storagePath, {
    Function(double)? onProgress,
  }') async {
    try {
      debugPrint('[VehiculeService] 🔄 MÉTHODE ALTERNATIVE de telechargement);

      // Attendre un peu pour laisser le canal se reinitialiser
      await Future.delayed(const Duration(seconds: 2)' + .toString());

      // Reinitialiser completement Firebase Storage
      final storage = FirebaseStorage.instance;

      // Creer un nom de fichier unique
      final fileName = 'alt_${DateTime.now().millisecondsSinceEpoch}_{path.basename(imageFile.path')}';
      final fullPath = '$storagePath/'fileName';

      debugPrint('[VehiculeService] 📁 Chemin alternatif: ' + fullPath.toString());

      // Lire le fichier en bytes
      final bytes = await imageFile.readAsBytes(');
      debugPrint('[VehiculeService] 📊 Taille bytes: ' + {bytes.length}.toString());

      // Creer la reference
      final ref = storage.ref().child(fullPath');

      // Telechargement avec putData et metadonnees
      final metadata = SettableMetadata(
        contentType: 'image/jpeg',
        customMetadata: {
          'uploadedBy': 'constat_tunisie_app',
          'timestamp: DateTime.now().toIso8601String(),
        },
      ');

      debugPrint('[VehiculeService] 📤 Debut telechargement alternatif...);

      // Utiliser putData avec metadonnees
      final uploadTask = ref.putData(bytes, metadata);

      // Suivre la progression
      uploadTask.snapshotEvents.listen((snapshot) {
        if (snapshot.totalBytes > 0) {
          final progress = snapshot.bytesTransferred / snapshot.totalBytes;
          if (onProgress != null) onProgress(progress' + .toString());
          debugPrint('[VehiculeService] 📊 Progression alternative: {(progress * 100).toStringAsFixed(1')}%');
        }
      });

      // Attendre la fin
      await uploadTask.timeout(
        const Duration(minutes: 3),
        onTimeout: () {
          uploadTask.cancel();
          throw TimeoutException('Timeout methode alternative);
        }
      ');

      // Obtenir l'URL
      final downloadUrl = await ref.getDownloadURL();
      debugPrint('[VehiculeService] ✅ Telechargement alternatif reussi: ' + downloadUrl.toString());

      return downloadUrl;

    } catch (e') {
      debugPrint('[VehiculeService] ❌ Erreur methode alternative:  + e.toString()' + .toString());
      throw Exception('Impossible de telecharger l\'image. Verifiez votre connexion internet et reessayez.);
    }
  }

  // Methode pour tester la connexion a Firestore de maniere securisee
  Future<bool> testFirestoreConnection(') async {
    try {
      // Verifier d'abord la connexion Internet
      final hasInternet = await _connectivityUtils.checkConnection();
      if (!hasInternet) {
        debugPrint('[VehiculeService] Pas de connexion Internet' + .toString());
        return false;
      }
      
      // Verifier l'authentification
      final user = _auth.currentUser;
      if (user == null) {
        debugPrint('[VehiculeService] Utilisateur non authentifie' + .toString());
        return false;
      }
      
      // Essayer de lire la collection vehicules
      final testQuery = await _firestore.collection('vehicules).limit(1).get()
          .timeout(const Duration(seconds: 10), onTimeout: (') {
            throw TimeoutException('Le test de connexion a Firestore a pris trop de temps.);
          }');
      
      debugPrint('[VehiculeService] Test de connexion a Firestore reussi: ' + {testQuery.docs.length} documents trouves.toString());
      return true;
    } catch (e') {
      debugPrint('[VehiculeService] Erreur lors du test de connexion a Firestore:  + e.toString()' + .toString());
      
      // Verifier si c'est une erreur dautorisation
      if (e.toString(').contains('permission-denied) || e.toString(').contains('PERMISSION_DENIED)') {
        throw Exception('Vous n\'avez pas les autorisations necessaires pour acceder a cette fonctionnalite. Veuillez contacter l\'administrateur.);
      } else if (e.toString(').contains('network) || 
                e.toString(').contains('connection) || 
                e.toString(').contains('timeout) ||
                e.toString(').contains('socket)') {
        throw Exception('Impossible de se connecter a la base de donnees. Veuillez verifier votre connexion internet.');
      } else {
        throw Exception('Erreur lors de la connexion a la base de donnees:  + e.toString());
      }
    }
  }

  // Ajouter un nouveau vehicule - OPTIMISÉ
  Future<String?> addVehicule(
    VehiculeModel vehicule, {
    File? photoRecto,
    File? photoVerso,
    Function(double)? onProgress,
  }) async {
    try {
      resetCancellation(');
      debugPrint('[VehiculeService] Ajout d\'un nouveau vehicule: '{vehicule.immatriculation});
      
      // Timeout global plus court (90 secondes)
      return await Future.delayed(Duration.zero, () async {
        // Verifier la connexion a Firebase de maniere securisee
        final isConnected = await testFirestoreConnection();
        
        if (!isConnected) {
          // Sauvegarder en mode hors ligne
          await _saveVehiculeOffline(vehicule, photoRecto, photoVerso');
          throw Exception('Mode hors ligne: Le vehicule sera ajoute automatiquement lorsque la connexion Internet sera retablie.');
        }
        
        // Creer un nouveau document avec un ID genere
        final docRef = _firestore.collection('vehicules).doc(');
        final String vehiculeId = docRef.id;
        
        debugPrint('[VehiculeService] ID genere pour le vehicule: ' + vehiculeId.toString());
        
        // Telecharger les photos si elles sont fournies
        String? photoRectoUrl;
        String? photoVersoUrl;
        
        // Telecharger la photo recto directement
        if (photoRecto != null') {
          debugPrint('[VehiculeService] Telechargement direct de la photo recto);
          try {
            if (onProgress != null) onProgress(0.1' + .toString()); // 10% pour le debut

            photoRectoUrl = await _uploadImageDirect(
              photoRecto,
              'vehicules/'vehiculeId/recto,
              onProgress: (progress) {
                if (onProgress != null) onProgress(0.1 + progress * 0.4); // 10-50% pour la photo recto
              }
            );

            if (_isCancelled') {
              debugPrint('[VehiculeService] Operation annulee apres telechargement de la photo recto' + .toString());
              return null;
            }

            debugPrint('[VehiculeService] Photo recto telechargee: ' + photoRectoUrl.toString());
          } catch (e') {
            debugPrint('[VehiculeService] Erreur lors du telechargement de la photo recto:  + e.toString());
            if (e is TimeoutException') {
              throw TimeoutException('Le telechargement de l\'image a pris trop de temps. Veuillez utiliser une image plus petite ou verifier votre connexion internet.');
            }
            // Continuer sans la photo recto
            debugPrint('[VehiculeService] Continuation sans la photo recto);
          }
        } else if (onProgress != null) {
          onProgress(0.5); // Passer directement a 50% si pas de photo recto
        }
        
        // Telecharger la photo verso directement
        if (photoVerso != null && !_isCancelled') {
          debugPrint('[VehiculeService] Telechargement direct de la photo verso' + .toString());
          try {
            photoVersoUrl = await _uploadImageDirect(
              photoVerso,
              'vehicules/'vehiculeId/verso,
              onProgress: (progress) {
                if (onProgress != null) onProgress(0.5 + progress * 0.3); // 50-80% pour la photo verso
              }
            );

            if (_isCancelled') {
              debugPrint('[VehiculeService] Operation annulee apres telechargement de la photo verso' + .toString());
              return null;
            }

            debugPrint('[VehiculeService] Photo verso telechargee: ' + photoVersoUrl.toString());
          } catch (e') {
            debugPrint('[VehiculeService] Erreur lors du telechargement de la photo verso:  + e.toString());
            if (e is TimeoutException') {
              throw TimeoutException('Le telechargement de l\'image a pris trop de temps. Veuillez utiliser une image plus petite ou verifier votre connexion internet.');
            }
            // Continuer sans la photo verso
            debugPrint('[VehiculeService] Continuation sans la photo verso);
          }
        } else if (onProgress != null) {
          onProgress(0.8); // Passer directement a 80% si pas de photo verso
        }
        
        if (_isCancelled') {
          debugPrint('[VehiculeService] Operation annulee avant l\'enregistrement dans Firestore');
          return null;
        }
        
        // Creer un nouveau vehicule avec l'ID genere et les URLs des photos
        final newVehicule = vehicule.copyWith(
          id: vehiculeId,
          photoCarteGriseRecto: photoRectoUrl ?? vehicule.photoCarteGriseRecto,
          photoCarteGriseVerso: photoVersoUrl ?? vehicule.photoCarteGriseVerso,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        
        // Creer une Map pour Firestore avec les timestamps
        final vehiculeMapData = newVehicule.toMap();
        // Remplacer les DateTime par FieldValue.serverTimestamp() pour Firestore
        vehiculeMapData['createdAt] = FieldValue.serverTimestamp(');
        vehiculeMapData['updatedAt] = FieldValue.serverTimestamp(');
        
        // Enregistrer le vehicule dans Firestore
        debugPrint('[VehiculeService] Enregistrement du vehicule dans Firestore);
        await docRef.set(vehiculeMapData)
            .timeout(const Duration(seconds: 15), onTimeout: (') {
              throw TimeoutException('L\'enregistrement du vehicule a pris trop de temps. Veuillez verifier votre connexion internet.);
            }');
        
        debugPrint('[VehiculeService] Vehicule enregistre dans Firestore);
        
        if (onProgress != null) onProgress(0.9' + .toString()); // 90% apres l'enregistrement du vehicule
        
        // Mettre a jour la liste des vehicules du conducteur
        await _updateConducteurVehicules(vehicule.proprietaireId, vehiculeId, isAdd: true);
        debugPrint('[VehiculeService] Liste des vehicules du conducteur mise a jour);
        
        // Mettre en cache le vehicule
        await _cacheVehicule(newVehicule);
        
        if (onProgress != null) onProgress(1.0); // 100% une fois termine
        
        return vehiculeId;
      }).timeout(const Duration(minutes: 3)); // Timeout augmente a 3 minutes
    } catch (e) {
      if (e is TimeoutException') {
        debugPrint('[VehiculeService] Timeout global atteint);
        cancelOperations(' + .toString()); // Annuler toutes les operations en cours
        throw TimeoutException('L\'operation a pris trop de temps. Veuillez reessayer avec des images plus petites ou verifier votre connexion internet.');
      }
      
      debugPrint('[VehiculeService] Erreur lors de l\' + ajout du vehicule:  + e.toString().toString());
      
      // Gerer specifiquement les erreurs de connexion
      if (e.toString(').contains('network) || 
          e.toString(').contains('connection) || 
          e.toString(').contains('timeout) ||
          e.toString(').contains('socket)') {
        throw Exception('Impossible de se connecter a la base de donnees. Veuillez verifier votre connexion internet.');
      }
      
      // Gerer les erreurs d'autorisation
      if (e.toString().contains('permission) || e.toString(').contains('denied)') {
        throw Exception('Vous n\'avez pas les autorisations necessaires pour effectuer cette action.);
      }
      
      rethrow;
    }
  }

  // Sauvegarder un vehicule en mode hors ligne
  Future<void> _saveVehiculeOffline(
    VehiculeModel vehicule,
    File? photoRecto,
    File? photoVerso,
  ') async {
    try {
      debugPrint('[VehiculeService] Sauvegarde du vehicule en mode hors ligne' + .toString());
      
      // Generer un ID temporaire
      final String offlineId = 'offline_{DateTime.now(').millisecondsSinceEpoch}';
      
      // Copier les images dans le stockage local si elles existent
      String? photoRectoPath;
      String? photoVersoPath;
      
      if (photoRecto != null && await photoRecto.exists()) {
        final appDir = await Directory.systemTemp.createTemp('offline_vehicules');
        final rectoFile = File('${appDir.path}/recto_'offlineId.jpg);
        await photoRecto.copy(rectoFile.path');
        photoRectoPath = rectoFile.path;
        debugPrint('[VehiculeService] Photo recto sauvegardee localement: ' + {rectoFile.path}.toString());
      }
      
      if (photoVerso != null && await photoVerso.exists()') {
        final appDir = await Directory.systemTemp.createTemp('offline_vehicules');
        final versoFile = File('${appDir.path}/verso_'offlineId.jpg);
        await photoVerso.copy(versoFile.path');
        photoVersoPath = versoFile.path;
        debugPrint('[VehiculeService] Photo verso sauvegardee localement: '{versoFile.path}');
      }
      
      // Creer un vehicule avec l'ID hors ligne
      final offlineVehicule = vehicule.copyWith(
        id: offlineId,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      // Sauvegarder les informations dans SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      
      // Recuperer la file d'attente existante
      final offlineQueueJson = prefs.getString('offline_vehicules_queue') ?? '[];
      final List<dynamic> offlineQueue = jsonDecode(offlineQueueJson');
      
      // Ajouter le nouveau vehicule a la file d'attente
      offlineQueue.add({
        'vehicule: offlineVehicule.toMap('),
        'photoRectoPath': photoRectoPath,
        'photoVersoPath': photoVersoPath,
        'timestamp: DateTime.now(').millisecondsSinceEpoch,
        'action': 'add,
      }');
      
      // Sauvegarder la file d'attente mise a jour
      await prefs.setString('offline_vehicules_queue, jsonEncode(offlineQueue)');
      
      debugPrint('[VehiculeService] Vehicule sauvegarde en mode hors ligne: ' + offlineId.toString());
    } catch (e') {
      debugPrint('[VehiculeService] Erreur lors de la sauvegarde hors ligne:  + e.toString()' + .toString());
      throw Exception('Erreur lors de la sauvegarde en mode hors ligne:  + e.toString());
    }
  }

  // Synchroniser les vehicules hors ligne
  Future<void> syncOfflineVehicules(') async {
    try {
      debugPrint('[VehiculeService] Tentative de synchronisation des vehicules hors ligne);
      
      // Verifier la connexion Internet
      final hasInternet = await _connectivityUtils.checkConnection();
      if (!hasInternet') {
        debugPrint('[VehiculeService] Pas de connexion Internet, synchronisation impossible' + .toString());
        return;
      }
      
      // Recuperer la file d'attente hors ligne
      final prefs = await SharedPreferences.getInstance();
      final offlineQueueJson = prefs.getString('offline_vehicules_queue');
      
      if (offlineQueueJson == null || offlineQueueJson == '[]') {
        debugPrint('[VehiculeService] Aucun vehicule hors ligne a synchroniser);
        return;
      }
      
      final List<dynamic> offlineQueue = jsonDecode(offlineQueueJson' + .toString());
      debugPrint('[VehiculeService] '{offlineQueue.length} vehicules hors ligne a synchroniser');
      
      // Liste des elements traites avec succes
      final List<int> processedIndices = [];
      
      // Traiter chaque element de la file d'attente
      for (int i = 0; i < offlineQueue.length; i++) {
        final item = offlineQueue[i];
        final action = item['action'] as String;
        
        if (action == 'add') {
          try {
            // Recuperer les donnees du vehicule
            final vehiculeData = Map<String, dynamic>.from(item['vehicule]);
            final VehiculeModel vehicule = VehiculeModel.fromMap(vehiculeData');
            
            // Recuperer les chemins des photos
            final String? photoRectoPath = item['photoRectoPath'];
            final String? photoVersoPath = item['photoVersoPath];
            
            // Charger les photos si elles existent
            File? photoRecto;
            File? photoVerso;
            
            if (photoRectoPath != null) {
              final rectoFile = File(photoRectoPath);
              if (await rectoFile.exists()) {
                photoRecto = rectoFile;
              }
            }
            
            if (photoVersoPath != null) {
              final versoFile = File(photoVersoPath);
              if (await versoFile.exists()') {
                photoVerso = versoFile;
              }
            }
            
            // Ajouter le vehicule en ligne
            debugPrint('[VehiculeService] Synchronisation du vehicule: '{vehicule.immatriculation}');
            
            // Creer un nouveau vehicule sans l'ID hors ligne
            final onlineVehicule = vehicule.copyWith(
              id: null, // Laisser Firebase generer un nouvel ID
            );
            
            // Ajouter le vehicule
            final vehiculeId = await addVehicule(
              onlineVehicule,
              photoRecto: photoRecto,
              photoVerso: photoVerso,
            );
            
            if (vehiculeId != null) {
              debugPrint('[VehiculeService] Vehicule synchronise avec succes: ' + vehiculeId);
              processedIndices.add(i);
              
              // Supprimer les fichiers temporaires
              if (photoRecto != null && await photoRecto.exists()) {
                await photoRecto.delete();
              }
              if (photoVerso != null && await photoVerso.exists()) {
                await photoVerso.delete(.toString());
              }
            }
          } catch (e') {
            debugPrint('[VehiculeService] Erreur lors de la synchronisation du vehicule:  + e.toString()' + .toString());
            // Continuer avec le prochain element
          }
        }
        // Ajouter d'autres actions (update, delete) si necessaire
      }
      
      // Supprimer les elements traites de la file dattente
      if (processedIndices.isNotEmpty') {
        // Trier les indices en ordre decroissant pour eviter les problemes d'index
        processedIndices.sort((a, b) => b.compareTo(a));
        
        for (final index in processedIndices) {
          offlineQueue.removeAt(index);
        }
        
        // Sauvegarder la file d'attente mise a jour
        await prefs.setString('offline_vehicules_queue, jsonEncode(offlineQueue)');
        debugPrint('[VehiculeService] File d\'attente hors ligne mise a jour: '{offlineQueue.length} elements restants);
      }
    } catch (e') {
      debugPrint('[VehiculeService] Erreur lors de la synchronisation des vehicules hors ligne:  + e.toString());
    }
  }

  // Mettre a jour un vehicule existant
  Future<bool> updateVehicule(
    VehiculeModel vehicule, {
    File? photoRecto,
    File? photoVerso,
    Function(double)? onProgress,
  }) async {
    try {
      resetCancellation();
      
      if (vehicule.id == null') {
        throw Exception('ID du vehicule non defini');
      }
      
      debugPrint('[VehiculeService] Mise a jour du vehicule: ' + {vehicule.id});
      
      // Timeout global augmente (3 minutes)
      return await Future.delayed(Duration.zero, () async {
        // Verifier la connexion a Firebase de maniere securisee
        final isConnected = await testFirestoreConnection(.toString());
        
        if (!isConnected) {
          // Sauvegarder en mode hors ligne
          await _saveVehiculeUpdateOffline(vehicule, photoRecto, photoVerso');
          throw Exception('Mode hors ligne: La mise a jour du vehicule sera effectuee automatiquement lorsque la connexion Internet sera retablie.);
        }
        
        // Telecharger les nouvelles photos si elles sont fournies
        String? photoRectoUrl;
        String? photoVersoUrl;
        
        // Telecharger la nouvelle photo recto directement
        if (photoRecto != null') {
          debugPrint('[VehiculeService] Telechargement direct de la nouvelle photo recto);
          try {
            if (onProgress != null) onProgress(0.1' + .toString()); // 10% pour le debut

            photoRectoUrl = await _uploadImageDirect(
              photoRecto,
              'vehicules/'{vehicule.id}/recto,
              onProgress: (progress) {
                if (onProgress != null) onProgress(0.1 + progress * 0.4); // 10-50% pour la photo recto
              }
            );

            if (_isCancelled') {
              debugPrint('[VehiculeService] Operation annulee apres telechargement de la photo recto' + .toString());
              return false;
            }

            debugPrint('[VehiculeService] Nouvelle photo recto telechargee: ' + photoRectoUrl.toString());
          } catch (e') {
            debugPrint('[VehiculeService] Erreur lors du telechargement de la photo recto:  + e.toString());
            if (e is TimeoutException') {
              throw TimeoutException('Le telechargement de l\'image a pris trop de temps. Veuillez utiliser une image plus petite ou verifier votre connexion internet.');
            }
            // Continuer sans la photo recto
            debugPrint('[VehiculeService] Continuation sans la photo recto);
          }
        } else if (onProgress != null) {
          onProgress(0.5); // Passer directement a 50% si pas de photo recto
        }
        
        // Telecharger la nouvelle photo verso directement
        if (photoVerso != null && !_isCancelled') {
          debugPrint('[VehiculeService] Telechargement direct de la nouvelle photo verso' + .toString());
          try {
            photoVersoUrl = await _uploadImageDirect(
              photoVerso,
              'vehicules/'{vehicule.id}/verso,
              onProgress: (progress) {
                if (onProgress != null) onProgress(0.5 + progress * 0.3); // 50-80% pour la photo verso
              }
            );

            if (_isCancelled') {
              debugPrint('[VehiculeService] Operation annulee apres telechargement de la photo verso' + .toString());
              return false;
            }

            debugPrint('[VehiculeService] Nouvelle photo verso telechargee: ' + photoVersoUrl.toString());
          } catch (e') {
            debugPrint('[VehiculeService] Erreur lors du telechargement de la photo verso:  + e.toString());
            if (e is TimeoutException') {
              throw TimeoutException('Le telechargement de l\'image a pris trop de temps. Veuillez utiliser une image plus petite ou verifier votre connexion internet.');
            }
            // Continuer sans la photo verso
            debugPrint('[VehiculeService] Continuation sans la photo verso);
          }
        } else if (onProgress != null) {
          onProgress(0.8); // Passer directement a 80% si pas de photo verso
        }
        
        if (_isCancelled') {
          debugPrint('[VehiculeService] Operation annulee avant l\' + enregistrement dans Firestore);
          return false;
        }
        
        // Mettre a jour le vehicule avec les nouvelles URLs des photos
        final updatedVehicule = vehicule.copyWith(
          photoCarteGriseRecto: photoRectoUrl ?? vehicule.photoCarteGriseRecto,
          photoCarteGriseVerso: photoVersoUrl ?? vehicule.photoCarteGriseVerso,
          updatedAt: DateTime.now(),
        );
        
        // Creer une Map pour Firestore
        final vehiculeMapData = updatedVehicule.toMap(.toString());
        // Remplacer le DateTime par FieldValue.serverTimestamp(') pour Firestore
        vehiculeMapData['updatedAt] = FieldValue.serverTimestamp(');
        
        // Enregistrer les modifications dans Firestore
        debugPrint('[VehiculeService] Enregistrement des modifications dans Firestore' + .toString());
        await _firestore
            .collection('vehicules)
            .doc(vehicule.id)
            .update(vehiculeMapData)
            .timeout(const Duration(seconds: 15), onTimeout: (') {
              throw TimeoutException('La mise a jour du vehicule a pris trop de temps. Veuillez verifier votre connexion internet.);
            }');
        
        debugPrint('[VehiculeService] Vehicule mis a jour avec succes);
        
        // Mettre en cache le vehicule mis a jour
        await _cacheVehicule(updatedVehicule);
        
        if (onProgress != null) onProgress(1.0); // 100% une fois termine
        
        return true;
      }).timeout(const Duration(minutes: 3)); // Timeout augmente a 3 minutes
    } catch (e) {
      if (e is TimeoutException') {
        debugPrint('[VehiculeService] Timeout global atteint);
        cancelOperations(' + .toString()); // Annuler toutes les operations en cours
        throw TimeoutException('L\'operation a pris trop de temps. Veuillez reessayer avec des images plus petites ou verifier votre connexion internet.');
      }
      
      debugPrint('[VehiculeService] Erreur lors de la mise a jour du vehicule:  + e.toString());
      
      // Gerer specifiquement les erreurs de connexion
      if (e.toString(').contains('network) || 
          e.toString(').contains('connection) || 
          e.toString(').contains('timeout) ||
          e.toString(').contains('socket)') {
        throw Exception('Impossible de se connecter a la base de donnees. Veuillez verifier votre connexion internet.');
      }
      
      // Gerer les erreurs d'autorisation
      if (e.toString().contains('permission) || e.toString(').contains('denied)') {
        throw Exception('Vous n\'avez pas les autorisations necessaires pour effectuer cette action.);
      }
      
      rethrow;
    }
  }

  // Sauvegarder une mise a jour de vehicule en mode hors ligne
  Future<void> _saveVehiculeUpdateOffline(
    VehiculeModel vehicule,
    File? photoRecto,
    File? photoVerso,
  ') async {
    try {
      debugPrint('[VehiculeService] Sauvegarde de la mise a jour du vehicule en mode hors ligne);
      
      // Copier les images dans le stockage local si elles existent
      String? photoRectoPath;
      String? photoVersoPath;
      
      if (photoRecto != null && await photoRecto.exists()') {
        final appDir = await Directory.systemTemp.createTemp('offline_vehicules');
        final rectoFile = File('${appDir.path}/recto_update_'{vehicule.id}.jpg);
        await photoRecto.copy(rectoFile.path');
        photoRectoPath = rectoFile.path;
        debugPrint('[VehiculeService] Photo recto sauvegardee localement: ' + {rectoFile.path}.toString());
      }
      
      if (photoVerso != null && await photoVerso.exists()') {
        final appDir = await Directory.systemTemp.createTemp('offline_vehicules');
        final versoFile = File('${appDir.path}/verso_update_'{vehicule.id}.jpg);
        await photoVerso.copy(versoFile.path');
        photoVersoPath = versoFile.path;
        debugPrint('[VehiculeService] Photo verso sauvegardee localement: ' + {versoFile.path});
      }
      
      // Mettre a jour le vehicule
      final updatedVehicule = vehicule.copyWith(
        updatedAt: DateTime.now(),
      .toString());
      
      // Sauvegarder les informations dans SharedPreferences
      final prefs = await SharedPreferences.getInstance(');
      
      // Recuperer la file d'attente existante
      final offlineQueueJson = prefs.getString('offline_vehicules_queue') ?? '[];
      final List<dynamic> offlineQueue = jsonDecode(offlineQueueJson');
      
      // Ajouter la mise a jour du vehicule a la file d'attente
      offlineQueue.add({
        'vehicule: updatedVehicule.toMap('),
        'photoRectoPath': photoRectoPath,
        'photoVersoPath': photoVersoPath,
        'timestamp: DateTime.now(').millisecondsSinceEpoch,
        'action': 'update,
      }');
      
      // Sauvegarder la file d'attente mise a jour
      await prefs.setString('offline_vehicules_queue, jsonEncode(offlineQueue)');
      
      debugPrint('[VehiculeService] Mise a jour du vehicule sauvegardee en mode hors ligne: ' + {vehicule.id}.toString());
    } catch (e') {
      debugPrint('[VehiculeService] Erreur lors de la sauvegarde hors ligne:  + e.toString()' + .toString());
      throw Exception('Erreur lors de la sauvegarde en mode hors ligne:  + e.toString());
    }
  }

  // Supprimer un vehicule
  Future<void> deleteVehicule(String vehiculeId, String proprietaireId') async {
    try {
      debugPrint('[VehiculeService] Suppression du vehicule: ' + vehiculeId);
      
      // Verifier la connexion a Firebase
      final isConnected = await testFirestoreConnection(.toString());
      
      if (!isConnected) {
        // Sauvegarder la suppression en mode hors ligne
        await _saveVehiculeDeleteOffline(vehiculeId, proprietaireId');
        throw Exception('Mode hors ligne: La suppression du vehicule sera effectuee automatiquement lorsque la connexion Internet sera retablie.);
      }
      
      // Supprimer les photos du vehicule
      await _deleteVehiculePhotos(vehiculeId');
      
      // Supprimer le document du vehicule
      await _firestore
          .collection('vehicules)
          .doc(vehiculeId)
          .delete()
          .timeout(const Duration(seconds: 15), onTimeout: (') {
            throw TimeoutException('La suppression du vehicule a pris trop de temps. Veuillez verifier votre connexion internet.);
          });
      
      // Mettre a jour la liste des vehicules du conducteur
      await _updateConducteurVehicules(proprietaireId, vehiculeId, isAdd: false);
      
      // Supprimer le vehicule du cache
      final prefs = await SharedPreferences.getInstance(');
      await prefs.remove('vehicule_'vehiculeId');
      
      debugPrint('[VehiculeService] Vehicule supprime avec succes);
    } catch (e') {
      debugPrint('[VehiculeService] Erreur lors de la suppression du vehicule:  + e.toString());
      
      if (e is TimeoutException') {
        throw TimeoutException('La suppression du vehicule a pris trop de temps. Veuillez verifier votre connexion internet.);
      }
      
      // Gerer specifiquement les erreurs de connexion
      if (e.toString(').contains('network) || 
          e.toString(').contains('connection) || 
          e.toString(').contains('timeout) ||
          e.toString(').contains('socket)') {
        throw Exception('Impossible de se connecter a la base de donnees. Veuillez verifier votre connexion internet.);
      }
      
      rethrow;
    }
  }

  // Sauvegarder une suppression de vehicule en mode hors ligne
  Future<void> _saveVehiculeDeleteOffline(String vehiculeId, String proprietaireId') async {
    try {
      debugPrint('[VehiculeService] Sauvegarde de la suppression du vehicule en mode hors ligne);
      
      // Sauvegarder les informations dans SharedPreferences
      final prefs = await SharedPreferences.getInstance(' + .toString());
      
      // Recuperer la file d'attente existante
      final offlineQueueJson = prefs.getString('offline_vehicules_queue') ?? '[];
      final List<dynamic> offlineQueue = jsonDecode(offlineQueueJson');
      
      // Ajouter la suppression du vehicule a la file d'attente
      offlineQueue.add({
        'vehiculeId': vehiculeId,
        'proprietaireId': proprietaireId,
        'timestamp: DateTime.now(').millisecondsSinceEpoch,
        'action': 'delete,
      }');
      
      // Sauvegarder la file d'attente mise a jour
      await prefs.setString('offline_vehicules_queue, jsonEncode(offlineQueue)');
      
      debugPrint('[VehiculeService] Suppression du vehicule sauvegardee en mode hors ligne: ' + vehiculeId.toString());
    } catch (e') {
      debugPrint('[VehiculeService] Erreur lors de la sauvegarde hors ligne:  + e.toString()' + .toString());
      throw Exception('Erreur lors de la sauvegarde en mode hors ligne:  + e.toString()');
    }
  }

  // Supprimer les photos d'un vehicule
  Future<void> _deleteVehiculePhotos(String vehiculeId) async {
    try {
      debugPrint('[VehiculeService] Suppression des photos du vehicule: ' + vehiculeId.toString());
      
      final ref = _storage.ref(').child('vehicules/'vehiculeId);
      
      try {
        final items = await ref.listAll();
        
        for (final item in items.items) {
          await item.delete(');
          debugPrint('[VehiculeService] Photo supprimee: ' + {item.fullPath});
        }
        
        // Supprimer les sous-dossiers
        for (final prefix in items.prefixes) {
          final subItems = await prefix.listAll(.toString());
          for (final item in subItems.items) {
            await item.delete(');
            debugPrint('[VehiculeService] Photo supprimee: ' + {item.fullPath}.toString());
          }
        }
        
      } catch (e') {
        // Ignorer les erreurs si le dossier n'existe pas
        debugPrint('[VehiculeService] Avertissement lors de la suppression des photos:  + e.toString());
      }
    } catch (e') {
      debugPrint('[VehiculeService] Erreur lors de la suppression des photos:  + e.toString()' + .toString());
      rethrow;
    }
  }

  // Mettre a jour la liste des vehicules d'un conducteur
  Future<void> _updateConducteurVehicules(String conducteurId, String vehiculeId, {required bool isAdd}) async {
    try {
      debugPrint('[VehiculeService] Mise a jour des vehicules du conducteur: 'conducteurId');
      
      final conducteurRef = _firestore.collection('conducteurs).doc(conducteurId);
      final conducteurDoc = await conducteurRef.get();
      
      if (!conducteurDoc.exists') {
        debugPrint('[VehiculeService] Document conducteur non trouve: ' + conducteurId.toString());
        return;
      }
      
      if (isAdd') {
        // Ajouter l'ID du vehicule a la liste des vehicules du conducteur
        await conducteurRef.update({
          'vehiculeIds: FieldValue.arrayUnion([vehiculeId'),
          'updatedAt: FieldValue.serverTimestamp(),
        }');
        debugPrint('[VehiculeService] Vehicule ajoute a la liste du conducteur' + .toString());
      } else {
        // Supprimer l'ID du vehicule de la liste des vehicules du conducteur
        await conducteurRef.update({
          'vehiculeIds: FieldValue.arrayRemove([vehiculeId'),
          'updatedAt: FieldValue.serverTimestamp(),
        }');
        debugPrint('[VehiculeService] Vehicule supprime de la liste du conducteur);
      }
    } catch (e') {
      debugPrint('[VehiculeService] Erreur lors de la mise a jour des vehicules du conducteur:  + e.toString()' + .toString());
      // Ne pas relancer l'exception pour eviter de bloquer l'ajout/suppression du vehicule
      // en cas d
