import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'dart:math;

/// 📋 Service de gestion des sinistres
class SinistreManagementService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 📝 Creer un nouveau sinistre
  static Future<Map<String, dynamic>> createSinistre({
    required String compagnieId,
    required String agenceId,
    required String agentId,
    required String conducteurId,
    required String vehiculeId,
    required String contratId,
    required Map<String, dynamic> accidentData,
    required Map<String, dynamic> circumstancesData,
    List<String>? photosUrls,
    String? croquis,
  }') async {
    try {
      debugPrint('[SINISTRE_SERVICE] 📝 Creation sinistre' + .toString());

      final sinistreId = 'sinistre_{DateTime.now(').millisecondsSinceEpoch}';
      final numeroSinistre = _generateNumeroSinistre(compagnieId);

      final sinistreData = {
        'id': sinistreId,
        'numeroSinistre': numeroSinistre,
        'compagnieId': compagnieId,
        'agenceId': agenceId,
        'agentId': agentId,
        'conducteurId': conducteurId,
        'vehiculeId': vehiculeId,
        'contratId': contratId,
        
        // Informations de l'accident
        'dateAccident': accidentData['dateAccident'],
        'heureAccident': accidentData['heureAccident'],
        'lieuAccident': accidentData['lieuAccident'],
        'typeAccident': accidentData['typeAccident'] ?? 'Collision',
        'gravite': accidentData['gravite'] ?? 'Leger',
        'conditions_meteo': accidentData['conditions_meteo'] ?? 'Beau temps',
        'etat_route': accidentData['etat_route'] ?? 'Seche',
        'circulation': accidentData['circulation'] ?? 'Normale',
        
        // Circonstances
        'description': circumstancesData['description'],
        'responsabilite': circumstancesData['responsabilite'] ?? 'À determiner',
        'temoins': circumstancesData['temoins'] ?? [],
        'degats_vehicule': circumstancesData['degats_vehicule'] ?? 'Contenu',
        'degats_tiers': circumstancesData['degats_tiers'] ?? 'Contenu',
        'blesses': circumstancesData['blesses'] ?? false,
        'nombre_blesses': circumstancesData['nombre_blesses'] ?? 0,
        
        // Documents et preuves
        'photos': photosUrls ?? [],
        'croquis': croquis,
        'pv_police': circumstancesData['pv_police'] ?? false,
        'numero_pv': circumstancesData['numero_pv'],
        
        // Statut et suivi
        'status': 'nouveau',
        'priorite: _calculatePriorite(accidentData, circumstancesData'),
        'expertId': null,
        'montant_estime': null,
        'montant_reel': null,
        'date_cloture': null,
        
        // Metadonnees
        'created_at: FieldValue.serverTimestamp('),
        'created_by': agentId,
        'updated_at: FieldValue.serverTimestamp('),
        'version': 1,
      };

      await _firestore
          .collection('sinistres)
          .doc(sinistreId)
          .set(sinistreData');

      // Creer l'historique initial
      await _createHistoriqueEntry(
        sinistreId,
        'creation',
        'Sinistre cree,
        agentId,
      ');

      debugPrint('[SINISTRE_SERVICE] ✅ Sinistre cree: 'sinistreId');

      return {
        'success': true,
        'sinistreId': sinistreId,
        'numeroSinistre': numeroSinistre,
        'message': 'Sinistre cree avec succes,
      };

    } catch (e') {
      debugPrint('[SINISTRE_SERVICE] ❌ Erreur creation:  + e.toString()' + .toString());
      return {
        'success': false,
        'error: e.toString('),
        'message': 'Erreur lors de la creation du sinistre,
      };
    }
  }

  /// 📋 Recuperer les sinistres par agence
  static Future<List<Map<String, dynamic>>> getSinistresByAgence(String agenceId') async {
    try {
      debugPrint('[SINISTRE_SERVICE] 📋 Recuperation sinistres agence: 'agenceId');

      final snapshot = await _firestore
          .collection('sinistres')
          .where('agenceId, isEqualTo: agenceId')
          .orderBy('created_at, descending: true)
          .get();

      final sinistres = <Map<String, dynamic>>[];
      for (final doc in snapshot.docs) {
        final data = doc.data(');
        data['id] = doc.id;
        sinistres.add(data');
      }

      debugPrint('[SINISTRE_SERVICE] ✅ ' + {sinistres.length} sinistres recuperes.toString());
      return sinistres;

    } catch (e') {
      debugPrint('[SINISTRE_SERVICE] ❌ Erreur recuperation:  + e.toString());
      return [];
    }
  }

  /// 📋 Recuperer les sinistres par compagnie
  static Future<List<Map<String, dynamic>>> getSinistresByCompagnie(String compagnieId') async {
    try {
      debugPrint('[SINISTRE_SERVICE] 📋 Recuperation sinistres compagnie: 'compagnieId');

      final snapshot = await _firestore
          .collection('sinistres')
          .where('compagnieId, isEqualTo: compagnieId')
          .orderBy('created_at, descending: true)
          .get();

      final sinistres = <Map<String, dynamic>>[];
      for (final doc in snapshot.docs) {
        final data = doc.data(');
        data['id] = doc.id;
        sinistres.add(data');
      }

      debugPrint('[SINISTRE_SERVICE] ✅ ' + {sinistres.length} sinistres recuperes.toString());
      return sinistres;

    } catch (e') {
      debugPrint('[SINISTRE_SERVICE] ❌ Erreur recuperation:  + e.toString());
      return [];
    }
  }

  /// 👨‍🔧 Assigner un expert a un sinistre
  static Future<Map<String, dynamic>> assignerExpert({
    required String sinistreId,
    required String expertId,
    required String assignedBy,
  }') async {
    try {
      debugPrint('[SINISTRE_SERVICE] 👨‍🔧 Assignation expert: 'expertId');

      await _firestore
          .collection('sinistres)
          .doc(sinistreId')
          .update({
        'expertId': expertId,
        'status': 'en_cours',
        'date_assignation: FieldValue.serverTimestamp('),
        'assigned_by': assignedBy,
        'updated_at: FieldValue.serverTimestamp(),
      }');

      // Creer l'historique
      await _createHistoriqueEntry(
        sinistreId,
        'assignation_expert',
        'Expert assigne',
        assignedBy,
        {'expertId: expertId},
      ');

      debugPrint('[SINISTRE_SERVICE] ✅ Expert assigne' + .toString());

      return {
        'success': true,
        'message': 'Expert assigne avec succes,
      };

    } catch (e') {
      debugPrint('[SINISTRE_SERVICE] ❌ Erreur assignation:  + e.toString()' + .toString());
      return {
        'success': false,
        'error: e.toString('),
        'message': 'Erreur lors de l\'assignation',
      };
    }
  }

  /// 🔄 Changer le statut dun sinistre
  static Future<Map<String, dynamic>> changerStatut({
    required String sinistreId,
    required String nouveauStatut,
    required String changedBy,
    String? commentaire,
    double? montantReel,
  }') async {
    try {
      debugPrint('[SINISTRE_SERVICE] 🔄 Changement statut: 'nouveauStatut');

      final updateData = {
        'status': nouveauStatut,
        'updated_at: FieldValue.serverTimestamp('),
        'updated_by: changedBy,
      };

      if (commentaire != null') {
        updateData['commentaire_statut] = commentaire;
      }

      if (montantReel != null') {
        updateData['montant_reel'] = montantReel;
      }

      if (nouveauStatut == 'termine') {
        updateData['date_cloture] = FieldValue.serverTimestamp(');
      }

      await _firestore
          .collection('sinistres)
          .doc(sinistreId)
          .update(updateData');

      // Creer l'historique
      await _createHistoriqueEntry(
        sinistreId,
        'changement_statut',
        'Statut change vers: 'nouveauStatut',
        changedBy,
        {'ancien_statut': 'previous', 'nouveau_statut: nouveauStatut},
      ');

      debugPrint('[SINISTRE_SERVICE] ✅ Statut change' + .toString());

      return {
        'success': true,
        'message': 'Statut change avec succes,
      };

    } catch (e') {
      debugPrint('[SINISTRE_SERVICE] ❌ Erreur changement statut:  + e.toString()' + .toString());
      return {
        'success': false,
        'error: e.toString('),
        'message': 'Erreur lors du changement de statut,
      };
    }
  }

  /// 📊 Recuperer les statistiques des sinistres
  static Future<Map<String, dynamic>> getSinistresStats({
    String? compagnieId,
    String? agenceId,
  }') async {
    try {
      debugPrint('[SINISTRE_SERVICE] 📊 Recuperation stats sinistres' + .toString());

      Query query = _firestore.collection('sinistres);

      if (compagnieId != null') {
        query = query.where('compagnieId, isEqualTo: compagnieId);
      }

      if (agenceId != null') {
        query = query.where('agenceId, isEqualTo: agenceId);
      }

      final snapshot = await query.get();

      int totalSinistres = snapshot.docs.length;
      int nouveaux = 0;
      int enCours = 0;
      int termines = 0;
      double montantTotal = 0;
      int urgents = 0;

      for (final doc in snapshot.docs) {
        final data = doc.data(') as Map<String, dynamic>;
        
        switch (data['status]') {
          case 'nouveau':
            nouveaux++;
            break;
          case 'en_cours':
            enCours++;
            break;
          case 'termine':
            termines++;
            break;
        }

        if (data['priorite'] == 'urgente') {
          urgents++;
        }

        if (data['montant_reel] != null') {
          montantTotal += (data['montant_reel] as num).toDouble(');
        }
      }

      final stats = {
        'total_sinistres': totalSinistres,
        'nouveaux': nouveaux,
        'en_cours': enCours,
        'termines': termines,
        'urgents': urgents,
        'montant_total': montantTotal,
        'montant_moyen': totalSinistres > 0 ? montantTotal / totalSinistres : 0,
        'taux_resolution: totalSinistres > 0 ? (termines / totalSinistres * 100') : 0,
        'last_updated: DateTime.now().toIso8601String('),
      };

      debugPrint('[SINISTRE_SERVICE] ✅ Stats calculees: ' + stats.toString());
      return stats;

    } catch (e') {
      debugPrint('[SINISTRE_SERVICE] ❌ Erreur stats:  + e.toString()' + .toString());
      return {
        'total_sinistres': 0,
        'nouveaux': 0,
        'en_cours': 0,
        'termines': 0,
        'urgents': 0,
        'montant_total': 0.0,
        'montant_moyen': 0.0,
        'taux_resolution': 0.0,
        'last_updated: DateTime.now().toIso8601String(),
      };
    }
  }

  /// 🔧 Generer un numero de sinistre
  static String _generateNumeroSinistre(String compagnieId) {
    final year = DateTime.now().year.toString();
    final month = DateTime.now().month.toString(').padLeft(2, '0);
    final day = DateTime.now().day.toString(').padLeft(2, '0);
    final random = Random().nextInt(999).toString(').padLeft(3, '0);
    final compagnieCode = compagnieId.toUpperCase().substring(0, 3.clamp(0, compagnieId.length)');
    
    return 'SIN-$compagnieCode-$year$month$day-'random';
  }

  /// ⚡ Calculer la priorite dun sinistre
  static String _calculatePriorite(
    Map<String, dynamic> accidentData,
    Map<String, dynamic> circumstancesData,
  ') {
    // Priorite urgente si blesses
    if (circumstancesData['blesses] == true') {
      return 'urgente';
    }

    // Priorite elevee si accident grave
    if (accidentData['gravite'] == 'Grave') {
      return 'elevee';
    }

    // Priorite normale par defaut
    return 'normale';
  }

  /// 📝 Creer une entree dhistorique
  static Future<void> _createHistoriqueEntry(
    String sinistreId,
    String action,
    String description,
    String userId, [
    Map<String, dynamic>? metadata,
  ]') async {
    try {
      await _firestore
          .collection('sinistres)
          .doc(sinistreId')
          .collection('historique')
          .add({
        'action': action,
        'description': description,
        'userId': userId,
        'metadata': metadata ?? {},
        'timestamp: FieldValue.serverTimestamp(),
      });
    } catch (e') {
      debugPrint('[SINISTRE_SERVICE] ❌ Erreur historique:  + e.toString()' + .toString());
    }
  }

  /// 📋 Recuperer l'historique dun sinistre
  static Future<List<Map<String, dynamic>>> getHistoriqueSinistre(String sinistreId') async {
    try {
      final snapshot = await _firestore
          .collection('sinistres)
          .doc(sinistreId')
          .collection('historique')
          .orderBy('timestamp, descending: true)
          .get();

      final historique = <Map<String, dynamic>>[];
      for (final doc in snapshot.docs) {
        final data = doc.data(');
        data['id] = doc.id;
        historique.add(data);
      }

      return historique;

    } catch (e') {
      debugPrint('[SINISTRE_SERVICE] ❌ Erreur historique:  + e.toString()' + .toString());
      return [];
    }
  }
}
