import 'package:flutter/material.dart';
import '../../models/professional_request_model_final.dart';
import '../../../../core/theme/modern_theme.dart';

/// 📋 Widget pour afficher une demande de compte professionnel
class ProfessionalRequestCard extends StatelessWidget {
  final ProfessionalRequestModel request;
  final VoidCallback? onApprove;
  final VoidCallback? onReject;
  final VoidCallback? onViewDetails;

  ;

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: ,
      ),
      child: (1),
                      ),
                      const SizedBox(height: 4),
                      ,
                      ),
                    ],
                  ),
                ),
                _buildStatusChip(),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Informations principales
            _buildInfoRow(Icons.email, 'Email', request.email),
            const SizedBox(height: 8),
            _buildInfoRow(Icons.phone, 'Téléphone', request.tel),
            const SizedBox(height: 8),
            _buildInfoRow(Icons.credit_card, 'CIN', request.cin),
            
            // Informations spécifiques selon le rôle
            if (request.roleDemande == 'agent_agence') ...[
              const SizedBox(height: 8),
              _buildInfoRow(Icons.business, 'Agence', request.nomAgence ?? 'N/A'),
              const SizedBox(height: 8),
              _buildInfoRow(Icons.domain, 'Compagnie', request.compagnie ?? 'N/A'),
            ],
            
            if (request.roleDemande == 'expert_auto') ...[
              const SizedBox(height: 8),
              _buildInfoRow(Icons.verified, 'N° Agrément', request.numAgrement ?? 'N/A'),
              const SizedBox(height: 8),
              _buildInfoRow(Icons.location_on, 'Zone', request.zoneIntervention ?? 'N/A'),
            ],
            
            if (request.roleDemande == 'admin_compagnie') ...[
              const SizedBox(height: 8),
              _buildInfoRow(Icons.corporate_fare, 'Compagnie', request.nomCompagnie ?? 'N/A'),
              const SizedBox(height: 8),
              _buildInfoRow(Icons.work, 'Fonction', request.fonction ?? 'N/A'),
            ],
            
            const SizedBox(height: 16),
            
            // Date de soumission
            Row(
              children: [
                ,
                const SizedBox(width: 8),
                ({_formatDate(request.envoyeLe)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: ModernTheme.textLight,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Boutons d'action
            if (request.status == 'en_attente') _buildActionButtons(context),
            
            // Bouton voir détails moderne
            if (onViewDetails != null) ...[
              const SizedBox(height: 12),
              ,
                        ModernTheme.primaryColor.withValues(alpha: 0.05),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: ModernTheme.primaryColor.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: onViewDetails,
                      borderRadius: BorderRadius.circular(12),
                      child: (1),
                            const SizedBox(width: 8),
                            ,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 🏷️ Chip de statut
  Widget _buildStatusChip() {
    Color backgroundColor;
    Color textColor;
    String label;
    IconData icon;

    switch (request.status) {
      case 'en_attente':
        backgroundColor = ModernTheme.warningColor.withValues(alpha: 0.1);
        textColor = ModernTheme.warningColor;
        label = 'En attente';
        icon = Icons.pending;
        break;
      case 'acceptee':
        backgroundColor = ModernTheme.successColor.withValues(alpha: 0.1);
        textColor = ModernTheme.successColor;
        label = 'Approuvée';
        icon = Icons.check_circle;
        break;
      case 'rejetee':
        backgroundColor = ModernTheme.errorColor.withValues(alpha: 0.1);
        textColor = ModernTheme.errorColor;
        label = 'Rejetée';
        icon = Icons.cancel;
        break;
      default:
        backgroundColor = Colors.grey.withValues(alpha: 0.1);
        textColor = Colors.grey;
        label = 'Inconnu';
        icon = Icons.help;
    }

    return Container(
      padding: ,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(icon, size: 16, color: textColor),
          const SizedBox(width: 4),
          ,
          ),
        ],
      ),
    );
  }

  /// 📝 Ligne d'information
  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        ,
        const SizedBox(width: 8),
        (label: ',
          style: TextStyle(
            fontSize: 14,
            color: ModernTheme.textLight,
            fontWeight: FontWeight.w500,
          ),
        ),
        Expanded(
          child: ,
          ),
        ),
      ],
    );
  }

  /// 🎯 Boutons d'action pour les demandes en attente
  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.const Icon(
            onPressed: onApprove,
            icon: const Icon(Icons.info),
            label: const Text('Approuver'),
            style: ElevatedButton.styleFrom(
              backgroundColor: ModernTheme.successColor,
              foregroundColor: Colors.white,
              padding: ,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.const Icon(
            onPressed: onReject,
            icon: const Icon(Icons.info),
            label: const Text('Rejeter'),
            style: ElevatedButton.styleFrom(
              backgroundColor: ModernTheme.errorColor,
              foregroundColor: Colors.white,
              padding: ,
          ),
        ),
      ],
    );
  }

  /// 📅 Formater la date
  String _formatDate(DateTime? date) {
    if (date == null) return 'Date inconnue';
    
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Aujourd\'hui à ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      return 'Hier à ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays < 7) {
      return 'Il y a ${difference.inDays} jours';
    } else {
      return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}
