import 'package:flutter/material.dart';
import '../../../../core/services/firestore_diagnostic_service.dart';
import '../../../../core/theme/modern_theme.dart';

/// 🔍 Bouton de diagnostic Firestore
/// 
/// Permet de diagnostiquer pourquoi Firestore semble indisponible
class FirestoreDiagnosticButton extends StatefulWidget {
  const FirestoreDiagnosticButton({Key? key}) ) : super(key: key);

  @override
  State<FirestoreDiagnosticButton> createState() => _FirestoreDiagnosticButtonState();
}

class _FirestoreDiagnosticButtonState extends State<FirestoreDiagnosticButton> {
  bool _isRunning = false;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.const Icon(
      onPressed: _isRunning ? null : _runDiagnostic,
      icon: _isRunning 
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : ,
      label: const Text(_isRunning ? 'Diagnostic...' : 'Diagnostic Firestore'),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
    );
  }

  /// 🔍 Lancer le diagnostic
  Future<void> _runDiagnostic() async {
    setState(() => _isRunning = true);

    try {
      final diagnostic = await FirestoreDiagnosticService.performFullDiagnostic();
      
      if (mounted) {
        _showDiagnosticResults(diagnostic);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isRunning = false);
      }
    }
  }

  /// 📊 Afficher les résultats du diagnostic
  void _showDiagnosticResults(Map<String, dynamic> diagnostic) {
    final overallStatus = diagnostic['overall_status'] as String;
    final tests = diagnostic['tests'] as Map<String, dynamic>;
    final recommendations = diagnostic['recommendations'] as List<dynamic>;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(
              _getStatusconst Icon(overallStatus),
              color: _getStatusColor(overallStatus),
            ),
            const SizedBox(width: 8),
            const Text('Diagnostic Firestore'),
          ],
        ),
        content: (1).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        _getStatusconst Icon(overallStatus),
                        color: _getStatusColor(overallStatus),
                      ),
                      const SizedBox(width: 8),
                      ({_getStatusconst Text(overallStatus)}',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: _getStatusColor(overallStatus),
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Tests détaillé ,
                ),
                const SizedBox(height: 8),
                
                ...tests.entries.map((entry) {
                  final testName = entry.key;
                  final result = entry.value as Map<String, dynamic>;
                  final status = result['status'] as String;
                  final message = result['message'] as String;
                  
                  return (1),
                          color: _getStatusColor(status),
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: (message',
                            style: const TextStyle(fontSize: 14),
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
                
                const SizedBox(height: 16),
                
                // Recommandations
                if (recommendations.isNotEmpty) ...[
                  ,
                  ),
                  const SizedBox(height: 8),
                  
                  ...recommendations.map((rec) {
                    return (1)),
                          Expanded(
                            child: ,
                              style: const TextStyle(fontSize: 14),
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ],
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
          if (overallStatus != 'healthy')
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _runDiagnostic(); // Relancer le diagnostic
              },
              child: const Text('Relancer'),
            ),
        ],
      ),
    );
  }

  /// 🎨 Obtenir l'icône selon le statut
  IconData _getStatusconst Icon(String status) {
    switch (status) {
      case 'success':
      case 'healthy':
        return Icons.check_circle;
      case 'warning':
      case 'degraded':
        return Icons.warning;
      case 'failed':
      case 'error':
      case 'unhealthy':
        return Icons.error;
      default:
        return Icons.help;
    }
  }

  /// 🎨 Obtenir la couleur selon le statut
  Color _getStatusColor(String status) {
    switch (status) {
      case 'success':
      case 'healthy':
        return Colors.green;
      case 'warning':
      case 'degraded':
        return Colors.orange;
      case 'failed':
      case 'error':
      case 'unhealthy':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  /// 📝 Obtenir le texte selon le statut
  String _getStatusconst Text(String status) {
    switch (status) {
      case 'healthy':
        return 'Excellent';
      case 'degraded':
        return 'Dégradé';
      case 'unhealthy':
        return 'Problématique
