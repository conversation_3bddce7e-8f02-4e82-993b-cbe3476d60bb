import 'package:flutter/material.dart';

/// 🏛️ Carte d'affichage d'une agence
class AgenceCard extends StatelessWidget {
  final Map<String, dynamic> agence;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onToggleStatus;
  final VoidCallback? onDelete;

  ;

  @override
  Widget build(BuildContext context) {
    final status = agence['status']?.toString().toLowerCase() ?? 'actif';
    final isActive = status == 'actif';
    final isFakeData = agence['isFakeData'] ?? false;

    return Container(
      margin: ,
        border: Border.all(
          color: isActive 
              ? const Color(0xFF10B981).withValues(alpha: 0.3)
              : Colors.grey[300]!,
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: (1),
                          decoration: BoxDecoration(
                            color: isActive 
                                ? const Color(0xFF10B981).withValues(alpha: 0.1)
                                : Colors.grey[200],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child:  : Colors.grey[600],
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              ,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              ,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Badges
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      // Badge de statut
                      Container(
                        padding:  : Colors.grey[400],
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: ,
                        ),
                      ),
                      
                      // Badge données de test
                      if (isFakeData) ...[
                        const SizedBox(height: 4),
                        Container(
                          padding: ,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                          ),
                          child: ,
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Informations principales
              _buildInfoRow(
                Icons.location_on_rounded,
                '${agence['ville'] ?? 'Ville'}, ${agence['gouvernorat'] ?? 'Gouvernorat'}',
              ),
              
              if (agence['adresse'] != null && agence['adresse'].toString().isNotEmpty) ...[
                const SizedBox(height: 6),
                _buildInfoRow(
                  Icons.home_rounded,
                  agence['adresse'],
                ),
              ],
              
              if (agence['responsable'] != null && agence['responsable'].toString().isNotEmpty) ...[
                const SizedBox(height: 6),
                _buildInfoRow(
                  Icons.person_rounded,
                  agence['responsable'],
                ),
              ],
              
              const SizedBox(height: 12),
              
              // Contacts
              Row(
                children: [
                  if (agence['telephone'] != null && agence['telephone'].toString().isNotEmpty)
                    Expanded(
                      child: _buildContactChip(
                        Icons.phone_rounded,
                        agence['telephone'],
                        const Color(0xFF3B82F6),
                      ),
                    ),
                  
                  if (agence['telephone'] != null && agence['email'] != null &&
                      agence['telephone'].toString().isNotEmpty && agence['email'].toString().isNotEmpty)
                    const SizedBox(width: 8),
                  
                  if (agence['email'] != null && agence['email'].toString().isNotEmpty)
                    Expanded(
                      child: _buildContactChip(
                        Icons.email_rounded,
                        agence['email'],
                        const Color(0xFF10B981),
                      ),
                    ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Actions
              Row(
                children: [
                  // Bouton voir détails
                  Expanded(
                    child: OutlinedButton.const Icon(
                      onPressed: onTap,
                      icon: const Icon(Icons.info),
                      label: const Text('Détails'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: const Color(0xFF667eea),
                        side: const BorderSide(color: Color(0xFF667eea)),
                        padding: ,
                      ),
                    ),
                  ),
                  
                  const SizedBox(width: 8),
                  
                  // Bouton modifier
                  Expanded(
                    child: ElevatedButton.const Icon(
                      onPressed: onEdit,
                      icon: const Icon(Icons.info),
                      label: const Text('Modifier'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF667eea),
                        foregroundColor: Colors.white,
                        padding: ,
                      ),
                    ),
                  ),
                  
                  const SizedBox(width: 8),
                  
                  // Menu actions
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'toggle_status':
                          onToggleStatus?.call();
                          break;
                        case 'delete':
                          onDelete?.call();
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        value: 'toggle_status',
                        child: Row(
                          children: [
                            ,
                            const SizedBox(width: 8),
                            const Text(isActive ? 'Désactiver' : 'Activer'),
                          ],
                        ),
                      ),
                      if (isFakeData)
                        ,
                              const SizedBox(width: 8),
                              const Text('Supprimer'),
                            ],
                          ),
                        ),
                    ],
                    child: Container(
                      padding: ,
                      ),
                      child: ,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 📋 Ligne d
