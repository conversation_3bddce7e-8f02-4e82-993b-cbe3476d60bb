import 'package:flutter/material.dart';
import '../../../core/services/firestore_connection_manager.dart';

/// 🚨 Bouton d'urgence pour créer les admins en cas de problème
class EmergencySetupButton extends StatefulWidget {
  const Text(\;

  @override
  State<EmergencySetupButton> createState() => _EmergencySetupButtonState();
}

class _EmergencySetupButtonState extends State<EmergencySetupButton> {
  bool _isWorking = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: ,
        child: (1),
                  const SizedBox(width: 8),
                  ,
                  ),
                  ,
                  if (_isWorking)
                    const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                ],
              ),
              const SizedBox(height: 8),
              const Text(
                'Si l\'auto-setup ne fonctionne pas à cause de problèmes de connexion, '
                'utilisez ce bouton pour forcer la création des admins.',
                style: TextStyle(fontSize: 12),
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  ElevatedButton.const Icon(
                    onPressed: _isWorking ? null : _runEmergencySetup,
                    icon: const Icon(Icons.info),
                    label: const Text('🔧 Réparer Connexion'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.const Icon(
                    onPressed: _isWorking ? null : _forceCreateAdmins,
                    icon: const Icon(Icons.info),
                    label: const Text('🚀 Créer Admins'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 🔧 Réparer la connexion Firestore
  Future<void> _runEmergencySetup() async {
    setState(() => _isWorking = true);

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: const Text('🔧 Réparation connexion Firestore...'),
          duration: Duration(seconds: 2),
        ),
      );

      final success = await FirestoreConnectionManager.optimizeAndRepairConnection();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(success 
              ? '✅ Connexion Firestore réparée !' 
              : '❌ Impossible de réparer la connexion'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );

        if (success) {
          // Afficher le statut de la connexion
          final status = await FirestoreConnectionManager.getConnectionStatus();
          _showConnectionStatus(status);
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isWorking = false);
      }
    }
  }

  /// 🚀 Forcer la création des admins
  Future<void> _forceCreateAdmins() async {
    setState(() => _isWorking = true);

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: const Text('🚀 Création forcée des admins...'),
          duration: Duration(seconds: 3),
        ),
      );

      final result = await FirestoreConnectionManager.createAdminsWithConnectionManager();
      
      if (mounted) {
        final success = result['success'] == true;
        final createdCount = (result['created_admins'] as List).length;
        final failedCount = (result['failed_admins'] as List).length;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (failedCount admins non créés'),
            backgroundColor: success ? Colors.green : Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );

        if (success) {
          _showSuccessDialog(result);
        } else {
          _showErrorDialog(result);
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isWorking = false);
      }
    }
  }

  /// 📊 Afficher le statut de la connexion
  void _showConnectionStatus(Map<String, dynamic> status) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('📊 Statut Connexion'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ({status['internet'] ? '✅' : '❌'}'),
            ({status['firestore'] ? '✅' : '❌'}'),
            ({status['optimized'] ? '✅' : '❌'}'),
            ({status['overall_status']}'),
            if (status['error'] != null)
              ({status['error']}', style: const TextStyle()),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// 🎉 Afficher le dialog de succès
  void _showSuccessDialog(Map<String, dynamic> result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🎉 Succès !'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ({(result['created_admins'] as List).length} admins créés:'),
            const SizedBox(height: 8),
            ...(result['created_admins'] as List<String>).map(
              (email) => (email', style: const TextStyle()),
            ),
            const SizedBox(height: 16),
            const Text('🎯 Vous pouvez maintenant:'),
            const Text('• Utiliser le Super Admin Dashboard'),
            const Text('• Créer d\'autres utilisateurs'),
            const Text('• Gérer les compagnies d\'assurance'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Parfait !'),
          ),
        ],
      ),
    );
  }

  /// ❌ Afficher le dialog d'erreur
  void _showErrorDialog(Map<String, dynamic> result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('❌ Problème'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if ((result['failed_admins'] as List).isNotEmpty) ...[
              ({(result['failed_admins'] as List).length}'),
              const SizedBox(height: 8),
              ...(result['failed_admins'] as List<String>).map(
                (email) => (email', style: const TextStyle()),
              ),
            ],
            const SizedBox(height: 16),
            const Text('💡 Solutions:'),
            const Text('• Vérifier la connexion Internet'),
            const Text('• Réessayer dans quelques minutes'),
            const Text('• Créer manuellement dans Firebase Console'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Compris
