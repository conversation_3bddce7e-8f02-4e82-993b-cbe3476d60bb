import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../auth/services/universal_auth_service.dart';

/// 👨‍💼 Écran d'administration pour gérer les demandes d'inscription
class AdminDemandesScreen extends StatefulWidget {
  const AdminDemandesScreen({Key? key}) ) : super(key: key);

  @override
  State<AdminDemandesScreen> createState() => _AdminDemandesScreenState();
}

class _AdminDemandesScreenState extends State<AdminDemandesScreen> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Demandes d\'Inscription'),
        backgroundColor: Colors.red[600],
        foregroundColor: Colors.white,
      ),
      body: StreamBuilder<QuerySnapshot>(
        stream: _firestore
            .collection('demandes_inscription')
            .where('statut', isEqualTo: 'en_attente')
            .snapshots(),
        builder: (context, snapshot) {
          if (snapshot.hasError) {
            return ({snapshot.error}'),
            );
          }

          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          final demandes = snapshot.data?.docs ?? [];

          if (demandes.isEmpty) {
            return ,
                  const SizedBox(height: 16),
                  ,
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            padding:  {
              final demande = demandes[index];
              final data = demande.data() as Map<String, dynamic>;
              
              return Card(
                margin: ,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // En-tête
                      Row(
                        children: [
                          CircleAvatar(
                            backgroundColor: Colors.blue[100],
                            child: ({data['nom']?[0] ?? 'Contenu'}',
                              style: TextStyle(
                                color: Colors.blue[800],
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                ({data['nom']}',
                                  style: ,
                                ),
                                ,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      
                      // Informations professionnelles
                      _buildInfoRow('Compagnie', data['compagnie'),
                      _buildInfoRow('Agence', data['agence'),
                      _buildInfoRow('Gouvernorat', data['gouvernorat'),
                      _buildInfoRow('Poste', data['poste'),
                      _buildInfoRow('Numéro Agent', data['numeroAgent'),
                      _buildInfoRow('Téléphone', data['telephone'),
                      
                      const SizedBox(height: 16),
                      
                      // Boutons d'action
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.const Icon(
                              onPressed: () => _approuverDemande(demande.id, data),
                              icon: const Icon(Icons.info),
                              label: const Text('Approuver'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: ElevatedButton.const Icon(
                              onPressed: () => _refuserDemande(demande.id),
                              icon: const Icon(Icons.info),
                              label: const Text('Refuser'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.red,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildInfoRow(String label, String? value) {
    return (1),
            ),
          ),
          Expanded(
            child: const Text(value ?? 'Non spécifié'),
          ),
        ],
      ),
    );
  }

  Future<void> _approuverDemande(String demandeId, Map<String, dynamic> data) async {
    try {
      // Créer le compte Firebase Auth et Firestore
      final result = await UniversalAuthService.signUp(
        email: data['email'],
        password: data['motDePasseTemporaire'],
        nom: data['nom'],
        prenom: data['prenom'],
        userType: 'assureur',
        additionalData: {
          'telephone': data['telephone'],
          'compagnie': data['compagnie'],
          'agence': data['agence'],
          'gouvernorat': data['gouvernorat'],
          'poste': data['poste'],
          'numeroAgent': data['numeroAgent'],
        },
      );

      if (result['success'] == true) {
        // Marquer la demande comme approuvée
        await _firestore.collection('demandes_inscription').doc(demandeId).update({
          'statut': 'approuvee',
          'dateApprobation': FieldValue.serverTimestamp(),
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: ({data['nom']}'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        throw Exception(result['error']);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _refuserDemande(String demandeId) async {
    try {
      await _firestore.collection('demandes_inscription').doc(demandeId).update({
        'statut': 'refusee',
        'dateRefus': FieldValue.serverTimestamp(),
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: const Text('❌ Demande refusée'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e
