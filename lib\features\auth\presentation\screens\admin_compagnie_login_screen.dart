import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../admin_compagnie/services/admin_compagnie_auth_service.dart';
import '../../../admin_compagnie/services/auth_diagnostic_service.dart';
import '../../../admin_compagnie/services/firestore_repair_service.dart';
import '../../../admin_compagnie/services/direct_firestore_fix.dart';

/// 🏢 Écran de connexion spécialisé pour Admin Compagnie
class AdminCompagnieLoginScreen extends StatefulWidget {
  const Text(\;

  @override
  State<AdminCompagnieLoginScreen> createState() => _AdminCompagnieLoginScreenState();
}

class _AdminCompagnieLoginScreenState extends State<AdminCompagnieLoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController(text: '<EMAIL>');
  final _passwordController = TextEditingController(text: 'AdminCompagnie123!');
  bool _isLoading = false;
  bool _obscurePassword = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  /// 🔐 Connexion avec le service spécialisé
  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final email = _emailController.text.trim();
      final password = _passwordController.text.trim();

      debugPrint('[ADMIN_COMPAGNIE_LOGIN] 🚀 Début connexion: $email');

      final success = await AdminCompagnieAuthService.signInAndNavigate(
        context: context,
        email: email,
        password: password,
      );

      if (!success && mounted) {
        setState(() => _isLoading = false);
      }

    } catch (e) {
      debugPrint('[ADMIN_COMPAGNIE_LOGIN] ❌ Erreur: $e');
      
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 🧪 Test avec credentials par défaut
  Future<void> _testDefaultLogin() async {
    setState(() => _isLoading = true);

    try {
      final success = await AdminCompagnieAuthService.testDefaultLogin(context);
      
      if (!success && mounted) {
        setState(() => _isLoading = false);
      }

    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 🔍 Diagnostic
  Future<void> _runDiagnostic() async {
    await AuthDiagnosticService.testAdminCompagnieLogin();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: const Text('🔍 Diagnostic terminé - Voir les logs'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  /// 🔧 Réparation automatique (ancienne méthode)
  Future<void> _repairFirestore() async {
    try {
      debugPrint('[ADMIN_COMPAGNIE_LOGIN] 🔧 Début réparation Firestore...');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: const Text('🔧 Réparation en cours...'),
            backgroundColor: Colors.blue,
          ),
        );
      }

      final success = await FirestoreRepairService.fullRepair();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(success ? '✅ Réparation réussie !' : '❌ Échec de la réparation'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }

      if (success) {
        debugPrint('[ADMIN_COMPAGNIE_LOGIN] ✅ Réparation réussie, tentative de connexion...');
        await _testDefaultLogin();
      }

    } catch (e) {
      debugPrint('[ADMIN_COMPAGNIE_LOGIN] ❌ Erreur réparation: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 🔧 Réparation directe (nouvelle méthode)
  Future<void> _directFirestoreFix() async {
    try {
      debugPrint('[ADMIN_COMPAGNIE_LOGIN] 🔧 Début réparation directe...');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: const Text('🔧 Réparation directe en cours...'),
            backgroundColor: Colors.purple,
          ),
        );
      }

      // Diagnostic avant réparation
      await DirectFirestoreFix.fullDiagnostic();

      // Réparation
      final success = await DirectFirestoreFix.fullFix();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(success ? '✅ Réparation directe réussie !' : '❌ Échec de la réparation directe'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }

      if (success) {
        debugPrint('[ADMIN_COMPAGNIE_LOGIN] ✅ Réparation directe réussie !');
        debugPrint('[ADMIN_COMPAGNIE_LOGIN] 🎯 Vous pouvez maintenant naviguer vers le dashboard');

        // Navigation directe vers le dashboard
        if (mounted) {
          Navigator.pushReplacementNamed(context, '/compagnie-dashboard');
        }
      }

    } catch (e) {
      debugPrint('[ADMIN_COMPAGNIE_LOGIN] ❌ Erreur réparation directe: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 🔍 Diagnostic des comptes Admin Compagnie existants
  Future<void> _diagnosticExistingAccounts() async {
    try {
      debugPrint('[ADMIN_COMPAGNIE_LOGIN] 🔍 Diagnostic des comptes Admin Compagnie existants...');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: const Text('🔍 Diagnostic des comptes existants...'),
            backgroundColor: Colors.blue,
          ),
        );
      }

      // Récupérer tous les comptes admin_compagnie
      final adminCompagnieAccounts = await FirebaseFirestore.instance
          .collection('users')
          .where('role', isEqualTo: 'admin_compagnie')
          .get();

      debugPrint('[ADMIN_COMPAGNIE_LOGIN] 📊 ${adminCompagnieAccounts.docs.length} comptes Admin Compagnie trouvés:');

      for (final doc in adminCompagnieAccounts.docs) {
        final data = doc.data();
        debugPrint('[ADMIN_COMPAGNIE_LOGIN] 📋 Compte: ${data['email']}');
        debugPrint('[ADMIN_COMPAGNIE_LOGIN]   - UID: ${doc.id}');
        debugPrint('[ADMIN_COMPAGNIE_LOGIN]   - Nom: ${data['prenom']} ${data['nom']}');
        debugPrint('[ADMIN_COMPAGNIE_LOGIN]   - Statut: ${data['status']}');
        debugPrint('[ADMIN_COMPAGNIE_LOGIN]   - CompagnieId: ${data['compagnieId']}');
        debugPrint('[ADMIN_COMPAGNIE_LOGIN]   - CompagnieNom: ${data['compagnieNom']}');
        debugPrint('[ADMIN_COMPAGNIE_LOGIN]   - Créé par: ${data['created_by']}');
        debugPrint('[ADMIN_COMPAGNIE_LOGIN]   - isFakeData: ${data['isFakeData']}');
        debugPrint('[ADMIN_COMPAGNIE_LOGIN]   - Créé le: ${data['created_at']}');
        debugPrint('[ADMIN_COMPAGNIE_LOGIN] ---');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: ({adminCompagnieAccounts.docs.length} comptes Admin Compagnie analysés - Voir les logs'),
            backgroundColor: Colors.green,
          ),
        );
      }

    } catch (e) {
      debugPrint('[ADMIN_COMPAGNIE_LOGIN] ❌ Erreur diagnostic: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: AppBar(
        title: const Text('Connexion Admin Compagnie'),
        backgroundColor: const Color(0xFF10B981),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: ,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Container(
                        padding: .withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: ,
                        ),
                      ),
                      const SizedBox(height: 16),
                      ,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      ,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 32),

                // Formulaire de connexion
                Container(
                  padding: ,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Email
                      TextFormField(
                        controller: _emailController,
                        keyboardType: TextInputType.emailAddress,
                        decoration: InputDecoration(
                          labelText: 'Email administrateur',
                          hintText: '<EMAIL>',
                          prefixIcon: ,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          filled: true,
                          fillColor: Colors.grey[50],
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Email requis';
                          }
                          if (!RegExp(r'^[^@]+@[^@]+\.[^@]+$').hasMatch(value)) {
                            return 'Format email invalide';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // Mot de passe
                      TextFormField(
                        controller: _passwordController,
                        obscureText: _obscurePassword,
                        decoration: InputDecoration(
                          labelText: 'Mot de passe',
                          hintText: 'Votre mot de passe sécurisé',
                          prefixIcon: ,
                          suffixIcon: IconButton(
                            icon: const Icon(Icons.info),
                            onPressed: () {
                              setState(() {
                                _obscurePassword = !_obscurePassword;
                              });
                            },
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          filled: true,
                          fillColor: Colors.grey[50],
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Mot de passe requis';
                          }
                          if (value.length < 6) {
                            return 'Minimum 6 caractères';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 24),

                      // Bouton de connexion
                      ,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 0,
                          ),
                          child: _isLoading
                              ? ,
                                  ),
                                )
                              : ,
                                ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // Boutons de test et diagnostic
                Container(
                  padding: ,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.orange.withValues(alpha: 0.2)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      ,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 12),
                      
                      // Bouton test rapide
                      OutlinedButton.const Icon(
                        onPressed: _isLoading ? null : _testDefaultLogin,
                        icon: const Icon(Icons.info),
                        label: const Text('Test Connexion Rapide'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.orange,
                          side: ,
                        ),
                      ),
                      
                      const SizedBox(height: 8),

                      // Bouton diagnostic
                      OutlinedButton.const Icon(
                        onPressed: _runDiagnostic,
                        icon: const Icon(Icons.info),
                        label: const Text('Diagnostic Complet'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.blue,
                          side: ,
                        ),
                      ),

                      const SizedBox(height: 8),

                      // Bouton réparation directe (SOLUTION)
                      ElevatedButton.const Icon(
                        onPressed: _directFirestoreFix,
                        icon: const Icon(Icons.info),
                        label: const Text('🚀 SOLUTION DIRECTE'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.purple,
                          foregroundColor: Colors.white,
                        ),
                      ),

                      const SizedBox(height: 8),

                      // Bouton diagnostic comptes existants
                      OutlinedButton.const Icon(
                        onPressed: _diagnosticExistingAccounts,
                        icon: const Icon(Icons.info),
                        label: const Text('🔍 Diagnostic Comptes Existants'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.blue,
                          side: ,
                        ),
                      ),

                      const SizedBox(height: 8),

                      // Bouton réparation (ancienne méthode)
                      OutlinedButton.const Icon(
                        onPressed: _repairFirestore,
                        icon: const Icon(Icons.info),
                        label: const Text('🔧 Réparer Firestore
