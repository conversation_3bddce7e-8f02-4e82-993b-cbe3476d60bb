import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import '../../auth/services/agent_registration_service.dart';
import '../../auth/models/agent_registration_model.dart';

class AgentRequestsScreen extends ConsumerStatefulWidget {
  const AgentRequestsScreen({Key? key}) ) : super(key: key);

  @override
  ConsumerState<AgentRequestsScreen> createState() => _AgentRequestsScreenState();
}

class _AgentRequestsScreenState extends ConsumerState<AgentRequestsScreen> {
  List<AgentRegistrationModel> _requests = [];
  Map<String, int> _stats = {};
  bool _isLoading = true;
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// 📊 Charger les données
  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      final requests = await AgentRegistrationService.getPendingRequests();
      final stats = await AgentRegistrationService.getRequestsStats();

      if (mounted) {
        setState(() {
          _requests = requests;
          _stats = stats;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Erreur chargement données: $e');
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// ✅ Approuver une demande
  Future<void> _approveRequest(AgentRegistrationModel request) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('✅ Approuver la demande'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Voulez-vous approuver la demande de :'),
            const SizedBox(height: 8),
            ({request.fullName}',
              style: ,
            ),
            ({request.agence}'),
            const SizedBox(height: 16),
            Container(
              padding: ,
                border: Border.all(color: Colors.green[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ,
                  ),
                  const SizedBox(height: 8),
                  const Text('• Création du compte Firebase Auth'),
                  const Text('• Enregistrement dans la base de données'),
                  const Text('• Envoi d\'un email de confirmation'),
                  const Text('• Activation de l\'accès à l\'application'),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('Approuver'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await AgentRegistrationService.approveAgentRequest(
          request.id,
          'admin', // TODO: Récupérer l'ID admin actuel
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: ({request.fullName} approuvée'),
              backgroundColor: Colors.green,
            ),
          );
          _loadData(); // Recharger les données
        }
      } catch (e) {
        debugPrint('Erreur approbation: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: (e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  /// ❌ Rejeter une demande
  Future<void> _rejectRequest(AgentRegistrationModel request) async {
    final reasonController = TextEditingController();

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('❌ Rejeter la demande'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Rejeter la demande de :'),
            const SizedBox(height: 8),
            ({request.fullName}',
              style: ,
            ),
            ({request.agence}'),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              maxLines: 3,
              decoration: const InputDecoration(
                labelText: 'Motif du rejet *',
                hintText: 'Expliquez pourquoi cette demande est rejetée...',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              if (reasonController.text.trim().isNotEmpty) {
                Navigator.of(context).pop(true);
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: const Text('Veuillez saisir un motif'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Rejeter'),
          ),
        ],
      ),
    );

    if (confirmed == true && reasonController.text.trim().isNotEmpty) {
      try {
        await AgentRegistrationService.rejectAgentRequest(
          request.id,
          'admin', // TODO: Récupérer l'ID admin actuel
          reasonController.text.trim(),
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: ({request.fullName} rejetée'),
              backgroundColor: Colors.red,
            ),
          );
          _loadData(); // Recharger les données
        }
      } catch (e) {
        debugPrint('Erreur rejet: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: (e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }

    reasonController.dispose();
  }

  /// 📋 Widget pour afficher une demande
  Widget _buildRequestCard(AgentRegistrationModel request) {
    return Card(
      margin: ,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // En-tête avec nom et statut
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ,
                      ),
                      ,
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: , radix: 16) + 0xFF000000),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: ({request.statusFormatted}',
                    style: ,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Informations professionnelles
            Container(
              padding: ,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildInfoRow('🏢 Compagnie', request.compagnie),
                  _buildInfoRow('🏪 Agence', request.agence),
                  _buildInfoRow('📍 Gouvernorat', request.gouvernorat),
                  _buildInfoRow('💼 Poste', request.poste),
                  _buildInfoRow('📞 Téléphone', request.telephone),
                  _buildInfoRow('🆔 N° Agent', request.numeroAgent),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Informations de soumission
            Row(
              children: [
                ,
                const SizedBox(width: 4),
                ({request.submissionTimeText}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
                ,
                const Text(
                  DateFormat('dd/MM/yyyy à HH:mm').format(request.submittedAt),
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Boutons d'action
            if (request.isPending) ...[
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.const Icon(
                      onPressed: () => _rejectRequest(request),
                      icon: const Icon(Icons.info),
                      label: const Text('Rejeter'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.red,
                        side: ,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton.const Icon(
                      onPressed: () => _approveRequest(request),
                      icon: const Icon(Icons.info),
                      label: const Text('Approuver'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 📝 Widget pour afficher une ligne d'information
  Widget _buildInfoRow(String label, String value) {
    return (1),
            ),
          ),
          Expanded(
            child: ,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Demandes d\'Agents',
        showBackButton: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Statistiques
                Container(
                  padding:  ?? '0',
                          Colors.blue,
                          Icons.people,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildStatCard(
                          'En attente',
                          _stats['pending']?.toString() ?? '0',
                          Colors.orange,
                          Icons.hourglass_empty,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildStatCard(
                          'Approuvés',
                          _stats['approved']?.toString() ?? '0',
                          Colors.green,
                          Icons.check_circle,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildStatCard(
                          'Rejetés',
                          _stats['rejected']?.toString() ?? '0
