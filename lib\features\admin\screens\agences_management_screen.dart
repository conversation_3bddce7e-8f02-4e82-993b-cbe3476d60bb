import 'package:flutter/material.dart';
import '../../../models/admin_models.dart';
import '../../../services/admin_service.dart';

class AgencesManagementScreen extends StatefulWidget {
  final String? compagnieId;

  ;

  @override
  State<AgencesManagementScreen> createState() => _AgencesManagementScreenState();
}

class _AgencesManagementScreenState extends State<AgencesManagementScreen> {
  final AdminService _adminService = AdminService();
  List<AgenceAssurance> _agences = [];
  List<CompagnieAssurance> _compagnies = [];
  String? _selectedCompagnieId;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _selectedCompagnieId = widget.compagnieId;
    _chargerDonnees();
  }

  Future<void> _chargerDonnees() async {
    try {
      setState(() => _isLoading = true);
      
      // Charger les compagnies
      final compagnies = await _adminService.obtenirCompagnies();
      setState(() => _compagnies = compagnies);

      // Charger les agences si une compagnie est sélectionnée
      if (_selectedCompagnieId != null) {
        await _chargerAgences(_selectedCompagnieId!);
      }
      
      setState(() => _isLoading = false);
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _chargerAgences(String compagnieId) async {
    try {
      final agences = await _adminService.obtenirAgences(compagnieId);
      setState(() => _agences = agences);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Gestion des Agences'),
        backgroundColor: Colors.green[800],
        foregroundColor: Colors.white,
        actions: [
          if (_selectedCompagnieId != null)
            IconButton(
              icon: const Icon(Icons.info),
              onPressed: _ajouterAgence,
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                _buildCompagnieSelector(),
                Expanded(
                  child: _selectedCompagnieId == null
                      ? _buildSelectCompagnieMessage()
                      : _agences.isEmpty
                          ? _buildEmptyState()
                          : _buildAgencesList(),
                ),
              ],
            ),
    );
  }

  Widget _buildCompagnieSelector() {
    return Container(
      padding: ,
          ),
          const SizedBox(height: 8),
          DropdownButtonFormField<String>(
            value: _selectedCompagnieId,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              contentPadding: ,
            hint: const Text('Choisir une compagnie'),
            items: _compagnies.map((compagnie) {
              return DropdownMenuItem(
                value: compagnie.id,
                child: ,
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedCompagnieId = value;
                _agences.clear();
              });
              if (value != null) {
                _chargerAgences(value);
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSelectCompagnieMessage() {
    return ,
          const SizedBox(height: 16),
          ,
          ),
          const SizedBox(height: 8),
          ,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    final compagnieNom = _compagnies
        .firstWhere((c) => c.id == _selectedCompagnieId)
        .nom;

    return ,
          const SizedBox(height: 16),
          ,
          ),
          const SizedBox(height: 8),
          (compagnieNom',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.const Icon(
            onPressed: _ajouterAgence,
            icon: const Icon(Icons.info),
            label: const Text('Ajouter une agence'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green[600],
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAgencesList() {
    return ListView.builder(
      padding:  {
        final agence = _agences[index];
        return Card(
          margin: ,
              ),
            ),
            title: const Text("Titre"),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ({agence.code}'),
                ({agence.gouvernorat}'),
                ({agence.email}'),
              ],
            ),
            trailing: PopupMenuButton(
              itemBuilder: (context) => [
                ,
                      const SizedBox(width: 8),
                      const Text('Modifier'),
                    ],
                  ),
                ),
                ,
                      const SizedBox(width: 8),
                      const Text('Agents'),
                    ],
                  ),
                ),
                ,
                      const SizedBox(width: 8),
                      ),
                    ],
                  ),
                ),
              ],
              onSelected: (value) {
                switch (value) {
                  case 'edit':
                    _modifierAgence(agence);
                    break;
                  case 'agents':
                    _voirAgents(agence);
                    break;
                  case 'delete':
                    _supprimerAgence(agence);
                    break;
                }
              },
            ),
            onTap: () => _voirDetailsAgence(agence),
          ),
        );
      },
    );
  }

  void _ajouterAgence() {
    if (_selectedCompagnieId == null) return;
    
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AjouterAgenceScreen(compagnieId: _selectedCompagnieId!),
      ),
    ).then((_) => _chargerAgences(_selectedCompagnieId!));
  }

  void _modifierAgence(AgenceAssurance agence) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ModifierAgenceScreen(agence: agence),
      ),
    ).then((_) => _chargerAgences(_selectedCompagnieId!));
  }

  void _voirAgents(AgenceAssurance agence) {
    Navigator.pushNamed(
      context,
      '/admin/agents',
      arguments: {
        'compagnieId': agence.compagnieId,
        'agenceId': agence.id,
      },
    );
  }

  void _voirDetailsAgence(AgenceAssurance agence) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text("Titre"),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Code', agence.code),
            _buildDetailRow('Adresse', agence.adresse),
            _buildDetailRow('Ville', agence.ville),
            _buildDetailRow('Gouvernorat', agence.gouvernorat),
            _buildDetailRow('Email', agence.email),
            _buildDetailRow('Téléphone', agence.telephone),
            _buildDetailRow('Statut', agence.active ? 'Active' : 'Inactive'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return (1),
            ),
          ),
          Expanded(child: const Text(value)),
        ],
      ),
    );
  }

  void _supprimerAgence(AgenceAssurance agence) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmer la suppression'),
        content: ({agence.nom}" ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implémenter la suppression
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: const Text('Suppression en cours de développement'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }
}

// Écrans d'ajout et modification (à implémenter)
class AjouterAgenceScreen extends StatelessWidget {
  final String compagnieId;

  ;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Ajouter une Agence'),
        backgroundColor: Colors.green[800],
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: const Text('Formulaire d\'ajout en cours de développement'),
      ),
    );
  }
}

class ModifierAgenceScreen extends StatelessWidget {
  final AgenceAssurance agence;

  ;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Modifier l\'Agence'),
        backgroundColor: Colors.green[800],
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: const Text('Formulaire de modification en cours de développement
