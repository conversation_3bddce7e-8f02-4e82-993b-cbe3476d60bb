import 'package:flutter/material.dart';
import '../services/admin_compagnie_auth_service.dart';
import '../services/admin_agence_auth_service.dart';
import '../services/super_admin_auth_service.dart';
import '../utils/user_type.dart';
import 'admin_compagnie_dashboard.dart';
import 'admin_agence_dashboard.dart';

/// 🔐 Écran de connexion Admin (Compagnie/Agence)
class AdminCompagnieLoginScreen extends StatefulWidget {
  const AdminCompagnieLoginScreen({Key? key}) : super(key: key);

  @override
  State<AdminCompagnieLoginScreen> createState() => _AdminCompagnieLoginScreenState();
}

class _AdminCompagnieLoginScreenState extends State<AdminCompagnieLoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  
  String _selectedAdminType = 'admin_compagnie';
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final email = _emailController.text.trim();
      final password = _passwordController.text;

      switch (_selectedAdminType) {
        case 'super_admin':
          final result = await SuperAdminAuthService.login(email, password);
          if (result['success'] && mounted) {
            Navigator.of(context).pushReplacementNamed('/super-admin-dashboard');
          } else {
            setState(() {
              _errorMessage = result['message'] ?? 'Erreur de connexion';
            });
          }
          break;

        case 'admin_compagnie':
          final result = await AdminCompagnieAuthService.login(email, password);
          if (result['success'] && mounted) {
            final userData = result['userData'];
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(
                builder: (context) => AdminCompagnieDashboard(
                  compagnieId: userData['compagnieId'],
                  compagnieNom: userData['compagnieNom'] ?? 'Compagnie',
                ),
              ),
            );
          } else {
            setState(() {
              _errorMessage = result['message'] ?? 'Erreur de connexion';
            });
          }
          break;

        case 'admin_agence':
          final result = await AdminAgenceAuthService.login(email, password);
          if (result['success'] && mounted) {
            final userData = result['userData'];
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(
                builder: (context) => AdminAgenceDashboard(
                  agenceId: userData['agenceId'],
                  agenceNom: userData['agenceNom'] ?? 'Agence',
                  compagnieId: userData['compagnieId'],
                  compagnieNom: userData['compagnieNom'],
                  data: userData,
                ),
              ),
            );
          } else {
            setState(() {
              _errorMessage = result['message'] ?? 'Erreur de connexion';
            });
          }
          break;
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Erreur de connexion: $e';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(height: 40),
              
              // Logo et titre
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    const Icon(
                      Icons.admin_panel_settings,
                      size: 80,
                      color: Colors.blue.shade600,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Administration',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade800,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Connexion sécurisée',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Sélection du type d'admin
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 5,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: _selectedAdminType,
                    isExpanded: true,
                    icon: const Icon(Icons.arrow_drop_down),
                    items: const [
                      DropdownMenuItem(
                        value: 'super_admin',
                        child: Row(
                          children: [
                            const Icon(Icons.security, color: Colors.red),
                            const SizedBox(width: 12),
                            const Text('Super Administrateur'),
                          ],
                        ),
                      ),
                      DropdownMenuItem(
                        value: 'admin_compagnie',
                        child: Row(
                          children: [
                            const Icon(Icons.business, color: Colors.blue),
                            const SizedBox(width: 12),
                            const Text('Admin Compagnie'),
                          ],
                        ),
                      ),
                      DropdownMenuItem(
                        value: 'admin_agence',
                        child: Row(
                          children: [
                            const Icon(Icons.store, color: Colors.green),
                            const SizedBox(width: 12),
                            const Text('Admin Agence'),
                          ],
                        ),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedAdminType = value!;
                        _errorMessage = null;
                      });
                    },
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Formulaire de connexion
              Form(
                key: _formKey,
                child: Column(
                  children: [
                    // Email
                    TextFormField(
                      controller: _emailController,
                      keyboardType: TextInputType.emailAddress,
                      decoration: InputDecoration(
                        labelText: 'Email',
                        prefixIcon: const Icon(Icons.email),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Veuillez saisir votre email';
                        }
                        if (!value.contains('@')) {
                          return 'Email invalide';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Mot de passe
                    TextFormField(
                      controller: _passwordController,
                      obscureText: true,
                      decoration: InputDecoration(
                        labelText: 'Mot de passe',
                        prefixIcon: const Icon(Icons.lock),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Veuillez saisir votre mot de passe';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 24),

                    // Message d'erreur
                    if (_errorMessage != null)
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        margin: const EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red.shade200),
                        ),
                        child: const Text(
                          _errorMessage!,
                          style: TextStyle(color: Colors.red.shade700),
                          textAlign: TextAlign.center,
                        ),
                      ),

                    // Bouton de connexion
                    const SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _login,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue.shade600,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: _isLoading
                            ? const CircularProgressIndicator(color: Colors.white)
                            : const Text(
                                'Se connecter',
                                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
