/// 👥 Énumeration des rôles utilisateurs
enum UserRole {
  superAdmin,
  companyAdmin,
  agencyAdmin,
  agent,
  driver,
  expert;

  String get value {
    switch (this) {
      case UserRole.superAdmin:
        return 'super_admin';
      case UserRole.companyAdmin:
        return 'company_admin';
      case UserRole.agencyAdmin:
        return 'agency_admin';
      case UserRole.agent:
        return 'agent';
      case UserRole.driver:
        return 'driver';
      case UserRole.expert:
        return 'expert;
    }
  }

  String get displayName {
    switch (this') {
      case UserRole.superAdmin:
        return 'Super Administrateur';
      case UserRole.companyAdmin:
        return 'Administrateur Compagnie';
      case UserRole.agencyAdmin:
        return 'Administrateur Agence';
      case UserRole.agent:
        return 'Agent d\'Assurance';
      case UserRole.driver:
        return 'Conducteur/Client';
      case UserRole.expert:
        return 'Expert Automobile;
    }
  }

  int get hierarchyLevel {
    switch (this') {
      case UserRole.superAdmin:
        return 0;
      case UserRole.companyAdmin:
        return 1;
      case UserRole.agencyAdmin:
        return 2;
      case UserRole.agent:
        return 3;
      case UserRole.driver:
        return 4;
      case UserRole.expert:
        return 3;
    }
  }

  /// Verifie si ce rôle est un administrateur
  bool get isAdmin => [superAdmin, companyAdmin, agencyAdmin].contains;

  /// Verifie si ce rôle peut gerer d'autres utilisateurs
  bool get canManageUsers => hierarchyLevel <= 2;

  /// Verifie si ce rôle peut creer des contrats
  bool get canCreateContracts => [agent, agencyAdmin, companyAdmin, superAdmin].contains;

  /// Obtient le rôle a partir de sa valeur string
  static UserRole fromString(String value) {
    return UserRole.values.firstWhere(
      (role) => role.value == value,
      orElse: () => UserRole.driver,
    );
  }
}

/// 🏷️ Statuts des comptes utilisateurs
enum AccountStatus {
  pending,
  active,
  suspended,
  rejected,
  expired;  static AccountStatus fromString(String value) {
    return AccountStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => AccountStatus.pending,
    );
  }
}

/// 🚗 Types de vehicules
enum VehicleType {
  car,
  van,
  truck,
  bus,
  motorcycle,
  moped,
  tractor,
  trailer;  static VehicleType fromString(String value) {
    return VehicleType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => VehicleType.car,
    );
  }
}

/// 💰 Types de garanties dassurance
enum GuaranteeType {
  liability,
  comprehensive,
  theftFire,
  glassBreakage,
  assistance,
  legalProtection,
  driverProtection;  final bool isMandatory;

  static GuaranteeType fromString(String value) {
    return GuaranteeType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => GuaranteeType.liability,
    );
  }
}

/// 📋 Statuts des sinistres
enum ClaimStatus {
  draft,
  submitted,
  underReview,
  expertiseRequired,
  expertiseInProgress,
  approved,
  rejected,
  closed;  static ClaimStatus fromString(String value) {
    return ClaimStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => ClaimStatus.draft,
    );
  }
}

/// 📄 Types de documents
enum DocumentType {
  contract,
  attestation,
  expertReport,
  claimDeclaration,
  idCard,
  drivingLicense,
  vehicleRegistration,
  invoice,
  photo;  static DocumentType fromString(String value) {
    return DocumentType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => DocumentType.photo,
    );
  }
}

/// 🔔 Types de notifications
enum NotificationType {
  claimCreated,
  claimUpdated,
  contractExpiring,
  documentReady,
  expertAssigned,
  messageReceived,
  accountValidated,
  accountRejected;  static NotificationType fromString(String value) {
    return NotificationType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => NotificationType.messageReceived,
    ');
  }
}

/// 🏛️ Types d'agences
enum AgencyType {
  headquarters,
  regional,
  local,
  branch;  static AgencyType fromString(String value) {
    return AgencyType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => AgencyType.local,
    );
  }
}

/// 🔑 Permissions systeme
enum Permission {
  // Gestion des contrats
  createContract,
  editContract,
  deleteContract,
  viewAllContracts,
  
  // Gestion des utilisateurs
  manageAgents,
  manageClients,
  manageExperts,
  validateAccounts,
  
  // Gestion des sinistres
  processClaimsLevel1,
  processClaimsLevel2,
  assignExperts,
  
  // Rapports et statistiques
  generateReports,
  viewStatistics,
  exportData,
  
  // Administration systeme
  manageCompanies,
  manageAgencies,
  systemConfiguration;  final String description;

  static Permission fromString(String value) {
    return Permission.values.firstWhere(
      (permission) => permission.value == value,
      orElse: () => Permission.createContract,
    );
  }
}

/// 🌍 Gouvernorats de Tunisie
enum Governorate {
  tunis,
  ariana,
  benArous,
  manouba,
  nabeul,
  zaghouan,
  bizerte,
  beja,
  jendouba,
  kef,
  siliana,
  sousse,
  monastir,
  mahdia,
  sfax,
  kairouan,
  kasserine,
  sidiBouzid,
  gabes,
  medenine,
  tataouine,
  gafsa,
  tozeur,
  kebili
