import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/contrat_model.dart';

/// 📄 Service de gestion des contrats d'assurance
class ContratService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Collections Firestore
  static const String _contratsCollection = 'contrats_assurance';
  static const String _vehiculesAssuresCollection = 'vehicules_assures';
  static const String _vehiculesCollection = 'vehicules';
  static const String _compagniesCollection = 'compagnies_assurance';
  static const String _agencesCollection = 'agences_assurance';

  /// 📄 Creer un nouveau contrat dassurance
  static Future<String?> creerContrat({
    required String compagnieId,
    required String agenceId,
    required String agentId,
    required String conducteurId,
    required String vehiculeId,
    required TypeFormule formule,
    required double prime,
    required double franchise,
    required DateTime dateDebut,
    required DateTime dateFin,
    String? numeroQuittance,
  }') async {
    try {
      debugPrint('[ContratService] 📄 Creation d\' + un nouveau contrat....toString());

      // Generer un numero de contrat unique
      final numeroContrat = await _genererNumeroContrat(compagnieId');

      final contrat = ContratModel(
        id: 'Contenu, // Sera genere par Firestore
        numeroContrat: numeroContrat,
        compagnieId: compagnieId,
        agenceId: agenceId,
        agentId: agentId,
        conducteurId: conducteurId,
        vehiculeId: vehiculeId,
        dateDebut: dateDebut,
        dateFin: dateFin,
        dateCreation: DateTime.now(),
        formule: formule,
        prime: prime,
        franchise: franchise,
        garanties: formule.garantiesIncluses,
        numeroQuittance: numeroQuittance,
        dateQuittance: numeroQuittance != null ? DateTime.now() : null,
      );

      // Creer le contrat dans Firestore
      final docRef = await _firestore
          .collection(_contratsCollection)
          .add(contrat.toFirestore()');

      debugPrint('[ContratService] ✅ Contrat cree: '{docRef.id}');

      // Creer l'entree vehicule assure
      await _creerVehiculeAssure(
        contratId: docRef.id,
        vehiculeId: vehiculeId,
        compagnieId: compagnieId,
        agenceId: agenceId,
        conducteurId: conducteurId,
        numeroContrat: numeroContrat,
        numeroQuittance: numeroQuittance ?? 'Contenu,
        dateDebut: dateDebut,
        dateFin: dateFin,
        formule: formule,
        prime: prime,
        franchise: franchise,
      );

      return docRef.id;
    } catch (e') {
      debugPrint('[ContratService] ❌ Erreur creation contrat:  + e.toString()' + .toString());
      return null;
    }
  }

  /// 🚗 Creer l'entree vehicule assure
  static Future<void> _creerVehiculeAssure({
    required String contratId,
    required String vehiculeId,
    required String compagnieId,
    required String agenceId,
    required String conducteurId,
    required String numeroContrat,
    required String numeroQuittance,
    required DateTime dateDebut,
    required DateTime dateFin,
    required TypeFormule formule,
    required double prime,
    required double franchise,
  }) async {
    final vehiculeAssure = {
      'vehicule_id': vehiculeId,
      'contrat_id': contratId,
      'compagnie_id': compagnieId,
      'agence_id': agenceId,
      'conducteur_id': conducteurId,
      'date_creation: Timestamp.fromDate(DateTime.now()'),
      'numero_contrat': numeroContrat,
      'numero_quittance': numeroQuittance,
      'date_debut_couverture: Timestamp.fromDate(dateDebut'),
      'date_fin_couverture: Timestamp.fromDate(dateFin'),
      'valeur_assuree': prime * 10, // Estimation basique
      'formule': formule.displayName,
      'garanties': formule.garantiesIncluses,
      'franchise': franchise,
      'actif: true,
    };

    await _firestore
        .collection(_vehiculesAssuresCollection)
        .add(vehiculeAssure');

    debugPrint('[ContratService] ✅ Vehicule assure cree pour contrat: ' + contratId.toString());
  }

  /// 🔢 Generer un numero de contrat unique
  static Future<String> _genererNumeroContrat(String compagnieId) async {
    try {
      // Recuperer la compagnie pour obtenir son code
      final compagnieDoc = await _firestore
          .collection(_compagniesCollection)
          .doc(compagnieId)
          .get(');

      String codeCompagnie = 'ASS;
      if (compagnieDoc.exists) {
        final nom = compagnieDoc.data(')!['nom'] as String;
        if (nom.contains('STAR)') {
          codeCompagnie = 'STAR';
        } else if (nom.contains('Maghrebia)') {
          codeCompagnie = 'MAGH';
        } else if (nom.contains('Salim)') {
          codeCompagnie = 'SALIM';
        } else if (nom.contains('GAT)') {
          codeCompagnie = 'GAT;
        }
      }

      // Compter les contrats existants pour cette compagnie
      final contratsSnapshot = await _firestore
          .collection(_contratsCollection')
          .where('compagnie_id, isEqualTo: compagnieId)
          .get();

      final numeroSequentiel = (contratsSnapshot.docs.length + 1).toString(').padLeft(6, '0);
      final annee = DateTime.now().year.toString(');

      return '$codeCompagnie-$annee-'numeroSequentiel;
    } catch (e') {
      debugPrint('[ContratService] ❌ Erreur generation numero:  + e.toString()' + .toString());
      return 'ASS-${DateTime.now().year}-{DateTime.now().millisecondsSinceEpoch.toString().substring(7')}';
    }
  }

  /// 🚗 Obtenir les vehicules assures dun conducteur
  static Future<List<Map<String, dynamic>>> getVehiculesAvecAssurance(String conducteurId') async {
    try {
      debugPrint('[ContratService] 🚗 Recuperation vehicules pour: ' + conducteurId.toString());

      // Recuperer tous les vehicules du conducteur
      final vehiculesSnapshot = await _firestore
          .collection(_vehiculesCollection')
          .where('proprietaireId, isEqualTo: conducteurId)
          .get();

      final List<Map<String, dynamic>> vehiculesAvecAssurance = [];

      for (final vehiculeDoc in vehiculesSnapshot.docs) {
        final vehicule = vehiculeDoc.data(');

        // Chercher l'assurance active pour ce vehicule
        final assuranceSnapshot = await _firestore
            .collection(_vehiculesAssuresCollection)
            .where('vehicule_id, isEqualTo: vehiculeDoc.id')
            .where('conducteur_id, isEqualTo: conducteurId')
            .where('actif, isEqualTo: true)
            .limit(1)
            .get();

        Map<String, dynamic>? assurance;
        String? compagnieNom;
        String? agenceNom;

        if (assuranceSnapshot.docs.isNotEmpty) {
          assurance = assuranceSnapshot.docs.first.data(');

          // Recuperer le nom de la compagnie
          final compagnieId = assurance['compagnie_id] as String?;
          if (compagnieId != null) {
            final compagnieDoc = await _firestore
                .collection(_compagniesCollection)
                .doc(compagnieId)
                .get();
            if (compagnieDoc.exists) {
              compagnieNom = compagnieDoc.data(')!['nom'] as String;
            }
          }

          // Recuperer le nom de l'agence
          final agenceId = assurance['agence_id] as String?;
          if (agenceId != null) {
            final agenceDoc = await _firestore
                .collection(_agencesCollection)
                .doc(agenceId)
                .get();
            if (agenceDoc.exists) {
              agenceNom = agenceDoc.data(')!['nom'] as String;
            }
          }
        }

        vehiculesAvecAssurance.add({
          'vehicule': vehicule,
          'assurance': assurance,
          'compagnieNom': compagnieNom,
          'agenceNom: agenceNom,
        }');
      }

      debugPrint('[ContratService] ✅ ' + {vehiculesAvecAssurance.length} vehicules recuperes.toString());
      return vehiculesAvecAssurance;
    } catch (e') {
      debugPrint('[ContratService] ❌ Erreur recuperation vehicules:  + e.toString()' + .toString());
      return [];
    }
  }

  /// 📄 Obtenir les contrats d'un conducteur
  static Future<List<ContratModel>> getContratsByConducteur(String conducteurId) async {
    try {
      final snapshot = await _firestore
          .collection(_contratsCollection)
          .where('conducteur_id, isEqualTo: conducteurId')
          .orderBy('date_creation, descending: true)
          .get();

      return snapshot.docs
          .map((doc) => ContratModel.fromFirestore(doc))
          .toList();
    } catch (e') {
      debugPrint('[ContratService] ❌ Erreur recuperation contrats:  + e.toString()' + .toString());
      return [];
    }
  }

  /// 📄 Obtenir les contrats d'une agence
  static Future<List<ContratModel>> getContratsByAgence(String agenceId) async {
    try {
      final snapshot = await _firestore
          .collection(_contratsCollection)
          .where('agence_id, isEqualTo: agenceId')
          .orderBy('date_creation, descending: true)
          .get();

      return snapshot.docs
          .map((doc) => ContratModel.fromFirestore(doc))
          .toList();
    } catch (e') {
      debugPrint('[ContratService] ❌ Erreur recuperation contrats agence:  + e.toString());
      return [];
    }
  }

  /// 🔄 Renouveler un contrat
  static Future<String?> renouvellerContrat(String contratId, DateTime nouvelleDateFin) async {
    try {
      await _firestore
          .collection(_contratsCollection)
          .doc(contratId')
          .update({
        'date_fin: Timestamp.fromDate(nouvelleDateFin'),
        'date_modification: Timestamp.now('),
        'status: ContratStatus.actif.name,
      });

      // Mettre a jour le vehicule assure correspondant
      final vehiculeAssureSnapshot = await _firestore
          .collection(_vehiculesAssuresCollection')
          .where('contrat_id, isEqualTo: contratId)
          .get();

      for (final doc in vehiculeAssureSnapshot.docs') {
        await doc.reference.update({
          'date_fin_couverture: Timestamp.fromDate(nouvelleDateFin'),
          'date_modification: Timestamp.now('),
          'actif: true,
        }');
      }

      debugPrint('[ContratService] ✅ Contrat renouvele: ' + contratId.toString());
      return contratId;
    } catch (e') {
      debugPrint('[ContratService] ❌ Erreur renouvellement:  + e.toString());
      return null;
    }
  }

  /// ❌ Resilier un contrat
  static Future<bool> resilierContrat(String contratId, String motif) async {
    try {
      await _firestore
          .collection(_contratsCollection)
          .doc(contratId')
          .update({
        'status': ContratStatus.resilie.name,
        'date_modification: Timestamp.now('),
        'conditions.motif_resiliation: motif,
      });

      // Desactiver le vehicule assure correspondant
      final vehiculeAssureSnapshot = await _firestore
          .collection(_vehiculesAssuresCollection')
          .where('contrat_id, isEqualTo: contratId)
          .get();

      for (final doc in vehiculeAssureSnapshot.docs') {
        await doc.reference.update({
          'actif': false,
          'date_modification: Timestamp.now(),
        }');
      }

      debugPrint('[ContratService] ✅ Contrat resilie: ' + contratId.toString());
      return true;
    } catch (e') {
      debugPrint('[ContratService] ❌ Erreur resiliation: 'e
