import 'package:flutter/material.dart';
import '../../../../core/theme/modern_theme.dart';
import '../../models/professional_request_model_final.dart';

/// 📝 Widget carte pour afficher une demande professionnelle
class RequestCard extends StatelessWidget {
  final ProfessionalRequestModel request;
  final VoidCallback? onApprove;
  final VoidCallback? onReject;
  final VoidCallback? onTap;

  ;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: ,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
        child: (1),
              
              ,
              
              // Informations principales
              _buildMainInfo(),
              
              ,
              
              // Informations secondaires
              _buildSecondaryInfo(),
              
              if (request.estEnAttente) ...[
                ,
                // Boutons d'action
                _buildActionButtons(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// 🔝 En-tête avec nom et statut
  Widget _buildHeader() {
    return Row(
      children: [
        // Avatar avec initiales
        CircleAvatar(
          radius: 24,
          backgroundColor: _getStatusColor().withValues(alpha: 0.1),
          child: ({request.nom[0]}',
            style: TextStyle(
              color: _getStatusColor(),
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ),
        
        ,
        
        // Nom et type
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ,
              ),
              const SizedBox(height: 2),
              ,
              ),
            ],
          ),
        ),
        
        // Badge de statut
        _buildStatusBadge(),
      ],
    );
  }

  /// 📋 Informations principales
  Widget _buildMainInfo() {
    return Column(
      children: [
        _buildInfoRow(
          icon: Icons.email,
          label: 'Email',
          value: request.email,
        ),
        ,
        _buildInfoRow(
          icon: Icons.phone,
          label: 'Téléphone',
          value: request.telephone,
        ),
      ],
    );
  }

  /// 📋 Informations secondaires
  Widget _buildSecondaryInfo() {
    return Column(
      children: [
        if (request.compagnieAssurance.isNotEmpty)
          _buildInfoRow(
            icon: Icons.business,
            label: 'Compagnie',
            value: request.compagnieAssurance,
          ),
        if (request.agence.isNotEmpty) ...[
          ,
          _buildInfoRow(
            icon: Icons.store,
            label: 'Agence',
            value: request.agence,
          ),
        ],
        if (request.zoneIntervention != null && request.zoneIntervention!.isNotEmpty) ...[
          ,
          _buildInfoRow(
            icon: Icons.location_on,
            label: 'Zone',
            value: request.zoneIntervention!,
          ),
        ],
        ,
        _buildInfoRow(
          icon: Icons.calendar_today,
          label: 'Date de demande',
          value: _formatDate(request.envoyeLe),
        ),
      ],
    );
  }

  /// 📝 Ligne d'information
  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        ,
        ,
        (label: ',
          style: ModernTheme.bodySmall.copyWith(
            color: ModernTheme.textLight,
            fontWeight: FontWeight.w500,
          ),
        ),
        Expanded(
          child: ,
          ),
        ),
      ],
    );
  }

  /// 🏷️ Badge de statut
  Widget _buildStatusBadge() {
    return Container(
      padding: .withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
        border: Border.all(
          color: _getStatusColor().withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          (1),
            size: 12,
            color: _getStatusColor(),
          ),
          const SizedBox(width: 4),
          ,
              fontWeight: FontWeight.w600,
              fontSize: 11,
            ),
          ),
        ],
      ),
    );
  }

  /// 🎯 Boutons d'action
  Widget _buildActionButtons() {
    return Row(
      children: [
        // Bouton Rejeter
        Expanded(
          child: OutlinedButton.const Icon(
            onPressed: onReject,
            icon: const Icon(Icons.info),
            label: const Text('Rejeter'),
            style: OutlinedButton.styleFrom(
              foregroundColor: ModernTheme.errorColor,
              side: ,
              padding: ,
          ),
        ),
        
        ,
        
        // Bouton Approuver
        Expanded(
          child: ElevatedButton.const Icon(
            onPressed: onApprove,
            icon: const Icon(Icons.info),
            label: const Text('Approuver'),
            style: ElevatedButton.styleFrom(
              backgroundColor: ModernTheme.successColor,
              foregroundColor: Colors.white,
              padding: ,
          ),
        ),
      ],
    );
  }

  /// 🎨 Couleur du statut
  Color _getStatusColor() {
    switch (request.statut) {
      case 'en_attente':
        return ModernTheme.warningColor;
      case 'approuvee':
        return ModernTheme.successColor;
      case 'rejetee':
        return ModernTheme.errorColor;
      default:
        return ModernTheme.textLight;
    }
  }

  /// 🎯 Icône du statut
  IconData _getStatus {
    switch (request.statut) {
      case 'en_attente':
        return Icons.pending_actions;
      case 'approuvee':
        return Icons.check_circle;
      case 'rejetee':
        return Icons.cancel;
      default:
        return Icons.help;
    }
  }

  /// 📅 Formater la date
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}
