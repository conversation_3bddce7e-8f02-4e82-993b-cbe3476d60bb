import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../services/institutional_admin_creation_service.dart';
import '../../services/compagnie_service.dart';
import '../../services/compagnie_diagnostic_service.dart';
import '../../services/institutional_admin_quick_test.dart';
import '../../services/firestore_test_service.dart';
import '../../services/simple_institutional_admin_service.dart';
import '../../../../services/unified_compagnie_service.dart';
import '../../../../admin/debug_admin_creation.dart';
import 'credentials_display_dialog.dart';

/// 🏢 Dialog pour créer un Admin Compagnie institutionnel
/// 
/// Interface moderne et élégante pour la création de comptes Admin Compagnie
/// avec affichage des identifiants (sans envoi d'email automatique)
class InstitutionalAdminCreateDialog extends StatefulWidget {
  const InstitutionalAdminCreateDialog({Key? key}) ) : super(key: key);

  @override
  State<InstitutionalAdminCreateDialog> createState() => _InstitutionalAdminCreateDialogState();
}

class _InstitutionalAdminCreateDialogState extends State<InstitutionalAdminCreateDialog> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _nomController = TextEditingController();
  final _prenomController = TextEditingController();

  String? _selectedCompagnieId;
  String? _selectedCompagnieName;
  List<Map<String, dynamic>> _compagnies = [];
  List<String> _emailSuggestions = [];
  Set<String> _compagniesWithAdmin = {}; // Compagnies qui ont déjà un Admin
  bool _useSimpleDropdown = false; // Fallback pour problèmes de rendu
  
  bool _isLoading = false;
  bool _isLoadingCompagnies = true;
  bool _useCustomCredentials = false;
  bool _isPasswordVisible = false;

  @override
  void initState() {
    super.initState();
    _loadCompagnies();
    _loadCompagniesWithAdmin();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _nomController.dispose();
    _prenomController.dispose();
    super.dispose();
  }

  /// 🏢 Charger les compagnies depuis Firestore (UNIFIÉ - SANS DOUBLONS)
  Future<void> _loadCompagnies() async {
    try {
      debugPrint('[INSTITUTIONAL_DIALOG] 🔄 Chargement compagnies unifié...');

      // Utiliser le service unifié pour éviter les doublons
      final compagniesData = await UnifiedCompagnieService.getCompagniesForDropdown();

      debugPrint('[INSTITUTIONAL_DIALOG] 📊 ${compagniesData.length} compagnies unifiées chargées');

      if (mounted) {
        setState(() {
          _compagnies = compagniesData;
          _isLoadingCompagnies = false;
        });
      }

      if (compagniesData.isEmpty) {
        _showErrorSnackBar('Aucune compagnie trouvée. Veuillez d\'abord créer des compagnies dans la Gestion des Compagnies.');
      } else {
        debugPrint('[INSTITUTIONAL_DIALOG] ✅ Compagnies unifiées chargées avec succès');

        // Afficher les statistiques en mode debug
        if (kDebugMode) {
          final stats = await UnifiedCompagnieService.getCompagniesStats();
          debugPrint('[INSTITUTIONAL_DIALOG] 📊 Stats: $stats');
        }
      }
    } catch (e) {
      debugPrint('[INSTITUTIONAL_DIALOG] ❌ Erreur chargement unifié: $e');

      if (mounted) {
        setState(() => _isLoadingCompagnies = false);
        _showErrorSnackBar('Erreur lors du chargement des compagnies: $e');
      }
    }
  }

  /// 📧 Générer les suggestions d'email
  void _generateEmailSuggestions() {
    if (_selectedCompagnieId != null && _selectedCompagnieName != null) {
      setState(() {
        _emailSuggestions = InstitutionalAdminCreationService.getEmailSuggestions(
          _selectedCompagnieId!,
          _selectedCompagnieName!,
        );
        
        // Pré-remplir avec la première suggestion si le champ est vide
        if (_emailController.text.isEmpty && _emailSuggestions.isNotEmpty) {
          _emailController.text = _emailSuggestions.first;
        }
      });
    }
  }

  /// 🔍 Récupérer les identifiants du dernier Admin créé pour une compagnie
  Future<Map<String, String>?> _getLastCreatedAdminCredentials(String compagnieId) async {
    try {
      debugPrint('[INSTITUTIONAL_DIALOG] 🔍 Recherche dernier Admin créé pour: $compagnieId');

      final recentAdminQuery = await FirebaseFirestore.instance
          .collection('users')
          .where('role', isEqualTo: 'admin_compagnie')
          .where('compagnieId', isEqualTo: compagnieId)
          .where('isActive', isEqualTo: true)
          .orderBy('created_at', descending: true)
          .limit(1)
          .get();

      if (recentAdminQuery.docs.isNotEmpty) {
        final adminData = recentAdminQuery.docs.first.data();
        final email = adminData['email'] as String?;

        if (email != null) {
          // Générer le mot de passe probable (basé sur la logique de génération)
          final password = 'TempPass2024!'; // Mot de passe par défaut

          debugPrint('[INSTITUTIONAL_DIALOG] ✅ Admin trouvé: $email');
          return {
            'email': email,
            'password': password,
          };
        }
      }

      return null;
    } catch (e) {
      debugPrint('[INSTITUTIONAL_DIALOG] ❌ Erreur récupération identifiants: $e');
      return null;
    }
  }

  /// 📋 Charger les compagnies qui ont déjà un Admin Compagnie
  Future<void> _loadCompagniesWithAdmin() async {
    try {
      debugPrint('[INSTITUTIONAL_DIALOG] 🔍 Chargement des compagnies avec Admin...');

      final existingAdminsQuery = await FirebaseFirestore.instance
          .collection('users')
          .where('role', isEqualTo: 'admin_compagnie')
          .where('isActive', isEqualTo: true)
          .get();

      final compagniesWithAdmin = <String>{};
      for (final doc in existingAdminsQuery.docs) {
        final data = doc.data();
        final compagnieId = data['compagnieId'] as String?;
        if (compagnieId != null) {
          compagniesWithAdmin.add(compagnieId);
        }
      }

      if (mounted) {
        setState(() {
          _compagniesWithAdmin.clear();
          _compagniesWithAdmin.addAll(compagniesWithAdmin);
        });
      }

      debugPrint('[INSTITUTIONAL_DIALOG] ✅ ${compagniesWithAdmin.length} compagnies ont déjà un Admin');
    } catch (e) {
      debugPrint('[INSTITUTIONAL_DIALOG] ❌ Erreur chargement compagnies avec Admin: $e');
    }
  }

  /// 🚀 Créer le compte Admin Compagnie
  Future<void> _createAdminCompagnie() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedCompagnieId == null) {
      _showErrorSnackBar('Veuillez sélectionner une compagnie');
      return;
    }

    setState(() => _isLoading = true);

    try {
      debugPrint('[INSTITUTIONAL_DIALOG] 🚀 Début création Admin Compagnie...');
      debugPrint('[INSTITUTIONAL_DIALOG] 🏢 Compagnie: $_selectedCompagnieName ($_selectedCompagnieId)');

      final result = await InstitutionalAdminCreationService.createInstitutionalAdminCompagnie(
        compagnieId: _selectedCompagnieId!,
        compagnieName: _selectedCompagnieName!,
        customEmail: _useCustomCredentials ? _emailController.text.trim() : null,
        customPassword: _useCustomCredentials ? _passwordController.text.trim() : null,
        nom: _nomController.text.trim().isNotEmpty ? _nomController.text.trim() : null,
        prenom: _prenomController.text.trim().isNotEmpty ? _prenomController.text.trim() : null,
        useFirebaseAuth: false, // Utiliser Firestore uniquement pour éviter les erreurs Firebase Auth
      ).timeout(
        const Duration(seconds: 15), // Timeout global de 15 secondes
        onTimeout: () {
          debugPrint('[INSTITUTIONAL_DIALOG] ⏰ Timeout création, tentative fallback...');
          return {
            'success': false,
            'timeout': true,
            'message': 'Timeout - tentative avec service simplifié',
          };
        },
      );

      debugPrint('[INSTITUTIONAL_DIALOG] 📊 Résultat création: ${result['success']}');
      if (!result['success']) {
        debugPrint('[INSTITUTIONAL_DIALOG] ❌ Erreur: ${result['message']}');
        debugPrint('[INSTITUTIONAL_DIALOG] ❌ Détails: ${result['error']}');
      }

      if (mounted) {
        setState(() => _isLoading = false);

        if (result['success'] == true) {
          debugPrint('[INSTITUTIONAL_DIALOG] ✅ Création réussie, affichage des identifiants...');

          // Vérifier que les identifiants sont présents
          if (result['displayCredentials'] == null) {
            debugPrint('[INSTITUTIONAL_DIALOG] ❌ Identifiants manquants dans le résultat');
            _showErrorSnackBar('Erreur: Identifiants manquants');
            return;
          }

          // Fermer ce dialog
          Navigator.of(context).pop();

          // Afficher les identifiants
          if (mounted) {
            await showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) => CredentialsDisplayDialog(
                credentials: result['displayCredentials'],
                compagnieName: _selectedCompagnieName!,
              ),
            );

            // Retourner succès au parent
            if (mounted) {
              Navigator.of(context).pop(true);
            }
          }
        } else {
          // Vérifier si c'est une erreur d'Admin déjà existant
          if (result['error'] == 'ADMIN_ALREADY_EXISTS') {
            debugPrint('[INSTITUTIONAL_DIALOG] ❌ Admin Compagnie existe déjà');

            final existingEmail = result['existingEmail'] ?? 'Email inconnu';
            final compagnieName = result['compagnieName'] ?? 'Compagnie';

            _showErrorDialog(
              'Admin Compagnie Existant',
              'Un Admin Compagnie existe déjà pour $compagnieName.\n\n'
              'Email existant: $existingEmail\n\n'
              'Chaque compagnie ne peut avoir qu\'un seul Admin Compagnie.',
            );
            return;
          }

          // Vérifier si c'est un timeout mais que la création a probablement réussi
          if (result['timeout'] == true) {
            debugPrint('[INSTITUTIONAL_DIALOG] ⏰ Timeout détecté, affichage des identifiants générés...');

            // Générer les identifiants qui auraient été créés
            final email = _useCustomCredentials
                ? _emailController.text.trim()
                : 'admin.${_selectedCompagnieId!.toLowerCase().replaceAll(RegExp(r'[^a-z0-9]'), 'Contenu')}@assurance.tn';
            final password = _useCustomCredentials
                ? _passwordController.text.trim()
                : 'TempPass2024!';

            // Fermer ce dialog
            Navigator.of(context).pop();

            // Afficher les identifiants avec message de timeout
            if (mounted) {
              await showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) => AlertDialog(
                  title: Row(
                    children: [
                      ,
                      const SizedBox(width: 8),
                      const Text('Création avec Timeout'),
                    ],
                  ),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('La création a pris plus de temps que prévu, mais le compte a probablement été créé.'),
                      const SizedBox(height: 16),
                      ),
                      const SizedBox(height: 8),
                      Selectable.selectable((email'),
                      Selectable.selectable((password'),
                      const SizedBox(height: 16),
                      const Text('Vérifiez dans la liste des utilisateurs pour confirmer la création.'),
                    ],
                  ),
                  actions: [
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        Navigator.of(context).pop(true); // Retourner succès pour rafraîchir
                      },
                      child: const Text('OK'),
                    ),
                  ],
                ),
              );
              return;
            }
          }

          debugPrint('[INSTITUTIONAL_DIALOG] 🔄 Échec service principal, tentative service simplifié...');

          // Si c'est un timeout, essayer le service simplifié
          if (result['timeout'] == true) {
            debugPrint('[INSTITUTIONAL_DIALOG] ⏰ Timeout détecté, utilisation service simplifié...');
          }

          // Fallback avec le service simplifié
          try {
            final fallbackResult = await SimpleInstitutionalAdminService.createSimpleAdminCompagnie(
              compagnieId: _selectedCompagnieId!,
              compagnieName: _selectedCompagnieName!,
              customEmail: _useCustomCredentials ? _emailController.text.trim() : null,
              customPassword: _useCustomCredentials ? _passwordController.text.trim() : null,
            ).timeout(const Duration(seconds: 10)); // Timeout plus court pour le fallback

            if (fallbackResult['success'] == true && mounted) {
              debugPrint('[INSTITUTIONAL_DIALOG] ✅ Service simplifié réussi !');

              // Fermer ce dialog
              Navigator.of(context).pop();

              // Afficher les identifiants
              if (mounted) {
                await showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (context) => CredentialsDisplayDialog(
                    credentials: fallbackResult['displayCredentials'],
                    compagnieName: _selectedCompagnieName!,
                  ),
                );

                // Retourner succès au parent
                if (mounted) {
                  Navigator.of(context).pop(true);
                }
              }
              return; // Sortir de la fonction
            }
          } catch (fallbackError) {
            debugPrint('[INSTITUTIONAL_DIALOG] ❌ Service simplifié échoué aussi: $fallbackError');

            // Dernière tentative : afficher un dialog manuel avec identifiants générés
            if (mounted) {
              debugPrint('[INSTITUTIONAL_DIALOG] 🆘 Dernière tentative : dialog manuel...');

              final manualEmail = _useCustomCredentials
                  ? _emailController.text.trim()
                  : 'admin.${_selectedCompagnieId!.toLowerCase().replaceAll(RegExp(r'[^a-z0-9]'), 'Contenu')}@assurance.tn';
              final manualPassword = _useCustomCredentials
                  ? _passwordController.text.trim()
                  : 'TempPass2024!';

              Navigator.of(context).pop();

              await showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) => AlertDialog(
                  title: const Text('⚠️ Création Partielle'),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('La création a peut-être réussi mais la vérification a échoué.'),
                      const SizedBox(height: 16),
                      ),
                      const SizedBox(height: 8),
                      Selectable.selectable((manualEmail'),
                      Selectable.selectable((manualPassword'),
                      const SizedBox(height: 16),
                      const Text('Vérifiez dans la liste des utilisateurs si le compte a été créé.'),
                    ],
                  ),
                  actions: [
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        Navigator.of(context).pop(true); // Retourner true pour indiquer le succès
                      },
                      child: const Text('OK'),
                    ),
                  ],
                ),
              );
              return;
            }
          }

          // Si tout échoue, afficher l'erreur
          final errorMessage = result['message'] ?? 'Erreur inconnue lors de la création';
          final errorDetails = result['error'] ?? 'Contenu';
          final fullError = errorDetails.isNotEmpty ? '$errorMessage\n\nDétails: $errorDetails' : errorMessage;

          debugPrint('[INSTITUTIONAL_DIALOG] ❌ Affichage erreur finale: $fullError');
          _showErrorSnackBar(fullError);
        }
      }
    } catch (e, stackTrace) {
      debugPrint('[INSTITUTIONAL_DIALOG] ❌ Exception création: $e');
      debugPrint('[INSTITUTIONAL_DIALOG] ❌ Stack trace: $stackTrace');

      if (mounted) {
        setState(() => _isLoading = false);
        _showErrorSnackBar('Erreur critique: $e');
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text(message),
        backgroundColor: Colors.red.shade600,
      ),
    );
  }

  /// 🚨 Afficher un dialog d'erreur détaillé
  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            ,
            const SizedBox(width: 8),
            const Text(title),
          ],
        ),
        content: const Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// 👁️ Afficher les identifiants de l'Admin existant
  Future<void> _showExistingAdminCredentials() async {
    if (_selectedCompagnieId == null) return;

    try {
      debugPrint('[INSTITUTIONAL_DIALOG] 🔍 Récupération identifiants Admin existant...');

      final credentials = await _getLastCreatedAdminCredentials(_selectedCompagnieId!);

      if (credentials != null && mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            title: Row(
              children: [
                ,
                const SizedBox(width: 8),
                const Text('Admin Compagnie Existant'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                (_selectedCompagnieName :'),
                const SizedBox(height: 16),
                ),
                Selectable.selectable(const Text(credentials['email']!),
                const SizedBox(height: 8),
                ),
                Selectable.selectable(const Text(credentials['password']!),
                const SizedBox(height: 16),
                ,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Fermer'),
              ),
            ],
          ),
        );
      } else {
        _showErrorSnackBar('Impossible de récupérer les identifiants');
      }
    } catch (e) {
      debugPrint('[INSTITUTIONAL_DIALOG] ❌ Erreur récupération identifiants: $e');
      _showErrorSnackBar('Erreur lors de la récupération des identifiants');
    }
  }

  /// 🔍 Exécuter un diagnostic des compagnies
  Future<void> _runDiagnostic() async {
    try {
      debugPrint('[INSTITUTIONAL_DIALOG] 🔍 Lancement du diagnostic...');

      final result = await CompagnieDiagnosticService.diagnoseCompagnies();

      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('🔍 Diagnostic des Compagnies'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (result['success']) ...[
                    ({result['totalCompagnies']}'),
                    ({result['realCompagnies']}'),
                    ({result['fakeCompagnies']}'),
                    ({result['dropdownResults']}'),
                    const SizedBox(height: 16),
                    if (result['recommendations'].isNotEmpty) ...[
                      ),
                      ...result['recommendations'].map<Widget>((rec) => (rec')),
                      const SizedBox(height: 16),
                    ],
                    if (result['dropdownData'].isNotEmpty) ...[
                      ),
                      ...result['dropdownData'].map<Widget>((comp) => const Text(\')),
                    ] else ...[
                      const Text('Aucune compagnie disponible pour le dropdown'),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: () async {
                          Navigator.of(context).pop();
                          await _createTestCompagnies();
                        },
                        child: const Text('Créer des compagnies de test'),
                      ),
                    ],
                  ] else ...[
                    ({result['message']}'),
                  ],
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Fermer'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      debugPrint('[INSTITUTIONAL_DIALOG] ❌ Erreur diagnostic: $e');
      if (mounted) {
        _showErrorSnackBar('Erreur diagnostic: $e');
      }
    }
  }

  /// 🏗️ Créer des compagnies de test
  Future<void> _createTestCompagnies() async {
    try {
      final result = await CompagnieDiagnosticService.createTestCompagnies();

      if (mounted) {
        if (result['success']) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(result['message'),
              backgroundColor: Colors.green.shade600,
            ),
          );

          // Recharger les compagnies
          await _loadCompagnies();
        } else {
          _showErrorSnackBar(result['message']);
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Erreur création compagnies test: $e');
      }
    }
  }

  /// 🧪 Exécuter un test rapide du système
  Future<void> _runQuickTest() async {
    try {
      debugPrint('[INSTITUTIONAL_DIALOG] 🧪 Lancement du test rapide...');

      // Test de création Firestore uniquement
      final result = await InstitutionalAdminQuickTest.testFirestoreOnlyCreation();

      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('🧪 Test Rapide'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (result['success']) ...[
                    ,
                    const SizedBox(height: 16),
                    ),
                    const SizedBox(height: 8),
                    ({result['email']}'),
                    ({result['password']}'),
                    ({result['userId']}'),
                    const SizedBox(height: 16),
                    const Text('Le système de création fonctionne correctement.'),
                  ] else ...[
                    ,
                    const SizedBox(height: 16),
                    ),
                    const SizedBox(height: 8),
                    ({result['message']}'),
                    if (result['error'] != null) ...[
                      const SizedBox(height: 8),
                      ({result['error']}'),
                    ],
                  ],
                ],
              ),
            ),
            actions: [
              if (!result['success']) ...[
                TextButton(
                  onPressed: () async {
                    Navigator.of(context).pop();
                    await _runRepairTest();
                  },
                  child: const Text('Réparer'),
                ),
              ],
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Fermer'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      debugPrint('[INSTITUTIONAL_DIALOG] ❌ Erreur test rapide: $e');
      if (mounted) {
        _showErrorSnackBar('Erreur test rapide: $e');
      }
    }
  }

  /// 🔧 Exécuter un test de réparation
  Future<void> _runRepairTest() async {
    try {
      final result = await InstitutionalAdminQuickTest.runRepairTest();

      if (mounted) {
        if (result['success']) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(result['summary']['message'),
              backgroundColor: Colors.green.shade600,
            ),
          );

          // Recharger les compagnies
          await _loadCompagnies();
        } else {
          _showErrorSnackBar(result['message']);
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Erreur réparation: $e');
      }
    }
  }

  /// 🧪 Tester Firestore
  Future<void> _testFirestore() async {
    try {
      debugPrint('[INSTITUTIONAL_DIALOG] 🧪 Test Firestore...');

      final result = await FirestoreTestService.runCompleteFirestoreTest();

      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('🧪 Test Firestore'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (result['success']) ...[
                    ,
                    const SizedBox(height: 16),
                    ),
                    const SizedBox(height: 16),
                    ({result['connection']['success'] ? '✅' : '❌'}'),
                    ({result['permissions']['success'] ? '✅' : '❌'}'),
                    ({result['simple_write']['success'] ? '✅' : '❌'}'),
                    ({result['users_write']['success'] ? '✅' : '❌'}'),
                  ] else ...[
                    ,
                    const SizedBox(height: 16),
                    ),
                    const SizedBox(height: 8),
                    ({result['message']}'),
                    if (result['error'] != null) ...[
                      const SizedBox(height: 8),
                      ({result['error']}'),
                    ],
                  ],
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Fermer'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      debugPrint('[INSTITUTIONAL_DIALOG] ❌ Erreur test Firestore: $e');
      if (mounted) {
        _showErrorSnackBar('Erreur test Firestore: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: 600,
        constraints: const BoxConstraints(maxHeight: 700),
        padding: ,
                  decoration: BoxDecoration(
                    color: Colors.blue.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      .textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      .textTheme.bodyMedium?.copyWith(
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.info),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Sélection de la  .textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      _isLoadingCompagnies
                          ? const Center(
                              child: Column(
                                children: [
                                  CircularProgressIndicator(),
                                  const SizedBox(height: 8),
                                  const Text('Chargement des compagnies...'),
                                ],
                              ),
                            )
                          : _compagnies.isEmpty
                              ? Container(
                                  padding: ,
                                    border: Border.all(color: Colors.orange.shade200),
                                  ),
                                  child: Column(
                                    children: [
                                      ,
                                      const SizedBox(height: 8),
                                      ,
                                      ),
                                      const SizedBox(height: 4),
                                      ,
                                        textAlign: TextAlign.center,
                                      ),
                                      const SizedBox(height: 8),
                                      Wrap(
                                        alignment: WrapAlignment.center,
                                        spacing: 8,
                                        runSpacing: 8,
                                        children: [
                                          ElevatedButton.const Icon(
                                            onPressed: _loadCompagnies,
                                            icon: const Icon(Icons.info),
                                            label: const Text('Recharger'),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.orange.shade600,
                                              foregroundColor: Colors.white,
                                              padding: ,
                                          ),
                                          ElevatedButton.const Icon(
                                            onPressed: _runDiagnostic,
                                            icon: const Icon(Icons.info),
                                            label: const Text('Diagnostic'),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.blue.shade600,
                                              foregroundColor: Colors.white,
                                              padding: ,
                                          ),
                                          ElevatedButton.const Icon(
                                            onPressed: _runQuickTest,
                                            icon: const Icon(Icons.info),
                                            label: const Text('Test'),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.purple.shade600,
                                              foregroundColor: Colors.white,
                                              padding: ,
                                          ),
                                          ElevatedButton.const Icon(
                                            onPressed: _testFirestore,
                                            icon: const Icon(Icons.info),
                                            label: const Text('Firestore'),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.green.shade600,
                                              foregroundColor: Colors.white,
                                              padding: ,
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                )
                              : Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Dropdown sécurisé
                                    DropdownButtonFormField<String>(
                                      value: _selectedCompagnieId,
                                      isExpanded: true, // Éviter les problèmes de taille
                                      decoration: InputDecoration(
                                        hintText: 'Sélectionner une compagnie',
                                        prefixIcon: ,
                                        border: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(8),
                                        ),
                                        helperText: '${_compagnies.length} compagnie(s) disponible(s)',
                                      ),
                                      items: _compagnies.map((compagnie) {
                                        final compagnieId = compagnie['id'] as String;
                                        final compagnieNom = compagnie['nom'] as String;
                                        final compagnieCode = compagnie['code'] as String? ?? 'Contenu';
                                        final hasAdmin = _compagniesWithAdmin.contains(compagnieId);

                                        // Format unifié : "Nom (CODE)"
                                        final displayText = compagnieCode.isNotEmpty
                                            ? '$compagnieNom ($compagnieCode)'
                                            : compagnieNom;

                                        return DropdownMenuItem<String>(
                                          value: compagnieId,
                                          enabled: !hasAdmin, // Désactiver si Admin existe déjà
                                          child: Row(
                                            children: [
                                              ,
                                              const SizedBox(width: 8),
                                              Expanded(
                                                child: (displayText (Admin existant)'
                                                      : displayText,
                                                  style: TextStyle(
                                                    color: hasAdmin ? Colors.grey.shade600 : null,
                                                    fontStyle: hasAdmin ? FontStyle.italic : null,
                                                    fontSize: 14,
                                                  ),
                                                  overflow: TextOverflow.ellipsis,
                                                ),
                                              ),
                                            ],
                                          ),
                                        );
                                      }).toList(),
                                      onChanged: (value) {
                                        if (value != null && _compagniesWithAdmin.contains(value)) {
                                          // Empêcher la sélection d'une compagnie qui a déjà un Admin
                                          _showErrorDialog(
                                            'Compagnie Non Disponible',
                                            'Cette compagnie a déjà un Admin Compagnie.\n\n'
                                            'Chaque compagnie ne peut avoir qu\'un seul Admin Compagnie.',
                                          );
                                          return;
                                        }

                                        debugPrint('[INSTITUTIONAL_DIALOG] 🏢 Compagnie sélectionnée: $value');
                                        setState(() {
                                          _selectedCompagnieId = value;
                                          _selectedCompagnieName = value != null
                                              ? _compagnies.firstWhere((c) => c['id'] == value)['nom'] as String
                                              : null;
                                        });
                                        _generateEmailSuggestions();
                                      },
                                      validator: (value) {
                                        if (value == null) {
                                          return 'Veuillez sélectionner une compagnie';
                                        }
                                        return null;
                                      },
                                    ),
                                  ],
                                ),
                      
                      const SizedBox(height: 24),
                      
                      // Option pour personnaliser les identifiants
                      CheckboxListTile(
                        title: const Text('Personnaliser les identifiants'),
                        subtitle: const Text('Sinon, ils seront générés automatiquement'),
                        value: _useCustomCredentials,
                        onChanged: (value) {
                          setState(() {
                            _useCustomCredentials = value ?? false;
                          });
                        },
                        controlAffinity: ListTileControlAffinity.leading,
                      ),
                      
                      if (_useCustomCredentials) ...[
                        const SizedBox(height: 16),
                        
                        // Champs personnalisés
                        Row(
                          children: [
                            Expanded(
                              child: TextFormField(
                                controller: _nomController,
                                decoration: InputDecoration(
                                  labelText: 'Nom (optionnel)',
                                  prefixIcon: ,
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: TextFormField(
                                controller: _prenomController,
                                decoration: InputDecoration(
                                  labelText: 'Prénom (optionnel)',
                                  prefixIcon: ,
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Email personnalisé
                        TextFormField(
                          controller: _emailController,
                          decoration: InputDecoration(
                            labelText: 'Email *',
                            prefixIcon: ,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            helperText: _emailSuggestions.isNotEmpty 
                                ? 'Suggestions: ${_emailSuggestions.take(2).join(', ')}'
                                : null,
                          ),
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Email requis';
                            }
                            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                              return 'Format d\'email invalide';
                            }
                            return null;
                          },
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Mot de passe personnalisé
                        TextFormField(
                          controller: _passwordController,
                          obscureText: !_isPasswordVisible,
                          decoration: InputDecoration(
                            labelText: 'Mot de passe *',
                            prefixIcon: ,
                            suffixIcon: IconButton(
                              onPressed: () {
                                setState(() {
                                  _isPasswordVisible = !_isPasswordVisible;
                                });
                              },
                              icon: const Icon(Icons.info),
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            helperText: 'Minimum 8 caractères, avec majuscules, chiffres et symboles',
                          ),
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Mot de passe requis';
                            }
                            if (value.length < 8) {
                              return 'Minimum 8 caractères';
                            }
                            return null;
                          },
                        ),
                      ],
                      
                      const SizedBox(height: 24),
                      
                      // Informations importantes
                      Container(
                        padding: ,
                          border: Border.all(color: Colors.amber.shade200),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                ,
                                const SizedBox(width: 8),
                                ,
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              '• Aucun email automatique ne sera envoyé\n'
                              '• Les identifiants seront affichés pour copie manuelle\n'
                              '• Ce compte est institutionnel (pas personnel)\n'
                              '• L\'Admin Compagnie peut créer des agences et leurs admins',
                              style: TextStyle(
                                color: Colors.amber.shade700,
                                fontSize: 13,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Boutons d'action
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                    child: const Text('Annuler'),
                  ),
                ),
                const SizedBox(width: 12),
                // Bouton conditionnel selon la disponibilité de la compagnie
                if (_selectedCompagnieId != null && _compagniesWithAdmin.contains(_selectedCompagnieId))
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _showExistingAdminCredentials,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange.shade600,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Voir Identifiants'),
                    ),
                  )
                else
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _createAdminCompagnie,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue.shade600,
                        foregroundColor: Colors.white,
                      ),
                      child: _isLoading
                          ? ,
                              ),
                            )
                          : const Text('Créer le Compte
