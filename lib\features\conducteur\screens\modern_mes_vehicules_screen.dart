import 'package:flutter/material.dart';
import '../../insurance/services/contrat_vehicule_service.dart';
import '../../insurance/models/contrat_assurance_model.dart';

/// 🚗 Interface moderne pour afficher les véhicules du conducteur
class ModernMesVehiculesScreen extends StatefulWidget {
  const ModernMesVehiculesScreen({Key? key}) ) : super(key: key);

  @override
  State<ModernMesVehiculesScreen> createState() => _ModernMesVehiculesScreenState();
}

class _ModernMesVehiculesScreenState extends State<ModernMesVehiculesScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  List<Map<String, dynamic>> _vehiculesAvecContrats = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _chargerVehicules();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _chargerVehicules() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        final vehicules = await ContratVehiculeService.getVehiculesAvecContrats(user.uid);
        setState(() {
          _vehiculesAvecContrats = vehicules;
          _isLoading = false;
        });
        _animationController.forward();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Erreur lors du chargement: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Mes Véhicules'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.info),
            onPressed: () {
              setState(() => _isLoading = true);
              _chargerVehicules();
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? _buildErrorWidget()
              : _vehiculesAvecContrats.isEmpty
                  ? _buildEmptyWidget()
                  : _buildVehiculesList(),
    );
  }

  Widget _buildErrorWidget() {
    return ,
          const SizedBox(height: 16),
          ,
          ),
          const SizedBox(height: 8),
          ,
          ),
          const SizedBox(height: 24),
          ElevatedButton.const Icon(
            onPressed: () {
              setState(() => _isLoading = true);
              _chargerVehicules();
            },
            icon: const Icon(Icons.info),
            label: const Text('Réessayer'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue[600],
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyWidget() {
    return ,
          const SizedBox(height: 16),
          ,
          ),
          const SizedBox(height: 8),
          ,
          ),
        ],
      ),
    );
  }

  Widget _buildVehiculesList() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: ListView.builder(
        padding:  {
          final item = _vehiculesAvecContrats[index];
          final vehicule = item['vehicule'] as VehiculeAssureModel;
          final contrat = item['contrat'] as ContratAssuranceModel?;
          final isAssure = item['isAssure'] as bool;
          final expireBientot = item['expireBientot'] as bool;

          return _buildVehiculeCard(vehicule, contrat, isAssure, expireBientot);
        },
      ),
    );
  }

  Widget _buildVehiculeCard(
    VehiculeAssureModel vehicule,
    ContratAssuranceModel? contrat,
    bool isAssure,
    bool expireBientot,
  ) {
    return Container(
      margin: ,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: ,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // En-tête avec statut
          Container(
            padding: .withValues(alpha: ,
              borderRadius: ,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: ,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ({vehicule.modele}',
                        style: ,
                      ),
                      ,
                      ),
                    ],
                  ),
                ),
                _buildStatusChip(isAssure, expireBientot),
              ],
            ),
          ),
          
          // Détails du véhicule
          (1)),
                _buildDetailRow('Couleur', vehicule.couleur),
                _buildDetailRow('Type', _formatTypeVehicule(vehicule.typeVehicule)),
                _buildDetailRow('Carburant', _formatCarburant(vehicule.carburant)),
                
                if (contrat != null) ...[
                  const Divider(height: 24),
                  _buildContractSection(contrat),
                ],
                
                const SizedBox(height: 16),
                _buildActionButtons(vehicule, contrat, isAssure),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return (1),
            ),
          ),
          Expanded(
            child: ,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContractSection(ContratAssuranceModel contrat) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        ),
        const SizedBox(height: 12),
        _buildDetailRow('N° Contrat', contrat.numeroContrat),
        _buildDetailRow('Type', _formatTypeContrat(contrat.typeContrat)),
        _buildDetailRow('Validité', '${_formatDate(contrat.dateDebut)} - ${_formatDate(contrat.dateFin)}'),
        _buildDetailRow('Prime mensuelle', '${contrat.getPrime('mensuel')} TND'),
        
        if (contrat.couvertures.isNotEmpty) ...[
          const SizedBox(height: 8),
          ,
          ),
          const SizedBox(height: 4),
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: contrat.couvertures.map((couverture) {
              return Container(
                padding: ,
                  border: Border.all(color: Colors.blue[200]!),
                ),
                child: const Text(
                  _formatCouverture(couverture),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.blue[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ],
    );
  }

  Widget _buildStatusChip(bool isAssure, bool expireBientot) {
    String text;
    Color color;
    
    if (!isAssure) {
      text = 'Non assuré';
      color = Colors.red;
    } else if (expireBientot) {
      text = 'Expire bientôt';
      color = Colors.orange;
    } else {
      text = 'Assuré';
      color = Colors.green;
    }
    
    return Container(
      padding: ,
      ),
      child: ,
      ),
    );
  }

  Widget _buildActionButtons(VehiculeAssureModel vehicule, ContratAssuranceModel? contrat, bool isAssure) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.const Icon(
            onPressed: () => _voirDetails(vehicule, contrat),
            icon: const Icon(Icons.info),
            label: const Text('Détails'),
            style: OutlinedButton.styleFrom(
              padding: ,
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.const Icon(
            onPressed: isAssure ? () => _declarerAccident(vehicule, contrat!) : null,
            icon: const Icon(Icons.info),
            label: const Text('Déclarer'),
            style: ElevatedButton.styleFrom(
              backgroundColor: isAssure ? Colors.red[600] : Colors.grey[400],
              foregroundColor: Colors.white,
              padding: ,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(bool isAssure, bool expireBientot) {
    if (!isAssure) return Colors.red;
    if (expireBientot) return Colors.orange;
    return Colors.green;
  }

  IconData _getVehiculeconst Icon(String type) {
    switch (type.toLowerCase()) {
      case 'moto':
        return Icons.motorcycle;
      case 'camion':
        return Icons.local_shipping;
      case 'utilitaire':
        return Icons.fire_truck;
      case 'autocar':
        return Icons.directions_bus;
      default:
        return Icons.directions_car;
    }
  }

  String _formatTypeVehicule(String type) {
    return type[0].toUpperCase() + type.substring(1);
  }

  String _formatCarburant(String carburant) {
    final Map<String, String> labels = {
      'essence': 'Essence',
      'diesel': 'Diesel',
      'electrique': 'Électrique',
      'hybride': 'Hybride',
      'gpl': 'GPL',
    };
    return labels[carburant] ?? carburant;
  }

  String _formatTypeContrat(String type) {
    final Map<String, String> labels = {
      'responsabilite_civile': 'Responsabilité civile',
      'tous_risques': 'Tous risques',
      'tiers_collision': 'Tiers collision',
      'vol_incendie': 'Vol et incendie',
    };
    return labels[type] ?? type;
  }

  String _formatCouverture(String couverture) {
    final Map<String, String> labels = {
      'responsabilite_civile': 'RC',
      'dommages_collision': 'Collision',
      'vol': 'Vol',
      'incendie': 'Incendie',
      'bris_de_glace': 'Bris de glace',
      'catastrophes_naturelles': 'Cat. naturelles',
      'assistance_depannage': 'Assistance',
      'protection_juridique': 'Protection juridique',
    };
    return labels[couverture] ?? couverture;
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  void _voirDetails(VehiculeAssureModel vehicule, ContratAssuranceModel? contrat) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildDetailsBottomSheet(vehicule, contrat),
    );
  }

  Widget _buildDetailsBottomSheet(VehiculeAssureModel vehicule, ContratAssuranceModel? contrat) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: ,
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            margin: ,
            ),
          ),
          
          // Contenu
          Expanded(
            child: SingleChildScrollView(
              padding: ,
                  ),
                  ,
                  ),
                  const SizedBox(height: 24),
                  
                  // Détails complets du véhicule et du contrat
                  // ... (à compléter selon les besoins)
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _declarerAccident(VehiculeAssureModel vehicule, ContratAssuranceModel contrat) {
    // Navigation vers l'écran de déclaration d'accident avec pré-remplissage
    // Cette fonctionnalité sera implémentée dans la prochaine étape
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: ({vehicule.immatriculation}'),
        action: SnackBarAction(
          label: 'Continuer',
          onPressed: () {
            // TODO: Implémenter la navigation vers l
