import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'dart:math';

/// 📊 Résultat de connexion Admin Compagnie
class AdminCompagnieLoginResult {
  final bool success;
  final String? error;
  final Map<String, dynamic>? adminData;
  final String? compagnieId;
  final String? compagnieNom;

  AdminCompagnieLoginResult({
    required this.success,
    this.error,
    this.adminData,
    this.compagnieId,
    this.compagnieNom,
  });
}

/// 🏢 Service d'authentification pour Admin Compagnie
class AdminCompagnieAuthService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// 🔐 Connexion Admin Compagnie
  static Future<AdminCompagnieLoginResult> loginAdminCompagnie({
    required String email,
    required String password,
  }) async {
    try {
      debugPrint('[ADMIN_COMPAGNIE_AUTH] 🔐 Tentative connexion: $email');

      // 1. Vérifier si l'utilisateur existe dans Firestore
      final userQuery = await _firestore
          .collection('users')
          .where('email', isEqualTo: email)
          .where('role', isEqualTo: 'admin_compagnie')
          .limit(1)
          .get();

      if (userQuery.docs.isEmpty) {
        return AdminCompagnieLoginResult(
          success: false,
          error: 'Aucun Admin Compagnie trouvé avec cet email',
        );
      }

      final userData = userQuery.docs.first.data();
      debugPrint('[ADMIN_COMPAGNIE_AUTH] 👤 Utilisateur trouvé: ${userData['compagnieNom']}');

      // 2. Vérifier le mot de passe (TOUS les champs possibles)
      final storedPassword = userData['password'] ??
                            userData['temporaryPassword'] ??
                            userData['motDePasseTemporaire'] ??
                            userData['motDePasse'] ??
                            userData['temp_password'] ??
                            userData['generated_password'];

      debugPrint('[ADMIN_COMPAGNIE_AUTH] 🔑 Vérification mot de passe...');
      debugPrint('[ADMIN_COMPAGNIE_AUTH] 📝 Champs disponibles: ${userData.keys.toList()}');
      debugPrint('[ADMIN_COMPAGNIE_AUTH] 🔍 Champs mot de passe:');
      debugPrint('[ADMIN_COMPAGNIE_AUTH]   - password: ${userData['password']}');
      debugPrint('[ADMIN_COMPAGNIE_AUTH]   - temporaryPassword: ${userData['temporaryPassword']}');
      debugPrint('[ADMIN_COMPAGNIE_AUTH]   - motDePasseTemporaire: ${userData['motDePasseTemporaire']}');
      debugPrint('[ADMIN_COMPAGNIE_AUTH]   - motDePasse: ${userData['motDePasse']}');
      debugPrint('[ADMIN_COMPAGNIE_AUTH]   - temp_password: ${userData['temp_password']}');
      debugPrint('[ADMIN_COMPAGNIE_AUTH]   - generated_password: ${userData['generated_password']}');

      if (storedPassword == null) {
        debugPrint('[ADMIN_COMPAGNIE_AUTH] ❌ Aucun mot de passe trouvé dans les champs');
        return AdminCompagnieLoginResult(
          success: false,
          error: '❌ MOT DE PASSE NON DÉFINI\n\nCe compte n\'a pas de mot de passe configuré.\n\n💡 Solutions:\n• Utilisez le bouton "🔧 Debug Admin" pour créer un mot de passe\n• Contactez le Super Admin\n• Recréez le compte avec un mot de passe',
        );
      }

      if (storedPassword != password) {
        debugPrint('[ADMIN_COMPAGNIE_AUTH] ❌ Mot de passe incorrect');
        debugPrint('[ADMIN_COMPAGNIE_AUTH] 🔍 Attendu: $storedPassword, Reçu: $password');
        return AdminCompagnieLoginResult(
          success: false,
          error: 'Mot de passe incorrect',
        );
      }

      debugPrint('[ADMIN_COMPAGNIE_AUTH] ✅ Mot de passe correct');

      // 3. Vérifier le statut
      final status = userData['status'] as String?;
      final isActive = userData['isActive'] as bool?;

      debugPrint('[ADMIN_COMPAGNIE_AUTH] 📊 Vérification statut: $status, actif: $isActive');

      if (status == 'supprime' || status == 'deleted') {
        return AdminCompagnieLoginResult(
          success: false,
          error: '❌ COMPTE SUPPRIMÉ\n\nCe compte a été supprimé.\n\n💡 Solutions:\n• Contactez le Super Admin pour le réactiver\n• Créez un nouveau compte\n• Utilisez un autre compte',
        );
      }

      if (status != 'actif' || isActive != true) {
        return AdminCompagnieLoginResult(
          success: false,
          error: '❌ COMPTE DÉSACTIVÉ\n\nStatut: $status\nActif: $isActive\n\n💡 Contactez l\'administrateur pour réactiver le compte.',
        );
      }

      // 4. Connexion Firebase Auth (optionnel pour la session)
      try {
        await _auth.signInAnonymously();
        debugPrint('[ADMIN_COMPAGNIE_AUTH] ✅ Session Firebase créée');
      } catch (e) {
        debugPrint('[ADMIN_COMPAGNIE_AUTH] ⚠️ Session Firebase échouée: $e');
      }

      // 5. Mettre à jour la dernière connexion
      await _firestore
          .collection('users')
          .doc(userData['uid'])
          .update({
        'last_login': FieldValue.serverTimestamp(),
        'login_count': FieldValue.increment(1),
      });

      debugPrint('[ADMIN_COMPAGNIE_AUTH] 🎉 Connexion réussie pour ${userData['compagnieNom']}');

      return AdminCompagnieLoginResult(
        success: true,
        data: userData,
        compagnieId: userData['compagnieId'],
        compagnieNom: userData['compagnieNom'],
      );

    } catch (e) {
      debugPrint('[ADMIN_COMPAGNIE_AUTH] ❌ Erreur connexion: $e');
      return AdminCompagnieLoginResult(
        success: false,
        error: 'Erreur de connexion: $e',
      );
    }
  }

  /// 📊 Récupérer les données de la compagnie
  static Future<Map<String, dynamic>?> getCompagnieData(String compagnieId) async {
    try {
      // Chercher dans plusieurs collections
      final collections = ['companies', 'compagnies_assurance', 'csv_imports'];
      
      for (String collection in collections) {
        try {
          final doc = await _firestore
              .collection(collection)
              .doc(compagnieId)
              .get();

          if (doc.exists) {
            debugPrint('[ADMIN_COMPAGNIE_AUTH] 🏢 Compagnie trouvée dans: $collection');
            return doc.data();
          }
        } catch (e) {
          continue;
        }
      }

      // Si pas trouvé, créer une entrée basique
      final compagnieData = {
        'id': compagnieId,
        'nom': compagnieId.replaceAll('_', ' '),
        'code': compagnieId,
        'status': 'actif',
        'created_at': FieldValue.serverTimestamp(),
      };

      await _firestore
          .collection('companies')
          .doc(compagnieId)
          .set(compagnieData);

      return compagnieData;

    } catch (e) {
      debugPrint('[ADMIN_COMPAGNIE_AUTH] ❌ Erreur récupération compagnie: $e');
      return null;
    }
  }

  /// 📈 Récupérer les statistiques de la compagnie
  static Future<Map<String, dynamic>> getCompagnieStats(String compagnieId) async {
    try {
      debugPrint('[ADMIN_COMPAGNIE_AUTH] 📈 Récupération stats pour: $compagnieId');

      // Compter les agences
      int agencesCount = 0;
      try {
        final agencesQuery = await _firestore
            .collection('agencies')
            .where('compagnieId', isEqualTo: compagnieId)
            .get();
        agencesCount = agencesQuery.docs.length;
      } catch (e) {
        debugPrint('[ADMIN_COMPAGNIE_AUTH] ⚠️ Erreur count agences: $e');
      }

      // Compter les agents
      int agentsCount = 0;
      try {
        final agentsQuery = await _firestore
            .collection('users')
            .where('compagnieId', isEqualTo: compagnieId)
            .where('role', isEqualTo: 'agent')
            .get();
        agentsCount = agentsQuery.docs.length;
      } catch (e) {
        debugPrint('[ADMIN_COMPAGNIE_AUTH] ⚠️ Erreur count agents: $e');
      }

      // Compter les contrats
      int contratsCount = 0;
      try {
        final contratsQuery = await _firestore
            .collection('contracts')
            .where('compagnieId', isEqualTo: compagnieId)
            .get();
        contratsCount = contratsQuery.docs.length;
      } catch (e) {
        debugPrint('[ADMIN_COMPAGNIE_AUTH] ⚠️ Erreur count contrats: $e');
      }

      // Compter les sinistres
      int sinistresCount = 0;
      try {
        final sinistresQuery = await _firestore
            .collection('claims')
            .where('compagnieId', isEqualTo: compagnieId)
            .get();
        sinistresCount = sinistresQuery.docs.length;
      } catch (e) {
        debugPrint('[ADMIN_COMPAGNIE_AUTH] ⚠️ Erreur count sinistres: $e');
      }

      final stats = {
        'total_agences': agencesCount,
        'total_agents': agentsCount,
        'total_contrats': contratsCount,
        'total_sinistres': sinistresCount,
        'last_updated': DateTime.now().toIso8601String(),
      };

      debugPrint('[ADMIN_COMPAGNIE_AUTH] 📊 Stats calculées: $stats');
      return stats;

    } catch (e) {
      debugPrint('[ADMIN_COMPAGNIE_AUTH] ❌ Erreur calcul stats: $e');
      return {
        'total_agences': 0,
        'total_agents': 0,
        'total_contrats': 0,
        'total_sinistres': 0,
        'error': e.toString(),
      };
    }
  }

  /// 👤 Créer un Admin Agence (par Admin Compagnie)
  static Future<Map<String, dynamic>> createAdminAgence({
    required String compagnieId,
    required String agenceId,
    required String nom,
    required String prenom,
    required String email,
    required String telephone,
    String? adresse,
    String? cin,
    required String createdBy,
  }) async {
    try {
      debugPrint('[ADMIN_COMPAGNIE_SERVICE] 👤 Création Admin Agence: $prenom $nom');
      debugPrint('[ADMIN_COMPAGNIE_SERVICE] 📋 Paramètres: compagnieId=$compagnieId, agenceId=$agenceId');

      // 1. Vérifier que l'agence existe et appartient à la compagnie
      debugPrint('[ADMIN_COMPAGNIE_SERVICE] 🔍 Vérification agence...');

      // Essayer d'abord la nouvelle structure hiérarchique
      var agenceDoc = await _firestore
          .collection('companies')
          .doc(compagnieId)
          .collection('agencies')
          .doc(agenceId)
          .get();

      Map<String, dynamic>? agenceData;
      bool isNewStructure = true;

      if (!agenceDoc.exists) {
        debugPrint('[ADMIN_COMPAGNIE_SERVICE] ⚠️ Agence non trouvée dans nouvelle structure, essai ancienne...');

        // Fallback vers l'ancienne structure
        final oldAgenceDoc = await _firestore
            .collection('agences')
            .doc(agenceId)
            .get();

        if (!oldAgenceDoc.exists) {
          debugPrint('[ADMIN_COMPAGNIE_SERVICE] ❌ Agence introuvable dans les deux structures: $agenceId');
          return {
            'success': false,
            'error': 'Agence introuvable',
            'message': 'L\'agence spécifiée n\'existe pas. Veuillez d\'abord migrer vos données vers la nouvelle structure hiérarchique.',
          };
        }

        agenceData = oldAgenceDoc.data()!;
        isNewStructure = false;
        debugPrint('[ADMIN_COMPAGNIE_SERVICE] ✅ Agence trouvée dans ancienne structure: ${agenceData['nom']}');
        debugPrint('[ADMIN_COMPAGNIE_SERVICE] 🔄 Migration recommandée vers nouvelle structure');
      } else {
        agenceData = agenceDoc.data()!;
        debugPrint('[ADMIN_COMPAGNIE_SERVICE] ✅ Agence trouvée dans nouvelle structure: ${agenceData['nom']}');
      }

      // 2. Vérifier qu'il n'y a pas déjà un admin pour cette agence
      debugPrint('[ADMIN_COMPAGNIE_SERVICE] 🔍 Vérification admin existant...');
      final existingAdmin = await _firestore
          .collection('users')
          .where('role', isEqualTo: 'admin_agence')
          .where('agenceId', isEqualTo: agenceId)
          .where('compagnieId', isEqualTo: compagnieId)
          .where('isActive', isEqualTo: true)
          .limit(1)
          .get();

      if (existingAdmin.docs.isNotEmpty) {
        final existingAdminData = existingAdmin.docs.first.data();
        debugPrint('[ADMIN_COMPAGNIE_SERVICE] ❌ Admin déjà existant: ${existingAdminData['email']}');
        return {
          'success': false,
          'error': 'Admin déjà existant',
          'message': 'Cette agence a déjà un administrateur: ${existingAdminData['prenom']} ${existingAdminData['nom']} (${existingAdminData['email']})',
        };
      }

      // 3. Vérifier que l'email n'est pas déjà utilisé
      debugPrint('[ADMIN_COMPAGNIE_SERVICE] 🔍 Vérification email...');
      final existingUser = await _firestore
          .collection('users')
          .where('email', isEqualTo: email)
          .where('isActive', isEqualTo: true)
          .limit(1)
          .get();

      if (existingUser.docs.isNotEmpty) {
        debugPrint('[ADMIN_COMPAGNIE_SERVICE] ❌ Email déjà utilisé: $email');
        return {
          'success': false,
          'error': 'Email déjà utilisé',
          'message': 'Un utilisateur avec cet email existe déjà',
        };
      }

      // 4. Générer un mot de passe temporaire (même format que Super Admin)
      final tempPassword = _generateSecurePassword();
      final adminId = 'admin_agence_${agenceId}_${DateTime.now().millisecondsSinceEpoch}';

      debugPrint('[ADMIN_COMPAGNIE_SERVICE] 🔐 Mot de passe généré: $tempPassword');

      final adminData = {
        'uid': adminId,
        'email': email,
        'nom': nom,
        'prenom': prenom,
        'role': 'admin_agence',
        'compagnieId': compagnieId,
        'agenceId': agenceId,
        'telephone': telephone,
        'adresse': adresse ?? 'Contenu',
        'cin': cin ?? 'Contenu',
        'status': 'actif',
        'isActive': true,
        'isFirstLogin': true,
        'passwordChangeRequired': true,
        'created_at': FieldValue.serverTimestamp(),
        'created_by': createdBy,
        'updated_at': FieldValue.serverTimestamp(),

        // Mots de passe dans tous les champs pour compatibilité (même format que Super Admin)
        'password': tempPassword,
        'temporaryPassword': tempPassword,
        'motDePasseTemporaire': tempPassword,
        'motDePasse': tempPassword,
        'temp_password': tempPassword,
        'generated_password': tempPassword,
      };

      // 5. Créer l'utilisateur dans Firestore
      debugPrint('[ADMIN_COMPAGNIE_SERVICE] 💾 Création utilisateur...');
      await _firestore
          .collection('users')
          .doc(adminId)
          .set(adminData);

      // 6. Mettre à jour l'agence avec l'admin
      debugPrint('[ADMIN_COMPAGNIE_SERVICE] 🔗 Liaison agence-admin...');

      final updateData = {
        'adminUid': adminId,
        'adminEmail': email,
        'adminNom': '$prenom $nom',
        'hasAdmin': true,
        'updated_at': FieldValue.serverTimestamp(),
      };

      if (isNewStructure) {
        // Nouvelle structure hiérarchique
        await _firestore
            .collection('companies')
            .doc(compagnieId)
            .collection('agencies')
            .doc(agenceId)
            .update(updateData);
      } else {
        // Ancienne structure (fallback)
        await _firestore
            .collection('agences')
            .doc(agenceId)
            .update(updateData);

        debugPrint('[ADMIN_COMPAGNIE_SERVICE] ⚠️ Agence mise à jour dans ancienne structure - Migration recommandée');
      }

      debugPrint('[ADMIN_COMPAGNIE_SERVICE] ✅ Admin Agence créé avec succès: $adminId');

      return {
        'success': true,
        'adminId': adminId,
        'email': email,
        'password': tempPassword,
        'message': 'Admin Agence créé avec succès',
        'displayCredentials': {
          'email': email,
          'password': tempPassword,
          'nom': '$prenom $nom',
          'role': 'Admin Agence',
          'agence': agenceData['nom'],
        },
      };

    } catch (e) {
      debugPrint('[ADMIN_COMPAGNIE_SERVICE] ❌ Erreur création Admin Agence: $e');
      debugPrint('[ADMIN_COMPAGNIE_SERVICE] 📍 Stack trace: ${StackTrace.current}');
      return {
        'success': false,
        'error': e.toString(),
        'message': 'Erreur lors de la création de l\'Admin Agence: ${e.toString()}',
      };
    }
  }

  /// 🔧 Générer un mot de passe sécurisé (même format que Super Admin)
  static String _generateSecurePassword() {
    ;

    // Générer un mot de passe de 12 caractères avec au moins:
    // - 1 majuscule, 1 minuscule, 1 chiffre, 1 caractère spécial
    String password = 'Contenu';

    // Ajouter au moins un de chaque type
    password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[random.nextInt(26)]; // Majuscule
    password += 'abcdefghijklmnopqrstuvwxyz'[random.nextInt(26)]; // Minuscule
    password += '0123456789'[random.nextInt(10)]; // Chiffre
    password += '!@#\$%^&*'[random.nextInt(8)]; // Caractère spécial

    // Compléter avec des caractères aléatoires
    for (int i = 4; i < 12; i++) {
      password += chars[random.nextInt(chars.length)];
    }

    // Mélanger les caractères
    final passwordList = password.split('Contenu');
    passwordList.shuffle(random);

    return passwordList.join();
  }

  /// 🚪 Déconnexion
  static Future<void> logout() async {
    try {
      await _auth.signOut();
      debugPrint('[ADMIN_COMPAGNIE_AUTH] 🚪 Déconnexion réussie');
    } catch (e) {
      debugPrint('[ADMIN_COMPAGNIE_AUTH] ❌ Erreur déconnexion: $e');
    }
  }

  /// ✅ Vérifier si un email Admin Compagnie existe
  static Future<bool> checkAdminCompagnieExists(String email) async {
    try {
      final query = await _firestore
          .collection('users')
          .where('email', isEqualTo: email)
          .where('role', isEqualTo: 'admin_compagnie')
          .limit(1)
          .get();

      return query.docs.isNotEmpty;
    } catch (e) {
      debugPrint('[ADMIN_COMPAGNIE_AUTH] ❌ Erreur vérification email: $e');
      return false;
    }
  }

  /// 🔍 Debug - Afficher les données utilisateur (mode développement uniquement)
  static Future<void> debugUserData(String email) async {
    if (!kDebugMode) return;

    try {
      final userQuery = await _firestore
          .collection('users')
          .where('email', isEqualTo: email)
          .limit(1)
          .get();

      if (userQuery.docs.isNotEmpty) {
        final userData = userQuery.docs.first.data();
        debugPrint('=== DEBUG USER DATA ===');
        debugPrint('Email: $email');
        debugPrint('Données complètes: $userData');
        debugPrint('Champs mot de passe:');
        debugPrint('  - password: ${userData['password']}');
        debugPrint('  - temporaryPassword: ${userData['temporaryPassword']}');
        debugPrint('  - motDePasseTemporaire: ${userData['motDePasseTemporaire']}');
        debugPrint('========================');
      } else {
        debugPrint('❌ Aucun utilisateur trouvé avec l\'email: $email');
      }
    } catch (e) {
      debugPrint('❌ Erreur debug: $e
