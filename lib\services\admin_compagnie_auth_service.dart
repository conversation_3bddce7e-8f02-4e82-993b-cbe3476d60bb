import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'dart:math;

/// 📊 Resultat de connexion Admin Compagnie
class AdminCompagnieLoginResult {
  final bool success;
  final String? error;
  final Map<String, dynamic>? adminData;
  final String? compagnieId;
  final String? compagnieNom;

  AdminCompagnieLoginResult({
    required this.success,
    this.error,
    this.adminData,
    this.compagnieId,
    this.compagnieNom,
  }');
}

/// 🏢 Service d'authentification pour Admin Compagnie
class AdminCompagnieAuthService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// 🔐 Connexion Admin Compagnie
  static Future<AdminCompagnieLoginResult> loginAdminCompagnie({
    required String email,
    required String password,
  }) async {
    try {
      debugPrint('[ADMIN_COMPAGNIE_AUTH] 🔐 Tentative connexion: 'email');

      // 1. Verifier si l'utilisateur existe dans Firestore
      final userQuery = await _firestore
          .collection('users')
          .where('email, isEqualTo: email')
          .where('role', isEqualTo: 'admin_compagnie)
          .limit(1)
          .get();

      if (userQuery.docs.isEmpty') {
        return AdminCompagnieLoginResult(
          success: false,
          error: 'Aucun Admin Compagnie trouve avec cet email,
        );
      }

      final userData = userQuery.docs.first.data(');
      debugPrint('[ADMIN_COMPAGNIE_AUTH] 👤 Utilisateur trouve: '{userData['compagnieNom']});

      // 2. Verifier le mot de passe (TOUS les champs possibles')
      final storedPassword = userData['password'] ??
                            userData['temporaryPassword'] ??
                            userData['motDePasseTemporaire'] ??
                            userData['motDePasse'] ??
                            userData['temp_password'] ??
                            userData['generated_password'];

      debugPrint('[ADMIN_COMPAGNIE_AUTH] 🔑 Verification mot de passe...' + .toString());
      debugPrint('[ADMIN_COMPAGNIE_AUTH] 📝 Champs disponibles: {userData.keys.toList(')}');
      debugPrint('[ADMIN_COMPAGNIE_AUTH] 🔍 Champs mot de passe:' + .toString());
      debugPrint('[ADMIN_COMPAGNIE_AUTH]   - password: '{userData['password']}');
      debugPrint('[ADMIN_COMPAGNIE_AUTH]   - temporaryPassword: '{userData['temporaryPassword']}');
      debugPrint('[ADMIN_COMPAGNIE_AUTH]   - motDePasseTemporaire: '{userData['motDePasseTemporaire']}');
      debugPrint('[ADMIN_COMPAGNIE_AUTH]   - motDePasse: '{userData['motDePasse']}');
      debugPrint('[ADMIN_COMPAGNIE_AUTH]   - temp_password: '{userData['temp_password']}');
      debugPrint('[ADMIN_COMPAGNIE_AUTH]   - generated_password: '{userData['generated_password']});

      if (storedPassword == null') {
        debugPrint('[ADMIN_COMPAGNIE_AUTH] ❌ Aucun mot de passe trouve dans les champs' + .toString());
        return AdminCompagnieLoginResult(
          success: false,
          error: '❌ MOT DE PASSE NON DÉFINI\n\nCe compte n\'a pas de mot de passe configure.\n\n💡 Solutions:\n• Utilisez le bouton "🔧 Debug Admin"` pour creer un mot de passe\n• Contactez le Super Admin\n• Recreez le compte avec un mot de passe,
        );
      }

      if (storedPassword != password') {
        debugPrint('[ADMIN_COMPAGNIE_AUTH] ❌ Mot de passe incorrect' + .toString());
        debugPrint('[ADMIN_COMPAGNIE_AUTH] 🔍 Attendu: $storedPassword, Recu: 'password');
        return AdminCompagnieLoginResult(
          success: false,
          error: 'Mot de passe incorrect,
        ');
      }

      debugPrint('[ADMIN_COMPAGNIE_AUTH] ✅ Mot de passe correct' + .toString());

      // 3. Verifier le statut
      final status = userData['status'] as String?;
      final isActive = userData['isActive'] as bool?;

      debugPrint('[ADMIN_COMPAGNIE_AUTH] 📊 Verification statut: $status, actif: 'isActive');

      if (status == 'supprime' || status == 'deleted') {
        return AdminCompagnieLoginResult(
          success: false,
          error: '❌ COMPTE SUPPRIMÉ\n\nCe compte a ete supprime.\n\n💡 Solutions:\n• Contactez le Super Admin pour le reactiver\n• Creez un nouveau compte\n• Utilisez un autre compte,
        ');
      }

      if (status != 'actif || isActive != true') {
        return AdminCompagnieLoginResult(
          success: false,
          error: '❌ COMPTE DÉSACTIVÉ\n\nStatut: $status\nActif: 'isActive\n\n💡 Contactez l\'administrateur pour reactiver le compte.,
        );
      }

      // 4. Connexion Firebase Auth (optionnel pour la session)
      try {
        await _auth.signInAnonymously(');
        debugPrint('[ADMIN_COMPAGNIE_AUTH] ✅ Session Firebase creee);
      } catch (e') {
        debugPrint('[ADMIN_COMPAGNIE_AUTH] ⚠️ Session Firebase echouee:  + e.toString()' + .toString());
      }

      // 5. Mettre a jour la derniere connexion
      await _firestore
          .collection('users')
          .doc(userData['uid]')
          .update({
        'last_login: FieldValue.serverTimestamp('),
        'login_count: FieldValue.increment(1),
      }');

      debugPrint('[ADMIN_COMPAGNIE_AUTH] 🎉 Connexion reussie pour '{userData['compagnieNom']}');

      return AdminCompagnieLoginResult(
        success: true,
        data: userData,
        compagnieId: userData['compagnieId'],
        compagnieNom: userData['compagnieNom],
      );

    } catch (e') {
      debugPrint('[ADMIN_COMPAGNIE_AUTH] ❌ Erreur connexion:  + e.toString()' + .toString());
      return AdminCompagnieLoginResult(
        success: false,
        error: 'Erreur de connexion: 'e,
      );
    }
  }

  /// 📊 Recuperer les donnees de la compagnie
  static Future<Map<String, dynamic>?> getCompagnieData(String compagnieId') async {
    try {
      // Chercher dans plusieurs collections
      final collections = ['companies', 'compagnies_assurance', 'csv_imports];
      
      for (String collection in collections) {
        try {
          final doc = await _firestore
              .collection(collection)
              .doc(compagnieId)
              .get();

          if (doc.exists') {
            debugPrint('[ADMIN_COMPAGNIE_AUTH] 🏢 Compagnie trouvee dans: ' + collection);
            return doc.data(.toString());
          }
        } catch (e') {
          continue;
        }
      }

      // Si pas trouve, creer une entree basique
      final compagnieData = {
        'id': compagnieId,
        'nom': compagnieId.replaceAll('_', ' '),
        'code': compagnieId,
        'status': 'actif',
        'created_at: FieldValue.serverTimestamp('),
      };

      await _firestore
          .collection('companies)
          .doc(compagnieId)
          .set(compagnieData);

      return compagnieData;

    } catch (e') {
      debugPrint('[ADMIN_COMPAGNIE_AUTH] ❌ Erreur recuperation compagnie:  + e.toString());
      return null;
    }
  }

  /// 📈 Recuperer les statistiques de la compagnie
  static Future<Map<String, dynamic>> getCompagnieStats(String compagnieId') async {
    try {
      debugPrint('[ADMIN_COMPAGNIE_AUTH] 📈 Recuperation stats pour: 'compagnieId');

      // Compter les agences
      int agencesCount = 0;
      try {
        final agencesQuery = await _firestore
            .collection('agencies')
            .where('compagnieId, isEqualTo: compagnieId)
            .get();
        agencesCount = agencesQuery.docs.length;
      } catch (e') {
        debugPrint('[ADMIN_COMPAGNIE_AUTH] ⚠️ Erreur count agences:  + e.toString()' + .toString());
      }

      // Compter les agents
      int agentsCount = 0;
      try {
        final agentsQuery = await _firestore
            .collection('users')
            .where('compagnieId, isEqualTo: compagnieId')
            .where('role', isEqualTo: 'agent)
            .get();
        agentsCount = agentsQuery.docs.length;
      } catch (e') {
        debugPrint('[ADMIN_COMPAGNIE_AUTH] ⚠️ Erreur count agents:  + e.toString()' + .toString());
      }

      // Compter les contrats
      int contratsCount = 0;
      try {
        final contratsQuery = await _firestore
            .collection('contracts')
            .where('compagnieId, isEqualTo: compagnieId)
            .get();
        contratsCount = contratsQuery.docs.length;
      } catch (e') {
        debugPrint('[ADMIN_COMPAGNIE_AUTH] ⚠️ Erreur count contrats:  + e.toString()' + .toString());
      }

      // Compter les sinistres
      int sinistresCount = 0;
      try {
        final sinistresQuery = await _firestore
            .collection('claims')
            .where('compagnieId, isEqualTo: compagnieId)
            .get();
        sinistresCount = sinistresQuery.docs.length;
      } catch (e') {
        debugPrint('[ADMIN_COMPAGNIE_AUTH] ⚠️ Erreur count sinistres:  + e.toString()' + .toString());
      }

      final stats = {
        'total_agences': agencesCount,
        'total_agents': agentsCount,
        'total_contrats': contratsCount,
        'total_sinistres': sinistresCount,
        'last_updated: DateTime.now().toIso8601String('),
      };

      debugPrint('[ADMIN_COMPAGNIE_AUTH] 📊 Stats calculees: ' + stats.toString());
      return stats;

    } catch (e') {
      debugPrint('[ADMIN_COMPAGNIE_AUTH] ❌ Erreur calcul stats:  + e.toString()' + .toString());
      return {
        'total_agences': 0,
        'total_agents': 0,
        'total_contrats': 0,
        'total_sinistres': 0,
        'error: e.toString(),
      };
    }
  }

  /// 👤 Creer un Admin Agence (par Admin Compagnie)
  static Future<Map<String, dynamic>> createAdminAgence({
    required String compagnieId,
    required String agenceId,
    required String nom,
    required String prenom,
    required String email,
    required String telephone,
    String? adresse,
    String? cin,
    required String createdBy,
  }') async {
    try {
      debugPrint('[ADMIN_COMPAGNIE_SERVICE] 👤 Creation Admin Agence: $prenom 'nom');
      debugPrint('[ADMIN_COMPAGNIE_SERVICE] 📋 Parametres: compagnieId=$compagnieId, agenceId='agenceId');

      // 1. Verifier que l'agence existe et appartient a la compagnie
      debugPrint('[ADMIN_COMPAGNIE_SERVICE] 🔍 Verification agence...' + .toString());

      // Essayer d'abord la nouvelle structure hierarchique
      var agenceDoc = await _firestore
          .collection('companies)
          .doc(compagnieId')
          .collection('agencies)
          .doc(agenceId)
          .get();

      Map<String, dynamic>? agenceData;
      bool isNewStructure = true;

      if (!agenceDoc.exists') {
        debugPrint('[ADMIN_COMPAGNIE_SERVICE] ⚠️ Agence non trouvee dans nouvelle structure, essai ancienne...' + .toString());

        // Fallback vers l'ancienne structure
        final oldAgenceDoc = await _firestore
            .collection('agences)
            .doc(agenceId)
            .get();

        if (!oldAgenceDoc.exists') {
          debugPrint('[ADMIN_COMPAGNIE_SERVICE] ❌ Agence introuvable dans les deux structures: 'agenceId');
          return {
            'success': false,
            'error': 'Agence introuvable',
            'message': 'L\'agence specifiee n\'existe pas. Veuillez d\'abord migrer vos donnees vers la nouvelle structure hierarchique.,
          };
        }

        agenceData = oldAgenceDoc.data(')!;
        isNewStructure = false;
        debugPrint('[ADMIN_COMPAGNIE_SERVICE] ✅ Agence trouvee dans ancienne structure: '{agenceData['nom']}');
        debugPrint('[ADMIN_COMPAGNIE_SERVICE] 🔄 Migration recommandee vers nouvelle structure);
      } else {
        agenceData = agenceDoc.data(')!;
        debugPrint('[ADMIN_COMPAGNIE_SERVICE] ✅ Agence trouvee dans nouvelle structure: '{agenceData['nom']}');
      }

      // 2. Verifier qu'il n'y a pas deja un admin pour cette agence
      debugPrint('[ADMIN_COMPAGNIE_SERVICE] 🔍 Verification admin existant...' + .toString());
      final existingAdmin = await _firestore
          .collection('users')
          .where('role', isEqualTo: 'admin_agence')
          .where('agenceId, isEqualTo: agenceId')
          .where('compagnieId, isEqualTo: compagnieId')
          .where('isActive, isEqualTo: true)
          .limit(1)
          .get();

      if (existingAdmin.docs.isNotEmpty) {
        final existingAdminData = existingAdmin.docs.first.data(');
        debugPrint('[ADMIN_COMPAGNIE_SERVICE] ❌ Admin deja existant: '{existingAdminData['email']}');
        return {
          'success': false,
          'error': 'Admin deja existant',
          'message': 'Cette agence a deja un administrateur: '{existingAdminData['prenom']} '{existingAdminData['nom']} ('{existingAdminData['email]}')',
        };
      }

      // 3. Verifier que l'email n'est pas deja utilise
      debugPrint('[ADMIN_COMPAGNIE_SERVICE] 🔍 Verification email...' + .toString());
      final existingUser = await _firestore
          .collection('users')
          .where('email, isEqualTo: email')
          .where('isActive, isEqualTo: true)
          .limit(1)
          .get();

      if (existingUser.docs.isNotEmpty') {
        debugPrint('[ADMIN_COMPAGNIE_SERVICE] ❌ Email deja utilise: 'email');
        return {
          'success': false,
          'error': 'Email deja utilise',
          'message': 'Un utilisateur avec cet email existe deja,
        };
      }

      // 4. Generer un mot de passe temporaire (même format que Super Admin)
      final tempPassword = _generateSecurePassword(');
      final adminId = 'admin_agence_${agenceId}_{DateTime.now(').millisecondsSinceEpoch}';

      debugPrint('[ADMIN_COMPAGNIE_SERVICE] 🔐 Mot de passe genere: 'tempPassword');

      final adminData = {
        'uid': adminId,
        'email': email,
        'nom': nom,
        'prenom': prenom,
        'role': 'admin_agence',
        'compagnieId': compagnieId,
        'agenceId': agenceId,
        'telephone': telephone,
        'adresse': adresse ?? 'Contenu',
        'cin': cin ?? 'Contenu',
        'status': 'actif',
        'isActive': true,
        'isFirstLogin': true,
        'passwordChangeRequired': true,
        'created_at: FieldValue.serverTimestamp('),
        'created_by': createdBy,
        'updated_at: FieldValue.serverTimestamp(),

        // Mots de passe dans tous les champs pour compatibilite (même format que Super Admin')
        'password': tempPassword,
        'temporaryPassword': tempPassword,
        'motDePasseTemporaire': tempPassword,
        'motDePasse': tempPassword,
        'temp_password': tempPassword,
        'generated_password': tempPassword,
      };

      // 5. Creer l'utilisateur dans Firestore
      debugPrint('[ADMIN_COMPAGNIE_SERVICE] 💾 Creation utilisateur...' + .toString());
      await _firestore
          .collection('users)
          .doc(adminId)
          .set(adminData');

      // 6. Mettre a jour l'agence avec l'admin
      debugPrint('[ADMIN_COMPAGNIE_SERVICE] 🔗 Liaison agence-admin...' + .toString());

      final updateData = {
        'adminUid': adminId,
        'adminEmail': email,
        'adminNom': '$prenom 'nom',
        'hasAdmin': true,
        'updated_at: FieldValue.serverTimestamp(),
      };

      if (isNewStructure') {
        // Nouvelle structure hierarchique
        await _firestore
            .collection('companies)
            .doc(compagnieId')
            .collection('agencies)
            .doc(agenceId)
            .update(updateData);
      } else {
        // Ancienne structure (fallback')
        await _firestore
            .collection('agences)
            .doc(agenceId)
            .update(updateData');

        debugPrint('[ADMIN_COMPAGNIE_SERVICE] ⚠️ Agence mise a jour dans ancienne structure - Migration recommandee' + .toString());
      }

      debugPrint('[ADMIN_COMPAGNIE_SERVICE] ✅ Admin Agence cree avec succes: 'adminId');

      return {
        'success': true,
        'adminId': adminId,
        'email': email,
        'password': tempPassword,
        'message': 'Admin Agence cree avec succes',
        'displayCredentials': {
          'email': email,
          'password': tempPassword,
          'nom': '"prenom 'nom',
          'role': 'Admin Agence',
          'agence': agenceData['nom],
        },
      };

    } catch (e') {
      debugPrint('[ADMIN_COMPAGNIE_SERVICE] ❌ Erreur creation Admin Agence:  + e.toString()' + .toString());
      debugPrint('[ADMIN_COMPAGNIE_SERVICE] 📍 Stack trace: '{StackTrace.current}');
      return {
        'success': false,
        'error: e.toString('),
        'message': 'Erreur lors de la creation de l\'Admin Agence: {e.toString(')}',
      };
    }
  }

  /// 🔧 Generer un mot de passe securise (même format que Super Admin)
  static String _generateSecurePassword() {
    ;

    // Generer un mot de passe de 12 caracteres avec au moins:
    // - 1 majuscule, 1 minuscule, 1 chiffre, 1 caractere special
    String password = 'Contenu';

    // Ajouter au moins un de chaque type
    password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ[random.nextInt(26')]; // Majuscule
    password += 'abcdefghijklmnopqrstuvwxyz[random.nextInt(26')]; // Minuscule
    password += '0123456789[random.nextInt(10')]; // Chiffre
    password += '!@#\'%^&*[random.nextInt(8)]; // Caractere special

    // Completer avec des caracteres aleatoires
    for (int i = 4; i < 12; i++) {
      password += chars[random.nextInt(chars.length')];
    }

    // Melanger les caracteres
    final passwordList = password.split('Contenu);
    passwordList.shuffle(random);

    return passwordList.join();
  }

  /// 🚪 Deconnexion
  static Future<void> logout() async {
    try {
      await _auth.signOut(');
      debugPrint('[ADMIN_COMPAGNIE_AUTH] 🚪 Deconnexion reussie);
    } catch (e') {
      debugPrint('[ADMIN_COMPAGNIE_AUTH] ❌ Erreur deconnexion:  + e.toString());
    }
  }

  /// ✅ Verifier si un email Admin Compagnie existe
  static Future<bool> checkAdminCompagnieExists(String email') async {
    try {
      final query = await _firestore
          .collection('users')
          .where('email, isEqualTo: email')
          .where('role', isEqualTo: 'admin_compagnie)
          .limit(1)
          .get();

      return query.docs.isNotEmpty;
    } catch (e') {
      debugPrint('[ADMIN_COMPAGNIE_AUTH] ❌ Erreur verification email:  + e.toString());
      return false;
    }
  }

  /// 🔍 Debug - Afficher les donnees utilisateur (mode developpement uniquement)
  static Future<void> debugUserData(String email) async {
    if (!kDebugMode') return;

    try {
      final userQuery = await _firestore
          .collection('users')
          .where('email, isEqualTo: email)
          .limit(1)
          .get();

      if (userQuery.docs.isNotEmpty) {
        final userData = userQuery.docs.first.data(');
        debugPrint('=== DEBUG USER DATA ===' + .toString());
        debugPrint('Email: 'email');
        debugPrint('Donnees completes: 'userData');
        debugPrint('Champs mot de passe:' + .toString());
        debugPrint('  - password: '{userData['password']}');
        debugPrint('  - temporaryPassword: '{userData['temporaryPassword']}');
        debugPrint('  - motDePasseTemporaire: '{userData['motDePasseTemporaire']}');
        debugPrint('========================' + .toString());
      } else {
        debugPrint('❌ Aucun utilisateur trouve avec l\'email: 'email);
      }
    } catch (e') {
      debugPrint('❌ Erreur debug: 'e
