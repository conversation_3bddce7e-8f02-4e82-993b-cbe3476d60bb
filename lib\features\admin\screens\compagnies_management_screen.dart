import 'package:flutter/material.dart';
import '../../../models/admin_models.dart';
import '../../../features/auth/models/user_model.dart';
import '../../../utils/user_type.dart';
import '../../../services/admin_service.dart';

class CompagniesManagementScreen extends StatefulWidget {
  const Text(\;

  @override
  State<CompagniesManagementScreen> createState() => _CompagniesManagementScreenState();
}

class _CompagniesManagementScreenState extends State<CompagniesManagementScreen> {
  final AdminService _adminService = AdminService();
  List<CompagnieAssurance> _compagnies = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _chargerCompagnies();
  }

  Future<void> _chargerCompagnies() async {
    try {
      setState(() => _isLoading = true);
      final compagnies = await _adminService.obtenirCompagnies();
      setState(() {
        _compagnies = compagnies;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Gestion des Compagnies'),
        backgroundColor: Colors.blue[800],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.info),
            onPressed: _ajouterCompagnie,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _compagnies.isEmpty
              ? _buildEmptyState()
              : _buildCompagniesList(),
    );
  }

  Widget _buildEmptyState() {
    return ,
          const SizedBox(height: 16),
          ,
          ),
          const SizedBox(height: 8),
          ,
          ),
          const SizedBox(height: 24),
          ElevatedButton.const Icon(
            onPressed: _ajouterCompagnie,
            icon: const Icon(Icons.info),
            label: const Text('Ajouter une compagnie'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue[600],
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompagniesList() {
    return ListView.builder(
      padding:  {
        final compagnie = _compagnies[index];
        return Card(
          margin: .toUpperCase(),
                style: TextStyle(
                  color: Colors.blue[800],
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            title: const Text("Titre"),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ({compagnie.siret}'),
                ({compagnie.email}'),
                ({compagnie.telephone}'),
              ],
            ),
            trailing: PopupMenuButton(
              itemBuilder: (context) => [
                ,
                      const SizedBox(width: 8),
                      const Text('Modifier'),
                    ],
                  ),
                ),
                ,
                      const SizedBox(width: 8),
                      const Text('Agences'),
                    ],
                  ),
                ),
                ,
                      const SizedBox(width: 8),
                      ),
                    ],
                  ),
                ),
              ],
              onSelected: (value) {
                switch (value) {
                  case 'edit':
                    _modifierCompagnie(compagnie);
                    break;
                  case 'agences':
                    _voirAgences(compagnie);
                    break;
                  case 'delete':
                    _supprimerCompagnie(compagnie);
                    break;
                }
              },
            ),
            onTap: () => _voirDetailsCompagnie(compagnie),
          ),
        );
      },
    );
  }

  void _ajouterCompagnie() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ,
      ),
    ).then((_) => _chargerCompagnies());
  }

  void _modifierCompagnie(CompagnieAssurance compagnie) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ModifierCompagnieScreen(compagnie: compagnie),
      ),
    ).then((_) => _chargerCompagnies());
  }

  void _voirAgences(CompagnieAssurance compagnie) {
    Navigator.pushNamed(
      context,
      '/admin/agences',
      arguments: {'compagnieId': compagnie.id},
    );
  }

  void _voirDetailsCompagnie(CompagnieAssurance compagnie) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text("Titre"),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('SIRET', compagnie.siret),
            _buildDetailRow('Adresse', compagnie.adresseSiege),
            _buildDetailRow('Email', compagnie.email),
            _buildDetailRow('Téléphone', compagnie.telephone),
            _buildDetailRow('Statut', compagnie.active ? 'Active' : 'Inactive'),
            _buildDetailRow('Date de création', 
                '${compagnie.dateCreation.day}/${compagnie.dateCreation.month}/${compagnie.dateCreation.year}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return (1),
            ),
          ),
          Expanded(child: const Text(value)),
        ],
      ),
    );
  }

  void _supprimerCompagnie(CompagnieAssurance compagnie) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmer la suppression'),
        content: ({compagnie.nom}" ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implémenter la suppression
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: const Text('Suppression en cours de développement'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }
}

// Écrans d'ajout et modification (à implémenter)
class AjouterCompagnieScreen extends StatelessWidget {
  const Text(\;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Ajouter une Compagnie'),
        backgroundColor: Colors.blue[800],
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: const Text('Formulaire d\'ajout en cours de développement'),
      ),
    );
  }
}

class ModifierCompagnieScreen extends StatelessWidget {
  final CompagnieAssurance compagnie;

  ;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Modifier la Compagnie'),
        backgroundColor: Colors.blue[800],
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: const Text('Formulaire de modification en cours de développement
