import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:constat_tunisie/features/admin/services/system_stats_service.dart';
import 'package:constat_tunisie/features/admin/services/dashboard_filter_service.dart';
import 'package:constat_tunisie/features/admin/services/dashboard_export_service.dart';

/// 📊 Écran Dashboard BI pour Super Admin
class BiDashboardScreen extends StatefulWidget {
  const BiDashboardScreen({Key? key}) ) : super(key: key);

  @override
  State<BiDashboardScreen> createState() => _BiDashboardScreenState();
}

class _BiDashboardScreenState extends State<BiDashboardScreen> {
  Map<String, dynamic> _globalStats = {};
  List<Map<String, dynamic>> _growthData = [];
  bool _isLoading = true;
  String? _error;

  // Variables de filtres
  String _selectedPeriod = 'Ce mois';
  String _selectedRole = 'Tous';
  String _selectedStatus = 'Tous';
  String _selectedCompany = 'Toutes';
  List<String> _availableCompanies = [];
  List<Map<String, dynamic>> _alertBadges = [];

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  /// 📊 Charger toutes les données du dashboard
  Future<void> _loadDashboardData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final results = await Future.wait([
        SystemStatsService.getGlobalStats(),
        SystemStatsService.getUserGrowthStats(days: 30),
      ]);

      setState(() {
        _globalStats = results[0] as Map<String, dynamic>;
        _growthData = results[1] as List<Map<String, dynamic>>;
        _isLoading = false;

        // Générer les badges d'alerte
        _alertBadges = DashboardFilterService.generateAlertBadges(
          stats: _globalStats['activity'] as Map<String, dynamic>? ?? {},
          securityStats: _globalStats['security'] as Map<String, dynamic>? ?? {},
        );

        // Extraire les compagnies disponibles
        _extractAvailableCompanies();
      });

    } catch (e) {
      setState(() {
        _error = 'Erreur lors du chargement des données: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: AppBar(
        title: const Text("Titre"),
          ),
        ),
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF1E293B),
        elevation: 0,
        centerTitle: false,
        actions: [
          Container(
            margin: ,
                decoration: BoxDecoration(
                  color: const Color(0xFF667eea).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ,
                  size: 20,
                ),
              ),
              onPressed: _loadDashboardData,
              tooltip: 'Actualiser',
            ),
          ),
        ],
      ),
      body: _isLoading
          ? _buildLoadingState()
          : _error != null
              ? _buildErrorState()
              : _buildDashboardContent(),
    );
  }

  /// 🔄 État de chargement moderne
  Widget _buildLoadingState() {
    return Container(
      decoration: , Color(0xFFE2E8F0)],
        ),
      ),
      child: ),
              strokeWidth: 3,
            ),
            const SizedBox(height: 24),
            ,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// ❌ État d'erreur moderne
  Widget _buildErrorState() {
    return Container(
      decoration: , Color(0xFFE2E8F0)],
        ),
      ),
      child: (1),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 20,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: .withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ,
                ),
              ),
              const SizedBox(height: 24),
              ,
                ),
              ),
              const SizedBox(height: 8),
              ,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton.const Icon(
                onPressed: _loadDashboardData,
                icon: const Icon(Icons.info),
                label: const Text('Réessayer'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF667eea),
                  foregroundColor: Colors.white,
                  padding: ,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 📊 Contenu principal du dashboard
  Widget _buildDashboardContent() {
    return RefreshIndicator(
      onRefresh: _loadDashboardData,
      color: const Color(0xFF667eea),
      child: CustomScrollView(
        slivers: [
          // En-tête avec gradient
          SliverToBoxAdapter(
            child: _buildModernHeader(),
          ),

          // Filtres compacts
          SliverToBoxAdapter(
            child: _buildCompactFilters(),
          ),

          // Alertes si présentes
          if (_alertBadges.isNotEmpty)
            SliverToBoxAdapter(
              child: _buildModernAlerts(),
            ),

          // KPIs principaux
          SliverToBoxAdapter(
            child: _buildModernKpis(),
          ),

          // Graphiques en grille
          SliverToBoxAdapter(
            child: _buildChartsGrid(),
          ),

          // Espacement final
          const SliverToBoxAdapter(
            child: const SizedBox(height: 32),
          ),
        ],
      ),
    );
  }

  /// 🎨 En-tête moderne avec gradient
  Widget _buildModernHeader() {
    final userStats = _globalStats['users'] as Map<String, dynamic>? ?? {};
    final lastUpdated = _globalStats['lastUpdated'] as String?;

    return Container(
      margin: ,
      decoration: BoxDecoration(
        gradient: , Color(0xFF764ba2)],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF667eea).withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: ,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ,
                    ),
                    ,
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          Row(
            children: [
              Expanded(
                child: _buildHeaderStat(
                  'Total',
                  '${userStats['total'] ?? 0}',
                  Icons.people_rounded,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildHeaderStat(
                  'Actifs',
                  '${userStats['active'] ?? 0}',
                  Icons.person_rounded,
                ),
              ),
            ],
          ),
          if (lastUpdated != null) ...[
            const SizedBox(height: 16),
            Row(
              children: [
                ,
                ),
                const SizedBox(width: 6),
                ({_formatDateTime(lastUpdated)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// 🎛️ Filtres compacts
  Widget _buildCompactFilters() {
    return Container(
      margin: ,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              ,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ,
                  ),
                ),
              ),
              TextButton.const Icon(
                onPressed: _exportData,
                icon: const Icon(Icons.info),
                label: const Text('Export'),
                style: TextButton.styleFrom(
                  foregroundColor: const Color(0xFF667eea),
                  padding: ,
              ),
            ],
          ),
          const SizedBox(height: 16),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildCompactFilter(
                  'Période',
                  _selectedPeriod,
                  Icons.calendar_today_rounded,
                  () => _showPeriodPicker(),
                ),
                const SizedBox(width: 12),
                _buildCompactFilter(
                  'Rôle',
                  _selectedRole,
                  Icons.person_rounded,
                  () => _showRolePicker(),
                ),
                const SizedBox(width: 12),
                _buildCompactFilter(
                  'Statut',
                  _selectedStatus,
                  Icons.flag_rounded,
                  () => _showStatusPicker(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 🏷️ Filtre compact
  Widget _buildCompactFilter(String label, String value, IconData icon, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: .withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: const Color(0xFF667eea).withValues(alpha: 0.2),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(icon, size: 16, color: const Color(0xFF667eea)),
            const SizedBox(width: 6),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  label,
                  style: const TextStyle(
                    fontSize: 10,
                    color: Color(0xFF64748B),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Text(
                  value,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF1E293B),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(width: 4),
            ,
            ),
          ],
        ),
      ),
    );
  }

  /// 📊 Statistique dans l'en-tête
  Widget _buildHeaderStat(String label, String value, IconData icon) {
    return Row(
      children: [
        Container(
          padding: ,
            borderRadius: BorderRadius.circular(8),
          ),
          child: ,
        ),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ,
            ),
            ,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 📅 Formater la date/heure
  String _formatDateTime(String isoString) {
    try {
      final dateTime = DateTime.parse(isoString);
      return '${dateTime.day}/${dateTime.month}/${dateTime.year} à ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return 'Date inconnue';
    }
  }

  /// 🏢 Extraire les compagnies disponibles
  void _extractAvailableCompanies() {
    // TODO: Récupérer les compagnies depuis Firestore
    _availableCompanies = ['STAR Assurance', 'GAT Assurance', 'Maghrebia'];
  }

  /// 🚨 Alertes modernes
  Widget _buildModernAlerts() {
    return Container(
      margin: ,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _alertBadges.map((badge) {
              final level = badge['level'] as String;
              final color = DashboardFilterService.getAlertColor(level);

              return Container(
                padding: ,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: color.withValues(alpha: 0.3)),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(badge['icon'] as IconData, size: 16, color: color),
                    const SizedBox(width: 6),
                    ,
                    ),
                    const SizedBox(width: 4),
                    Container(
                      padding: ,
                      ),
                      child: ({badge['count']}',
                        style: ,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  /// 📊 KPIs modernes
  Widget _buildModernKpis() {
    final userStats = _globalStats['users'] as Map<String, dynamic>? ?? {};
    final activityStats = _globalStats['activity'] as Map<String, dynamic>? ?? {};
    final companyStats = _globalStats['companies'] as Map<String, dynamic>? ?? {};

    return Container(
      margin: ,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.5, // Augmenté pour plus d'espace
        children: [
          _buildModernKpiCard(
            'Utilisateurs Actifs',
            '${userStats['active'] ?? 0}',
            Icons.people_rounded,
            const Color(0xFF10B981),
            subtitle: 'Sur ${userStats['total'] ?? 0} total',
          ),
          _buildModernKpiCard(
            'Compagnies',
            '${companyStats['totalCompanies'] ?? 0}',
            Icons.business_rounded,
            const Color(0xFF3B82F6),
            subtitle: '${companyStats['totalAgencies'] ?? 0} agences',
          ),
          _buildModernKpiCard(
            'Actions 24h',
            '${activityStats['auditLogs24h'] ?? 0}',
            Icons.timeline_rounded,
            const Color(0xFF8B5CF6),
            subtitle: 'Logs d\'audit',
          ),
          _buildModernKpiCard(
            'Emails',
            '${activityStats['emailsSent24h'] ?? 0}',
            Icons.email_rounded,
            const Color(0xFFF59E0B),
            subtitle: '${activityStats['emailSuccessRate'] ?? 100}% succès',
          ),
        ],
      ),
    );
  }

  /// 📊 Carte KPI moderne (sans overflow)
  Widget _buildModernKpiCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    String? subtitle,
  }) {
    return Container(
      padding: ,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Container(
                padding: ,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: const Icon(icon, color: color, size: 16),
              ),
              ,
              Container(
                padding: .withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(3),
                ),
                child: ,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Flexible(
            child: ,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(height: 2),
          Flexible(
            child: ,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 1),
            Flexible(
              child: const Text(
                subtitle,
                style: const TextStyle(
                  fontSize: 9,
                  color: Color(0xFF94A3B8),
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 📈 Grille de graphiques détaillés
  Widget _buildChartsGrid() {
    final userStats = _globalStats['users'] as Map<String, dynamic>? ?? {};
    final roleStats = userStats['byRole'] as Map<String, dynamic>? ?? {};
    final statusStats = userStats['byStatus'] as Map<String, dynamic>? ?? {};
    final activityStats = _globalStats['activity'] as Map<String, dynamic>? ?? {};
    final actionStats = activityStats['actionStats'] as Map<String, dynamic>? ?? {};
    final companyStats = _globalStats['companies'] as Map<String, dynamic>? ?? {};

    return Container(
      margin: ,
            ),
          ),
          const SizedBox(height: 16),

          // Graphique répartition par rôle
          _buildModernChartCard(
            title: 'Répartition par Rôle',
            subtitle: 'Distribution des utilisateurs',
            child: _buildRoleDistributionChart(roleStats),
          ),

          const SizedBox(height: 16),

          // Graphique statuts des comptes
          _buildModernChartCard(
            title: 'Statuts des Comptes',
            subtitle: 'État des utilisateurs',
            child: _buildStatusChart(statusStats),
          ),

          const SizedBox(height: 16),

          // Évolution des inscriptions
          _buildModernChartCard(
            title: 'Évolution des Inscriptions',
            subtitle: 'Nouveaux utilisateurs (30 derniers jours)',
            child: _buildGrowthChart(),
          ),

          const SizedBox(height: 16),

          // Actions administratives
          _buildModernChartCard(
            title: 'Actions Administratives',
            subtitle: 'Dernières 24 heures',
            child: _buildActionsChart(actionStats),
          ),

          const SizedBox(height: 16),

          // Résumé compagnies
          _buildModernSummaryCard(companyStats),
        ],
      ),
    );
  }

  /// 📊 Carte de graphique moderne
  Widget _buildModernChartCard({
    required String title,
    required String subtitle,
    required Widget child,
  }) {
    return Container(
      padding: ,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ,
            ),
          ),
          const SizedBox(height: 4),
          const Text(
            subtitle,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF64748B),
            ),
          ),
          const SizedBox(height: 20),
          const SizedBox(
            height: 200,
            child: child,
          ),
        ],
      ),
    );
  }

  /// 🥧 Graphique répartition par rôle (Pie Chart)
  Widget _buildRoleDistributionChart(Map<String, dynamic> roleStats) {
    if (roleStats.isEmpty) {
      return const Center(
        child: const Text(
          'Aucune donnée disponible',
          style: TextStyle(color: Color(0xFF64748B)),
        ),
      );
    }

    final colors = [
      const Color(0xFF667eea),
      const Color(0xFF764ba2),
      const Color(0xFF10B981),
      const Color(0xFFF59E0B),
      const Color(0xFFEF4444),
      const Color(0xFF8B5CF6),
    ];

    final sections = roleStats.entries.map((entry) {
      final index = roleStats.keys.toList().indexOf(entry.key);
      final color = colors[index % colors.length];

      return PieChartSectionData(
        value: (entry.value as num).toDouble(),
        title: '${entry.value}',
        color: color,
        radius: 60,
        titleStyle: ,
      );
    }).toList();

    return Row(
      children: [
        Expanded(
          flex: 2,
          child: PieChart(
            PieChartData(
              sections: sections,
              centerSpaceRadius: 40,
              sectionsSpace: 2,
            ),
          ),
        ),
        const SizedBox(width: 20),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: roleStats.entries.map((entry) {
              final index = roleStats.keys.toList().indexOf(entry.key);
              final color = colors[index % colors.length];

              return (1),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  /// 📊 Graphique statuts (Bar Chart)
  Widget _buildStatusChart(Map<String, dynamic> statusStats) {
    if (statusStats.isEmpty) {
      return const Center(
        child: const Text(
          'Aucune donnée disponible',
          style: TextStyle(color: Color(0xFF64748B)),
        ),
      );
    }

    final barGroups = statusStats.entries.map((entry) {
      final index = statusStats.keys.toList().indexOf(entry.key);
      Color color;

      switch (entry.key.toLowerCase()) {
        case 'actif':
          color = const Color(0xFF10B981);
          break;
        case 'inactif':
          color = const Color(0xFF6B7280);
          break;
        case 'bloqué':
          color = const Color(0xFFEF4444);
          break;
        default:
          color = const Color(0xFF3B82F6);
      }

      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: (entry.value as num).toDouble(),
            color: color,
            width: 20,
            borderRadius: ,
          ),
        ],
      );
    }).toList();

    return BarChart(
      BarChartData(
        barGroups: barGroups,
        titlesData: FlTitlesData(
          leftTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: true, reservedSize: 40),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                final keys = statusStats.keys.toList();
                if (value.toInt() < keys.length) {
                  return (1)],
                      style: const TextStyle(
                        fontSize: 10,
                        color: Color(0xFF64748B),
                      ),
                    ),
                  );
                }
                 const Text('Contenu');
              },
            ),
          ),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: false),
        gridData: const FlGridData(show: true, drawVerticalLine: false),
      ),
    );
  }

  /// 📈 Graphique de croissance (Line Chart)
  Widget _buildGrowthChart() {
    if (_growthData.isEmpty) {
      return const Center(
        child: const Text(
          'Aucune donnée de croissance disponible',
          style: TextStyle(color: Color(0xFF64748B)),
        ),
      );
    }

    final spots = _growthData.asMap().entries.map((entry) {
      final index = entry.key;
      final data = entry.value;
      final value = (data['count'] as num?)?.toDouble() ?? 0.0;

      return FlSpot(index.toDouble(), value);
    }).toList();

    return LineChart(
      LineChartData(
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true,
            color: const Color(0xFF667eea),
            barWidth: 3,
            dotData: const FlDotData(show: true),
            belowBarData: BarAreaData(
              show: true,
              color: const Color(0xFF667eea).withValues(alpha: 0.1),
            ),
          ),
        ],
        titlesData: FlTitlesData(
          leftTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: true, reservedSize: 40),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                if (value.toInt() < _growthData.length) {
                  final data = _growthData[value.toInt()];
                  final date = data['date'] as String? ?? 'Contenu';
                  if (date.isNotEmpty) {
                    try {
                      final dateTime = DateTime.parse(date);
                      return ({dateTime.month}',
                        style: const TextStyle(
                          fontSize: 10,
                          color: Color(0xFF64748B),
                        ),
                      );
                    } catch (e) {
                       ,
                        style: const TextStyle(
                          fontSize: 10,
                          color: Color(0xFF64748B),
                        ),
                      );
                    }
                  }
                }
                 const Text('Contenu');
              },
            ),
          ),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: false),
        gridData: const FlGridData(show: true, drawVerticalLine: false),
      ),
    );
  }

  /// ⚡ Graphique actions administratives
  Widget _buildActionsChart(Map<String, dynamic> actionStats) {
    if (actionStats.isEmpty) {
      return const Center(
        child: const Text(
          'Aucune action administrative récente',
          style: TextStyle(color: Color(0xFF64748B)),
        ),
      );
    }

    // Prendre les 5 actions les plus fréquentes
    final sortedActions = actionStats.entries.toList()
      ..sort((a, b) => (b.value as num).compareTo(a.value as num));

    final topActions = sortedActions.take(5).toList();

    final barGroups = topActions.asMap().entries.map((entry) {
      final index = entry.key;
      final actionEntry = entry.value;

      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: (actionEntry.value as num).toDouble(),
            color: const Color(0xFF8B5CF6),
            width: 25,
            borderRadius: ,
          ),
        ],
      );
    }).toList();

    return BarChart(
      BarChartData(
        barGroups: barGroups,
        titlesData: FlTitlesData(
          leftTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: true, reservedSize: 40),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                if (value.toInt() < topActions.length) {
                  final action = topActions[value.toInt()].key;
                  final displayName = action.replaceAll('_', ' ');
                  return (1)}...'
                          : displayName,
                      style: const TextStyle(
                        fontSize: 9,
                        color: Color(0xFF64748B),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  );
                }
                 const Text('Contenu');
              },
            ),
          ),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: false),
        gridData: const FlGridData(show: true, drawVerticalLine: false),
      ),
    );
  }

  /// 📋 Carte résumé moderne
  Widget _buildModernSummaryCard(Map<String, dynamic> companyStats) {
    return Container(
      padding: , Color(0xFF059669)],
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF10B981).withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              ,
              const SizedBox(width: 12),
              ,
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: _buildSummaryItem(
                  'Compagnies',
                  '${companyStats['totalCompanies'] ?? 0}',
                  Icons.domain_rounded,
                ),
              ),
              Expanded(
                child: _buildSummaryItem(
                  'Agences',
                  '${companyStats['totalAgencies'] ?? 0}',
                  Icons.location_city_rounded,
                ),
              ),
              Expanded(
                child: _buildSummaryItem(
                  'Moyenne',
                  '${companyStats['averageAgenciesPerCompany'] ?? 0}',
                  Icons.analytics_rounded,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 📊 Élément de résumé
  Widget _buildSummaryItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Container(
          padding: ,
            borderRadius: BorderRadius.circular(8),
          ),
          child: ,
        ),
        const SizedBox(height: 8),
        ,
        ),
        const SizedBox(height: 4),
        ,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// 🎛️ Sélecteurs de filtres
  void _showPeriodPicker() {
    showModalBottomSheet(
      context: context,
      builder: (context) => _buildPickerSheet(
        'Sélectionner une période',
        DashboardFilterService.predefinedPeriods.keys.toList(),
        _selectedPeriod,
        _onPeriodChanged,
      ),
    );
  }

  void _showRolePicker() {
    showModalBottomSheet(
      context: context,
      builder: (context) => _buildPickerSheet(
        'Sélectionner un rôle',
        DashboardFilterService.filterTypes,
        _selectedRole,
        _onRoleChanged,
      ),
    );
  }

  void _showStatusPicker() {
    showModalBottomSheet(
      context: context,
      builder: (context) => _buildPickerSheet(
        'Sélectionner un statut',
        DashboardFilterService.statusFilters,
        _selectedStatus,
        _onStatusChanged,
      ),
    );
  }

  /// 📋 Sheet de sélection
  Widget _buildPickerSheet(
    String title,
    List<String> options,
    String selected,
    Function(String) onSelected,
  ) {
    return Container(
      padding: ,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ,
            ),
          ),
          const SizedBox(height: 16),
          ...options.map((option) {
            final isSelected = option == selected;
            return ListTile(
              title: const Text(option),
              trailing: isSelected ? ) : null,
              onTap: () {
                onSelected(option);
                Navigator.pop(context);
              },
            );
          }).toList(),
        ],
      ),
    );
  }

  /// 📄 Exporter les données
  void _exportData() async {
    try {
      await DashboardExportService.exportToPdf(
        globalStats: _globalStats,
        growthData: _growthData,
        period: _selectedPeriod,
        context: context,
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e
