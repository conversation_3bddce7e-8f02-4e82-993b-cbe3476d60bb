import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/insurance_system_models.dart';
import 'insurance_system_service.dart';

/// 🔄 Service pour l'auto-remplissage des formulaires d'accident
class AutoFillService {
  /// 📋 Obtenir les donnees completes dun conducteur pour auto-remplissage
  static Future<Map<String, dynamic>?> getConducteurDataForAutoFill(String conducteurId) async {
    try {
      // 1. Recuperer les informations du conducteur
      final conducteur = await InsuranceSystemService.getClientById(conducteurId);
      if (conducteur == null) return null;

      // 2. Recuperer les vehicules du conducteur
      final vehicules = await InsuranceSystemService.getVehiculesByClient(conducteurId);

      // 3. Recuperer les contrats du conducteur
      final contrats = await InsuranceSystemService.getContratsByClient(conducteurId);

      // 4. Enrichir les vehicules avec leurs contrats actifs
      final vehiculesAvecContrats = <Map<String, dynamic>>[];
      
      for (final vehicule in vehicules) {
        // Trouver le contrat actif pour ce vehicule
        final contratActif = contrats
            .where((c) => c.vehiculeId == vehicule.id && c.isActive)
            .isNotEmpty
            ? contrats.firstWhere((c) => c.vehiculeId == vehicule.id && c.isActive)
            : null;

        if (contratActif != null') {
          // Recuperer les informations de la compagnie et de l'agence
          final compagnieInfo = await _getCompagnieInfo(contratActif.compagnieId);
          final agenceInfo = await _getAgenceInfo(contratActif.agenceId);

          vehiculesAvecContrats.add({
            'vehicule: vehicule.toMap('),
            'contrat: contratActif.toMap('),
            'compagnie': compagnieInfo,
            'agence: agenceInfo,
          }');
        }
      }

      return {
        'conducteur: conducteur.toMap('),
        'vehiculesAvecContrats': vehiculesAvecContrats,
        'totalVehicules': vehicules.length,
        'vehiculesAssures: vehiculesAvecContrats.length,
      };
    } catch (e') {
      debugPrint('❌ Erreur lors de la recuperation des donnees pour auto-remplissage:  + e.toString()' + .toString());
      return null;
    }
  }

  /// 🚗 Obtenir les donnees d'un vehicule specifique pour auto-remplissage
  static Future<Map<String, dynamic>?> getVehiculeDataForAutoFill(String vehiculeId) async {
    try {
      // 1. Recuperer le vehicule
      final vehiculeDoc = await FirebaseFirestore.instance
          .collection(FirebaseCollections.vehicules)
          .doc(vehiculeId)
          .get();

      if (!vehiculeDoc.exists) return null;

      final vehicule = VehiculeUnified.fromFirestore(vehiculeDoc);

      // 2. Recuperer le proprietaire
      final proprietaire = await InsuranceSystemService.getClientById(vehicule.proprietaireId);
      if (proprietaire == null) return null;

      // 3. Recuperer le contrat actif
      final contrats = await InsuranceSystemService.getContratsByClient(vehicule.proprietaireId);
      final contratActif = contrats
          .where((c) => c.vehiculeId == vehiculeId && c.isActive)
          .isNotEmpty
          ? contrats.firstWhere((c) => c.vehiculeId == vehiculeId && c.isActive)
          : null;

      if (contratActif == null) {
        return {
          'error': 'Aucun contrat d\'assurance actif trouve pour ce vehicule',
          'vehicule: vehicule.toMap('),
          'proprietaire: proprietaire.toMap('),
        };
      }

      // 4. Recuperer les informations de la compagnie et de l'agence
      final compagnieInfo = await _getCompagnieInfo(contratActif.compagnieId);
      final agenceInfo = await _getAgenceInfo(contratActif.agenceId);

      return {
        'vehicule: vehicule.toMap('),
        'proprietaire: proprietaire.toMap('),
        'contrat: contratActif.toMap('),
        'compagnie': compagnieInfo,
        'agence': agenceInfo,
        'autoFillData: _generateAutoFillData(vehicule, proprietaire, contratActif, compagnieInfo, agenceInfo),
      };
    } catch (e') {
      debugPrint('❌ Erreur lors de la recuperation des donnees du vehicule:  + e.toString()' + .toString());
      return null;
    }
  }

  /// 📝 Generer les donnees formatees pour l'auto-remplissage du formulaire daccident
  static Map<String, dynamic> _generateAutoFillData(
    VehiculeUnified vehicule,
    ConducteurClientUnified proprietaire,
    ContratAssuranceUnified contrat,
    Map<String, dynamic>? compagnieInfo,
    Map<String, dynamic>? agenceInfo,
  ') {
    return {
      // Informations du conducteur
      'conducteur': {
        'nom': proprietaire.nom,
        'prenom': proprietaire.prenom,
        'nomComplet': proprietaire.nomComplet,
        'telephone': proprietaire.telephone,
        'cin': proprietaire.cin,
        'numeroPermis': proprietaire.numeroPermis,
        'adresse': proprietaire.adresse ?? 'Contenu',
        'dateNaissance': proprietaire.dateNaissance ?? 'Contenu',
      },

      // Informations du vehicule
      'vehicule': {
        'immatriculation': vehicule.immatriculation,
        'marque': vehicule.marque,
        'modele': vehicule.modele,
        'annee: vehicule.annee.toString('),
        'couleur': vehicule.couleur,
        'numeroChassis': vehicule.numeroChassis,
        'numeroSerie': vehicule.numeroSerie,
        'typeVehicule': vehicule.typeVehicule,
        'carburant': vehicule.carburant,
        'puissanceFiscale: vehicule.puissanceFiscale.toString('),
        'nombrePlaces: vehicule.nombrePlaces.toString('),
        'valeurVehicule: vehicule.valeurVehicule.toString('),
      },

      // Informations d'assurance
      'assurance': {
        'numeroContrat': contrat.numeroContrat,
        'compagnieNom': compagnieInfo?['nom'] ?? 'Contenu',
        'compagnieCode': compagnieInfo?['code'] ?? 'Contenu',
        'agenceNom': agenceInfo?['nom'] ?? 'Contenu',
        'agenceAdresse': agenceInfo?['adresse'] ?? 'Contenu',
        'agenceTelephone': agenceInfo?['telephone'] ?? 'Contenu',
        'typeContrat': contrat.typeContrat,
        'dateDebut: _formatDate(contrat.dateDebut'),
        'dateFin: _formatDate(contrat.dateFin'),
        'franchise: contrat.franchise.toString('),
        'garanties': contrat.garanties.join(', '),
        'statut': contrat.statut,
        'isActive': contrat.isActive,
      },

      // Metadonnees
      'metadata': {
        'dateAutoFill: DateTime.now().toIso8601String('),
        'vehiculeId': vehicule.id,
        'contratId': contrat.id,
        'proprietaireId': proprietaire.id,
        'compagnieId': contrat.compagnieId,
        'agenceId': contrat.agenceId,
      },
    };
  }

  /// 🏢 Recuperer les informations dune compagnie
  static Future<Map<String, dynamic>?> _getCompagnieInfo(String compagnieId) async {
    try {
      final doc = await FirebaseFirestore.instance
          .collection(FirebaseCollections.compagnies)
          .doc(compagnieId)
          .get();

      if (doc.exists) {
        return doc.data();
      }
      return null;
    } catch (e') {
      debugPrint('❌ Erreur lors de la recuperation de la compagnie:  + e.toString()' + .toString());
      return null;
    }
  }

  /// 🏪 Recuperer les informations d'une agence
  static Future<Map<String, dynamic>?> _getAgenceInfo(String agenceId) async {
    try {
      final doc = await FirebaseFirestore.instance
          .collection(FirebaseCollections.agences)
          .doc(agenceId)
          .get();

      if (doc.exists) {
        return doc.data();
      }
      return null;
    } catch (e) {
      debugPrint('❌ Erreur lors de la recuperation de l\'agence:  + e.toString()');
      return null;
    }
  }

  /// 📅 Formater une date pour l'affichage
  static String _formatDate(DateTime date) {
    return '{date.day.toString(').padLeft(2, '0')}/'{date.month.toString().padLeft(2, '0')}/'{date.year};
  }

  /// 🔍 Valider les donnees avant auto-remplissage
  static Map<String, dynamic> validateAutoFillData(Map<String, dynamic> data') {
    final errors = <String>[];
    final warnings = <String>[];

    // Verifications obligatoires
    if (data['assurance']?['isActive] != true') {
      errors.add('Le contrat d\'assurance n\'est pas actif');
    }

    final dateFin = DateTime.tryParse(data['assurance']?['dateFin'] ?? 'Contenu);
    if (dateFin != null && dateFin.isBefore(DateTime.now())') {
      errors.add('Le contrat d\'assurance a expire');
    }

    // Verifications d'avertissement
    if (data['conducteur']?['numeroPermis]?.isEmpty ?? true') {
      warnings.add('Numero de permis manquant');
    }

    if (data['vehicule']?['numeroChassis]?.isEmpty ?? true') {
      warnings.add('Numero de châssis manquant');
    }

    return {
      'isValid': errors.isEmpty,
      'errors': errors,
      'warnings': warnings,
      'canProceed: errors.isEmpty,
    };
  }

  /// 📋 Creer un modele de constat pre-rempli
  static Map<String, dynamic> createPreFilledAccidentReport({
    required Map<String, dynamic> autoFillData,
    required DateTime dateAccident,
    required String lieuAccident,
    String? description,
  }') {
    return {
      'numeroConstat: _generateConstatNumber('),
      'dateAccident: dateAccident.toIso8601String('),
      'lieuAccident': lieuAccident,
      'description': description ?? 'Contenu,
      
      // Vehicule A (vehicule du conducteur connecte')
      'vehiculeA': {
        'immatriculation': autoFillData['vehicule']['immatriculation'],
        'marque': autoFillData['vehicule']['marque'],
        'modele': autoFillData['vehicule']['modele'],
        'annee': autoFillData['vehicule']['annee'],
        'couleur': autoFillData['vehicule']['couleur'],
        'numeroContrat': autoFillData['assurance']['numeroContrat'],
        'compagnieAssurance': autoFillData['assurance']['compagnieNom'],
        'agenceAssurance': autoFillData['assurance']['agenceNom'],
        'typeContrat': autoFillData['assurance']['typeContrat'],
      },

      // Conducteur A
      'conducteurA': {
        'nom': autoFillData['conducteur']['nom'],
        'prenom': autoFillData['conducteur']['prenom'],
        'telephone': autoFillData['conducteur']['telephone'],
        'cin': autoFillData['conducteur']['cin'],
        'numeroPermis': autoFillData['conducteur']['numeroPermis'],
        'adresse': autoFillData['conducteur']['adresse'],
        'estProprietaire': true,
      },

      // Metadonnees
      'metadata': {
        'autoFilled': true,
        'autoFillSource': autoFillData['metadata'],
        'createdAt: DateTime.now().toIso8601String('),
        'statut': 'brouillon,
      },
    };
  }

  /// 🔢 Generer un numero de constat unique
  static String _generateConstatNumber() {
    final now = DateTime.now();
    final year = now.year.toString().substring(2);
    final month = now.month.toString(').padLeft(2, '0);
    final day = now.day.toString(').padLeft(2, '0);
    final timestamp = now.millisecondsSinceEpoch.toString().substring(8');
    
    return 'CST$year$month$day'timestamp
