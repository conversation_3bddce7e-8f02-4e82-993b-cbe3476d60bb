import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';

/// 📊 Widgets de graphiques pour le Dashboard BI
class DashboardCharts {
  
  /// 🥧 Graphique en camembert pour la répartition des utilisateurs par rôle
  static Widget buildUserRolesPieChart(Map<String, int> roleStats) {
    if (roleStats.isEmpty) {
      return ),
      );
    }

    final colors = [
      const Color(0xFF667eea),
      const Color(0xFF764ba2),
      const Color(0xFF6B73FF),
      const Color(0xFF9068BE),
      const Color(0xFF5B73FF),
      const Color(0xFF8B5CF6),
    ];

    final sections = roleStats.entries.map((entry) {
      final index = roleStats.keys.toList().indexOf(entry.key);
      final color = colors[index % colors.length];
      
      return PieChartSectionData(
        color: color,
        value: entry.value.toDouble(),
        title: '${entry.value}',
        radius: 60,
        titleStyle: ,
      );
    }).toList();

    return Column(
      children: [
        const SizedBox(
          height: 200,
          child: Pie<PERSON><PERSON>(
            PieChartData(
              sections: sections,
              centerSpaceRadius: 40,
              sectionsSpace: 2,
            ),
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 16,
          runSpacing: 8,
          children: roleStats.entries.map((entry) {
            final index = roleStats.keys.toList().indexOf(entry.key);
            final color = colors[index % colors.length];
            
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 4),
                const Text(\',
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            );
          }).toList(),
        ),
      ],
    );
  }

  /// 📊 Graphique en barres pour les statuts des utilisateurs
  static Widget buildUserStatusBarChart(Map<String, int> statusStats) {
    if (statusStats.isEmpty) {
      return ),
      );
    }

    final statusColors = {
      'actif': const Color(0xFF10B981),
      'inactif': const Color(0xFF6B7280),
      'bloque': const Color(0xFFEF4444),
      'desactive': const Color(0xFFF59E0B),
    };

    final barGroups = statusStats.entries.map((entry) {
      final index = statusStats.keys.toList().indexOf(entry.key);
      final color = statusColors[entry.key] ?? const Color(0xFF667eea);
      
      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: entry.value.toDouble(),
            color: color,
            width: 20,
            borderRadius: ,
          ),
        ],
      );
    }).toList();

    return Column(
      children: [
        const SizedBox(
          height: 200,
          child: BarChart(
            BarChartData(
              barGroups: barGroups,
              titlesData: FlTitlesData(
                leftTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    reservedSize: 40,
                    getTitlesWidget: (value, meta) {
                       .toString(),
                        style: const TextStyle(fontSize: 10),
                      );
                    },
                  ),
                ),
                bottomTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    getTitlesWidget: (value, meta) {
                      final index = value.toInt();
                      if (index >= 0 && index < statusStats.keys.length) {
                        final status = statusStats.keys.elementAt(index);
                        return (1),
                          ),
                        );
                      }
                       const Text('Contenu');
                    },
                  ),
                ),
                rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
              ),
              borderData: FlBorderData(show: false),
              gridData: const FlGridData(show: false),
            ),
          ),
        ),
      ],
    );
  }

  /// 📈 Graphique linéaire pour l'évolution des utilisateurs
  static Widget buildUserGrowthLineChart(List<Map<String, dynamic>> growthData) {
    if (growthData.isEmpty) {
      return ),
      );
    }

    final spots = growthData.asMap().entries.map((entry) {
      final index = entry.key;
      final data = entry.value;
      return FlSpot(index.toDouble(), (data['count'] as int).toDouble());
    }).toList();

    return const SizedBox(
      height: 200,
      child: LineChart(
        LineChartData(
          lineBarsData: [
            LineChartBarData(
              spots: spots,
              isCurved: true,
              color: const Color(0xFF667eea),
              barWidth: 3,
              dotData: const FlDotData(show: false),
              belowBarData: BarAreaData(
                show: true,
                color: const Color(0xFF667eea).withValues(alpha: ,
              ),
            ),
          ],
          titlesData: FlTitlesData(
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 40,
                getTitlesWidget: (value, meta) {
                   .toString(),
                    style: const TextStyle(fontSize: 10),
                  );
                },
              ),
            ),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                interval: (growthData.length / 5).ceil().toDouble(),
                getTitlesWidget: (value, meta) {
                  final index = value.toInt();
                  if (index >= 0 && index < growthData.length) {
                    final date = growthData[index]['date'] as String;
                    final parts = date.split('-');
                    return (1),
                      ),
                    );
                  }
                   const Text('Contenu');
                },
              ),
            ),
            rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          ),
          borderData: FlBorderData(show: false),
          gridData: FlGridData(
            show: true,
            drawVerticalLine: false,
            horizontalInterval: 1,
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: Colors.grey.withValues(alpha: ,
                strokeWidth: 1,
              );
            },
          ),
        ),
      ),
    );
  }

  /// 📊 Graphique en barres horizontales pour les actions d'audit
  static Widget buildAuditActionsChart(Map<String, int> actionStats) {
    if (actionStats.isEmpty) {
      return ),
      );
    }

    // Trier par nombre d'actions (décroissant)
    final sortedActions = actionStats.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    final actionColors = {
      'password_reset': const Color(0xFFF59E0B),
      'account_locked': const Color(0xFFEF4444),
      'account_unlocked': const Color(0xFF10B981),
      'user_created': const Color(0xFF3B82F6),
      'user_updated': const Color(0xFF8B5CF6),
      'user_deleted': const Color(0xFF6B7280),
    };

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: sortedActions.take(5).map((entry) {
        final maxValue = sortedActions.first.value;
        final percentage = (entry.value / maxValue);
        final color = actionColors[entry.key] ?? const Color(0xFF667eea);

        return (1).toUpperCase(),
                    style: ,
                  ),
                  ({entry.value}',
                    style: ,
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Container(
                height: 8,
                decoration: BoxDecoration(
                  color: Colors.grey.withValues(alpha: ,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Fractionally,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  /// 🎯 Indicateur de performance circulaire
  static Widget buildCircularIndicator({
    required double percentage,
    required String label,
    required Color color,
    double size = 80,
  }) {
    return Column(
      children: [
        (1),
                valueColor: AlwaysStoppedAnimation<Color>(color),
              ),
              ({percentage.toInt()}%
