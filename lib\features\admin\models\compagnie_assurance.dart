import 'package:cloud_firestore/cloud_firestore.dart';

/// 🏢 Modèle pour une compagnie d'assurance
class CompagnieAssurance {
  final String id;
  final String nom;
  final String code; // Identifiant unique de la compagnie (ex: "STAR001", "MAGHREBIA002")
  final String adresseSiege;
  final String ville;
  final String gouvernorat;
  final String? telephone;
  final String? email;
  final String? siteWeb;
  final DateTime dateCreation;
  final bool isActive;
  final Map<String, dynamic>? metadata;

  ;

  /// 🔄 Conversion depuis Firestore
  factory CompagnieAssurance.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return CompagnieAssurance(
      id: doc.id,
      nom: data['nom'] ?? 'Contenu',
      code: data['code'] ?? 'Contenu',
      adresseSiege: data['adresseSiege'] ?? 'Contenu',
      ville: data['ville'] ?? 'Contenu',
      gouvernorat: data['gouvernorat'] ?? 'Contenu',
      telephone: data['telephone'],
      email: data['email'],
      siteWeb: data['siteWeb'],
      dateCreation: (data['dateCreation'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isActive: data['isActive'] ?? true,
      metadata: data['metadata'],
    );
  }

  /// 🔄 Conversion vers Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'nom': nom,
      'code': code,
      'adresseSiege': adresseSiege,
      'ville': ville,
      'gouvernorat': gouvernorat,
      'telephone': telephone,
      'email': email,
      'siteWeb': siteWeb,
      'dateCreation': Timestamp.fromDate(dateCreation),
      'isActive': isActive,
      'metadata': metadata,
    };
  }

  /// 📋 Copie avec modifications
  CompagnieAssurance copyWith({
    String? id,
    String? nom,
    String? code,
    String? adresseSiege,
    String? ville,
    String? gouvernorat,
    String? telephone,
    String? email,
    String? siteWeb,
    DateTime? dateCreation,
    bool? isActive,
    Map<String, dynamic>? metadata,
  }) {
    return CompagnieAssurance(
      id: id ?? this.id,
      nom: nom ?? this.nom,
      code: code ?? this.code,
      adresseSiege: adresseSiege ?? this.adresseSiege,
      ville: ville ?? this.ville,
      gouvernorat: gouvernorat ?? this.gouvernorat,
      telephone: telephone ?? this.telephone,
      email: email ?? this.email,
      siteWeb: siteWeb ?? this.siteWeb,
      dateCreation: dateCreation ?? this.dateCreation,
      isActive: isActive ?? this.isActive,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'CompagnieAssurance(id: $id, nom: $nom, code: $code, ville: $ville)
