import 'package:cloud_firestore/cloud_firestore.dart';

/// 🚗 Modèle pour un véhicule assuré
class VehiculeAssure {
  final String id;
  final String immatriculation;
  final String marque;
  final String modele;
  final int annee;
  final String couleur;
  final String numeroSerie; // Numéro de châssis
  final String conducteurId; // 🔗 Propriétaire du véhicule
  final String? contractId; // 🔗 Contrat d'assurance actuel
  final String? compagnieId; // 🔗 Compagnie d'assurance
  final String? agenceId; // 🔗 Agence d'assurance
  final DateTime dateCreation;
  final bool isActive;
  final Map<String, dynamic>? metadata;

  ;

  /// 🔄 Conversion depuis Firestore
  factory VehiculeAssure.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return VehiculeAssure(
      id: doc.id,
      immatriculation: data['immatriculation'] ?? 'Contenu',
      marque: data['marque'] ?? 'Contenu',
      modele: data['modele'] ?? 'Contenu',
      annee: data['annee'] ?? 0,
      couleur: data['couleur'] ?? 'Contenu',
      numeroSerie: data['numeroSerie'] ?? 'Contenu',
      conducteurId: data['conducteurId'] ?? 'Contenu',
      contractId: data['contractId'],
      compagnieId: data['compagnieId'],
      agenceId: data['agenceId'],
      dateCreation: (data['dateCreation'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isActive: data['isActive'] ?? true,
      metadata: data['metadata'],
    );
  }

  /// 🔄 Conversion vers Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'immatriculation': immatriculation,
      'marque': marque,
      'modele': modele,
      'annee': annee,
      'couleur': couleur,
      'numeroSerie': numeroSerie,
      'conducteurId': conducteurId,
      'contractId': contractId,
      'compagnieId': compagnieId,
      'agenceId': agenceId,
      'dateCreation': Timestamp.fromDate(dateCreation),
      'isActive': isActive,
      'metadata': metadata,
    };
  }

  /// 🎯 Vérifications d'état
  bool get hasContract => contractId != null && contractId!.isNotEmpty;
  bool get isInsured => hasContract && compagnieId != null;

  /// 📋 Copie avec modifications
  VehiculeAssure copyWith({
    String? id,
    String? immatriculation,
    String? marque,
    String? modele,
    int? annee,
    String? couleur,
    String? numeroSerie,
    String? conducteurId,
    String? contractId,
    String? compagnieId,
    String? agenceId,
    DateTime? dateCreation,
    bool? isActive,
    Map<String, dynamic>? metadata,
  }) {
    return VehiculeAssure(
      id: id ?? this.id,
      immatriculation: immatriculation ?? this.immatriculation,
      marque: marque ?? this.marque,
      modele: modele ?? this.modele,
      annee: annee ?? this.annee,
      couleur: couleur ?? this.couleur,
      numeroSerie: numeroSerie ?? this.numeroSerie,
      conducteurId: conducteurId ?? this.conducteurId,
      contractId: contractId ?? this.contractId,
      compagnieId: compagnieId ?? this.compagnieId,
      agenceId: agenceId ?? this.agenceId,
      dateCreation: dateCreation ?? this.dateCreation,
      isActive: isActive ?? this.isActive,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'VehiculeAssure(id: $id, immatriculation: $immatriculation, marque: $marque, modele: $modele)
