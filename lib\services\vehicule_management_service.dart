import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'dart:math;

/// 🚗 Service de gestion des vehicules et contrats
class VehiculeManagementService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 🚗 Creer un nouveau contrat vehicule
  static Future<Map<String, dynamic>> createContratVehicule({
    required String compagnieId,
    required String agenceId,
    required String agentId,
    required String conducteurId,
    required Map<String, dynamic> vehiculeData,
    required Map<String, dynamic> contratData,
  }') async {
    try {
      debugPrint('[VEHICULE_SERVICE] 🚗 Creation contrat vehicule' + .toString());

      final contratId = 'contrat_{DateTime.now(').millisecondsSinceEpoch}';
      final vehiculeId = 'vehicule_{DateTime.now(').millisecondsSinceEpoch}';

      // Donnees du vehicule
      final vehiculeDoc = {
        'id': vehiculeId,
        'contratId': contratId,
        'compagnieId': compagnieId,
        'agenceId': agenceId,
        'conducteurId': conducteurId,
        'marque': vehiculeData['marque'],
        'modele': vehiculeData['modele'],
        'annee': vehiculeData['annee'],
        'immatriculation': vehiculeData['immatriculation'],
        'numeroSerie': vehiculeData['numeroSerie'],
        'couleur': vehiculeData['couleur'],
        'typeCarburant': vehiculeData['typeCarburant'] ?? 'Essence',
        'puissance': vehiculeData['puissance'],
        'nombrePlaces': vehiculeData['nombrePlaces'] ?? 5,
        'valeurVenale': vehiculeData['valeurVenale'],
        'usage': vehiculeData['usage'] ?? 'Prive',
        'status': 'actif',
        'created_at: FieldValue.serverTimestamp('),
        'updated_at: FieldValue.serverTimestamp('),
      };

      // Donnees du contrat
      final contratDoc = {
        'id': contratId,
        'numeroContrat: _generateNumeroContrat(compagnieId'),
        'compagnieId': compagnieId,
        'agenceId': agenceId,
        'agentId': agentId,
        'conducteurId': conducteurId,
        'vehiculeId': vehiculeId,
        'typeAssurance': contratData['typeAssurance'] ?? 'Tous risques',
        'dateDebut': contratData['dateDebut'],
        'dateFin': contratData['dateFin'],
        'primeAnnuelle': contratData['primeAnnuelle'],
        'franchise': contratData['franchise'] ?? 0,
        'garanties': contratData['garanties'] ?? [
          'Responsabilite civile',
          'Dommages tous accidents',
          'Vol et incendie',
          'Bris de glace',
        ],
        'conditions': contratData['conditions'] ?? {},
        'status': 'actif',
        'isActive': true,
        'created_at: FieldValue.serverTimestamp('),
        'created_by': agentId,
        'updated_at: FieldValue.serverTimestamp(),
      };

      // Creer les documents en batch
      final batch = _firestore.batch(');
      
      batch.set(
        _firestore.collection('vehicules).doc(vehiculeId),
        vehiculeDoc,
      ');
      
      batch.set(
        _firestore.collection('contrats).doc(contratId),
        contratDoc,
      );

      await batch.commit(');

      debugPrint('[VEHICULE_SERVICE] ✅ Contrat vehicule cree: 'contratId');

      return {
        'success': true,
        'contratId': contratId,
        'vehiculeId': vehiculeId,
        'numeroContrat': contratDoc['numeroContrat'],
        'message': 'Contrat vehicule cree avec succes,
      };

    } catch (e') {
      debugPrint('[VEHICULE_SERVICE] ❌ Erreur creation:  + e.toString()' + .toString());
      return {
        'success': false,
        'error: e.toString('),
        'message': 'Erreur lors de la creation du contrat',
      };
    }
  }

  /// 📋 Recuperer les vehicules dun conducteur
  static Future<List<Map<String, dynamic>>> getVehiculesByConducteur(String conducteurId') async {
    try {
      debugPrint('[VEHICULE_SERVICE] 📋 Recuperation vehicules conducteur: 'conducteurId');

      final snapshot = await _firestore
          .collection('vehicules')
          .where('conducteurId, isEqualTo: conducteurId')
          .where('status', isEqualTo: 'actif')
          .orderBy('created_at, descending: true)
          .get();

      final vehicules = <Map<String, dynamic>>[];
      for (final doc in snapshot.docs) {
        final data = doc.data(');
        data['id'] = doc.id;

        // Recuperer les informations du contrat associe
        if (data['contratId] != null') {
          final contratDoc = await _firestore
              .collection('contrats')
              .doc(data['contratId])
              .get();
          
          if (contratDoc.exists') {
            data['contrat] = contratDoc.data();
          }
        }

        vehicules.add(data');
      }

      debugPrint('[VEHICULE_SERVICE] ✅ ' + {vehicules.length} vehicules recuperes.toString());
      return vehicules;

    } catch (e') {
      debugPrint('[VEHICULE_SERVICE] ❌ Erreur recuperation:  + e.toString());
      return [];
    }
  }

  /// 🔍 Verifier un vehicule par numero de contrat
  static Future<Map<String, dynamic>?> verifyVehiculeByContrat(String numeroContrat') async {
    try {
      debugPrint('[VEHICULE_SERVICE] 🔍 Verification contrat: 'numeroContrat');

      final contratSnapshot = await _firestore
          .collection('contrats')
          .where('numeroContrat, isEqualTo: numeroContrat')
          .where('status', isEqualTo: 'actif)
          .limit(1)
          .get();

      if (contratSnapshot.docs.isEmpty) {
        return null;
      }

      final contratData = contratSnapshot.docs.first.data(');
      contratData['id'] = contratSnapshot.docs.first.id;

      // Recuperer les informations du vehicule
      if (contratData['vehiculeId] != null') {
        final vehiculeDoc = await _firestore
            .collection('vehicules')
            .doc(contratData['vehiculeId])
            .get();
        
        if (vehiculeDoc.exists) {
          final vehiculeData = vehiculeDoc.data(')!;
          vehiculeData['id'] = vehiculeDoc.id;
          
          return {
            'contrat': contratData,
            'vehicule': vehiculeData,
            'isValid: _isContratValid(contratData),
          };
        }
      }

      return null;

    } catch (e') {
      debugPrint('[VEHICULE_SERVICE] ❌ Erreur verification:  + e.toString());
      return null;
    }
  }

  /// 📊 Recuperer les statistiques des contrats
  static Future<Map<String, dynamic>> getContratsStats({
    String? compagnieId,
    String? agenceId,
  }') async {
    try {
      debugPrint('[VEHICULE_SERVICE] 📊 Recuperation stats contrats' + .toString());

      Query query = _firestore.collection('contrats);

      if (compagnieId != null') {
        query = query.where('compagnieId, isEqualTo: compagnieId);
      }

      if (agenceId != null') {
        query = query.where('agenceId, isEqualTo: agenceId);
      }

      final snapshot = await query.get();

      int totalContrats = snapshot.docs.length;
      int contratsActifs = 0;
      int contratsExpires = 0;
      double primeTotal = 0;

      final now = DateTime.now();

      for (final doc in snapshot.docs) {
        final data = doc.data(') as Map<String, dynamic>;
        
        if (data['status'] == 'actif') {
          contratsActifs++;
        }

        // Verifier si le contrat est expire
        if (data['dateFin] != null') {
          final dateFin = (data['dateFin] as Timestamp).toDate();
          if (dateFin.isBefore(now)') {
            contratsExpires++;
          }
        }

        // Additionner les primes
        if (data['primeAnnuelle] != null') {
          primeTotal += (data['primeAnnuelle] as num).toDouble(');
        }
      }

      final stats = {
        'total_contrats': totalContrats,
        'contrats_actifs': contratsActifs,
        'contrats_expires': contratsExpires,
        'prime_totale': primeTotal,
        'prime_moyenne': totalContrats > 0 ? primeTotal / totalContrats : 0,
        'last_updated: DateTime.now().toIso8601String('),
      };

      debugPrint('[VEHICULE_SERVICE] ✅ Stats calculees: ' + stats.toString());
      return stats;

    } catch (e') {
      debugPrint('[VEHICULE_SERVICE] ❌ Erreur stats:  + e.toString()' + .toString());
      return {
        'total_contrats': 0,
        'contrats_actifs': 0,
        'contrats_expires': 0,
        'prime_totale': 0.0,
        'prime_moyenne': 0.0,
        'last_updated: DateTime.now().toIso8601String(),
      };
    }
  }

  /// 🔧 Generer un numero de contrat
  static String _generateNumeroContrat(String compagnieId) {
    final year = DateTime.now().year.toString().substring(2);
    final month = DateTime.now().month.toString(').padLeft(2, '0);
    final random = Random().nextInt(9999).toString(').padLeft(4, '0);
    final compagnieCode = compagnieId.toUpperCase().substring(0, 3.clamp(0, compagnieId.length)');
    
    return '$compagnieCode$year$month'random;
  }

  /// ✅ Verifier si un contrat est valide
  static bool _isContratValid(Map<String, dynamic> contratData') {
    if (contratData['status'] != 'actif') return false;
    
    if (contratData['dateFin] != null') {
      final dateFin = (contratData['dateFin] as Timestamp).toDate();
      return dateFin.isAfter(DateTime.now());
    }
    
    return true;
  }

  /// 🔄 Renouveler un contrat
  static Future<Map<String, dynamic>> renewContrat({
    required String contratId,
    required DateTime nouvelleDateFin,
    required double nouvellePrime,
  }') async {
    try {
      debugPrint('[VEHICULE_SERVICE] 🔄 Renouvellement contrat: 'contratId');

      await _firestore
          .collection('contrats)
          .doc(contratId')
          .update({
        'dateFin: Timestamp.fromDate(nouvelleDateFin'),
        'primeAnnuelle': nouvellePrime,
        'status': 'actif',
        'renewed_at: FieldValue.serverTimestamp('),
        'updated_at: FieldValue.serverTimestamp(),
      }');

      debugPrint('[VEHICULE_SERVICE] ✅ Contrat renouvele' + .toString());

      return {
        'success': true,
        'message': 'Contrat renouvele avec succes,
      };

    } catch (e') {
      debugPrint('[VEHICULE_SERVICE] ❌ Erreur renouvellement:  + e.toString()' + .toString());
      return {
        'success': false,
        'error: e.toString('),
        'message': 'Erreur lors du renouvellement,
      };
    }
  }

  /// 🗑️ Resilier un contrat
  static Future<Map<String, dynamic>> resilierContrat({
    required String contratId,
    required String motif,
  }') async {
    try {
      debugPrint('[VEHICULE_SERVICE] 🗑️ Resiliation contrat: 'contratId');

      await _firestore
          .collection('contrats)
          .doc(contratId')
          .update({
        'status': 'resilie',
        'motif_resiliation': motif,
        'date_resiliation: FieldValue.serverTimestamp('),
        'updated_at: FieldValue.serverTimestamp(),
      }');

      // Desactiver le vehicule associe
      final contratDoc = await _firestore
          .collection('contrats)
          .doc(contratId)
          .get();

      if (contratDoc.exists && contratDoc.data(')!['vehiculeId] != null') {
        await _firestore
            .collection('vehicules)
            .doc(contratDoc.data(')!['vehiculeId]')
            .update({
          'status': 'inactif',
          'updated_at: FieldValue.serverTimestamp(),
        }');
      }

      debugPrint('[VEHICULE_SERVICE] ✅ Contrat resilie' + .toString());

      return {
        'success': true,
        'message': 'Contrat resilie avec succes,
      };

    } catch (e') {
      debugPrint('[VEHICULE_SERVICE] ❌ Erreur resiliation:  + e.toString()' + .toString());
      return {
        'success': false,
        'error: e.toString('),
        'message': 'Erreur lors de la resiliation',
      };
    }
  }
}
