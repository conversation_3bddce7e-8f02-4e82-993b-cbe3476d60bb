import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/services/init_test_data_service.dart';
import '../../models/professional_account_request.dart';
import '../../../../core/config/app_router.dart';

/// 👥 Écran de sélection du type d'utilisateur moderne
class UserTypeSelectionScreen extends StatelessWidget {
  const UserTypeSelectionScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF0F0F23), // Bleu très sombre
              Color(0xFF1A1A2E), // Bleu sombre
              Color(0xFF16213E), // Bleu moyen
            ],
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: <PERSON>umn(
            children: [
              // En-tête moderne et compact
              _buildModernHeader(context),

              // Contenu principal avec design moderne
              Expanded(
                child: _buildModernContent(context),
              ),

              // Footer avec informations
              _buildFooter(context),
            ],
          ),
        ),
      ),
    );
  }

  /// 📱 En-tête moderne et compact
  Widget _buildModernHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          // Logo et titre
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Icon(
                  Icons.security,
                  size: 32,
                  color: AppTheme.primaryColor,
                ),
              ),
              const SizedBox(width: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Constat Tunisie',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Text(
                    'Choisissez votre profil',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 📋 Contenu principal moderne
  Widget _buildModernContent(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        children: [
          // Bouton demande professionnelle en haut
          _buildProfessionalRequestButton(context),
          
          const SizedBox(height: 24),
          
          // Cartes des types d'utilisateurs
          Expanded(
            child: ListView(
              children: [
                _buildUserTypeCard(
                  context,
                  icon: Icons.drive_eta,
                  title: 'Conducteur',
                  subtitle: 'Déclarer un accident, gérer mes véhicules',
                  color: AppTheme.primaryColor,
                  onTap: () => Navigator.pushNamed(context, AppRouter.conducteurLogin),
                ),
                
                const SizedBox(height: 16),
                
                _buildUserTypeCard(
                  context,
                  icon: Icons.business,
                  title: 'Agent d\'Assurance',
                  subtitle: 'Gérer les contrats et sinistres',
                  color: Colors.orange,
                  onTap: () => Navigator.pushNamed(context, AppRouter.agentLogin),
                ),
                
                const SizedBox(height: 16),
                
                _buildUserTypeCard(
                  context,
                  icon: Icons.engineering,
                  title: 'Expert',
                  subtitle: 'Évaluer les dommages et sinistres',
                  color: Colors.green,
                  onTap: () => Navigator.pushNamed(context, AppRouter.expertLogin),
                ),
                
                const SizedBox(height: 16),
                
                _buildUserTypeCard(
                  context,
                  icon: Icons.admin_panel_settings,
                  title: 'Administrateur',
                  subtitle: 'Gestion complète du système',
                  color: Colors.red,
                  onTap: () => Navigator.pushNamed(context, AppRouter.adminLogin),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 📋 Bouton pour demander un compte professionnel
  Widget _buildProfessionalRequestButton(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
      child: OutlinedButton.const Icon(
        onPressed: () {
          print('🔥 BOUTON HAUT CLIQUÉ - Navigation vers formulaire professionnel');
          Navigator.pushNamed(context, AppRouter.professionalAccountRequest);
        },
        icon: const Icon(Icons.info),
        label: const Text('Demander un Compte Professionnel'),
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
          foregroundColor: AppTheme.primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  /// 🎴 Carte de type d'utilisateur
  Widget _buildUserTypeCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              // Icône
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Icon(
                  icon,
                  size: 30,
                  color: color,
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Texte
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    const Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Flèche
              const Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: AppTheme.textSecondary,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 🦶 Footer avec informations
  Widget _buildFooter(BuildContext context) {
    return Column(
      children: [
        const Divider(),
        const SizedBox(height: 16),
        
        // Boutons d'aide - Version responsive
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Expanded(
              child: TextButton.const Icon(
                onPressed: () {
                  // Ouvrir l'aide
                  _showHelpDialog(context);
                },
                icon: const Icon(Icons.help_outline),
                label: const Text(
                  'Aide',
                  style: TextStyle(fontSize: 12),
                  overflow: TextOverflow.ellipsis,
                ),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                ),
              ),
            ),
            Expanded(
              child: TextButton.const Icon(
                onPressed: () {
                  // Ouvrir les conditions
                  _showTermsDialog(context);
                },
                icon: const Icon(Icons.description_outlined),
                label: const Text(
                  'Conditions',
                  style: TextStyle(fontSize: 12),
                  overflow: TextOverflow.ellipsis,
                ),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                ),
              ),
            ),
            Expanded(
              child: TextButton.const Icon(
                onPressed: () {
                  // Contacter le support
                  _showContactDialog(context);
                },
                icon: const Icon(Icons.contact_support_outlined),
                label: const Text(
                  'Contact',
                  style: TextStyle(fontSize: 12),
                  overflow: TextOverflow.ellipsis,
                ),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                ),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // Version de l'application
        const Text(
          'Version ${AppConstants.appVersion}',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppTheme.textHint,
          ),
        ),

        const SizedBox(height: 8),

        // 🔐 Accès Super Admin (discret)
        _buildSuperAdminAccess(context),
      ],
    );
  }

  /// ❓ Dialog d'aide
  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Aide'),
        content: const Text(
          'Cette application vous permet de gérer vos constats d\'accident.\n\n'
          'Choisissez votre profil pour accéder aux fonctionnalités appropriées.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  /// 📋 Dialog des conditions
  void _showTermsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Conditions d\'utilisation'),
        content: const Text(
          'En utilisant cette application, vous acceptez nos conditions d\'utilisation.\n\n'
          'Vos données sont protégées selon la réglementation en vigueur.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  /// 📞 Dialog de contact
  void _showContactDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Nous contacter'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Pour toute question ou assistance :'),
            const SizedBox(height: 16),
            Row(
              children: [
                const Icon(Icons.email, size: 16),
                const SizedBox(width: 8),
                const Text('<EMAIL>'),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.phone, size: 16),
                const SizedBox(width: 8),
                const Text('+216 XX XXX XXX'),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  /// 🔐 Accès Super Admin discret
  Widget _buildSuperAdminAccess(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Accès Super Admin
        GestureDetector(
          onTap: () {
            Navigator.pushNamed(context, '/super-admin-login');
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: const Text(
              '•••',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.textHint.withValues(alpha: 0.5),
                fontSize: 12,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
