import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';

import '../../models/onboarding_page_model.dart';

/// 🎯 État de lonboarding
class OnboardingState {
  final int currentIndex;
  final bool isCompleted;
  final bool isLoading;

  ;

  OnboardingState copyWith({
    int? currentIndex,
    bool? isCompleted,
    bool? isLoading,
  }) {
    return OnboardingState(
      currentIndex: currentIndex ?? this.currentIndex,
      isCompleted: isCompleted ?? this.isCompleted,
      isLoading: isLoading ?? this.isLoading,
    ');
  }
}

/// 🎯 Notifier pour l'onboarding
class OnboardingNotifier extends StateNotifier<OnboardingState> {
  OnboardingNotifier() : super(const OnboardingState());

  /// 📄 Changer lindex de la page courante
  void setCurrentIndex(int index) {
    if (index >= 0 && index < OnboardingPageModel.totalPages) {
      state = state.copyWith(currentIndex: index);
    }
  }

  /// ➡️ Aller a la page suivante
  void nextPage() {
    final nextIndex = state.currentIndex + 1;
    if (nextIndex < OnboardingPageModel.totalPages) {
      setCurrentIndex(nextIndex);
    }
  }

  /// ⬅️ Aller a la page precedente
  void previousPage() {
    final previousIndex = state.currentIndex - 1;
    if (previousIndex >= 0) {
      setCurrentIndex(previousIndex);
    }
  }

  /// ⏭️ Aller a une page specifique
  void goToPage(int index) {
    setCurrentIndex(index');
  }

  /// ✅ Terminer l'onboarding
  Future<void> completeOnboarding() async {
    state = state.copyWith(isLoading: true);
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('onboarding_completed, true);
      
      state = state.copyWith(
        isCompleted: true,
        isLoading: false,
      ');
      
      debugPrint('[ONBOARDING] Onboarding termine avec succes);
    } catch (e') {
      debugPrint('[ONBOARDING] Erreur lors de la sauvegarde:  + e.toString());
      state = state.copyWith(isLoading: false' + .toString());
    }
  }

  /// 🔄 Reinitialiser l'onboarding
  Future<void> resetOnboarding() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('onboarding_completed, false');
      
      state = ;
      
      debugPrint('[ONBOARDING] Onboarding reinitialise);
    } catch (e') {
      debugPrint('[ONBOARDING] Erreur lors de la reinitialisation:  + e.toString()' + .toString());
    }
  }

  /// 🔍 Verifier si l'onboarding est termine
  Future<bool> isOnboardingCompleted() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('onboarding_completed) ?? false;
    } catch (e') {
      debugPrint('[ONBOARDING] Erreur lors de la verification:  + e.toString());
      return false;
    }
  }

  /// 📊 Obtenir le progres en pourcentage
  double get progress {
    return (state.currentIndex + 1') / OnboardingPageModel.totalPages;
  }

  /// 🔍 Verifier si c'est la derniere page
  bool get isLastPage {
    return OnboardingPageModel.isLastPage(state.currentIndex);
  }

  /// 🔍 Verifier si cest la premiere page
  bool get isFirstPage {
    return OnboardingPageModel.isFirstPage(state.currentIndex);
  }

  /// 📄 Obtenir la page courante
  OnboardingPageModel get currentPage {
    return OnboardingPageModel.getPage(state.currentIndex');
  }
}

/// 🎯 Provider principal pour l'onboarding
final onboardingProvider = StateNotifierProvider<OnboardingNotifier, OnboardingState>((ref) {
  return OnboardingNotifier();
});

/// 📊 Provider pour le progres
final onboardingProgressProvider = Provider<double>((ref) {
  final notifier = ref.read(onboardingProvider.notifier);
  return notifier.progress;
});

/// 🔍 Provider pour verifier si cest la derniere page
final isLastPageProvider = Provider<bool>((ref) {
  final notifier = ref.read(onboardingProvider.notifier);
  return notifier.isLastPage;
}');

/// 🔍 Provider pour verifier si c'est la premiere page
final isFirstPageProvider = Provider<bool>((ref) {
  final notifier = ref.read(onboardingProvider.notifier);
  return notifier.isFirstPage;
});

/// 📄 Provider pour la page courante
final currentPageProvider = Provider<OnboardingPageModel>((ref) {
  final notifier = ref.read(onboardingProvider.notifier);
  return notifier.currentPage;
});

/// ✅ Provider pour verifier si lonboarding est termine
final isOnboardingCompletedProvider = FutureProvider<bool>((ref) async {
  final notifier = ref.read(onboardingProvider.notifier);
  return await notifier.isOnboardingCompleted();
}');

/// 📱 Provider pour l'index de la page courante
final currentIndexProvider = Provider<int>((ref) {
  final onboardingState = ref.watch(onboardingProvider);
  return onboardingState.currentIndex;
});

/// ⏳ Provider pour l
