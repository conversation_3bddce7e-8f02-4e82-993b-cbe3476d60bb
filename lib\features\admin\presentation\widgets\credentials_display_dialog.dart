import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 🔐 Dialog pour afficher les identifiants d'un compte Admin Compagnie
/// 
/// Affiche de manière élégante et sécurisée les identifiants générés
/// pour que le Super Admin puisse les copier et transmettre manuellement
class CredentialsDisplayDialog extends StatefulWidget {
  final Map<String, dynamic> credentials;
  final String compagnieName;

   ) : super(key: key);

  @override
  State<CredentialsDisplayDialog> createState() => _CredentialsDisplayDialogState();
}

class _CredentialsDisplayDialogState extends State<CredentialsDisplayDialog> {
  bool _isPasswordVisible = false;
  bool _emailCopied = false;
  bool _passwordCopied = false;

  @override
  Widget build(BuildContext context) {
    final email = widget.credentials['email'] as String;
    final password = widget.credentials['password'] as String;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: 500,
        padding: ,
              child: ,
            ),
            
            const SizedBox(height: 16),
            
            //  .textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.green.shade700,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 8),
            
            // Sous-titre avec nom de la  .textTheme.titleMedium?.copyWith(
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 24),
            
            // Message d'instruction
            Container(
              padding: ,
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Row(
                children: [
                  ,
                  const SizedBox(width: 12),
                  Expanded(
                    child: ,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Champ Email
            _buildCredentialField(
              label: 'Email de connexion',
              value: email,
              icon: Icons.email_outlined,
              isCopied: _emailCopied,
              onCopy: () => _copyToClipboard(email, 'email'),
            ),
            
            const SizedBox(height: 16),
            
            // Champ Mot de passe
            _buildCredentialField(
              label: 'Mot de passe',
              value: password,
              icon: Icons.lock_outline,
              isCopied: _passwordCopied,
              isPassword: true,
              isVisible: _isPasswordVisible,
              onVisibilityToggle: () {
                setState(() {
                  _isPasswordVisible = !_isPasswordVisible;
                });
              },
              onCopy: () => _copyToClipboard(password, 'password'),
            ),
            
            const SizedBox(height: 24),
            
            // Informations supplémentaires
            Container(
              padding: ,
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ,
                  ),
                  const SizedBox(height: 8),
                  _buildInfoItem('• Ce compte est institutionnel (pas personnel)'),
                  _buildInfoItem('• L\'Admin Compagnie peut créer des agences et leurs admins'),
                  _buildInfoItem('• Accès limité aux données de sa compagnie uniquement'),
                  _buildInfoItem('• Aucun email automatique n\'a été envoyé'),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Boutons d'action
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.const Icon(
                    onPressed: () => _copyAllCredentials(),
                    icon: const Icon(Icons.info),
                    label: const Text('Copier Tout'),
                    style: OutlinedButton.styleFrom(
                      padding: ,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.const Icon(
                    onPressed: () => Navigator.of(context).pop(true),
                    icon: const Icon(Icons.info),
                    label: const Text('Terminé'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green.shade600,
                      foregroundColor: Colors.white,
                      padding: ,
                  ),
                ),
              ],
            ),
          ],
          ),
        ),
      ),
    );
  }

  Widget _buildCredentialField({
    required String label,
    required String value,
    required IconData icon,
    required bool isCopied,
    required VoidCallback onCopy,
    bool isPassword = false,
    bool isVisible = true,
    VoidCallback? onVisibilityToggle,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        ),
        const SizedBox(height: 8),
        Container(
          padding: ,
            borderRadius: BorderRadius.circular(8),
            color: Colors.grey.shade50,
          ),
          child: Row(
            children: [
              ,
              const SizedBox(width: 12),
              Expanded(
                child: ,
                ),
              ),
              if (isPassword && onVisibilityToggle != null) ...[
                IconButton(
                  onPressed: onVisibilityToggle,
                  icon: const Icon(Icons.info),
                  padding: ,
                ),
                const SizedBox(width: 8),
              ],
              IconButton(
                onPressed: onCopy,
                icon: const Icon(Icons.info),
                padding: ,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildInfoItem(String text) {
    return (1),
      ),
    );
  }

  void _copyToClipboard(String value, String type) {
    Clipboard.setData(ClipboardData(text: value));
    
    setState(() {
      if (type == 'email') {
        _emailCopied = true;
      } else if (type == 'password') {
        _passwordCopied = true;
      }
    });

    // Réinitialiser l'état après 2 secondes
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          if (type == 'email') {
            _emailCopied = false;
          } else if (type == 'password') {
            _passwordCopied = false;
          }
        });
      }
    });

    // Afficher un snackbar de confirmation
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: ({type == 'email' ? 'Email' : 'Mot de passe'} copié !'),
        duration: const Duration(seconds: 1),
        backgroundColor: Colors.green.shade600,
      ),
    );
  }

  void _copyAllCredentials() {
    final email = widget.credentials['email'] as String;
    final password = widget.credentials['password'] as String;
    final compagnie = widget.compagnieName;
    
    final allCredentials = 'Contenu''
Identifiants Admin Compagnie - $compagnie

Email: $email
Mot de passe: $password

Rôle: Admin Compagnie
Compagnie: $compagnie
Type de compte: Institutionnel

Instructions:
- Transmettez ces identifiants à la compagnie d'assurance
- L'Admin Compagnie peut créer des agences et leurs admins
- Accès limité aux données de sa compagnie uniquement
'Contenu'';

    Clipboard.setData(ClipboardData(text: allCredentials));
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Tous les identifiants copiés !
