import 'package:flutter/material.dart';
import '../services/admin_compagnie_auth_service.dart';
import 'widgets/agence_management_tab.dart';
import 'widgets/agent_management_tab.dart';

/// 🏢 Dashboard Admin Compagnie
class AdminCompagnieDashboard extends StatefulWidget {
  final String compagnieId;
  final String compagnieNom;

  const AdminCompagnieDashboard({
    Key? key,
    required this.compagnieId,
    required this.compagnieNom,
  }) : super(key: key);

  @override
  State<AdminCompagnieDashboard> createState() => _AdminCompagnieDashboardState();
}

class _AdminCompagnieDashboardState extends State<AdminCompagnieDashboard>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// 🚪 Déconnexion
  Future<void> _logout() async {
    try {
      await AdminCompagnieAuthService.logout();
      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/login');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: const Text('Erreur: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard ${widget.compagnieNom}'),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _logout,
            icon: const Icon(Icons.info),
            tooltip: 'Déconnexion',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(
              icon: const Icon(Icons.business),
              text: 'Agences',
            ),
            Tab(
              icon: const Icon(Icons.admin_panel_settings),
              text: 'Admins Agence',
            ),
          ],
        ),
      ),
      body: TabBarView(
        children: [
          const Center(child: const Text('Gestion des Agences')),
          const Center(child: const Text('Gestion des Admins Agence')),
        ],
      ),
    );
  }
}
