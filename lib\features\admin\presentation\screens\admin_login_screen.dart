import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'admin_dashboard_router.dart';
import '../../../../services/admin_compagnie_auth_service.dart';
import '../../../../admin/admin_compagnie_dashboard.dart';
import 'super_admin_dashboard_screen.dart';

/// 🔐 Écran de connexion pour les administrateurs
class AdminLoginScreen extends ConsumerStatefulWidget {
  const Text(\;

  @override
  ConsumerState<AdminLoginScreen> createState() => _AdminLoginScreenState();
}

class _AdminLoginScreenState extends ConsumerState<AdminLoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  String? _selectedAdminType;
  bool _isLoading = false;
  bool _obscurePassword = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: SafeArea(
        child: (1),
              padding: ,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Form(
                key: _formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildHeader(),
                    const SizedBox(height: 32),
                    _buildAdminTypeSelector(),
                    const SizedBox(height: 24),
                    _buildEmailField(),
                    const SizedBox(height: 16),
                    _buildPasswordField(),
                    const SizedBox(height: 32),
                    _buildLoginButton(),
                    const SizedBox(height: 16),
                    _buildForgotPasswordLink(),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 📱 En-tête avec logo et titre
  Widget _buildHeader() {
    return Column(
      children: [
        Container(
          padding: .withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(50),
          ),
          child: ,
          ),
        ),
        const SizedBox(height: 16),
        ,
          ),
        ),
        const SizedBox(height: 8),
        ,
        ),
      ],
    );
  }

  /// 🎯 Sélecteur de type d'admin
  Widget _buildAdminTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _selectedAdminType,
          decoration: InputDecoration(
            hintText: 'Sélectionnez votre type d\'admin',
            prefixIcon: ,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFFE2E8F0)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFFE2E8F0)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFF3B82F6), width: 2),
            ),
          ),
          items: , size: 20),
                  const SizedBox(width: 8),
                  const Text('Super Admin'),
                ],
              ),
            ),
            DropdownMenuItem(
              value: 'admin_compagnie',
              child: Row(
                children: [
                  , size: 20),
                  const SizedBox(width: 8),
                  const Text('Admin Compagnie'),
                ],
              ),
            ),
            DropdownMenuItem(
              value: 'admin_agence',
              child: Row(
                children: [
                  , size: 20),
                  const SizedBox(width: 8),
                  const Text('Admin Agence'),
                ],
              ),
            ),
          ],
          onChanged: (value) {
            setState(() {
              _selectedAdminType = value;
            });
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Veuillez sélectionner un type d\'admin';
            }
            return null;
          },
        ),
      ],
    );
  }

  /// 📧 Champ email
  Widget _buildEmailField() {
    return TextFormField(
      controller: _emailController,
      keyboardType: TextInputType.emailAddress,
      decoration: InputDecoration(
        labelText: 'Email',
        hintText: '<EMAIL>',
        prefixIcon: ,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFFE2E8F0)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFFE2E8F0)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF3B82F6), width: 2),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Veuillez saisir votre email';
        }
        if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
          return 'Veuillez saisir un email valide';
        }
        return null;
      },
    );
  }

  /// 🔒 Champ mot de passe
  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      obscureText: _obscurePassword,
      decoration: InputDecoration(
        labelText: 'Mot de passe',
        hintText: '••••••••',
        prefixIcon: ,
        suffixIcon: IconButton(
          icon: const Icon(Icons.info),
          onPressed: () {
            setState(() {
              _obscurePassword = !_obscurePassword;
            });
          },
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFFE2E8F0)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFFE2E8F0)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF3B82F6), width: 2),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Veuillez saisir votre mot de passe';
        }
        if (value.length < 6) {
          return 'Le mot de passe doit contenir au moins 6 caractères';
        }
        return null;
      },
    );
  }

  /// 🔐 Bouton de connexion
  Widget _buildLoginButton() {
    return ,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
        child: _isLoading
            ? ,
                ),
              )
            : ,
              ),
      ),
    );
  }

  /// 🔗 Lien mot de passe oublié
  Widget _buildForgotPasswordLink() {
    return TextButton(
      onPressed: () {
        // TODO: Implémenter la récupération de mot de passe
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: const Text('Récupération de mot de passe - À implémenter'),
          ),
        );
      },
      child: const Text(
        'Mot de passe oublié ?',
        style: TextStyle(
          color: Color(0xFF3B82F6),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 🔐 Gestion de la connexion
  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final email = _emailController.text.trim();
      final password = _passwordController.text;

      // Vérification spéciale pour le super admin
      if (_selectedAdminType == 'super_admin' &&
          email == '<EMAIL>' &&
          password == 'Acheya123') {

        if (mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => ,
            ),
          );
        }
        return;
      }

      // Pour Admin Compagnie, utiliser le nouveau service
      if (_selectedAdminType == 'admin_compagnie') {
        final result = await AdminCompagnieAuthService.loginAdminCompagnie(
          email: email,
          password: password,
        );

        if (result.success && result.adminData != null) {
          if (mounted) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(
                builder: (context) => AdminCompagnieDashboard(
                  data: result.adminData!,
                  compagnieId: result.compagnieId!,
                  compagnieNom: result.compagnieNom!,
                ),
              ),
            );
          }
          return;
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: ,
                backgroundColor: const Color(0xFFEF4444),
              ),
            );
          }
          return;
        }
      }

      // Pour les autres types d'admin, utiliser le router existant
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const AdminDashboardRouter(
              userId: 'demo-user-id',
            ),
          ),
        );
      }

    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e
