import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/theme/modern_theme.dart';
import '../providers/super_admin_provider.dart';
import '../providers/dashboard_data_provider.dart';
import '../widgets/dashboard_card.dart';
import '../widgets/stats_card.dart';
import '../widgets/recent_activity_card.dart';
// import 'professional_requests_screen.dart'; // Import inutilisé
import 'professional_requests_management_screen.dart';
import 'compagnies_management_screen.dart';
import 'agences_management_screen.dart';
import 'bi_dashboard_screen.dart';
import '../../services/data_initialization_service.dart';
// import 'test_requests_screen.dart'; // Gardé en commentaire au cas où

/// 🏠 Dashboard Super Admin - Interface principale
class SuperAdminDashboardScreen extends ConsumerStatefulWidget {
  const Text(\;

  @override
  ConsumerState<SuperAdminDashboardScreen> createState() => _SuperAdminDashboardScreenState();
}

class _SuperAdminDashboardScreenState extends ConsumerState<SuperAdminDashboardScreen> {
  int _selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      drawer: _buildDrawer(),
      backgroundColor: ModernTheme.backgroundColor,
      body: _buildBody(),
      bottomNavigationBar: _buildBottomNavigation(),
    );
  }

  /// 🔝 Barre d'application moderne
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text("Titre"),
      ),
      backgroundColor: ModernTheme.primaryColor,
      elevation: 0,
      leading: Builder(
        builder: (context) => IconButton(
          icon: const Icon(Icons.info),
          onPressed: () => Scaffold.of(context).openDrawer(),
          tooltip: 'Menu',
        ),
      ),
      flexibleSpace: Container(
        decoration: ,
        ),
      ),
      actions: [
        // Notifications
        IconButton(
          icon: const Badge(
            label: const Text('3'),
            child: ,
          ),
          onPressed: () => _showNotifications(),
        ),
        // Profil
        PopupMenuButton<String>(
          icon: const Icon(Icons.info),
          ),
          offset: const Offset(-120, 0), // Décalage vers la gauche pour éviter l'overflow
          constraints: const BoxConstraints(
            maxWidth: 200, // Largeur maximale du menu
          ),
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            ,
                    const SizedBox(width: 8),
                    Expanded(
                      child: const Text(
                        'Profil',
                        style: TextStyle(fontSize: 14),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            ,
                    const SizedBox(width: 8),
                    Expanded(
                      child: const Text(
                        'Paramètres',
                        style: TextStyle(fontSize: 14),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            ,
            ,
                    const SizedBox(width: 8),
                    Expanded(
                      child: ,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            ,
                    const SizedBox(width: 8),
                    Expanded(
                      child: ,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 📱 Menu latéral
  Widget _buildDrawer() {
    return Drawer(
      child: Column(
        children: [
          // En-tête du drawer
          Container(
            height: 200,
            decoration: ],
              ),
            ),
            child: ,
                  ),
                  const SizedBox(height: 16),
                  ,
                  ),
                  ,
                  ),
                ],
              ),
            ),
          ),
          // Menu items
          Expanded(
            child: ListView(
              padding: ,
                _buildDrawerItem(
                  icon: Icons.people,
                  title: 'Gestion Utilisateurs',
                  index: 1,
                ),
                _buildDrawerItem(
                  icon: Icons.pending_actions,
                  title: 'Demandes en attente',
                  index: 2,
                  badge: '5',
                ),
                _buildDrawerItem(
                  icon: Icons.domain,
                  title: 'Gestion Compagnies',
                  index: 3,
                ),
                _buildDrawerItem(
                  icon: Icons.business,
                  title: 'Gestion Agences',
                  index: 4,
                ),
                _buildDrawerItem(
                  icon: Icons.car_crash,
                  title: 'Sinistres',
                  index: 5,
                ),
                _buildDrawerItem(
                  icon: Icons.analytics,
                  title: 'Statistiques',
                  index: 6,
                ),
                _buildDrawerItem(
                  icon: Icons.upload_file,
                  title: 'Import CSV',
                  index: 9,
                ),
                _buildDrawerItem(
                  icon: Icons.settings,
                  title: 'Paramètres',
                  index: 7,
                ),
                ,
                ListTile(
                  leading: ,
                  title: const Text("Titre"),
                  ),
                  onTap: () {
                    Navigator.pop(context); // Fermer le drawer
                    _exitSuperAdminSpace();
                  },
                ),
                ,
                ListTile(
                  leading: ,
                  title: const Text('Aide'),
                  onTap: () => _showHelp(),
                ),
                ListTile(
                  leading: ,
                  title: const Text('À propos'),
                  onTap: () => _showAbout(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 📋 Item du menu latéral
  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required int index,
    String? badge,
  }) {
    final isSelected = _selectedIndex == index;
    
    return Container(
      margin: ,
        color: isSelected ? AppTheme.primaryColor.withValues(alpha: 0.1) : null,
      ),
      child: ListTile(
        leading: ,
        title: const Text("Titre"),
        ),
        trailing: badge != null
            ? Badge(
                label: const Text(badge),
                backgroundColor: AppTheme.errorColor,
              )
            : null,
        onTap: () {
          setState(() {
            _selectedIndex = index;
          });
          Navigator.pop(context);
        },
      ),
    );
  }

  /// 📱 Corps principal
  Widget _buildBody() {
    switch (_selectedIndex) {
      case 0:
        return _buildDashboardHome();
      case 1:
        return _buildUsersManagement();
      case 2:
        return _buildPendingRequests();
      case 3:
        return _buildCompagniesManagement();
      case 4:
        return _buildAgenciesManagement();
      case 5:
        return _buildClaimsManagement();
      case 6:
        return _buildStatistics();
      case 7:
        return _buildSettings();
      case 8:
        return _buildFakeDataManagement();
      case 9:
        return _buildCsvImport();
      default:
        return _buildDashboardHome();
    }
  }

  /// 🏠 Accueil du dashboard
  Widget _buildDashboardHome() {
    return SingleChildScrollView(
      padding: ,

          const SizedBox(height: 12), // Espacement ultra-réduit

          // Statistiques rapides
          _buildQuickStats(),

          const SizedBox(height: 12), // Espacement ultra-réduit

          // Activité récente
          _buildRecentActivity(),

          const SizedBox(height: 12), // Espacement ultra-réduit

          // Actions rapides
          _buildQuickActions(),
        ],
      ),
    );
  }

  /// 🎨 En-tête moderne
  Widget _buildModernHeader() {
    return Container(
      padding: ,
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ,
                ),
                ,
                ,
                  ),
                ),
              ],
            ),
          ),
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
            ),
            child: ,
          ),
        ],
      ),
    );
  }

  /// 📊 Statistiques rapides
  Widget _buildQuickStats() {
    final stats = ref.watch(dashboardStatsProvider);
    final trends = ref.watch(statsTrendsProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        ),
        const SizedBox(height: 4), // Espacement absolu minimal
        stats.when(
          data: (data) => GridView.count(
            shrinkWrap: true,
            physics: ,
            crossAxisCount: 2,
            crossAxisSpacing: 4, // Espacement absolu minimal
            mainAxisSpacing: 4, // Espacement absolu minimal
            childAspectRatio: 2.3, // Ratio maximal
            children: [
              StatsCard(
                title: 'Utilisateurs',
                value: (data['total_users'] ?? 0) +
                       (data['total_agents'] ?? 0) +
                       (data['total_experts'] ?? 0) +
                       (data['total_conducteurs'] ?? 0),
                icon: Icons.people_rounded,
                color: ModernTheme.primaryColor,
                trend: trends['users'],
              ),
              StatsCard(
                title: 'Agences',
                value: data['total_agencies'] ?? 0,
                icon: Icons.business_rounded,
                color: ModernTheme.successColor,
                trend: trends['agencies'],
              ),
              StatsCard(
                title: 'Sinistres',
                value: data['total_claims'] ?? 0,
                icon: Icons.car_crash_rounded,
                color: ModernTheme.warningColor,
                trend: trends['claims'],
              ),
              StatsCard(
                title: 'En attente',
                value: data['pending_requests'] ?? 0,
                icon: Icons.pending_actions_rounded,
                color: ModernTheme.errorColor,
                trend: trends['pending'],
              ),
            ],
          ),
          loading: () => GridView.count(
            shrinkWrap: true,
            physics: ,
            crossAxisCount: 2,
            crossAxisSpacing: 4,
            mainAxisSpacing: 4,
            childAspectRatio: 2.3,
            children: ,
              StatsCard(
                title: 'Agences',
                value: '...',
                icon: Icons.business_rounded,
                color: ModernTheme.successColor,
                isLoading: true,
              ),
              StatsCard(
                title: 'Sinistres',
                value: '...',
                icon: Icons.car_crash_rounded,
                color: ModernTheme.warningColor,
                isLoading: true,
              ),
              StatsCard(
                title: 'En attente',
                value: '...',
                icon: Icons.pending_actions_rounded,
                color: ModernTheme.errorColor,
                isLoading: true,
              ),
            ],
          ),
          error: (error, stack) => ,
                const SizedBox(height: 8),
                ({error.toString()}',
                  style: ,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                ElevatedButton(
                  onPressed: () => ref.refresh(dashboardStatsProvider),
                  child: const Text('Réessayer'),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 🕒 Activité récente
  Widget _buildRecentActivity() {
    return ;
  }

  /// ⚡ Actions rapides
  Widget _buildQuickActions() {
    final quickActions = ref.watch(quickActionsProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: ,
          crossAxisCount: 2,
          crossAxisSpacing: ModernTheme.spacingM,
          mainAxisSpacing: ModernTheme.spacingM,
          childAspectRatio: 2.5, // Augmenté pour éviter l'overflow
          children: quickActions.map((action) {
            return DashboardCard(
              title: action['title'] as String,
              icon: action['icon'] as IconData,
              color: action['color'] as Color,
              badge: action['count']?.toString(),
              onTap: () => _handleQuickAction(action['title'] as String),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// 🎯 Gérer les actions rapides
  void _handleQuickAction(String actionTitle) {
    switch (actionTitle) {
      case 'Initialiser Collections':
        _initializeCollections();
        break;
      case 'Test Alternative':
        _openAlternativeTest();
        break;
      case 'Import CSV Assurance':
        _openInsuranceCsv();
        break;
      case 'CSV Simplifié':
        _openSimpleCsv();
        break;
      case 'Diagnostic Firestore':
        _openFirestoreDiagnostic();
        break;
      case 'Contournement':
        _openWorkaround();
        break;
      case 'Gestion Compagnies':
        setState(() {
          _selectedIndex = 3; // Index de la gestion des compagnies
        });
        break;
      case 'Gestion Agences':
        setState(() {
          _selectedIndex = 4; // Index de la gestion des agences
        });
        break;
      case 'Demandes Professionnelles':
        setState(() {
          _selectedIndex = 2; // Index des demandes en attente
        });
        break;
      case 'Utilisateurs Actifs':
        setState(() {
          _selectedIndex = 1; // Index de la gestion des utilisateurs
        });
        break;
      case 'Rapports':
        setState(() {
          _selectedIndex = 5; // Index des statistiques
        });
        break;
    }
  }

  /// 👥 Gestion des utilisateurs
  Widget _buildUsersManagement() {
    // Naviguer vers l'écran dédié de gestion des utilisateurs
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Navigator.pushNamed(context, '/admin/users');
    });

    return Container(
      padding: ,
            ,
            ,
          ],
        ),
      ),
    );
  }

  /// 📝 Demandes en attente
  Widget _buildPendingRequests() {
    // Interface complète - Règles Firestore simplifiées déployées
    return ;
    // Écran de test (gardé en commentaire au cas où)
    // return ;
  }

  /// 🏢 Gestion des compagnies d'assurance
  Widget _buildCompagniesManagement() {
    return ;
  }

  /// 🏢 Gestion des agences
  Widget _buildAgenciesManagement() {
    return ;
  }

  /// 🚗 Gestion des sinistres
  Widget _buildClaimsManagement() {
    return ,
          const SizedBox(height: 16),
          ,
          ),
          const SizedBox(height: 8),
          const Text('Suivi de tous les sinistres déclarés'),
        ],
      ),
    );
  }

  /// 📊 Statistiques - Dashboard BI complet
  Widget _buildStatistics() {
    return ;
  }

  /// 🎲 Gestion des données de test
  Widget _buildFakeDataManagement() {
    return const Center(
      child: const Text(
        'Gestion des données de test - Fonctionnalité supprimée',
        style: TextStyle(fontSize: 18),
      ),
    );
  }

  /// 📊 Importation CSV
  Widget _buildCsvImport() {
    return const Center(
      child: const Text(
        'Importation CSV - Fonctionnalité supprimée',
        style: TextStyle(fontSize: 18),
      ),
    );
  }

  /// 🚀 Initialiser les collections Firestore
  Future<void> _initializeCollections() async {
    try {
      // Afficher un dialog de confirmation
      final confirm = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('🏗️ Initialiser Collections'),
          content: const Text(
            'Cette action va créer les collections Firestore essentielles :\n'
            '• users\n'
            '• companies\n'
            '• agencies\n\n'
            'Cela permettra de résoudre les problèmes de timeout.\n\n'
            'Voulez-vous continuer ?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Initialiser'),
            ),
          ],
        ),
      );

      if (confirm != true) return;

      // Afficher un indicateur de chargement
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            content: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                const SizedBox(width: 16),
                const Text('Initialisation des collections...'),
              ],
            ),
          ),
        );
      }

      // Initialiser les collections (fonctionnalité supprimée)
      final success = true; // Simulation

      // Fermer le dialog de chargement
      if (mounted) {
        Navigator.of(context).pop();
      }

      // Afficher le résultat
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(
              success
                ? '✅ Collections initialisées avec succès !'
                : '❌ Erreur lors de l\'initialisation des collections',
            ),
            backgroundColor: success ? Colors.green : Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }

    } catch (e) {
      // Fermer le dialog de chargement si ouvert
      if (mounted) {
        Navigator.of(context).pop();
      }

      // Afficher l'erreur
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// 🧪 Fonctionnalité supprimée
  void _openAlternativeTest() {
    _showFeatureRemoved('Test Alternatif');
  }

  /// 📊 Fonctionnalité supprimée
  void _openInsuranceCsv() {
    _showFeatureRemoved('Import CSV Assurance');
  }

  /// 📋 Fonctionnalité supprimée
  void _openSimpleCsv() {
    _showFeatureRemoved('CSV Simplifié');
  }

  /// 🔍 Fonctionnalité supprimée
  void _openFirestoreDiagnostic() {
    _showFeatureRemoved('Diagnostic Firestore');
  }

  /// 🔧 Fonctionnalité supprimée
  void _openWorkaround() {
    _showFeatureRemoved('Contournement Firestore');
  }

  /// 🚫 Afficher message de fonctionnalité supprimée
  void _showFeatureRemoved(String featureName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Fonctionnalité supprimée'),
        content: (featureName a été supprimé pour nettoyer le code.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// ⚙️ Paramètres et outils d'administration
  Widget _buildSettings() {
    return SingleChildScrollView(
      padding: ,
            decoration: ModernTheme.gradientDecoration(
              colors: [Colors.purple.shade400, Colors.purple.shade600],
              borderRadius: ModernTheme.radiusLarge,
            ),
            child: Row(
              children: [
                ,
                ,
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ,
                      ),
                      const SizedBox(height: 4),
                      ,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          ,

          // Outils de données
          _buildSettingsSection(
            title: 'Gestion des Données',
            icon: Icons.storage_rounded,
            children: [
              _buildSettingsTile(
                title: 'Initialiser Compagnies',
                subtitle: 'Créer les compagnies d\'assurance tunisiennes',
                icon: Icons.domain_add_rounded,
                color: ModernTheme.primaryColor,
                onTap: _initializeCompagnies,
              ),
              _buildSettingsTile(
                title: 'Statistiques Système',
                subtitle: 'Voir les statistiques détaillées',
                icon: Icons.analytics_rounded,
                color: ModernTheme.secondaryColor,
                onTap: _showSystemStats,
              ),
              _buildSettingsTile(
                title: 'Données de Test',
                subtitle: 'Générer fake data pour tester le Dashboard BI',
                icon: Icons.science_rounded,
                color: Colors.purple,
                onTap: _showFakeDataManagement,
              ),
            ],
          ),

          ,

          // Configuration système
          _buildSettingsSection(
            title: 'Configuration',
            icon: Icons.tune_rounded,
            children: [
              _buildSettingsTile(
                title: 'Sauvegarder Données',
                subtitle: 'Exporter les données du système',
                icon: Icons.backup_rounded,
                color: ModernTheme.successColor,
                onTap: _exportData,
              ),
              _buildSettingsTile(
                title: 'Logs Système',
                subtitle: 'Consulter les journaux d\'activité',
                icon: Icons.description_rounded,
                color: ModernTheme.warningColor,
                onTap: _showSystemLogs,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 📱 Navigation inférieure moderne
  Widget _buildBottomNavigation() {
    final pendingRequests = ref.watch(dashboardStatsProvider);

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        top: false, // Pas de padding en haut
        child: Container(
          height: 56, // Hauteur réduite
          padding: ,
              _buildNavItem(
                icon: Icons.people_rounded,
                label: 'Utilisateurs',
                index: 1,
                isSelected: _selectedIndex == 1,
              ),
              _buildNavItemWithBadge(
                icon: Icons.pending_actions_rounded,
                label: 'Demandes',
                index: 2,
                isSelected: _selectedIndex == 2,
                badgeCount: pendingRequests.when(
                  data: (data) => data['pending_requests'] ?? 0,
                  loading: () => 0,
                  error: (_, __) => 0,
                ),
              ),
              _buildNavItem(
                icon: Icons.more_horiz_rounded,
                label: 'Plus',
                index: 3,
                isSelected: _selectedIndex == 3,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 🎯 Item de navigation simple
  Widget _buildNavItem({
    required IconData icon,
    required String label,
    required int index,
    required bool isSelected,
  }) {
    return Expanded(
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedIndex = index;
          });
        },
        borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
        child: Container(
          padding: ,
              const SizedBox(height: 2),
              ,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 🎯 Item de navigation avec badge
  Widget _buildNavItemWithBadge({
    required IconData icon,
    required String label,
    required int index,
    required bool isSelected,
    required int badgeCount,
  }) {
    return Expanded(
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedIndex = index;
          });
        },
        borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
        child: Container(
          padding: ,
                  if (badgeCount > 0)
                    Positioned(
                      right: -4,
                      top: -4,
                      child: Container(
                        padding: ,
                        constraints: const BoxConstraints(
                          minWidth: 12,
                          minHeight: 12,
                        ),
                        child: ,
                          style: ,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 2),
              ,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // === MÉTHODES D'ACTION ===

  void _handleMenuAction(String action) {
    switch (action) {
      case 'profile':
        _showProfile();
        break;
      case 'settings':
        setState(() {
          _selectedIndex = 6;
        });
        break;
      case 'exit_admin':
        _exitSuperAdminSpace();
        break;
      case 'logout':
        _logout();
        break;
    }
  }

  void _showNotifications() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Notifications'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: ,
              title: const Text('Nouvelle demande d\'agent'),
              subtitle: const Text('Il y a 2 heures'),
            ),
            ListTile(
              leading: ,
              title: const Text('Sinistre en attente'),
              subtitle: const Text('Il y a 4 heures'),
            ),
            ListTile(
              leading: ,
              title: const Text('Agence créée avec succès'),
              subtitle: const Text('Hier'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  void _showProfile() {
    final superAdminState = ref.read(superAdminProvider);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Profil Super Admin'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ({superAdminState.adminData?['email'] ?? 'N/A'}'),
            ({superAdminState.adminData?['lastName']}'),
            ({superAdminState.adminData?['role'] ?? 'N/A'}'),
            ({superAdminState.adminData?['status'] ?? 'N/A'}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  void _logout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Déconnexion'),
        content: const Text('Êtes-vous sûr de vouloir vous déconnecter ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(superAdminProvider.notifier).signOut();
              Navigator.pushReplacementNamed(context, '/');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: const Text('Déconnexion'),
          ),
        ],
      ),
    );
  }

  /// 🚪 Quitter l'espace Super Admin
  void _exitSuperAdminSpace() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            ,
            const SizedBox(width: 8),
            const Text('Quitter l\'espace Super Admin'),
          ],
        ),
        content: const Text(
          'Voulez-vous quitter l\'espace Super Admin ?\n\n'
          'Vous serez redirigé vers l\'écran de connexion principal.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context); // Fermer le dialog
              // Retourner à l'écran de connexion principal
              Navigator.pushNamedAndRemoveUntil(
                context,
                '/login',
                (route) => false,
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Quitter'),
          ),
        ],
      ),
    );
  }

  void _showHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Aide'),
        content: const Text(
          'Dashboard Super Admin\n\n'
          '• Gérez tous les utilisateurs du système\n'
          '• Validez les demandes de comptes professionnels\n'
          '• Supervisez les agences et sinistres\n'
          '• Consultez les statistiques détaillées\n\n'
          'Pour plus d\'aide, contactez le support technique.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  void _showAbout() {
    showDialog(
      context: context,
      builder: (context) => const AboutDialog(
        applicationName: 'Constat Tunisie',
        applicationVersion: '1.0.0',
        applicationLegalese: '© 2024 Constat Tunisie. Tous droits réservés.',
        children: [
          const Text('Application de gestion des sinistres automobiles en Tunisie.'),
        ],
      ),
    );
  }

  // Actions rapides supprimées - gérées dans _handleQuickAction

  /// 📋 Section des paramètres
  Widget _buildSettingsSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          (1),
                ,
                ,
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  /// 🔧 Tuile de paramètre
  Widget _buildSettingsTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
      child: (1),
                borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
              ),
              child: const Icon(icon, color: color, size: 20),
            ),
            ,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ,
                  ),
                  const SizedBox(height: 2),
                  ,
                  ),
                ],
              ),
            ),
            ,
          ],
        ),
      ),
    );
  }

  /// 🚀 Initialiser les compagnies d'assurance
  Future<void> _initializeCompagnies() async {
    try {
      // Afficher un dialog de confirmation
      final confirm = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Initialiser les Compagnies'),
          content: const Text(
            'Cette action va créer les compagnies d\'assurance tunisiennes principales. '
            'Les données existantes ne seront pas affectées.\n\n'
            'Voulez-vous continuer ?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Initialiser'),
            ),
          ],
        ),
      );

      if (confirm != true) return;

      // Afficher un indicateur de chargement
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                const SizedBox(width: 16),
                const Text('Initialisation en cours...'),
              ],
            ),
          ),
        );
      }

      // Initialiser les données
      final dataService = DataInitializationService();
      await dataService.initializeCompagnies();

      // Fermer le dialog de chargement
      if (mounted) Navigator.of(context).pop();

      // Afficher un message de succès
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: const Text('Compagnies initialisées avec succès !'),
            backgroundColor: Colors.green,
          ),
        );
      }

      // Rafraîchir les données du dashboard
      final _ = ref.refresh(dashboardStatsProvider);

    } catch (e) {
      // Fermer le dialog de chargement si ouvert
      if (mounted) Navigator.of(context).pop();

      // Afficher l'erreur
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 📊 Afficher les statistiques système
  void _showSystemStats() {
    setState(() {
      _selectedIndex = 6; // Aller aux statistiques
    });
  }

  /// 🎲 Afficher la gestion des données de test
  void _showFakeDataManagement() {
    setState(() {
      _selectedIndex = 8; // Aller aux données de test
    });
  }

  /// 💾 Exporter les données
  void _exportData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: const Text('Fonctionnalité d\'export en cours de développement'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  /// 📝 Afficher les logs système
  void _showSystemLogs() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: const Text('Fonctionnalité de logs en cours de développement
