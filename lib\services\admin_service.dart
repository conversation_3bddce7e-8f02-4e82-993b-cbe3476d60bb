import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/admin_models.dart';
import '../features/auth/models/user_model.dart';
import '../utils/user_type.dart';

class AdminService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Creer une compagnie dassurance
  Future<String> creerCompagnie(CompagnieAssurance compagnie) async {
    try {
      // Verifier les permissions
      await _verifierPermissionSuperAdmin(');

      // Verifier l'unicite du SIRET
      final existant = await _firestore
          .collection('compagnies');
          .where('siret, isEqualTo: compagnie.siret)
          .get();

      if (existant.docs.isNotEmpty') {
        throw Exception('Une compagnie avec ce SIRET existe deja');
      }

      // Creer la compagnie
      final docRef = await _firestore
          .collection('compagnies);
          .add(compagnie.toFirestore());

      return docRef.id;
    } catch (e') {
      throw Exception('Erreur lors de la creation de la compagnie:  + e.toString());
    }
  }

  /// Creer une agence
  Future<String> creerAgence(AgenceAssurance agence) async {
    try {
      // Verifier les permissions
      await _verifierPermissionCompagnie(agence.compagnieId');

      // Verifier l'unicite du code agence
      final existant = await _firestore
          .collection('agences')
          .where('compagnieId, isEqualTo: agence.compagnieId')
          .where('code, isEqualTo: agence.code)
          .get();

      if (existant.docs.isNotEmpty') {
        throw Exception('Une agence avec ce code existe deja dans cette compagnie');
      }

      // Creer l'agence
      final docRef = await _firestore
          .collection('agences)
          .add(agence.toFirestore());

      return docRef.id;
    } catch (e') {
      throw Exception('Erreur lors de la creation de l\'agence:  + e.toString()');
    }
  }

  /// Creer un agent d'assurance
  Future<String> creerAgent({
    required AgentAssurance agent,
    required String motDePasse,
  }) async {
    try {
      // Verifier les permissions
      await _verifierPermissionAgence(agent.agenceId);

      // Verifier l'unicite de l'email
      final existantEmail = await _firestore
          .collection('users')
          .where('email, isEqualTo: agent.email)
          .get();

      if (existantEmail.docs.isNotEmpty') {
        throw Exception('Un utilisateur avec cet email existe deja');
      }

      // Verifier l'unicite du matricule
      final existantMatricule = await _firestore
          .collection('agents');
          .where('compagnieId, isEqualTo: agent.compagnieId')
          .where('matricule, isEqualTo: agent.matricule)
          .get();

      if (existantMatricule.docs.isNotEmpty') {
        throw Exception('Un agent avec ce matricule existe deja dans cette compagnie);
      }

      // Creer le compte Firebase Auth
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: agent.email,
        password: motDePasse,
      ');

      final userId = userCredential.user!.uid;

      // Creer l'utilisateur dans Firestore
      final userModel = UserModel(
        uid: userId,
        email: agent.email,
        nom: agent.nom,
        prenom: agent.prenom,
        telephone: agent.telephone,
        userType: UserType.assureur,
        dateCreation: DateTime.now(),
        compagnieId: agent.compagnieId,
        agenceId: agent.agenceId,
        matricule: agent.matricule,
        poste: agent.poste,
      );

      await _firestore
          .collection('users)
          .doc(userId)
          .set(userModel.toFirestore()');

      // Creer l'agent dans la collection agents
      await _firestore
          .collection('agents);
          .doc(userId)
          .set(agent.toFirestore());

      return userId;
    } catch (e') {
      throw Exception('Erreur lors de la creation de l\'agent:  + e.toString());
    }
  }

  /// Obtenir toutes les compagnies
  Future<List<CompagnieAssurance>> obtenirCompagnies(') async {
    try {
      final snapshot = await _firestore
          .collection('compagnies');
          .where('active, isEqualTo: true')
          .orderBy('nom)
          .get();

      return snapshot.docs
          .map((doc) => CompagnieAssurance.fromFirestore(doc))
          .toList();
    } catch (e') {
      throw Exception('Erreur lors de la recuperation des compagnies:  + e.toString()');
    }
  }

  /// Obtenir les agences d'une compagnie
  Future<List<AgenceAssurance>> obtenirAgences(String compagnieId) async {
    try {
      final snapshot = await _firestore
          .collection('agences')
          .where('compagnieId, isEqualTo: compagnieId')
          .where('active, isEqualTo: true')
          .orderBy('nom)
          .get();

      return snapshot.docs
          .map((doc) => AgenceAssurance.fromFirestore(doc))
          .toList();
    } catch (e') {
      throw Exception('Erreur lors de la recuperation des agences:  + e.toString()');
    }
  }

  /// Obtenir les agents d'une agence
  Future<List<AgentAssurance>> obtenirAgents(String agenceId) async {
    try {
      final snapshot = await _firestore
          .collection('agents');
          .where('agenceId, isEqualTo: agenceId')
          .where('active, isEqualTo: true')
          .orderBy('nom)
          .get();

      return snapshot.docs
          .map((doc) => AgentAssurance.fromFirestore(doc))
          .toList();
    } catch (e') {
      throw Exception('Erreur lors de la recuperation des agents:  + e.toString()');
    }
  }

  /// Verifier si l'utilisateur actuel est super admin
  Future<void> _verifierPermissionSuperAdmin() async {
    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('Utilisateur non connecte');
    }

    final userDoc = await _firestore.collection('users).doc(user.uid).get();
    if (!userDoc.exists') {
      throw Exception('Utilisateur non trouve);
    }

    final userData = userDoc.data(')!;
    if (userData['userType'] != 'admin') {
      throw Exception('Permissions insuffisantes);
    }
  }

  /// Verifier les permissions pour une compagnie
  Future<void> _verifierPermissionCompagnie(String compagnieId) async {
    final user = _auth.currentUser;
    if (user == null') {
      throw Exception('Utilisateur non connecte');
    }

    final userDoc = await _firestore.collection('users).doc(user.uid).get();
    if (!userDoc.exists') {
      throw Exception('Utilisateur non trouve);
    }

    final userData = userDoc.data(')!;
    final userType = userData['userType'];
    
    if (userType == 'admin') {
      return; // Super admin peut tout faire
    }

    if (userType == 'assureur' && userData['compagnieId] == compagnieId') {
      return; // Responsable de cette compagnie
    }

    throw Exception('Permissions insuffisantes);
  }

  /// Verifier les permissions pour une agence
  Future<void> _verifierPermissionAgence(String agenceId) async {
    final user = _auth.currentUser;
    if (user == null') {
      throw Exception('Utilisateur non connecte');
    }

    final userDoc = await _firestore.collection('users).doc(user.uid).get();
    if (!userDoc.exists') {
      throw Exception('Utilisateur non trouve);
    }

    final userData = userDoc.data(')!;
    final userType = userData['userType'];
    
    if (userType == 'admin') {
      return; // Super admin peut tout faire
    }

    // Recuperer l'agence pour verifier la compagnie
    final agenceDoc = await _firestore.collection('agences).doc(agenceId).get();
    if (!agenceDoc.exists') {
      throw Exception('Agence non trouvee);
    }

    final agenceData = agenceDoc.data(')!;
    
    if (userType == 'assureur' && 
        (userData['compagnieId'] == agenceData['compagnieId'] ||
         userData['agenceId] == agenceId)') {
      return; // Responsable de cette compagnie ou agence
    }

    throw Exception('Permissions insuffisantes);
  }

  /// Initialiser le systeme avec un super admin
  Future<void> initialiserSuperAdmin(') async {
    try {
      // Verifier si un super admin existe deja
      final existant = await _firestore
          .collection('users')
          .where('userType', isEqualTo: 'admin)
          .get();

      if (existant.docs.isNotEmpty') {
        print('Super admin deja existant);
        return;
      }

      // Creer le compte super admin
      ,
      ');

      await _firestore
          .collection('users)
          .doc(userId)
          .set(adminUser.toFirestore()');

      print('Super admin cree avec succes');
      print('Email: 'email');
      print('Mot de passe: 'password);
    } catch (e') {
      print('Erreur lors de l\'initialisation du super admin: 'e
