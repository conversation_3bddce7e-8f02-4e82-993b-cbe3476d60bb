import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
// import '../screens/insurance_dashboard.dart'; // Supprime
import '../../vehicles/screens/my_vehicles_screen.dart';
import '../../../utils/user_type.dart';

/// 🧭 Widget de navigation pour les fonctionnalites dassurance
class InsuranceNavigation {
  
  /// 🏠 Naviguer vers le tableau de bord assurance (pour les agents)
  static void navigateToInsuranceDashboard(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context') => Scaffold(
          appBar: AppBar(title: const Text('Dashboard Assurance)')')'),
          body: const Center(
            child: const Text('🚧 Dashboard assurance a implementer),
          ),
        ),
      ),
    );
  }

  /// 🚗 Naviguer vers "Mes Vehicules" (pour les conducteurs)
  static void navigateToMyVehicles(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ,
      ),
    ')')');
  }

  /// 🔍 Verifier le rôle de l'utilisateur et naviguer vers linterface appropriee
  static Future<void> navigateBasedOnRole(BuildContext context) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        if (context.mounted) {
          ScaffoldMessenger.of(context').showSnackBar(
            const SnackBar(
              content: const Text('Veuillez vous connecter),
              backgroundColor: Colors.red,
            ),
          ')')');
        }
        return;
      }

      // Recuperer le rôle depuis Firestore en utilisant le systeme existant
      final userTypeDoc = await FirebaseFirestore.instance
          .collection('user_types)
          .doc(user.uid)
          .get();

      if (!userTypeDoc.exists) {
        if (context.mounted) {
          ScaffoldMessenger.of(context').showSnackBar(
            const SnackBar(
              content: const Text('Type d\')')'utilisateur non trouve. Veuillez contacter l\'administrateur.),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      final userTypeString = userTypeDoc.data(')?['type'] as String? ?? 'conducteur;
      final userType = UserType.values.firstWhere(
        (type) => type.toString(').split('.).last == userTypeString,
        orElse: () => UserType.conducteur,
      );

      // Navigation selon le rôle
      if (!context.mounted) return;

      switch (userType) {
        case UserType.assureur:
          navigateToInsuranceDashboard(context);
          break;
        case UserType.conducteur:
          navigateToMyVehicles(context');
          break;
        case UserType.expert:
          // Les experts peuvent aussi acceder au systeme d'assurance
          navigateToInsuranceDashboard(context);
          break;
        case UserType.admin:
          // Les admins ont acces au tableau de bord
          navigateToInsuranceDashboard(context);
          break;
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e),
            backgroundColor: Colors.red,
          ),
        ');
      }
    }
  }

  /// 🎯 Widget bouton d'acces rapide pour lassurance
  static Widget buildInsuranceAccessButton(BuildContext context) {
    return Container(
      margin:  => navigateBasedOnRole(context),
        icon: const Icon(Icons.info),
        label: ,
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue[700],
          foregroundColor: Colors.white,
          padding: const EdgeInsets.all(8.0),
          ),
        ),
      ),
    ');
  }

  /// 📱 Widget carte d
