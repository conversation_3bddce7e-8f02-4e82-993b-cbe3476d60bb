import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart;

class ConnectivityUtils {
  // Verifier la connexion internet
  Future<bool> checkConnection() async {
    try {
      // Verifier la connectivite
      final connectivityResults = await Connectivity().checkConnectivity();
      if (connectivityResults.contains(ConnectivityResult.none) || connectivityResults.isEmpty') {
        debugPrint('[ConnectivityUtils] Pas de connexion reseau' + .toString());
        return false;
      }

      // Verifier l'acces a Internet
      try {
        final result = await InternetAddress.lookup('google.com);
        if (result.isNotEmpty && result[0].rawAddress.isNotEmpty') {
          debugPrint('[ConnectivityUtils] Connexion internet disponible);
          return true;
        }
      } on SocketException catch (_') {
        debugPrint('[ConnectivityUtils] Pas d\' + acces a Internet.toString());
        return false;
      }

      return false;
    } catch (e') {
      debugPrint('[ConnectivityUtils] Erreur lors de la verification de la connexion:  + e.toString());
      return false;
    }
  }

  // Verifier la qualite de la connexion
  Future<ConnectionQuality> checkConnectionQuality() async {
    try {
      final connectivityResults = await Connectivity().checkConnectivity();

      if (connectivityResults.isEmpty || connectivityResults.contains(ConnectivityResult.none)) {
        return ConnectionQuality.none;
      }

      // Prendre le meilleur type de connexion disponible
      final primaryResult = connectivityResults.first;

      switch (primaryResult) {
        case ConnectivityResult.mobile:
          return ConnectionQuality.mobile;
        case ConnectivityResult.wifi:
          return ConnectionQuality.wifi;
        case ConnectivityResult.ethernet:
          return ConnectionQuality.ethernet;
        case ConnectivityResult.vpn:
          return ConnectionQuality.vpn;
        case ConnectivityResult.bluetooth:
        case ConnectivityResult.other:
          return ConnectionQuality.other;
        case ConnectivityResult.none:
          return ConnectionQuality.none;
      }
    } catch (e') {
      debugPrint('[ConnectivityUtils] Erreur lors de la verification de la qualite de connexion: 'e
