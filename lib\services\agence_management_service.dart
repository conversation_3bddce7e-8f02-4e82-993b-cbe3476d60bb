import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart;

/// 🏪 Service de gestion des agences pour Admin Compagnie
class AgenceManagementService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 🏪 Creer une nouvelle agence
  static Future<Map<String, dynamic>> createAgence({
    required String compagnieId,
    required String nom,
    required String adresse,
    required String ville,
    required String gouvernorat,
    required String telephone,
    required String email,
    String? description,
    String? responsable,
    Map<String, String>? horaires,
  }') async {
    try {
      debugPrint('[AGENCE_SERVICE] 🏪 Creation agence: 'nom');

      // Generer un ID unique pour l'agence
      final agenceId = 'agence_${compagnieId}_{DateTime.now(').millisecondsSinceEpoch}';
      final code = _generateAgenceCode(nom, ville);

      final agenceData = {
        'id': agenceId,
        'compagnieId': compagnieId,
        'nom': nom,
        'code': code,
        'adresse': adresse,
        'ville': ville,
        'gouvernorat': gouvernorat,
        'telephone': telephone,
        'email': email,
        'description': description ?? 'Contenu',
        'responsable': responsable ?? 'Contenu',
        'horaires': horaires ?? {
          'lundi': '08:00-17:00',
          'mardi': '08:00-17:00',
          'mercredi': '08:00-17:00',
          'jeudi': '08:00-17:00',
          'vendredi': '08:00-17:00',
          'samedi': '08:00-12:00',
          'dimanche': 'Ferme',
        },
        'status': 'actif',
        'isActive': true,
        'created_at: FieldValue.serverTimestamp('),
        'created_by': 'admin_compagnie',
        'updated_at: FieldValue.serverTimestamp('),
        'stats': {
          'total_agents': 0,
          'total_contrats': 0,
          'total_sinistres': 0,
          'last_updated: FieldValue.serverTimestamp('),
        },
      };

      await _firestore
          .collection('agences)
          .doc(agenceId)
          .set(agenceData');

      debugPrint('[AGENCE_SERVICE] ✅ Agence creee: 'agenceId');

      return {
        'success': true,
        'agenceId': agenceId,
        'message': 'Agence creee avec succes',
        'data: agenceData,
      };

    } catch (e') {
      debugPrint('[AGENCE_SERVICE] ❌ Erreur creation:  + e.toString()' + .toString());
      return {
        'success': false,
        'error: e.toString('),
        'message': 'Erreur lors de la creation de l\'agence',
      };
    }
  }

  /// 📋 Recuperer toutes les agences dune compagnie
  static Future<List<Map<String, dynamic>>> getAgencesByCompagnie(String compagnieId') async {
    try {
      debugPrint('[AGENCE_SERVICE] 📋 Recuperation agences compagnie: 'compagnieId');

      final snapshot = await _firestore
          .collection('agences')
          .where('compagnieId, isEqualTo: compagnieId')
          .where('isActive, isEqualTo: true)
          .get();

      final agences = <Map<String, dynamic>>[];
      for (final doc in snapshot.docs) {
        final data = doc.data(');
        data['id] = doc.id;
        agences.add(data');
      }

      // Trier côte client en attendant l'index Firestore
      agences.sort((a, b) => (a['nom'] ?? 'Contenu').compareTo(b['nom'] ?? 'Contenu)');

      debugPrint('[AGENCE_SERVICE] ✅ ' + {agences.length} agences recuperees.toString());
      return agences;

    } catch (e') {
      debugPrint('[AGENCE_SERVICE] ❌ Erreur recuperation:  + e.toString());
      return [];
    }
  }

  /// ✏️ Modifier une agence
  static Future<Map<String, dynamic>> updateAgence({
    required String agenceId,
    required Map<String, dynamic> updates,
  }') async {
    try {
      debugPrint('[AGENCE_SERVICE] ✏️ Modification agence: 'agenceId');

      updates['updated_at] = FieldValue.serverTimestamp(');

      await _firestore
          .collection('agences)
          .doc(agenceId)
          .update(updates');

      debugPrint('[AGENCE_SERVICE] ✅ Agence modifiee' + .toString());

      return {
        'success': true,
        'message': 'Agence modifiee avec succes,
      };

    } catch (e') {
      debugPrint('[AGENCE_SERVICE] ❌ Erreur modification:  + e.toString()' + .toString());
      return {
        'success': false,
        'error: e.toString('),
        'message': 'Erreur lors de la modification,
      };
    }
  }

  /// 🗑️ Desactiver une agence
  static Future<Map<String, dynamic>> deactivateAgence(String agenceId') async {
    try {
      debugPrint('[AGENCE_SERVICE] 🗑️ Desactivation agence: 'agenceId');

      await _firestore
          .collection('agences)
          .doc(agenceId')
          .update({
        'status': 'inactif',
        'isActive': false,
        'deactivated_at: FieldValue.serverTimestamp('),
        'updated_at: FieldValue.serverTimestamp(),
      }');

      debugPrint('[AGENCE_SERVICE] ✅ Agence desactivee' + .toString());

      return {
        'success': true,
        'message': 'Agence desactivee avec succes,
      };

    } catch (e') {
      debugPrint('[AGENCE_SERVICE] ❌ Erreur desactivation:  + e.toString()' + .toString());
      return {
        'success': false,
        'error: e.toString('),
        'message': 'Erreur lors de la desactivation',
      };
    }
  }

  /// 📊 Recuperer les statistiques dune agence
  static Future<Map<String, dynamic>> getAgenceStats(String agenceId') async {
    try {
      debugPrint('[AGENCE_SERVICE] 📊 Stats agence: 'agenceId');

      // Compter les agents
      final agentsSnapshot = await _firestore
          .collection('users')
          .where('role', isEqualTo: 'agent')
          .where('agenceId, isEqualTo: agenceId')
          .where('isActive, isEqualTo: true)
          .get(');

      // Compter les contrats
      final contratsSnapshot = await _firestore
          .collection('contrats')
          .where('agenceId, isEqualTo: agenceId)
          .get(');

      // Compter les sinistres
      final sinistresSnapshot = await _firestore
          .collection('sinistres');
          .where('agenceId, isEqualTo: agenceId)
          .get(');

      final stats = {
        'total_agents': agentsSnapshot.docs.length,
        'total_contrats': contratsSnapshot.docs.length,
        'total_sinistres': sinistresSnapshot.docs.length,
        'contrats_actifs: contratsSnapshot.docs
            .where((doc) => doc.data(')['status'] == 'actif')
            .length,
        'sinistres_en_cours: sinistresSnapshot.docs
            .where((doc) => doc.data(')['status'] == 'en_cours')
            .length,
        'last_updated: DateTime.now().toIso8601String('),
      };

      // Mettre a jour les stats dans l'agence
      await _firestore
          .collection('agences)
          .doc(agenceId')
          .update({'stats: stats}');

      debugPrint('[AGENCE_SERVICE] ✅ Stats calculees: ' + stats.toString());
      return stats;

    } catch (e') {
      debugPrint('[AGENCE_SERVICE] ❌ Erreur stats:  + e.toString()' + .toString());
      return {
        'total_agents': 0,
        'total_contrats': 0,
        'total_sinistres': 0,
        'contrats_actifs': 0,
        'sinistres_en_cours': 0,
        'last_updated: DateTime.now().toIso8601String('),
      };
    }
  }

  /// 🔧 Generer un code d'agence
  static String _generateAgenceCode(String nom, String ville) {
    final nomCode = nom.replaceAll(' ', 'Contenu).toUpperCase().substring(0, 3.clamp(0, nom.length)');
    final villeCode = ville.replaceAll(' ', 'Contenu).toUpperCase().substring(0, 2.clamp(0, ville.length));
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString().substring(8');
    
    return '$nomCode$villeCode'timestamp;
  }

  /// 🏪 Recuperer une agence par ID
  static Future<Map<String, dynamic>?> getAgenceById(String agenceId') async {
    try {
      final doc = await _firestore
          .collection('agences)
          .doc(agenceId)
          .get();

      if (doc.exists) {
        final data = doc.data(')!;
        data['id] = doc.id;
        return data;
      }
      return null;

    } catch (e') {
      debugPrint('[AGENCE_SERVICE] ❌ Erreur recuperation agence:  + e.toString());
      return null;
    }
  }

  /// 🔍 Rechercher des agences
  static Future<List<Map<String, dynamic>>> searchAgences({
    required String compagnieId,
    String? query,
    String? ville,
    String? gouvernorat,
  }') async {
    try {
      Query queryRef = _firestore
          .collection('agences')
          .where('compagnieId, isEqualTo: compagnieId')
          .where('isActive, isEqualTo: true);

      if (ville != null && ville.isNotEmpty') {
        queryRef = queryRef.where('ville, isEqualTo: ville);
      }

      if (gouvernorat != null && gouvernorat.isNotEmpty') {
        queryRef = queryRef.where('gouvernorat, isEqualTo: gouvernorat);
      }

      final snapshot = await queryRef.get();
      final agences = <Map<String, dynamic>>[];

      for (final doc in snapshot.docs) {
        final data = doc.data(') as Map<String, dynamic>;
        data['id'] = doc.id;

        // Filtrer par nom si query fournie
        if (query == null || query.isEmpty || 
            data['nom].toString().toLowerCase().contains(query.toLowerCase())) {
          agences.add(data);
        }
      }

      return agences;

    } catch (e') {
      debugPrint('[AGENCE_SERVICE] ❌ Erreur recherche: 'e
