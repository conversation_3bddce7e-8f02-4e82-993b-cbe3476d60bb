import 'package:flutter/material.dart';

/// 📊 Onglet des statistiques pour Admin Agence
class AgenceStatisticsTab extends StatefulWidget {
  final String agenceId;
  final String agenceNom;
  final String compagnieId;

   ) : super(key: key);

  @override
  State<AgenceStatisticsTab> createState() => _AgenceStatisticsTabState();
}

class _AgenceStatisticsTabState extends State<AgenceStatisticsTab> {
  Map<String, dynamic> _stats = {};
  bool _isLoading = true;
  String _selectedPeriod = 'mois';

  @override
  void initState() {
    super.initState();
    _loadStatistics();
  }

  /// 📊 Charger les statistiques
  Future<void> _loadStatistics() async {
    setState(() => _isLoading = true);
    
    try {
      // TODO: Implémenter le service de récupération des statistiques
      // Pour l'instant, données de test
      await Future.delayed(const Duration(seconds: 1));
      setState(() {
        _stats = _generateTestStats();
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: (e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  /// 🧪 Générer des statistiques de test
  Map<String, dynamic> _generateTestStats() {
    return {
      'agents': {
        'total': 8,
        'actifs': 7,
        'inactifs': 1,
        'performance_moyenne': 85.5,
      },
      'sinistres': {
        'total_mois': 23,
        'nouveaux': 5,
        'en_cours': 12,
        'termines': 6,
        'montant_total': 45600.0,
        'montant_moyen': 1982.6,
      },
      'contrats': {
        'total': 156,
        'actifs': 142,
        'expires': 14,
        'nouveaux_mois': 8,
      },
      'experts': {
        'disponibles': 3,
        'occupes': 2,
        'dossiers_assignes': 12,
      },
      'evolution_mensuelle': [
        {'mois': 'Jan', 'sinistres': 18, 'montant': 38200},
        {'mois': 'Fév', 'sinistres': 22, 'montant': 41500},
        {'mois': 'Mar', 'sinistres': 19, 'montant': 39800},
        {'mois': 'Avr', 'sinistres': 25, 'montant': 48300},
        {'mois': 'Mai', 'sinistres': 23, 'montant': 45600},
      ],
      'top_agents': [
        {'nom': 'Mohamed Trabelsi', 'constats': 12, 'score': 92},
        {'nom': 'Leila Sassi', 'constats': 10, 'score': 88},
        {'nom': 'Ahmed Bouaziz', 'constats': 8, 'score': 85},
      ],
    };
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // En-tête
          Container(
            padding: ,
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    ,
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ,
                          ),
                          ({widget.agenceNom}',
                            style: ,
                          ),
                        ],
                      ),
                    ),
                    ElevatedButton.const Icon(
                      onPressed: _exportReport,
                      icon: const Icon(Icons.info),
                      label: const Text('Exporter'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: Colors.indigo.shade600,
                      ),
                    ),
                  ],
                ),
                
                // Sélecteur de période
                const SizedBox(height: 16),
                Row(
                  children: [
                    ,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Container(
                        padding: ,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            value: _selectedPeriod,
                            dropdownColor: Colors.indigo.shade700,
                            style: ,
                            items: ),
                              ),
                              DropdownMenuItem(
                                value: 'mois',
                                child: ),
                              ),
                              DropdownMenuItem(
                                value: 'trimestre',
                                child: ),
                              ),
                              DropdownMenuItem(
                                value: 'annee',
                                child: ),
                              ),
                            ],
                            onChanged: (value) {
                              setState(() {
                                _selectedPeriod = value!;
                              });
                              _loadStatistics();
                            },
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Contenu des statistiques
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _buildStatisticsContent(),
          ),
        ],
      ),
    );
  }

  /// 📊 Contenu des statistiques
  Widget _buildStatisticsContent() {
    return RefreshIndicator(
      onRefresh: _loadStatistics,
      child: SingleChildScrollView(
        padding: ,
            const SizedBox(height: 20),
            
            // Graphique d'évolution
            _buildEvolutionChart(),
            const SizedBox(height: 20),
            
            // Top agents
            _buildTopAgents(),
            const SizedBox(height: 20),
            
            // Répartition des sinistres
            _buildSinistresBreakdown(),
          ],
        ),
      ),
    );
  }

  /// 📋 Cartes de résumé
  Widget _buildSummaryCards() {
    final agents = _stats['agents'] as Map<String, dynamic>? ?? {};
    final sinistres = _stats['sinistres'] as Map<String, dynamic>? ?? {};
    final contrats = _stats['contrats'] as Map<String, dynamic>? ?? {};
    final experts = _stats['experts'] as Map<String, dynamic>? ?? {};

    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: ,
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      childAspectRatio: 1.5,
      children: [
        _buildSummaryCard(
          'Agents Actifs',
          '${agents['actifs'] ?? 0}/${agents['total'] ?? 0}',
          Icons.people,
          Colors.green,
        ),
        _buildSummaryCard(
          'Sinistres ce mois',
          '${sinistres['total_mois'] ?? 0}',
          Icons.warning,
          Colors.orange,
        ),
        _buildSummaryCard(
          'Contrats Actifs',
          '${contrats['actifs'] ?? 0}',
          Icons.description,
          Colors.blue,
        ),
        _buildSummaryCard(
          'Experts Disponibles',
          '${experts['disponibles'] ?? 0}',
          Icons.engineering,
          Colors.purple,
        ),
      ],
    );
  }

  /// 📈 Carte de résumé
  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: (1),
            const SizedBox(height: 8),
            ,
            ),
            ,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 📈 Graphique d'évolution
  Widget _buildEvolutionChart() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: (1),
            ),
            const SizedBox(height: 16),
            Container(
              height: 200,
              child: ,
                    const SizedBox(height: 16),
                    ,
                    ),
                    const SizedBox(height: 8),
                    ,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 🏆 Top agents
  Widget _buildTopAgents() {
    final topAgents = _stats['top_agents'] as List<dynamic>? ?? [];

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: (1),
            ),
            const SizedBox(height: 16),
            ...topAgents.asMap().entries.map((entry) {
              final index = entry.key;
              final agent = entry.value as Map<String, dynamic>;
              return _buildTopAgentItem(index + 1, agent);
            }).toList(),
          ],
        ),
      ),
    );
  }

  /// 🥇 Item top agent
  Widget _buildTopAgentItem(int rank, Map<String, dynamic> agent) {
    Color rankColor = Colors.grey;
    IconData rankIcon = Icons.emoji_events;
    
    switch (rank) {
      case 1:
        rankColor = Colors.amber;
        break;
      case 2:
        rankColor = Colors.grey[400]!;
        break;
      case 3:
        rankColor = Colors.brown;
        break;
    }

    return (1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: (rank',
                style: TextStyle(
                  color: rankColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ,
                ),
                ({agent['score']}%',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: ,
              borderRadius: BorderRadius.circular(12),
            ),
            child: ({agent['score']}%',
              style: ,
            ),
          ),
        ],
      ),
    );
  }

  /// 📊 Répartition des sinistres
  Widget _buildSinistresBreakdown() {
    final sinistres = _stats['sinistres'] as Map<String, dynamic>? ?? {};

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: (1),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildBreakdownItem(
                    'Nouveaux',
                    sinistres['nouveaux'] ?? 0,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildBreakdownItem(
                    'En cours',
                    sinistres['en_cours'] ?? 0,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildBreakdownItem(
                    'Terminés',
                    sinistres['termines'] ?? 0,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                ,
                const SizedBox(width: 4),
                ({sinistres['montant_total'] ?? 0} TND',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.green[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                ,
                const SizedBox(width: 4),
                ({sinistres['montant_moyen']?.toStringAsFixed(1) ?? 0} TND',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.blue[600],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 📊 Item de répartition
  Widget _buildBreakdownItem(String label, int value, Color color) {
    return Column(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: color.withValues(alpha: ,
            borderRadius: BorderRadius.circular(30),
          ),
          child: (value',
              style: TextStyle(
                color: color,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        ,
        ),
      ],
    );
  }

  /// 📄 Exporter le rapport
  void _exportReport() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Exporter le rapport'),
        content: const Text('Fonctionnalité d\'export bientôt disponible'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK
