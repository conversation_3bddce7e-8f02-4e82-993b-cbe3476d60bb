import 'package:flutter/material.dart';
import '../services/admin_agence_auth_service.dart';
import 'widgets/agence_agents_tab.dart';
import 'widgets/agence_sinistres_tab.dart';
import 'widgets/agence_experts_tab.dart';
import 'widgets/agence_statistics_tab.dart';

/// 🏪 Dashboard Admin Agence
class AdminAgenceDashboard extends StatefulWidget {
  final String agenceId;
  final String agenceNom;
  final String compagnieId;
  final String? compagnieNom;
  final Map<String, dynamic> adminData;

  const AdminAgenceDashboard({
    Key? key,
    required this.agenceId,
    required this.agenceNom,
    required this.compagnieId,
    this.compagnieNom,
    required this.adminData,
  }) : super(key: key);

  @override
  State<AdminAgenceDashboard> createState() => _AdminAgenceDashboardState();
}

class _AdminAgenceDashboardState extends State<AdminAgenceDashboard>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// 🚪 Déconnexion
  Future<void> _logout() async {
    await AdminAgenceAuthService.logout();
    if (mounted) {
      Navigator.of(context).pushReplacementNamed('/');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Dashboard ${widget.agenceNom}'),
        backgroundColor: Colors.teal,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: _logout,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(icon: const Icon(Icons.people), text: 'Agents'),
            Tab(icon: const Icon(Icons.report), text: 'Sinistres'),
            Tab(icon: const Icon(Icons.engineering), text: 'Experts'),
            Tab(icon: const Icon(Icons.analytics), text: 'Stats'),
          ],
        ),
      ),
      body: const TabBarView(
        children: [
          Center(child: const Text('Agents Tab')),
          Center(child: const Text('Sinistres Tab')),
          Center(child: const Text('Experts Tab')),
          Center(child: const Text('Stats Tab')),
        ],
      ),
    );
  }
}
