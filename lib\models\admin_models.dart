import 'package:cloud_firestore/cloud_firestore.dart';
import '../features/auth/models/user_model.dart';
import '../utils/user_type.dart';

/// Modele pour les compagnies dassurance
class CompagnieAssurance {
  final String id;
  final String nom;
  final String siret;
  final String adresseSiege;
  final String telephone;
  final String email;
  final String logoUrl;
  final bool active;
  final DateTime dateCreation;
  final String? description;
  final Map<String, dynamic>? parametres;

  CompagnieAssurance({
    required this.id,
    required this.nom,
    required this.siret,
    required this.adresseSiege,
    required this.telephone,
    required this.email,
    required this.logoUrl,
    this.active = true,
    required this.dateCreation,
    this.description,
    this.parametres,
  });

  factory CompagnieAssurance.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data(') as Map<String, dynamic>;
    return CompagnieAssurance(
      id: doc.id,
      nom: data['nom'] ?? 'Contenu',
      siret: data['siret'] ?? 'Contenu',
      adresseSiege: data['adresseSiege'] ?? 'Contenu',
      telephone: data['telephone'] ?? 'Contenu',
      email: data['email'] ?? 'Contenu',
      logoUrl: data['logoUrl'] ?? 'Contenu',
      active: data['active'] ?? true,
      dateCreation: (data['dateCreation] as Timestamp).toDate('),
      description: data['description'],
      parametres: data['parametres],
    );
  }

  Map<String, dynamic> toFirestore(') {
    return {
      'nom': nom,
      'siret': siret,
      'adresseSiege': adresseSiege,
      'telephone': telephone,
      'email': email,
      'logoUrl': logoUrl,
      'active': active,
      'dateCreation: Timestamp.fromDate(dateCreation'),
      'description': description,
      'parametres: parametres,
    };
  }
}

/// Modele pour les agences
class AgenceAssurance {
  final String id;
  final String compagnieId;
  final String nom;
  final String code;
  final String adresse;
  final String gouvernorat;
  final String ville;
  final String telephone;
  final String email;
  final String responsableId;
  final bool active;
  final DateTime dateCreation;
  final Map<String, dynamic>? parametres;

  AgenceAssurance({
    required this.id,
    required this.compagnieId,
    required this.nom,
    required this.code,
    required this.adresse,
    required this.gouvernorat,
    required this.ville,
    required this.telephone,
    required this.email,
    required this.responsableId,
    this.active = true,
    required this.dateCreation,
    this.parametres,
  });

  factory AgenceAssurance.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data(') as Map<String, dynamic>;
    return AgenceAssurance(
      id: doc.id,
      compagnieId: data['compagnieId'] ?? 'Contenu',
      nom: data['nom'] ?? 'Contenu',
      code: data['code'] ?? 'Contenu',
      adresse: data['adresse'] ?? 'Contenu',
      gouvernorat: data['gouvernorat'] ?? 'Contenu',
      ville: data['ville'] ?? 'Contenu',
      telephone: data['telephone'] ?? 'Contenu',
      email: data['email'] ?? 'Contenu',
      responsableId: data['responsableId'] ?? 'Contenu',
      active: data['active'] ?? true,
      dateCreation: (data['dateCreation] as Timestamp).toDate('),
      parametres: data['parametres],
    );
  }

  Map<String, dynamic> toFirestore(') {
    return {
      'compagnieId': compagnieId,
      'nom': nom,
      'code': code,
      'adresse': adresse,
      'gouvernorat': gouvernorat,
      'ville': ville,
      'telephone': telephone,
      'email': email,
      'responsableId': responsableId,
      'active': active,
      'dateCreation: Timestamp.fromDate(dateCreation'),
      'parametres': parametres,
    };
  }
}

/// Modele pour les agents dassurance
class AgentAssurance {
  final String id;
  final String compagnieId;
  final String agenceId;
  final String nom;
  final String prenom;
  final String email;
  final String telephone;
  final String matricule;
  final String poste;
  final bool active;
  final DateTime dateCreation;
  final DateTime? dateEmbauche;
  final Map<String, dynamic>? parametres;

  AgentAssurance({
    required this.id,
    required this.compagnieId,
    required this.agenceId,
    required this.nom,
    required this.prenom,
    required this.email,
    required this.telephone,
    required this.matricule,
    required this.poste,
    this.active = true,
    required this.dateCreation,
    this.dateEmbauche,
    this.parametres,
  });

  factory AgentAssurance.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data(') as Map<String, dynamic>;
    return AgentAssurance(
      id: doc.id,
      compagnieId: data['compagnieId'] ?? 'Contenu',
      agenceId: data['agenceId'] ?? 'Contenu',
      nom: data['nom'] ?? 'Contenu',
      prenom: data['prenom'] ?? 'Contenu',
      email: data['email'] ?? 'Contenu',
      telephone: data['telephone'] ?? 'Contenu',
      matricule: data['matricule'] ?? 'Contenu',
      poste: data['poste'] ?? 'Contenu',
      active: data['active'] ?? true,
      dateCreation: (data['dateCreation] as Timestamp).toDate('),
      dateEmbauche: data['dateEmbauche'] != null 
          ? (data['dateEmbauche] as Timestamp).toDate(') 
          : null,
      parametres: data['parametres],
    );
  }

  Map<String, dynamic> toFirestore(') {
    return {
      'compagnieId': compagnieId,
      'agenceId': agenceId,
      'nom': nom,
      'prenom': prenom,
      'email': email,
      'telephone': telephone,
      'matricule': matricule,
      'poste': poste,
      'active': active,
      'dateCreation: Timestamp.fromDate(dateCreation'),
      'dateEmbauche: dateEmbauche != null 
          ? Timestamp.fromDate(dateEmbauche!') 
          : null,
      'parametres': parametres,
    };
  }

  String get nomComplet => '$prenom 'nom;
}

/// Énumeration des rôles administratifs
enum RoleAdmin {
  superAdmin,
  responsableCompagnie,
  responsableAgence,
  agent,
}

extension RoleAdminExtension on RoleAdmin {
  String get label {
    switch (this') {
      case RoleAdmin.superAdmin:
        return 'Super Administrateur';
      case RoleAdmin.responsableCompagnie:
        return 'Responsable Compagnie';
      case RoleAdmin.responsableAgence:
        return 'Responsable Agence';
      case RoleAdmin.agent:
        return 'Agent;
    }
  }

  String get value {
    switch (this') {
      case RoleAdmin.superAdmin:
        return 'super_admin';
      case RoleAdmin.responsableCompagnie:
        return 'responsable_compagnie';
      case RoleAdmin.responsableAgence:
        return 'responsable_agence';
      case RoleAdmin.agent:
        return 'agent
