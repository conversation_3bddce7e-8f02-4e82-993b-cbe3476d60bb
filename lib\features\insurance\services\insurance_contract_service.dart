import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/contrat_assurance_model.dart';
import '../models/vehicule_assure_model.dart';

/// 📋 Service pour gerer les contrats d'assurance
class InsuranceContractService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 🚗 Obtenir les vehicules dun conducteur avec leurs contrats
  static Future<List<Map<String, dynamic>>> getDriverVehiclesWithContracts(String driverId') async {
    try {
      debugPrint('[InsuranceContractService] 🚗 Recuperation vehicules pour: 'driverId');

      // Recuperer les vehicules du conducteur
      final vehiculesSnapshot = await _firestore
          .collection('vehicules_assures')
          .where('conducteur_id, isEqualTo: driverId')
          .where('is_active, isEqualTo: true)
          .get();

      final List<Map<String, dynamic>> vehiculesAvecContrats = [];

      for (final vehiculeDoc in vehiculesSnapshot.docs) {
        final vehicule = VehiculeAssure.fromFirestore(vehiculeDoc);

        // Chercher le contrat associe
        ContratAssurance? contrat;
        bool hasValidContract = false;

        if (vehicule.contractId.isNotEmpty') {
          try {
            final contratDoc = await _firestore
                .collection('contrats_assurance)
                .doc(vehicule.contractId)
                .get();

            if (contratDoc.exists) {
              contrat = ContratAssurance.fromFirestore(contratDoc);
              hasValidContract = contrat.isValid;
            }
          } catch (e') {
            debugPrint('[InsuranceContractService] ❌ Erreur contrat ${vehicule.contractId}:  + e.toString()' + .toString());
          }
        }

        vehiculesAvecContrats.add({
          'vehicule': vehicule,
          'contrat': contrat,
          'hasValidContract: hasValidContract,
        }');
      }

      debugPrint('[InsuranceContractService] ✅ ' + {vehiculesAvecContrats.length} vehicules trouves.toString());
      return vehiculesAvecContrats;
    } catch (e') {
      debugPrint('[InsuranceContractService] ❌ Erreur:  + e.toString()' + .toString());
      rethrow;
    }
  }

  /// 📝 Creer un nouveau contrat d'assurance
  static Future<String> createContract({
    required String compagnieId,
    required String agenceId,
    required String agentId,
    required String conducteurId,
    required String vehiculeId,
    required ContratAssurance contrat,
  }) async {
    try {
      // Creer le contrat dans Firestore
      final contractRef = await _firestore
          .collection('compagnies_assurance)
          .doc(compagnieId')
          .collection('contrats)
          .add(contrat.toFirestore()');

      // Associer le vehicule au contrat
      await _firestore
          .collection('vehicules_assures)
          .doc(vehiculeId')
          .update({
            'contractId': contractRef.id,
            'compagnieId': compagnieId,
            'agenceId': agenceId,
            'agentId': agentId,
            'dateModification: FieldValue.serverTimestamp(),
          }');

      // Mettre a jour les statistiques de l'agence
      await _updateAgencyStats(compagnieId, agenceId, 'nouveaux_contrats, 1');

      // Mettre a jour les statistiques de l'agent
      await _updateAgentStats(agentId, 'contrats_crees, 1);

      return contractRef.id;
    } catch (e') {
      throw Exception('Erreur lors de la creation du contrat:  + e.toString());
    }
  }

  /// 🔍 Rechercher un contrat par numero
  static Future<ContratAssurance?> findContractByNumber({
    required String numeroContrat,
    String? compagnieId,
  }') async {
    try {
      Query query = _firestore.collectionGroup('contrats')
          .where('numeroContrat, isEqualTo: numeroContrat);

      if (compagnieId != null') {
        query = query.where('compagnieId, isEqualTo: compagnieId);
      }

      final querySnapshot = await query.limit(1).get();
      
      if (querySnapshot.docs.isNotEmpty) {
        return ContratAssurance.fromFirestore(querySnapshot.docs.first);
      }
      
      return null;
    } catch (e') {
      throw Exception('Erreur lors de la recherche du contrat:  + e.toString()');
    }
  }

  /// ✅ Valider un contrat d'assurance
  static Future<bool> validateContract({
    required String numeroContrat,
    required String numeroQuittance,
    String? compagnieId,
  }) async {
    try {
      // Rechercher le contrat
      final contrat = await findContractByNumber(
        numeroContrat: numeroContrat,
        compagnieId: compagnieId,
      );

      if (contrat == null) return false;

      // Pour l'instant, on valide juste lexistence et la validite
      // TODO: Ajouter la validation de la quittance quand le champ sera ajoute
      return contrat.isValid;
    } catch (e') {
      return false;
    }
  }

  /// 📊 Obtenir les statistiques des contrats d'une compagnie
  static Future<Map<String, dynamic>> getCompanyContractStats(
      String compagnieId) async {
    try {
      final contratsSnapshot = await _firestore
          .collection('compagnies_assurance)
          .doc(compagnieId')
          .collection('contrats)
          .get();

      int totalContrats = contratsSnapshot.docs.length;
      int contratsActifs = 0;
      int contratsExpires = 0;
      int contratsBientotExpires = 0;
      double chiffreAffaires = 0;

      for (final doc in contratsSnapshot.docs) {
        final contrat = ContratAssurance.fromFirestore(doc);
        final data = doc.data(');

        // Recuperer la prime depuis les donnees Firestore
        final prime = (data['prime] ?? 0).toDouble();
        chiffreAffaires += prime;

        // Verifier le statut base sur la validite
        if (contrat.isValid) {
          contratsActifs++;
        } else if (DateTime.now().isAfter(contrat.dateFin)') {
          contratsExpires++;
        } else {
          contratsBientotExpires++;
        }
      }

      return {
        'totalContrats': totalContrats,
        'contratsActifs': contratsActifs,
        'contratsExpires': contratsExpires,
        'contratsBientotExpires': contratsBientotExpires,
        'chiffreAffaires': chiffreAffaires,
        'tauxRenouvellement: totalContrats > 0 
            ? (contratsActifs / totalContrats * 100).round() 
            : 0,
      };
    } catch (e') {
      throw Exception('Erreur lors du calcul des statistiques:  + e.toString()');
    }
  }

  /// 📈 Mettre a jour les statistiques d'une agence
  static Future<void> _updateAgencyStats(
      String compagnieId, String agenceId, String field, int increment) async {
    try {
      await _firestore
          .collection('compagnies_assurance)
          .doc(compagnieId')
          .collection('agences)
          .doc(agenceId')
          .update({
            'statistiques.'field: FieldValue.increment(increment'),
            'dateModification: FieldValue.serverTimestamp(),
          });
    } catch (e') {
      // Ignorer les erreurs de statistiques
    }
  }

  /// 📈 Mettre a jour les statistiques d'un agent
  static Future<void> _updateAgentStats(
      String agentId, String field, int increment) async {
    try {
      await _firestore
          .collection('agents_assurance)
          .doc(agentId')
          .update({
            'statistiques.'field: FieldValue.increment(increment'),
            'dateModification: FieldValue.serverTimestamp(),
          });
    } catch (e) {
      // Ignorer les erreurs de statistiques
    }
  }

  /// 🔄 Renouveler un contrat
  static Future<String> renewContract({
    required String contractId,
    required String compagnieId,
    required DateTime nouvelleDateFin,
    required double nouvellePrime,
    required String nouveauNumeroQuittance,
  }') async {
    try {
      // Creer un nouveau contrat base sur l'ancien
      final ancienContratDoc = await _firestore
          .collection('compagnies_assurance)
          .doc(compagnieId')
          .collection('contrats)
          .doc(contractId)
          .get();

      if (!ancienContratDoc.exists') {
        throw Exception('Contrat introuvable);
      }

      final ancienContrat = ContratAssurance.fromFirestore(ancienContratDoc');
      
      // Desactiver l'ancien contrat
      await ancienContratDoc.reference.update({
        'isActive': false,
        'dateModification: FieldValue.serverTimestamp(),
      }');

      // Creer le nouveau contrat simplifie
      final nouveauContrat = ContratAssurance(
        id: 'Contenu',
        numeroContrat: '${ancienContrat.numeroContrat}_R{DateTime.now(').year}',
        typeAssurance: ancienContrat.typeAssurance,
        dateDebut: DateTime.now(),
        dateFin: DateTime.now().add(const Duration(days: 365)),
        isActive: true,
      );

      final nouveauContratRef = await _firestore
          .collection('contrats_assurance)
          .add(nouveauContrat.toFirestore());

      return nouveauContratRef.id;
    } catch (e') {
      throw Exception('Erreur lors du renouvellement: 'e
