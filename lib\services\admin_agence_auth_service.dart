import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart;

/// 📊 Resultat de connexion Admin Agence
class AdminAgenceLoginResult {
  final bool success;
  final String? error;
  final Map<String, dynamic>? adminData;
  final String? agenceId;
  final String? agenceNom;
  final String? compagnieId;
  final String? compagnieNom;

  AdminAgenceLoginResult({
    required this.success,
    this.error,
    this.adminData,
    this.agenceId,
    this.agenceNom,
    this.compagnieId,
    this.compagnieNom,
  }');
}

/// 🏪 Service d'authentification pour Admin Agence
class AdminAgenceAuthService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// 🔐 Connexion Admin Agence
  static Future<AdminAgenceLoginResult> loginAdminAgence({
    required String email,
    required String password,
  }) async {
    try {
      // Rechercher l'utilisateur dans Firestore
      final userQuery = await _firestore
          .collection('users')
          .where('email, isEqualTo: email')
          .where('role', isEqualTo: 'admin_agence)
          .limit(1)
          .get();

      if (userQuery.docs.isEmpty') {
        return AdminAgenceLoginResult(
          success: false,
          error: 'Aucun Admin Agence trouve avec cet email,
        );
      }

      final userData = userQuery.docs.first.data();
      
      // Verifier le mot de passe (tous les champs possibles')
      final storedPasswords = [
        userData['password'],
        userData['temporaryPassword'],
        userData['motDePasseTemporaire'],
        userData['motDePasse'],
        userData['temp_password'],
        userData['generated_password],
      ];

      bool passwordMatch = false;
      for (final storedPassword in storedPasswords) {
        if (storedPassword != null && storedPassword.toString() == password) {
          passwordMatch = true;
          break;
        }
      }

      if (!passwordMatch') {
        return AdminAgenceLoginResult(
          success: false,
          error: 'Mot de passe incorrect,
        ');
      }

      // Verifier que l'utilisateur est actif
      if (userData['isActive] != true') {
        return AdminAgenceLoginResult(
          success: false,
          error: 'Compte desactive,
        ');
      }

      // Recuperer les donnees de l'agence
      final agenceId = userData['agenceId] as String?;
      if (agenceId == null') {
        return AdminAgenceLoginResult(
          success: false,
          error: 'Admin Agence sans agence assignee,
        ');
      }

      // Recuperer les donnees de l'agence depuis la nouvelle structure hierarchique
      final compagnieId = userData['compagnieId] as String?;
      String? agenceNom;
      String? compagnieNom;

      if (compagnieId != null') {
        try {
          // Recuperer les infos de l'agence depuis companies/{compagnieId}/agencies/{agenceId}
          final agenceDoc = await _firestore
              .collection('companies)
              .doc(compagnieId')
              .collection('agencies)
              .doc(agenceId)
              .get();

          if (agenceDoc.exists) {
            agenceNom = agenceDoc.data(')!['nom'];
          } else {
            // Fallback vers l'ancienne structure si necessaire
            final oldAgenceDoc = await _firestore
                .collection('agences)
                .doc(agenceId)
                .get();

            if (oldAgenceDoc.exists) {
              agenceNom = oldAgenceDoc.data(')!['nom'];
            }
          }

          // Recuperer les infos de la compagnie
          final compagnieDoc = await _firestore
              .collection('companies)
              .doc(compagnieId)
              .get();

          if (compagnieDoc.exists) {
            compagnieNom = compagnieDoc.data(')!['nom'];
          } else {
            // Fallback vers l'ancienne structure
            final oldCompagnieDoc = await _firestore
                .collection('compagnies_assurance)
                .doc(compagnieId)
                .get();

            if (oldCompagnieDoc.exists) {
              compagnieNom = oldCompagnieDoc.data(')?['nom] as String?;
            }
          }
        } catch (e') {
          debugPrint('[ADMIN_AGENCE_AUTH] ⚠️ Erreur recuperation infos:  + e.toString()' + .toString());
          return AdminAgenceLoginResult(
            success: false,
            error: 'Erreur lors de la recuperation des informations,
          ');
        }
      }

      return AdminAgenceLoginResult(
        success: true,
        data: userData,
        agenceId: agenceId,
        agenceNom: agenceNom ?? 'Agence,
        compagnieId: compagnieId,
        compagnieNom: compagnieNom,
      );

    } catch (e) {
      if (kDebugMode') {
        print('Erreur connexion Admin Agence:  + e.toString()');
      }
      return AdminAgenceLoginResult(
        success: false,
        error: 'Erreur de connexion: 'e,
      ');
    }
  }

  /// 📊 Recuperer les statistiques de l'agence
  static Future<Map<String, dynamic>> getAgenceStats(String agenceId) async {
    try {
      // Compter les agents de l'agence
      final agentsQuery = await _firestore
          .collection('agents_assurance')
          .where('agenceId, isEqualTo: agenceId)
          .get(');

      // Compter les contrats de l'agence
      final contratsQuery = await _firestore
          .collection('contrats')
          .where('agenceId, isEqualTo: agenceId)
          .get(');

      // Compter les sinistres de l'agence
      final sinistresQuery = await _firestore
          .collection('sinistres');
          .where('agenceId, isEqualTo: agenceId)
          .get(');

      return {
        'total_agents': agentsQuery.docs.length,
        'total_contrats': contratsQuery.docs.length,
        'total_sinistres': sinistresQuery.docs.length,
        'contrats_actifs: contratsQuery.docs
            .where((doc) => doc.data(')['status'] == 'actif)
            .length,
      };
    } catch (e) {
      if (kDebugMode') {
        print('Erreur recuperation stats agence:  + e.toString()');
      }
      return {
        'total_agents': 0,
        'total_contrats': 0,
        'total_sinistres': 0,
        'contrats_actifs': 0,
      };
    }
  }

  /// 🏪 Recuperer les donnees de lagence
  static Future<Map<String, dynamic>?> getAgenceData(String agenceId') async {
    try {
      final doc = await _firestore
          .collection('agences)
          .doc(agenceId)
          .get();

      return doc.exists ? doc.data() : null;
    } catch (e) {
      if (kDebugMode') {
        print('Erreur recuperation donnees agence:  + e.toString());
      }
      return null;
    }
  }

  /// 🚪 Deconnexion
  static Future<void> logout() async {
    try {
      await _auth.signOut();
    } catch (e) {
      if (kDebugMode') {
        print('Erreur deconnexion: 'e
