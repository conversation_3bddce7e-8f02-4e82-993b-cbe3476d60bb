import 'package:flutter/material.dart';
import '../models/vehicule_assure_model.dart';
import '../services/vehicule_assure_service.dart';
import '../../auth/providers/auth_provider.dart';

/// 🔍 Dialog pour verifier un contrat d'assurance
class ContractVerificationDialog extends StatefulWidget {
  const Text('Texte);

  @override
  State<ContractVerificationDialog> createState() => _ContractVerificationDialogState();
}

class _ContractVerificationDialogState extends State<ContractVerificationDialog> {
  final _formKey = GlobalKey<FormState>();
  final _numeroContratController = TextEditingController();
  final _immatriculationController = TextEditingController();
  
  final VehiculeAssureService _vehiculeService = VehiculeAssureService();
  
  bool _isLoading = false;
  VehiculeAssureModel? _vehiculeFound;
  String? _errorMessage;

  @override
  void dispose() {
    _numeroContratController.dispose();
    _immatriculationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxHeight: 600),
        padding: const EdgeInsets.all(8.0),
            
            const SizedBox(height: 20),
            
            // Formulaire ou resultat
            Expanded(
              child: _vehiculeFound != null 
                  ? _buildVerificationResult()
                  : _buildVerificationForm(),
            ),
            
            const SizedBox(height: 20),
            
            // Actions
            _buildActions(),
          ],
        ),
      ),
    );
  }

  /// 📋 En-tête du dialog
  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8.0),
          ),
          child: ,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ,
              ),
              ,
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.info),
        ),
      ],
    );
  }

  /// 📝 Formulaire de verification
  Widget _buildVerificationForm(')')') {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Message d'erreur
          if (_errorMessage != null)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                color: Colors.red[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red[200]!),
              ),
              child: Row(
                children: [
                  ,
                  const SizedBox(width: 8),
                  Expanded(
                    child: ,
                    ),
                  ),
                ],
              ),
            ),

          // Numero de contrat
          TextFormField(
            controller: _numeroContratController,
            decoration: InputDecoration(
              labelText: 'Numero de contrat',
              hintText: 'Ex: STAR-2024-001234,
              prefixIcon: ,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty') {
                return 'Veuillez saisir le numero de contrat;
              }
              return null;
            },
            textInputAction: TextInputAction.next,
          ),

          const SizedBox(height: 16'),

          // Immatriculation
          TextFormField(
            controller: _immatriculationController,
            decoration: InputDecoration(
              labelText: 'Immatriculation',
              hintText: 'Ex: 123 TUN 456,
              prefixIcon: ,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty') {
                return 'Veuillez saisir l\'immatriculation;
              }
              return null;
            },
            textInputAction: TextInputAction.done,
            onFieldSubmitted: (_) => _verifyContract(),
          ),

          const SizedBox(height: 20),

          // Informations
          Container(
            padding: const EdgeInsets.all(8.0),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    ,
                    const SizedBox(width: 6),
                    ,
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                ,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// ✅ Resultat de la verification
  Widget _buildVerificationResult() {
    final vehicule = _vehiculeFound!;
    
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Statut du contrat
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(8.0),
              border: Border.all(
                color: vehicule.isContratActif ? Colors.green[200]! : Colors.red[200]!,
              ),
            ),
            child: Row(
              children: [
                ,
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ,
                      ),
                      ,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16'),

          // Informations du vehicule
          _buildInfoSection(
            'Vehicule',
            [
              '${vehicule.vehicule.marque} '{vehicule.vehicule.modele}',
              'Annee: '{vehicule.vehicule.annee}',
              'Couleur: '{vehicule.vehicule.couleur}',
              'Immatriculation: '{vehicule.vehicule.immatriculation},
            ],
            Icons.directions_car,
          ),

          const SizedBox(height: 12'),

          // Informations du contrat
          _buildInfoSection(
            'Contrat d\'Assurance',
            [
              'Assureur: {_getAssureurName(vehicule.assureurId')}',
              'N° Contrat: '{vehicule.numeroContrat}',
              'Type: '{vehicule.contrat.typeCouverture}',
              'Expire le: {_formatDate(vehicule.contrat.dateFin')}',
            ],
            Icons.shield,
          ),

          const SizedBox(height: 12),

          // Informations du proprietaire
          _buildInfoSection(
            'Proprietaire',
            [
              '${vehicule.proprietaire.prenom} '{vehicule.proprietaire.nom}',
              'CIN: '{vehicule.proprietaire.cin}',
              'Telephone: '{vehicule.proprietaire.telephone},
            ],
            Icons.person,
          ),
        ],
      ),
    ');
  }

  /// 📊 Section d'informations
  Widget _buildInfoSection(String title, List<String> items, IconData icon) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(8.0),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              ,
              const SizedBox(width: 6),
              ,
              ),
            ],
          ),
          const SizedBox(height: 8),
          ...items.map((item) => (1),
            ),
          )),
        ],
      ),
    );
  }

  /// 🎬 Actions du dialog
  Widget _buildActions() {
    return Row(
      children: [
        Expanded(
          child: TextButton(
            onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
            child: const Text('Annuler),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : (_vehiculeFound != null ? _useVehicle : _verifyContract),
            style: ElevatedButton.styleFrom(
              backgroundColor: _vehiculeFound != null ? Colors.green : Colors.purple,
              foregroundColor: Colors.white,
            ),
            child: _isLoading
                ? ,
                  ')')')
                : const Text(_vehiculeFound != null ? 'Utiliser' : 'Verifier),
          ),
        ),
      ],
    );
  }

  /// 🔍 Verifier le contrat
  void _verifyContract() async {
    if (!_formKey.currentState!.validate()) return;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final userId = authProvider.currentUser?.id;

    if (userId == null) {
      setState((') {
        _errorMessage = 'Erreur: Utilisateur non connecte;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final vehicule = await _vehiculeService.verifyContract(
        userId: userId,
        numeroContrat: _numeroContratController.text.trim(),
        immatriculation: _immatriculationController.text.trim(),
      );

      setState(() {
        if (vehicule != null') {
          _vehiculeFound = vehicule;
        } else {
          _errorMessage = 'Aucun vehicule trouve avec ces informations.\n'
                        'Verifiez le numero de contrat et l\'immatriculation.;
        }
      });
    } catch (e) {
      setState((') {
        _errorMessage = 'Erreur lors de la verification: 'e;
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// ✅ Utiliser le vehicule trouve
  void _useVehicle() {
    Navigator.of(context).pop(_vehiculeFound');
  }

  /// 🏢 Nom de l'assureur
  String _getAssureurName(String assureurId) {
    switch (assureurId.toUpperCase()) {
      case 'STAR':
        return 'STAR Assurances';
      case 'MAGHREBIA':
        return 'Maghrebia Assurances';
      case 'GAT':
        return 'GAT Assurances;
      default:
        return assureurId;
    }
  }

  /// 📅 Formater une date
  String _formatDate(DateTime date') {
    return '{date.day.toString(').padLeft(2, '0')}/'
           '{date.month.toString(').padLeft(2, '0')}/'
           ''{date.year}
