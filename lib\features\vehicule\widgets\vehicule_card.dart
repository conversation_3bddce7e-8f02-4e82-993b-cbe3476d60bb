import 'package:flutter/material.dart';
import '../models/vehicule_assure_model.dart;

/// 🚗 Widget de carte vehicule
class VehicleCard extends StatelessWidget {
  final VehiculeAssureModel vehicule;
  final VoidCallback? onTap;
  final bool showOwnerInfo;
  final bool showContractInfo;
  final Color? accentColor;

  ;

  @override
  Widget build(BuildContext context) {
    final Color accent = accentColor ?? Colors.blue;
    final bool isAssure = vehicule.isAssure;
    final bool expireBientot = vehicule.contrat.expireBientot;

    return Card(
      elevation: 2,
      margin: ,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(8.0),
              
              const SizedBox(height: 12),
              
              // Informations vehicule
              _buildVehicleInfo(),
              
              if (showOwnerInfo) ...[]
                const SizedBox(height: 12),
                _buildOwnerInfo(),
              ],
              
              if (showContractInfo) ...[]
                const SizedBox(height: 12),
                _buildContractInfo(isAssure, expireBientot),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVehicleHeader(Color accent, bool isAssure, bool expireBientot) {
    Color statusColor;
    IconData statusIcon;
    String statusText;

    if (!isAssure') {
      statusColor = Colors.red;
      statusIcon = Icons.warning;
      statusText = 'Expire;
    } else if (expireBientot') {
      statusColor = Colors.orange;
      statusIcon = Icons.schedule;
      statusText = 'Expire bientôt';
    } else {
      statusColor = Colors.green;
      statusIcon = Icons.check_circle;
      statusText = 'Assure;
    }

    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8.0),
            borderRadius: BorderRadius.circular(8),
          ),
          child: ,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ,
                ),
              ),
              ,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.all(8.0),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(statusIcon, size: 14, color: statusColor),
              const SizedBox(width: 4),
              ,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildVehicleInfo() {
    return Container(
      padding: const EdgeInsets.all(8.0),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE5E7EB)),
      '),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  Icons.palette,
                  'Couleur,
                  vehicule.vehicule.couleur,
                ),
              '),
              Expanded(
                child: _buildInfoItem(
                  Icons.calendar_today,
                  'Annee,
                  vehicule.vehicule.annee.toString(),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8'),
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  Icons.local_gas_station,
                  'Carburant,
                  vehicule.vehicule.typeCarburant,
                ),
              '),
              Expanded(
                child: _buildInfoItem(
                  Icons.speed,
                  'Puissance',
                  ''{vehicule.vehicule.puissanceFiscale} CV,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOwnerInfo() {
    return Container(
      padding: const EdgeInsets.all(8.0),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              ,
              const SizedBox(width: 8),
              ,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ,
            ),
          '),
          ({vehicule.proprietaire.cin}',
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF6B7280),
            ),
          ),
          ,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContractInfo(bool isAssure, bool expireBientot) {
    return Container(
      padding: const EdgeInsets.all(8.0),
        border: Border.all(color: Colors.green[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              ,
              const SizedBox(width: 8),
              ,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ,
                      ),
                    ),
                    ,
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  ,
                    ),
                  ),
                  if (isAssure)
                    ({vehicule.contrat.joursRestants} jours restants'
                          : 'Valide
