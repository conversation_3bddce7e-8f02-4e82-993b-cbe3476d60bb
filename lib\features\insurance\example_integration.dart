import 'package:flutter/material.dart';
import 'widgets/insurance_navigation.dart';
import 'services/notification_service.dart';
import 'utils/insurance_styles.dart';

/// 📱 Exemple d'intégration des fonctionnalités d'assurance dans l'app principale
class InsuranceIntegrationExample extends StatefulWidget {
  const InsuranceIntegrationExample({Key? key}) ) : super(key: key);

  @override
  State<InsuranceIntegrationExample> createState() => _InsuranceIntegrationExampleState();
}

class _InsuranceIntegrationExampleState extends State<InsuranceIntegrationExample> {
  int _notificationCount = 0;

  @override
  void initState() {
    super.initState();
    _initializeNotifications();
    _loadNotificationCount();
  }

  Future<void> _initializeNotifications() async {
    await InsuranceNotificationService.initializeLocalNotifications();
  }

  Future<void> _loadNotificationCount() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      // Écouter les notifications en temps réel
      InsuranceNotificationService.getUserNotifications(user.uid).listen((notifications) {
        final unreadCount = notifications.where((n) => !n['isRead']).length;
        if (mounted) {
          setState(() {
            _notificationCount = unreadCount;
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text("Titre"),
        ),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          // Badge de notification
          Stack(
            children: [
              IconButton(
                icon: const Icon(Icons.info),
                onPressed: _showNotifications,
              ),
              if (_notificationCount > 0)
                Positioned(
                  right: 8,
                  top: 8,
                  child: InsuranceNavigation.buildNotificationBadge(context, _notificationCount),
                ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: ,
            const SizedBox(height: 24),

            // Section des fonctionnalités principales
            _buildMainFeaturesSection(),
            const SizedBox(height: 24),

            // Section assurance
            _buildInsuranceSection(),
            const SizedBox(height: 24),

            // Section actions rapides
            _buildQuickActionsSection(),
          ],
        ),
      ),
    );
  }

  /// 👋 Section de bienvenue
  Widget _buildWelcomeSection() {
    final user = FirebaseAuth.instance.currentUser;
    final userName = user?.displayName ?? user?.email?.split('@').first ?? 'Utilisateur';

    return Container(
      width: double.infinity,
      padding: ,
          ),
          const SizedBox(height: 8),
          ,
              fontSize: 16,
            ),
          ),
          if (_notificationCount > 0) ...[
            const SizedBox(height: 16),
            Container(
              padding: ,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  ,
                  const SizedBox(width: 8),
                  (_notificationCount nouvelle(s) notification(s)',
                    style: ,
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 🎯 Section des fonctionnalités principales
  Widget _buildMainFeaturesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: ,
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.2,
          children: [
            _buildFeatureCard(
              title: 'Déclaration',
              subtitle: 'Accident',
              icon: Icons.report_problem,
              color: Colors.red,
              onTap: () => _showComingSoon('Déclaration d\'accident'),
            ),
            _buildFeatureCard(
              title: 'Collaboration',
              subtitle: 'Multi-conducteurs',
              icon: Icons.group,
              color: Colors.purple,
              onTap: () => _showComingSoon('Collaboration'),
            ),
            _buildFeatureCard(
              title: 'Reconstruction',
              subtitle: 'IA Vidéo',
              icon: Icons.smart_toy,
              color: Colors.orange,
              onTap: () => _showComingSoon('Reconstruction IA'),
            ),
            _buildFeatureCard(
              title: 'Historique',
              subtitle: 'Mes constats',
              icon: Icons.history,
              color: Colors.green,
              onTap: () => _showComingSoon('Historique'),
            ),
          ],
        ),
      ],
    );
  }

  /// 🛡️ Section assurance
  Widget _buildInsuranceSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        const SizedBox(height: 16),
        InsuranceNavigation.buildInsuranceCard(context),
      ],
    );
  }

  /// ⚡ Section actions rapides
  Widget _buildQuickActionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildQuickActionButton(
                'Mes Véhicules',
                Icons.directions_car,
                Colors.blue,
                () => InsuranceNavigation.navigateToMyVehicles(context),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickActionButton(
                'Urgence',
                Icons.emergency,
                Colors.red,
                () => _showEmergencyContacts(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFeatureCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: (1),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(icon, color: color, size: 28),
              ),
              const SizedBox(height: 12),
              ,
                textAlign: TextAlign.center,
              ),
              ,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickActionButton(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return ElevatedButton.const Icon(
      onPressed: onTap,
      icon: const Icon(icon, size: 20),
      label: const Text(title),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: ,
        ),
      ),
    );
  }

  /// 🔔 Afficher les notifications
  void _showNotifications() {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: ),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        expand: false,
        builder: (context, scrollController) => Column(
          children: [
            Container(
              padding: ,
                  const SizedBox(width: 8),
                  ,
                  ),
                  ,
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.info),
                  ),
                ],
              ),
            ),
            Expanded(
              child: StreamBuilder<List<Map<String, dynamic>>>(
                stream: InsuranceNotificationService.getUserNotifications(user.uid),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (!snapshot.hasData || snapshot.data!.isEmpty) {
                    return ,
                          const SizedBox(height: 16),
                          const Text('Aucune notification'),
                        ],
                      ),
                    );
                  }

                  return ListView.builder(
                    controller: scrollController,
                    itemCount: snapshot.data!.length,
                    itemBuilder: (context, index) {
                      final notification = snapshot.data![index];
                      return _buildNotificationItem(notification);
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationItem(Map<String, dynamic> notification) {
    final isRead = notification['isRead'] ?? false;
    
    return ListTile(
      leading: Container(
        padding: ,
        ),
        child: ,
      ),
      title: const Text("Titre"),
      ),
      subtitle: const Text(notification['message'] ?? 'Contenu'),
      trailing: isRead ? null : Container(
        width: 8,
        height: 8,
        decoration: ,
      ),
      onTap: () async {
        if (!isRead) {
          await InsuranceNotificationService.markAsRead(notification['id']);
        }
        if (mounted) {
          Navigator.pop(context);
          // Traiter l'action de la notification
        }
      },
    );
  }

  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: (feature - Fonctionnalité à venir'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showEmergencyContacts() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🚨 Contacts d\'Urgence'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: ,
              title: const Text('Police'),
              subtitle: const Text('197'),
            ),
            ListTile(
              leading: ,
              title: const Text('SAMU'),
              subtitle: const Text('190'),
            ),
            ListTile(
              leading: ,
              title: const Text('Protection Civile'),
              subtitle: const Text('198'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer
