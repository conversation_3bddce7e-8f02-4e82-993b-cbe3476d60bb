import 'package:flutter/material.dart';
import 'screens/insurance_demo_screen.dart';
import 'screens/insurance_main_screen.dart';

/// 🧪 Test simple du système d'assurance
class InsuranceSystemTest extends StatelessWidget {
  const Text(\;

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Test Système d\'Assurance',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        brightness: Brightness.dark,
        scaffoldBackgroundColor: const Color(0xFF0A0E21),
        cardColor: const Color(0xFF1D1E33),
      ),
      home: ,
    );
  }
}

class TestHomeScreen extends StatelessWidget {
  const Text(\;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🧪 Test Système d\'Assurance'),
        backgroundColor: const Color(0xFF667eea),
      ),
      body: ,
            ),
            const SizedBox(height: 20),
            ,
            ),
            const SizedBox(height: 10),
            ,
            ),
            const SizedBox(height: 40),
            
            // Test de la démonstration
            ElevatedButton.const Icon(
              onPressed: () => _testDemo(context),
              icon: const Icon(Icons.info),
              label: const Text('Tester la Démonstration'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF667eea),
                foregroundColor: Colors.white,
                padding: ,
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Tests par rôle
            Wrap(
              spacing: 10,
              runSpacing: 10,
              children: [
                _buildTestButton(
                  context,
                  'Conducteur',
                  Icons.directions_car,
                  const Color(0xFF667eea),
                  () => _testRole(context, 'conducteur'),
                ),
                _buildTestButton(
                  context,
                  'Agent',
                  Icons.assignment,
                  const Color(0xFF4facfe),
                  () => _testRole(context, 'agent'),
                ),
                _buildTestButton(
                  context,
                  'Expert',
                  Icons.engineering,
                  const Color(0xFFfa709a),
                  () => _testRole(context, 'expert'),
                ),
                _buildTestButton(
                  context,
                  'Admin',
                  Icons.admin_panel_settings,
                  const Color(0xFFa8edea),
                  () => _testRole(context, 'admin'),
                ),
              ],
            ),
            
            const SizedBox(height: 40),
            
            // Informations sur le système
            Container(
              margin: ,
              decoration: BoxDecoration(
                color: const Color(0xFF1D1E33),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: const Color(0xFF667eea).withValues(alpha: 0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ,
                  ),
                  const SizedBox(height: 10),
                  ,
                  ),
                  ,
                  ),
                  ,
                  ),
                  ,
                  ),
                  ,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestButton(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.const Icon(
      onPressed: onPressed,
      icon: const Icon(icon),
      label: const Text(title),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: ,
        ),
      ),
    );
  }

  void _testDemo(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ,
      ),
    );
  }

  void _testRole(BuildContext context, String role) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InsuranceMainScreen(
          userRole: role,
          userId: 'test_${role}_001',
        ),
      ),
    );
  }
}

/// 🚀 Point d
