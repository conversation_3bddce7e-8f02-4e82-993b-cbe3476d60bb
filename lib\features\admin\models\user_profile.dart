import 'package:cloud_firestore/cloud_firestore.dart';

/// 👤 Modèle pour un utilisateur du système
class UserProfile {
  final String uid;
  final String fullName;
  final String email;
  final String phone;
  final String role; // super_admin, admin_compagnie, admin_agence, agent_agence, expert_auto, conducteur
  final String? compagnieId; // 🔗 Lien vers la compagnie (pour admin_compagnie, admin_agence, agent_agence)
  final String? agenceId; // 🔗 Lien vers l'agence (pour admin_agence, agent_agence)
  final String? governorate;
  final String? licenseNumber;
  final String? experience;
  final String? fonction; // Pour admin_compagnie
  final String? zoneIntervention; // Pour expert_auto
  final DateTime dateCreation;
  final DateTime? lastLogin;
  final bool isActive;
  final String status; // active, suspended, pending
  final Map<String, dynamic>? metadata;

  ;

  /// 🔄 Conversion depuis Firestore
  factory UserProfile.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserProfile(
      uid: doc.id,
      fullName: data['fullName'] ?? 'Contenu',
      email: data['email'] ?? 'Contenu',
      phone: data['phone'] ?? 'Contenu',
      role: data['role'] ?? 'Contenu',
      compagnieId: data['compagnieId'],
      agenceId: data['agenceId'],
      governorate: data['governorate'],
      licenseNumber: data['licenseNumber'],
      experience: data['experience'],
      fonction: data['fonction'],
      zoneIntervention: data['zoneIntervention'],
      dateCreation: (data['dateCreation'] as Timestamp?)?.toDate() ?? DateTime.now(),
      lastLogin: (data['lastLogin'] as Timestamp?)?.toDate(),
      isActive: data['isActive'] ?? true,
      status: data['status'] ?? 'active',
      metadata: data['metadata'],
    );
  }

  /// 🔄 Conversion vers Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'fullName': fullName,
      'email': email,
      'phone': phone,
      'role': role,
      'compagnieId': compagnieId,
      'agenceId': agenceId,
      'governorate': governorate,
      'licenseNumber': licenseNumber,
      'experience': experience,
      'fonction': fonction,
      'zoneIntervention': zoneIntervention,
      'dateCreation': Timestamp.fromDate(dateCreation),
      'lastLogin': lastLogin != null ? Timestamp.fromDate(lastLogin!) : null,
      'isActive': isActive,
      'status': status,
      'metadata': metadata,
    };
  }

  /// 🎯 Vérifications de rôle
  bool get isSuperAdmin => role == 'super_admin';
  bool get isAdminCompagnie => role == 'admin_compagnie';
  bool get isAdminAgence => role == 'admin_agence';
  bool get isAgent => role == 'agent_agence';
  bool get isExpert => role == 'expert_auto';
  bool get isConducteur => role == 'conducteur';

  /// 🔗 Vérifications de liens
  bool get hasCompagnie => compagnieId != null && compagnieId!.isNotEmpty;
  bool get hasAgence => agenceId != null && agenceId!.isNotEmpty;

  /// 📋 Copie avec modifications
  UserProfile copyWith({
    String? uid,
    String? fullName,
    String? email,
    String? phone,
    String? role,
    String? compagnieId,
    String? agenceId,
    String? governorate,
    String? licenseNumber,
    String? experience,
    String? fonction,
    String? zoneIntervention,
    DateTime? dateCreation,
    DateTime? lastLogin,
    bool? isActive,
    String? status,
    Map<String, dynamic>? metadata,
  }) {
    return UserProfile(
      uid: uid ?? this.uid,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      role: role ?? this.role,
      compagnieId: compagnieId ?? this.compagnieId,
      agenceId: agenceId ?? this.agenceId,
      governorate: governorate ?? this.governorate,
      licenseNumber: licenseNumber ?? this.licenseNumber,
      experience: experience ?? this.experience,
      fonction: fonction ?? this.fonction,
      zoneIntervention: zoneIntervention ?? this.zoneIntervention,
      dateCreation: dateCreation ?? this.dateCreation,
      lastLogin: lastLogin ?? this.lastLogin,
      isActive: isActive ?? this.isActive,
      status: status ?? this.status,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'UserProfile(uid: $uid, fullName: $fullName, role: $role, compagnieId: $compagnieId, agenceId: $agenceId)
