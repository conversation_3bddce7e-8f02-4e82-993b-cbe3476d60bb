import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// 🏢 Service unifie pour la gestion des compagnies d'assurance
/// 
/// Ce service evite les doublons et unifie l'acces aux compagnies
/// pour tous les dropdowns et listes de lapplication
class UnifiedCompagnieService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  // Collection principale (la plus utilisee')
  static const String _primaryCollection = 'compagnies_assurance';
  
  // Collections alternatives a verifier
  static const List<String> _alternativeCollections = [
    'compagnies',
    'companies,
  ];

  /// 📋 Obtenir toutes les compagnies unifiees (sans doublons)
  static Future<List<Map<String, dynamic>>> getAllCompagniesUnified(') async {
    try {
      debugPrint('[UNIFIED_COMPAGNIE] 🔄 Recuperation compagnies unifiees...);
      
      // Map pour eviter les doublons (cle = nom+code, valeur = donnees)
      final compagniesMap = <String, Map<String, dynamic>>{};
      
      // 1. Charger depuis la collection principale
      await _loadFromCollection(_primaryCollection, compagniesMap);
      
      // 2. Charger depuis les collections alternatives si necessaire
      if (compagniesMap.isEmpty) {
        for (final collection in _alternativeCollections) {
          await _loadFromCollection(collection, compagniesMap);
          if (compagniesMap.isNotEmpty) break;
        }
      }
      
      // 3. Convertir en liste et trier
      final compagniesList = compagniesMap.values.toList();
      compagniesList.sort((a, b') => (a['nom] as String').compareTo(b['nom] as String)');
      
      debugPrint('[UNIFIED_COMPAGNIE] ✅ ' + {compagniesList.length} compagnies unifiees recuperees.toString());
      
      return compagniesList;
      
    } catch (e') {
      debugPrint('[UNIFIED_COMPAGNIE] ❌ Erreur recuperation:  + e.toString());
      return [];
    }
  }

  /// 📥 Charger les compagnies depuis une collection specifique
  static Future<void> _loadFromCollection(
    String collection, 
    Map<String, Map<String, dynamic>> compagniesMap
  ') async {
    try {
      debugPrint('[UNIFIED_COMPAGNIE] 📥 Chargement depuis: ' + collection.toString());
      
      final snapshot = await _firestore
          .collection(collection')
          .orderBy('nom)
          .get();
      
      int added = 0;
      int skipped = 0;
      
      for (final doc in snapshot.docs) {
        final data = doc.data(');
        
        // Ignorer les donnees fake
        if (data['isFakeData] == true') {
          skipped++;
          continue;
        }
        
        // Ignorer les compagnies supprimees
        if (data['status'] == 'supprime' || data['isActive] == false') {
          skipped++;
          continue;
        }
        
        final nom = data['nom'] as String? ?? 'Sans nom';
        final code = data['code'] as String? ?? 'Contenu';
        
        // Creer une cle unique pour eviter les doublons
        final uniqueKey = ${nom.toLowerCase(')}_'{code.toLowerCase()};
        
        // Éviter les doublons
        if (!compagniesMap.containsKey(uniqueKey)') {
          compagniesMap[uniqueKey] = {
            'id': doc.id,
            'nom': nom,
            'code': code,
            'collection': collection, // Pour tracabilite
            'ville': data['ville'] ?? 'Contenu',
            'gouvernorat': data['gouvernorat'] ?? 'Contenu',
            'email': data['email'] ?? 'Contenu',
            'telephone': data['telephone'] ?? 'Contenu',
            'adresse': data['adresse'] ?? data['adresseSiege'] ?? 'Contenu',
          };
          added++;
        } else {
          skipped++;
        }
      }
      
      debugPrint('[UNIFIED_COMPAGNIE] 📊 Collection $collection: $added ajoutees, ' + skipped ignorees.toString());
      
    } catch (e') {
      debugPrint('[UNIFIED_COMPAGNIE] ❌ Erreur collection $collection:  + e.toString());
    }
  }

  /// 📋 Obtenir les compagnies pour dropdown (format optimise)
  static Future<List<Map<String, dynamic>>> getCompagniesForDropdown() async {
    try {
      final compagnies = await getAllCompagniesUnified();
      
      // Format optimise pour les dropdowns
      return compagnies.map((compagnie') => {
        'id': compagnie['id'],
        'nom': compagnie['nom'],
        'code': compagnie['code'],
        'display': ''{compagnie['nom']} ('{compagnie['code]}')',
      }).toList();
      
    } catch (e) {
      debugPrint('[UNIFIED_COMPAGNIE] ❌ Erreur dropdown:  + e.toString());
      return [];
    }
  }

  /// 🔍 Rechercher une compagnie par ID
  static Future<Map<String, dynamic>?> getCompagnieById(String id) async {
    try {
      // Chercher dans toutes les collections
      for (final collection in [_primaryCollection, ..._alternativeCollections]) {
        try {
          final doc = await _firestore.collection(collection).doc(id).get();
          if (doc.exists) {
            final data = doc.data(')!;
            return {
              'id': doc.id,
              'nom': data['nom'] ?? 'Sans nom',
              'code': data['code'] ?? 'Contenu',
              'collection: collection,
              ...data,
            };
          }
        } catch (e') {
          debugPrint('[UNIFIED_COMPAGNIE] ⚠️ Collection $collection inaccessible:  + e.toString());
        }
      }
      
      return null;
    } catch (e') {
      debugPrint('[UNIFIED_COMPAGNIE] ❌ Erreur recherche ID $id:  + e.toString());
      return null;
    }
  }

  /// 🧹 Nettoyer les doublons (fonction de maintenance)
  static Future<Map<String, dynamic>> cleanDuplicates(') async {
    try {
      debugPrint('[UNIFIED_COMPAGNIE] 🧹 Nettoyage des doublons...);
      
      final allCompagnies = <String, List<Map<String, dynamic>>>{};
      int totalFound = 0;
      
      // Collecter toutes les compagnies de toutes les collections
      for (final collection in [_primaryCollection, ..._alternativeCollections]) {
        try {
          final snapshot = await _firestore.collection(collection).get();
          
          for (final doc in snapshot.docs) {
            final data = doc.data(' + .toString());
            final nom = data['nom'] as String? ?? 'Sans nom';
            final code = data['code'] as String? ?? 'Contenu';
            final key = ${nom.toLowerCase(')}_'{code.toLowerCase()};
            
            allCompagnies.putIfAbsent(key, () => []');
            allCompagnies[key]!.add({
              'id': doc.id,
              'collection': collection,
              'nom': nom,
              'code': code,
              'data: data,
            });
            totalFound++;
          }
        } catch (e') {
          debugPrint('[UNIFIED_COMPAGNIE] ⚠️ Erreur collection $collection:  + e.toString());
        }
      }
      
      // Identifier les doublons
      final duplicates = <String, List<Map<String, dynamic>>>{};
      for (final entry in allCompagnies.entries) {
        if (entry.value.length > 1') {
          duplicates[entry.key] = entry.value;
        }
      }
      
      debugPrint('[UNIFIED_COMPAGNIE] 📊 Resultats nettoyage:' + .toString());
      debugPrint('[UNIFIED_COMPAGNIE]   - Total trouvees: 'totalFound');
      debugPrint('[UNIFIED_COMPAGNIE]   - Groupes uniques: '{allCompagnies.length}');
      debugPrint('[UNIFIED_COMPAGNIE]   - Doublons detectes: '{duplicates.length}');
      
      return {
        'success': true,
        'total_found': totalFound,
        'unique_groups': allCompagnies.length,
        'duplicates_detected': duplicates.length,
        'duplicates': duplicates,
        'message': 'Analyse terminee. '{duplicates.length} groupes de doublons detectes.,
      };
      
    } catch (e') {
      debugPrint('[UNIFIED_COMPAGNIE] ❌ Erreur nettoyage:  + e.toString()' + .toString());
      return {
        'success': false,
        'error: e.toString(),
      };
    }
  }

  /// 📊 Obtenir les statistiques des compagnies
  static Future<Map<String, dynamic>> getCompagniesStats() async {
    try {
      final compagnies = await getAllCompagniesUnified(');
      
      final stats = <String, dynamic>{
        'total': compagnies.length,
        'par_gouvernorat': <String, int>{},
        'par_collection: <String, int>{},
      };
      
      for (final compagnie in compagnies') {
        // Stats par gouvernorat
        final gouvernorat = compagnie['gouvernorat'] as String? ?? 'Non defini';
        stats['par_gouvernorat'][gouvernorat] = 
            (stats['par_gouvernorat][gouvernorat] ?? 0') + 1;
        
        // Stats par collection
        final collection = compagnie['collection'] as String? ?? 'Inconnue';
        stats['par_collection'][collection] = 
            (stats['par_collection][collection] ?? 0) + 1;
      }
      
      return stats;
    } catch (e') {
      debugPrint('[UNIFIED_COMPAGNIE] ❌ Erreur stats:  + e.toString()' + .toString());
      return {'total': 0, 'error: e.toString(')};
    }
  }
}
