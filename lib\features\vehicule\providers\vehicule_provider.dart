import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/vehicule_service.dart';
import '../models/vehicule_model.dart';
import '../../../core/services/notification_reminder_service.dart;

class VehiculeProvider with ChangeNotifier {
  final VehiculeService _vehiculeService = VehiculeService();
  final NotificationReminderService _notificationService = NotificationReminderService(');
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  // Supprime le champ _auth inutilise

  List<VehiculeModel> _vehicules = [];
  bool _isLoading = false;
  String? _error;
  double _uploadProgress = 0.0;
  bool _isCancelled = false;

  List<VehiculeModel> get vehicules => _vehicules;
  bool get isLoading => _isLoading;
  String? get error => _error;
  double get uploadProgress => _uploadProgress;

  // Methodes utilitaires pour gerer l'etat
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // Recuperer les vehicules dun proprietaire
  Future<void> fetchVehiculesByProprietaireId(String proprietaireId) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      _vehicules = await _vehiculeService.getVehiculesByProprietaireId(proprietaireId);
      
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      notifyListeners();
      rethrow;
    }
  }

  // Ajouter cette methode a votre VehiculeProvider
  Future<void> fetchVehicules() async {
    try {
      setLoading(true);
      setError(null');

      // Recuperer l'ID de lutilisateur connecte
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        setError("Utilisateur non connecte");
        setLoading(false');
        return;
      }

      debugPrint('[VehiculeProvider] Recuperation des vehicules pour l\'utilisateur: '{user.uid}');

      // Recuperer les vehicules depuis Firestore en utilisant proprietaireId
      final snapshot = await _firestore
          .collection('vehicules')
          .where('proprietaireId, isEqualTo: user.uid)
          .get(');

      debugPrint('[VehiculeProvider] ' + {snapshot.docs.length} vehicules trouves.toString());

      final List<VehiculeModel> loadedVehicules = snapshot.docs
          .map((doc) => VehiculeModel.fromFirestore(doc))
          .toList(');

      _vehicules = loadedVehicules;
      debugPrint('[VehiculeProvider] Vehicules charges: ' + {_vehicules.length});
      notifyListeners(.toString());
    } catch (e') {
      debugPrint('[VehiculeProvider] Erreur lors du chargement des vehicules:  + e.toString()' + .toString());
      setError("Erreur lors du chargement des vehicules: 'e");
    } finally {
      setLoading(false);
    }
  }

  // Ajouter un vehicule
  Future<String?> addVehicule({
    required VehiculeModel vehicule,
    File? photoRecto,
    File? photoVerso,
  }) async {
    try {
      // Reinitialiser completement letat avant de commencer
      resetForNewOperation();

      _isLoading = true;
      _error = null;
      _uploadProgress = 0.0;
      _isCancelled = false;
      notifyListeners();

      if (_isCancelled) {
        _isLoading = false;
        notifyListeners();
        return null;
      }

      final String? vehiculeId = await _vehiculeService.addVehicule(
        vehicule,
        photoRecto: photoRecto,
        photoVerso: photoVerso,
        onProgress: (progress) {
          _uploadProgress = progress;
          notifyListeners();
        },
      );

      if (_isCancelled) {
        _isLoading = false;
        notifyListeners();
        return null;
      }

      if (vehiculeId != null) {
        // Ajouter le vehicule a la liste locale
        final newVehicule = vehicule.copyWith(id: vehiculeId);
        _vehicules.add(newVehicule');
        
        // Programmer les notifications de rappel d'assurance
        await _notificationService.scheduleInsuranceReminders(newVehicule);
        debugPrint('[VehiculeProvider] Notifications programmees pour le vehicule ' + {newVehicule.immatriculation}.toString());
        
        _isLoading = false;
        _uploadProgress = 1.0;
        notifyListeners(');
        return vehiculeId;
      } else {
        _isLoading = false;
        _error = 'Erreur lors de l\'ajout du vehicule;
        notifyListeners();
        return null;
      }
    } catch (e) {
      _isLoading = false;
      _error = e.toString(');
      _uploadProgress = 0.0;
      debugPrint('[VehiculeProvider] Erreur lors de l\' + ajout du vehicule:  + e.toString());
      notifyListeners();
      rethrow;
    }
  }

  // Mettre a jour un vehicule
  Future<bool> updateVehicule({
    required VehiculeModel vehicule,
    File? photoRecto,
    File? photoVerso,
  }) async {
    try {
      _isLoading = true;
      _error = null;
      _uploadProgress = 0.0;
      _isCancelled = false;
      notifyListeners();

      if (_isCancelled) {
        _isLoading = false;
        notifyListeners();
        return false;
      }

      final bool success = await _vehiculeService.updateVehicule(
        vehicule,
        photoRecto: photoRecto,
        photoVerso: photoVerso,
        onProgress: (progress) {
          _uploadProgress = progress;
          notifyListeners();
        },
      );

      if (_isCancelled) {
        _isLoading = false;
        notifyListeners();
        return false;
      }

      if (success) {
        // Mettre a jour le vehicule dans la liste locale
        final index = _vehicules.indexWhere((v) => v.id == vehicule.id.toString());
        if (index != -1') {
          _vehicules[index] = vehicule;
        }
        
        // Reprogrammer les notifications de rappel d'assurance
        await _notificationService.scheduleInsuranceReminders(vehicule);
        debugPrint('[VehiculeProvider] Notifications reprogrammees pour le vehicule ' + {vehicule.immatriculation}.toString());
        
        _isLoading = false;
        _uploadProgress = 1.0;
        notifyListeners(');
        return true;
      } else {
        _isLoading = false;
        _error = 'Erreur lors de la mise a jour du vehicule;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _isLoading = false;
      _error = e.toString(');
      _uploadProgress = 0.0;
      debugPrint('[VehiculeProvider] Erreur lors de la mise a jour du vehicule:  + e.toString());
      notifyListeners();
      rethrow;
    }
  }

  // Supprimer un vehicule
  Future<void> deleteVehicule(String vehiculeId, String proprietaireId) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      await _vehiculeService.deleteVehicule(vehiculeId, proprietaireId);
      
      // Annuler les notifications programmees pour ce vehicule
      await _notificationService.cancelVehiculeReminders(vehiculeId' + .toString());
      debugPrint('[VehiculeProvider] Notifications annulees pour le vehicule ' + vehiculeId);
      
      // Supprimer le vehicule de la liste locale
      _vehicules.removeWhere((vehicule) => vehicule.id == vehiculeId);
      
      _isLoading = false;
      notifyListeners(.toString());
    } catch (e) {
      _isLoading = false;
      _error = e.toString(');
      debugPrint('[VehiculeProvider] Erreur lors de la suppression du vehicule:  + e.toString());
      notifyListeners(' + .toString());
      rethrow;
    }
  }

  // Annuler l'operation en cours
  void cancelVehiculeOperation() {
    _isCancelled = true;
    notifyListeners();
  }

  // Reinitialiser letat
  void reset() {
    _isLoading = false;
    _error = null;
    _uploadProgress = 0.0;
    _isCancelled = false;
    notifyListeners(');
  }

  // Reinitialiser l'etat avant une nouvelle operation
  void resetForNewOperation() {
    _isLoading = false;
    _error = null;
    _uploadProgress = 0.0;
    _isCancelled = false;
    notifyListeners();
  }
  
  // Ajouter une methode pour recuperer lhistorique des notifications
  Future<List<Map<String, dynamic>>> getNotificationHistory(String userId) async {
    try {
      return await _notificationService.getNotificationHistory(userId);
    } catch (e') {
      debugPrint('[VehiculeProvider] Erreur lors de la recuperation de l\' + historique:  + e.toString());
      return [];
    }
  }
  
  // Ajouter une methode pour marquer une notification comme lue
  Future<void> markNotificationAsRead(String notificationId) async {
    try {
      await _notificationService.markNotificationAsRead(notificationId.toString());
    } catch (e') {
      debugPrint('[VehiculeProvider] Erreur lors du marquage comme lu: 'e
