import 'package:flutter/material.dart';
import '../../services/agence_management_service.dart';
import '../../services/tunisia_hierarchy_service.dart';
import '../../services/admin_compagnie_auth_service.dart';
import '../../common/widgets/simple_credentials_dialog.dart';

/// 🏪 Onglet de gestion des agences
class AgenceManagementTab extends StatefulWidget {
  final String compagnieId;
  final String compagnieNom;

   ) : super(key: key);

  @override
  State<AgenceManagementTab> createState() => _AgenceManagementTabState();
}

class _AgenceManagementTabState extends State<AgenceManagementTab> {
  List<Map<String, dynamic>> _agences = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAgences();
  }

  /// 📋 Charger les agences
  Future<void> _loadAgences() async {
    setState(() => _isLoading = true);
    
    try {
      final agences = await AgenceManagementService.getAgencesByCompagnie(widget.compagnieId);
      setState(() {
        _agences = agences;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: (e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  /// ➕ Créer une nouvelle agence
  Future<void> _createAgence() async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => _CreateAgenceDialog(
        compagnieId: widget.compagnieId,
        compagnieNom: widget.compagnieNom,
      ),
    );

    if (result != null && result['success'] == true) {
      _loadAgences();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: const Text('Agence créée avec succès'), backgroundColor: Colors.green),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // En-tête
          Container(
            padding: ,
            ),
            child: Row(
              children: [
                ,
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ,
                      ),
                      ({_agences.length} agence(s) - ${widget.compagnieNom}',
                        style: ,
                      ),
                    ],
                  ),
                ),
                ElevatedButton.const Icon(
                  onPressed: _createAgence,
                  icon: const Icon(Icons.info),
                  label: const Text('Nouvelle Agence'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: Colors.blue.shade600,
                  ),
                ),
              ],
            ),
          ),

          // Liste des agences
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _agences.isEmpty
                    ? _buildEmptyState()
                    : _buildAgencesList(),
          ),
        ],
      ),
    );
  }

  /// 📋 Liste des agences
  Widget _buildAgencesList() {
    return RefreshIndicator(
      onRefresh: _loadAgences,
      child: ListView.builder(
        padding:  {
          final agence = _agences[index];
          return _buildAgenceCard(agence);
        },
      ),
    );
  }

  /// 🏪 Carte d'agence
  Widget _buildAgenceCard(Map<String, dynamic> agence) {
    final stats = agence['stats'] as Map<String, dynamic>? ?? {};
    
    return Card(
      margin: ),
      child: (1),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ,
                      ),
                      ({agence['gouvernorat']}',
                        style: TextStyle(color: Colors.grey[600),
                      ),
                    ],
                  ),
                ),
                PopupMenuButton(
                  itemBuilder: (context) => [
                    ,
                          const SizedBox(width: 8),
                          const Text('Modifier'),
                        ],
                      ),
                    ),
                    if (agence['adminUid'] == null || agence['adminUid'].toString().isEmpty)
                      ,
                            const SizedBox(width: 8),
                            ),
                          ],
                        ),
                      )
                    else
                      PopupMenuItem(
                        value: 'view_admin',
                        child: Row(
                          children: [
                            ,
                            const SizedBox(width: 8),
                            ({agence['adminNom'] ?? 'Non défini'}',
                                 style: const TextStyle()),
                          ],
                        ),
                      ),
                    ,
                          const SizedBox(width: 8),
                          ),
                        ],
                      ),
                    ),
                  ],
                  onSelected: (value) => _handleAgenceAction(value.toString(), agence),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // Informations de contact
            Row(
              children: [
                ,
                const SizedBox(width: 4),
                const Text(agence['telephone'] ?? 'N/A'),
                const SizedBox(width: 16),
                ,
                const SizedBox(width: 4),
                Expanded(child: const Text(agence['email'] ?? 'N/A')),
              ],
            ),
            const SizedBox(height: 8),
            
            Row(
              children: [
                ,
                const SizedBox(width: 4),
                Expanded(child: const Text(agence['adresse'] ?? 'N/A')),
              ],
            ),
            const SizedBox(height: 12),
            
            // Statistiques
            Row(
              children: [
                _buildStatChip('Agents', stats['total_agents']?.toString() ?? '0', Colors.blue),
                const SizedBox(width: 8),
                _buildStatChip('Contrats', stats['total_contrats']?.toString() ?? '0', Colors.green),
                const SizedBox(width: 8),
                _buildStatChip('Sinistres', stats['total_sinistres']?.toString() ?? '0', Colors.orange),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 📊 Chip de statistique
  Widget _buildStatChip(String label, String value, Color color) {
    return Container(
      padding: ,
        borderRadius: BorderRadius.circular(12),
      ),
      child: (value',
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 📭 État vide
  Widget _buildEmptyState() {
    return ,
          const SizedBox(height: 16),
          ,
          ),
          const SizedBox(height: 8),
          ,
          ),
          const SizedBox(height: 16),
          ElevatedButton.const Icon(
            onPressed: _createAgence,
            icon: const Icon(Icons.info),
            label: const Text('Créer une agence'),
          ),
        ],
      ),
    );
  }

  /// 🎯 Gérer les actions sur une agence
  void _handleAgenceAction(String action, Map<String, dynamic> agence) {
    switch (action) {
      case 'edit':
        // TODO: Implémenter la modification
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: const Text('Modification bientôt disponible')),
        );
        break;
      case 'create_admin':
        _createAdminAgence(agence);
        break;
      case 'view_admin':
        _showAdminInfo(agence);
        break;
      case 'deactivate':
        _confirmDeactivateAgence(agence);
        break;
    }
  }

  /// 👤 Créer un Admin Agence
  Future<void> _createAdminAgence(Map<String, dynamic> agence) async {
    // Vérifier si l'agence a déjà un admin
    if (agence['adminUid'] != null && agence['adminUid'].toString().isNotEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: ({agence['adminNom'] ?? agence['adminEmail']}'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => _CreateAdminAgenceDialog(
        compagnieId: widget.compagnieId,
        agenceId: agence['id'],
        agenceNom: agence['nom'],
      ),
    );

    if (result != null && result['success'] == true) {
      _loadAgences();
      if (mounted) {
        // Afficher les identifiants avec le dialog moderne
        showSimpleCredentialsDialog(
          context: context,
          title: '🎉 Admin Agence créé avec succès',
          primaryColor: Colors.green,
          credentials: {
            'nom': result['displayCredentials']['nom'],
            'email': result['displayCredentials']['email'],
            'password': result['displayCredentials']['password'],
            'agence': result['displayCredentials']['agence'],
            'role': 'Admin Agence',
          },
        );
      }
    }
  }

  /// 👤 Afficher les informations de l'Admin Agence
  void _showAdminInfo(Map<String, dynamic> agence) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: ({agence['nom']}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ({agence['adminNom'] ?? 'Non défini'}'),
            ({agence['adminEmail'] ?? 'Non défini'}'),
            const SizedBox(height: 12),
            ,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  /// ⚠️ Confirmer la désactivation
  void _confirmDeactivateAgence(Map<String, dynamic> agence) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Désactiver l\'agence'),
        content: ({agence['nom']}" ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final result = await AgenceManagementService.deactivateAgence(agence['id']);
              if (result['success']) {
                _loadAgences();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: const Text('Agence désactivée'), backgroundColor: Colors.orange),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: ),
          ),
        ],
      ),
    );
  }
}

/// 🏗️ Dialog de création d'agence
class _CreateAgenceDialog extends StatefulWidget {
  final String compagnieId;
  final String compagnieNom;

  ;

  @override
  State<_CreateAgenceDialog> createState() => _CreateAgenceDialogState();
}

class _CreateAgenceDialogState extends State<_CreateAgenceDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nomController = TextEditingController();
  final _adresseController = TextEditingController();
  final _villeController = TextEditingController();
  final _gouvernoratController = TextEditingController();
  final _telephoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _responsableController = TextEditingController();
  bool _isCreating = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Nouvelle Agence'),
      content: ,
                  ),
                  validator: (value) => value?.isEmpty == true ? 'Nom requis' : null,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _adresseController,
                  decoration: ,
                  ),
                  validator: (value) => value?.isEmpty == true ? 'Adresse requise' : null,
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _villeController,
                        decoration: ,
                        ),
                        validator: (value) => value?.isEmpty == true ? 'Ville requise' : null,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _gouvernoratController,
                        decoration: ,
                        ),
                        validator: (value) => value?.isEmpty == true ? 'Gouvernorat requis' : null,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _telephoneController,
                  decoration: ,
                  ),
                  validator: (value) => value?.isEmpty == true ? 'Téléphone requis' : null,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _emailController,
                  decoration: ,
                  ),
                  validator: (value) {
                    if (value?.isEmpty == true) return 'Email requis';
                    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value!)) {
                      return 'Format email invalide';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _responsableController,
                  decoration: ,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isCreating ? null : () => Navigator.of(context).pop(),
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: _isCreating ? null : _createAgence,
          child: _isCreating
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Créer'),
        ),
      ],
    );
  }

  Future<void> _createAgence() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isCreating = true);

    try {
      final result = await AgenceManagementService.createAgence(
        compagnieId: widget.compagnieId,
        nom: _nomController.text.trim(),
        adresse: _adresseController.text.trim(),
        ville: _villeController.text.trim(),
        gouvernorat: _gouvernoratController.text.trim(),
        telephone: _telephoneController.text.trim(),
        email: _emailController.text.trim(),
        responsable: _responsableController.text.trim(),
      );

      if (mounted) {
        Navigator.of(context).pop(result);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: (e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isCreating = false);
      }
    }
  }

  @override
  void dispose() {
    _nomController.dispose();
    _adresseController.dispose();
    _villeController.dispose();
    _gouvernoratController.dispose();
    _telephoneController.dispose();
    _emailController.dispose();
    _responsableController.dispose();
    super.dispose();
  }
}

/// 👤 Dialog de création d'Admin Agence
class _CreateAdminAgenceDialog extends StatefulWidget {
  final String compagnieId;
  final String agenceId;
  final String agenceNom;

  ;

  @override
  State<_CreateAdminAgenceDialog> createState() => _CreateAdminAgenceDialogState();
}

class _CreateAdminAgenceDialogState extends State<_CreateAdminAgenceDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nomController = TextEditingController();
  final _prenomController = TextEditingController();
  final _emailController = TextEditingController();
  final _telephoneController = TextEditingController();
  final _adresseController = TextEditingController();
  final _cinController = TextEditingController();
  bool _isCreating = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: ({widget.agenceNom}'),
      content: ,
                        ),
                        validator: (value) => value?.isEmpty == true ? 'Prénom requis' : null,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _nomController,
                        decoration: ,
                        ),
                        validator: (value) => value?.isEmpty == true ? 'Nom requis' : null,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                TextFormField(
                  controller: _emailController,
                  decoration: ,
                  ),
                  validator: (value) {
                    if (value?.isEmpty == true) return 'Email requis';
                    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value!)) {
                      return 'Format email invalide';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                TextFormField(
                  controller: _telephoneController,
                  decoration: ,
                  ),
                  validator: (value) => value?.isEmpty == true ? 'Téléphone requis' : null,
                ),
                const SizedBox(height: 16),

                TextFormField(
                  controller: _adresseController,
                  decoration: ,
                  ),
                ),
                const SizedBox(height: 16),

                TextFormField(
                  controller: _cinController,
                  decoration: ,
                  ),
                ),
                const SizedBox(height: 16),

                Container(
                  padding: ,
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          ,
                          const SizedBox(width: 8),
                          ,
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      ,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isCreating ? null : () => Navigator.of(context).pop(),
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: _isCreating ? null : _createAdminAgence,
          child: _isCreating
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Créer Admin'),
        ),
      ],
    );
  }

  Future<void> _createAdminAgence() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isCreating = true);

    try {
      final result = await AdminCompagnieAuthService.createAdminAgence(
        compagnieId: widget.compagnieId,
        agenceId: widget.agenceId,
        nom: _nomController.text.trim(),
        prenom: _prenomController.text.trim(),
        email: _emailController.text.trim(),
        telephone: _telephoneController.text.trim(),
        adresse: _adresseController.text.trim(),
        cin: _cinController.text.trim(),
        createdBy: 'admin_compagnie', // TODO: Récupérer l'ID de l'admin connecté
      );

      if (mounted) {
        Navigator.of(context).pop(result);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: (e
