import 'package:flutter/material.dart';

/// 👨‍🔧 Onglet de gestion des experts pour Admin Agence
class AgenceExpertsTab extends StatefulWidget {
  final String agenceId;
  final String agenceNom;
  final String compagnieId;

   ) : super(key: key);

  @override
  State<AgenceExpertsTab> createState() => _AgenceExpertsTabState();
}

class _AgenceExpertsTabState extends State<AgenceExpertsTab> {
  List<Map<String, dynamic>> _experts = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadExperts();
  }

  /// 📋 Charger les experts disponibles
  Future<void> _loadExperts() async {
    setState(() => _isLoading = true);
    
    try {
      // TODO: Implémenter le service de récupération des experts
      // Pour l'instant, données de test
      await Future.delayed(const Duration(seconds: 1));
      setState(() {
        _experts = _generateTestExperts();
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: (e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  /// 🧪 Générer des experts de test
  List<Map<String, dynamic>> _generateTestExperts() {
    return [
      {
        'id': 'EXP001',
        'nom': 'Karim',
        'prenom': 'Bennour',
        'email': '<EMAIL>',
        'telephone': '+216 98 123 456',
        'specialites': ['Automobile', 'Moto'],
        'status': 'disponible',
        'experience': '8 ans',
        'rating': 4.8,
        'dossiers_en_cours': 3,
        'dossiers_termines': 127,
        'zone_intervention': 'Tunis, Ariana',
      },
      {
        'id': 'EXP002',
        'nom': 'Leila',
        'prenom': 'Mansouri',
        'email': '<EMAIL>',
        'telephone': '+216 97 234 567',
        'specialites': ['Habitation', 'Commerce'],
        'status': 'occupe',
        'experience': '12 ans',
        'rating': 4.9,
        'dossiers_en_cours': 5,
        'dossiers_termines': 203,
        'zone_intervention': 'Tunis, Ben Arous',
      },
      {
        'id': 'EXP003',
        'nom': 'Ahmed',
        'prenom': 'Trabelsi',
        'email': '<EMAIL>',
        'telephone': '+216 99 345 678',
        'specialites': ['Automobile', 'Habitation'],
        'status': 'disponible',
        'experience': '5 ans',
        'rating': 4.6,
        'dossiers_en_cours': 2,
        'dossiers_termines': 89,
        'zone_intervention': 'Sfax, Mahdia',
      },
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // En-tête
          Container(
            padding: ,
            ),
            child: Row(
              children: [
                ,
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ,
                      ),
                      ({_experts.length} expert(s) - ${widget.agenceNom}',
                        style: ,
                      ),
                    ],
                  ),
                ),
                ElevatedButton.const Icon(
                  onPressed: _showRequestExpertDialog,
                  icon: const Icon(Icons.info),
                  label: const Text('Demander Expert'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: Colors.purple.shade600,
                  ),
                ),
              ],
            ),
          ),

          // Liste des experts
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _experts.isEmpty
                    ? _buildEmptyState()
                    : _buildExpertsList(),
          ),
        ],
      ),
    );
  }

  /// 📋 Liste des experts
  Widget _buildExpertsList() {
    return RefreshIndicator(
      onRefresh: _loadExperts,
      child: ListView.builder(
        padding:  {
          final expert = _experts[index];
          return _buildExpertCard(expert);
        },
      ),
    );
  }

  /// 👨‍🔧 Carte d'expert
  Widget _buildExpertCard(Map<String, dynamic> expert) {
    final status = expert['status'] as String;
    final specialites = expert['specialites'] as List<dynamic>;
    
    Color statusColor = status == 'disponible' ? Colors.green : Colors.orange;
    IconData statusIcon = status == 'disponible' ? Icons.check_circle : Icons.work;
    
    return Card(
      margin: ),
      child: (1),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ({expert['nom']}',
                        style: ,
                      ),
                      ({expert['experience']}',
                        style: TextStyle(color: Colors.grey[600),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: ,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      ,
                      const SizedBox(width: 4),
                      ,
                        style: ,
                      ),
                    ],
                  ),
                ),
                PopupMenuButton(
                  itemBuilder: (context) => [
                    ,
                          const SizedBox(width: 8),
                          const Text('Voir profil'),
                        ],
                      ),
                    ),
                    ,
                          const SizedBox(width: 8),
                          const Text('Assigner dossier'),
                        ],
                      ),
                    ),
                    ,
                          const SizedBox(width: 8),
                          const Text('Contacter'),
                        ],
                      ),
                    ),
                  ],
                  onSelected: (value) => _handleExpertAction(value.toString(), expert),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // Spécialités
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: specialites.map((specialite) => Container(
                padding: ,
                  border: Border.all(color: Colors.purple.shade200),
                ),
                child: ,
                  style: TextStyle(
                    color: Colors.purple.shade700,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              )).toList(),
            ),
            const SizedBox(height: 12),
            
            // Informations de contact
            Row(
              children: [
                ,
                const SizedBox(width: 4),
                Expanded(child: const Text(expert['email'], style: const TextStyle(fontSize: 12))),
                const SizedBox(width: 16),
                ,
                const SizedBox(width: 4),
                const Text(expert['telephone'], style: const TextStyle(fontSize: 12)),
              ],
            ),
            const SizedBox(height: 8),
            
            Row(
              children: [
                ,
                const SizedBox(width: 4),
                Expanded(child: ({expert['zone_intervention']}', style: const TextStyle(fontSize: 12))),
              ],
            ),
            const SizedBox(height: 12),
            
            // Statistiques
            Row(
              children: [
                _buildStatChip('Note', '${expert['rating']}/5', Colors.amber),
                const SizedBox(width: 8),
                _buildStatChip('En cours', '${expert['dossiers_en_cours']}', Colors.orange),
                const SizedBox(width: 8),
                _buildStatChip('Terminés', '${expert['dossiers_termines']}', Colors.green),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 📊 Chip de statistique
  Widget _buildStatChip(String label, String value, Color color) {
    return Container(
      padding: ,
        borderRadius: BorderRadius.circular(12),
      ),
      child: (value',
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 📭 État vide
  Widget _buildEmptyState() {
    return ,
          const SizedBox(height: 16),
          ,
          ),
          const SizedBox(height: 8),
          ,
          ),
          const SizedBox(height: 16),
          ElevatedButton.const Icon(
            onPressed: _showRequestExpertDialog,
            icon: const Icon(Icons.info),
            label: const Text('Demander un expert'),
          ),
        ],
      ),
    );
  }

  /// 🎯 Gérer les actions sur un expert
  void _handleExpertAction(String action, Map<String, dynamic> expert) {
    switch (action) {
      case 'view':
        _showExpertProfile(expert);
        break;
      case 'assign':
        _showAssignDossierDialog(expert);
        break;
      case 'contact':
        _showContactExpertDialog(expert);
        break;
    }
  }

  /// 👁️ Afficher le profil de l'expert
  void _showExpertProfile(Map<String, dynamic> expert) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: ({expert['nom']}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Email', expert['email'),
              _buildDetailRow('Téléphone', expert['telephone'),
              _buildDetailRow('Expérience', expert['experience'),
              _buildDetailRow('Note', '${expert['rating']}/5'),
              _buildDetailRow('Statut', expert['status'),
              _buildDetailRow('Zone d\'intervention', expert['zone_intervention'),
              _buildDetailRow('Spécialités', (expert['specialites'] as List).join(', ')),
              _buildDetailRow('Dossiers en cours', '${expert['dossiers_en_cours']}'),
              _buildDetailRow('Dossiers terminés', '${expert['dossiers_termines']}'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  /// 📋 Ligne de détail
  Widget _buildDetailRow(String label, String value) {
    return (1),
            ),
          ),
          Expanded(child: const Text(value)),
        ],
      ),
    );
  }

  /// 📋 Dialog d'assignation de dossier
  void _showAssignDossierDialog(Map<String, dynamic> expert) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: ({expert['nom']}'),
        content: const Text('Fonctionnalité bientôt disponible'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// 📞 Dialog de contact expert
  void _showContactExpertDialog(Map<String, dynamic> expert) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: ({expert['nom']}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: ,
              title: const Text(expert['telephone'),
              subtitle: const Text('Appeler'),
              onTap: () {
                // TODO: Implémenter l'appel
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: const Text('Fonction d\'appel bientôt disponible')),
                );
              },
            ),
            ListTile(
              leading: ,
              title: const Text(expert['email'),
              subtitle: const Text('Envoyer un email'),
              onTap: () {
                // TODO: Implémenter l'email
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: const Text('Fonction d\'email bientôt disponible')),
                );
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  /// 🔍 Dialog de demande d'expert
  void _showRequestExpertDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Demander un expert'),
        content: const Text('Fonctionnalité bientôt disponible'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK
