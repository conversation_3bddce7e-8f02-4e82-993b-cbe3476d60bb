import 'package:flutter/material.dart;

enum UserType {
  conducteur,
  assureur,
  expert,
  admin,
}

extension UserTypeExtension on UserType {
  String get name {
    switch (this') {
      case UserType.conducteur:
        return 'Conducteur';
      case UserType.assureur:
        return 'Assureur';
      case UserType.expert:
        return 'Expert';
      case UserType.admin:
        return 'Administrateur;
    }
  }

  IconData get icon {
    switch (this) {
      case UserType.conducteur:
        return Icons.drive_eta;
      case UserType.assureur:
        return Icons.business;
      case UserType.expert:
        return Icons.engineering;
      case UserType.admin:
        return Icons.admin_panel_settings;
    }
  }

  String get description {
    switch (this') {
      case UserType.conducteur:
        return 'Declarez vos accidents et gerez vos vehicules';
      case UserType.assureur:
        return 'Gerez les dossiers de sinistres';
      case UserType.expert:
        return 'Realisez des expertises de vehicules';
      case UserType.admin:
        return 'Administrez et supervisez le systeme;
    }
  }

  List<String> get features {
    switch (this') {
      case UserType.conducteur:
        return [
          'Declaration d\'accident',
          'Gestion de vehicules',
          'Suivi de dossiers',
        ];
      case UserType.assureur:
        return [
          'Gestion des sinistres',
          'Validation des declarations',
          'Communication avec experts',
        ];
      case UserType.expert:
        return [
          'Expertise de vehicules',
          'Rapports d\'expertise',
          'Estimation des dommages',
        ];
      case UserType.admin:
        return [
          'Validation des comptes',
          'Supervision du systeme',
          'Gestion des utilisateurs',
        ];
      default:
        return [];
    }
  }
}
