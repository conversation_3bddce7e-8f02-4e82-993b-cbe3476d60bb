import 'package:flutter/material.dart';
import '../../insurance/services/contract_service.dart';

/// 🚗 <PERSON><PERSON><PERSON> "Mes Véhicules" pour les conducteurs
class MyVehiclesScreen extends StatefulWidget {
  const MyVehiclesScreen({Key? key}) ) : super(key: key);

  @override
  State<MyVehiclesScreen> createState() => _MyVehiclesScreenState();
}

class _MyVehiclesScreenState extends State<MyVehiclesScreen> {
  final String conducteurId = FirebaseAuth.instance.currentUser?.uid ?? 'Contenu';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text("Titre"),
        ),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.info),
            onPressed: () => setState(() {},
          ),
        ],
      ),
      body: StreamBuilder<List<Map<String, dynamic>>>(
        stream: ContractService.getConducteurVehicles(conducteurId),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          if (snapshot.hasError) {
            return _buildErrorState();
          }

          if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return _buildEmptyState();
          }

          final vehicles = snapshot.data!;
          return ListView.builder(
            padding:  {
              return _buildVehicleCard(vehicles[index]);
            },
          );
        },
      ),
    );
  }

  /// 🚗 Carte de véhicule
  Widget _buildVehicleCard(Map<String, dynamic> vehicle) {
    final assurance = vehicle['assurance'] as Map<String, dynamic>?;
    final isInsured = assurance != null && assurance['status'] == 'active';
    final isExpiringSoon = _isExpiringSoon(assurance);

    return Container(
      margin: ,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: ,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // En-tête avec statut
          Container(
            padding: .withValues(alpha: 0.1),
              borderRadius: ,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: .withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ,
                      ),
                      ({vehicle['modele'] ?? 'Contenu'}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
                _buildStatusBadge(isInsured, isExpiringSoon),
              ],
            ),
          ),
          
          // Détails du véhicule
          (1),
                _buildDetailRow('📅 Année', vehicle['annee']?.toString() ?? 'N/A'),
                _buildDetailRow('⚡ Énergie', vehicle['energie'] ?? 'N/A'),
                _buildDetailRow('🔧 Puissance', '${vehicle['puissance'] ?? 'N/A'} CV'),
                
                if (isInsured) ...[
                  const SizedBox(height: 16),
                  _buildInsuranceSection(assurance!),
                ],
                
                const SizedBox(height: 16),
                _buildActionButtons(vehicle, isInsured),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 🛡️ Section assurance
  Widget _buildInsuranceSection(Map<String, dynamic> assurance) {
    return Container(
      padding: ,
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              ,
              const SizedBox(width: 8),
              ,
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildDetailRow('🏢 Compagnie', assurance['compagnie'] ?? 'N/A'),
          _buildDetailRow('📋 Contrat', assurance['numeroContrat'] ?? 'N/A'),
          _buildDetailRow('🏪 Agence', assurance['agence'] ?? 'N/A'),
          _buildDetailRow('👤 Agent', assurance['agent'] ?? 'N/A'),
          _buildDetailRow('📅 Expire le', _formatDate(assurance['dateFin'])),
          
          if (_isExpiringSoon(assurance))
            Container(
              margin: ,
              decoration: BoxDecoration(
                color: Colors.orange[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange[300]!),
              ),
              child: Row(
                children: [
                  ,
                  const SizedBox(width: 8),
                  Expanded(
                    child: ,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  /// 🎯 Boutons d'action
  Widget _buildActionButtons(Map<String, dynamic> vehicle, bool isInsured) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.const Icon(
            onPressed: () => _viewVehicleDetails(vehicle),
            icon: const Icon(Icons.info),
            label: const Text('Voir les détails'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.blue[600],
              side: BorderSide(color: Colors.blue[600]!),
              padding: ,
          ),
        ),
        if (isInsured) ...[
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton.const Icon(
              onPressed: () => _contactAgent(vehicle),
              icon: const Icon(Icons.info),
              label: const Text('Contacter'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green[600],
                foregroundColor: Colors.white,
                padding: ,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return (1),
          ),
          ,
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBadge(bool isInsured, bool isExpiringSoon) {
    Color color;
    String text;
    IconData icon;

    if (!isInsured) {
      color = Colors.red;
      text = 'Non assuré';
      icon = Icons.warning;
    } else if (isExpiringSoon) {
      color = Colors.orange;
      text = 'Expire bientôt';
      icon = Icons.schedule;
    } else {
      color = Colors.green;
      text = 'Assuré';
      icon = Icons.check_circle;
    }

    return Container(
      padding: ,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          ,
          const SizedBox(width: 4),
          ,
          ),
        ],
      ),
    );
  }

  /// ❌ État d'erreur
  Widget _buildErrorState() {
    return ,
          const SizedBox(height: 16),
          ,
          ),
          const SizedBox(height: 8),
          ,
          ),
          const SizedBox(height: 24),
          ElevatedButton.const Icon(
            onPressed: () => setState(() {},
            icon: const Icon(Icons.info),
            label: const Text('Réessayer'),
          ),
        ],
      ),
    );
  }

  /// 🚫 État vide
  Widget _buildEmptyState() {
    return ,
          const SizedBox(height: 24),
          ,
          ),
          const SizedBox(height: 8),
          ,
          ),
          const SizedBox(height: 32),
          Container(
            padding: ,
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Column(
              children: [
                ,
                const SizedBox(height: 8),
                ,
                ),
                const SizedBox(height: 8),
                ,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 🔧 Méthodes utilitaires
  Color _getStatusColor(bool isInsured, bool isExpiringSoon) {
    if (!isInsured) return Colors.red;
    if (isExpiringSoon) return Colors.orange;
    return Colors.green;
  }

  bool _isExpiringSoon(Map<String, dynamic>? assurance) {
    if (assurance == null || assurance['dateFin'] == null) return false;

    try {
      final endDate = assurance['dateFin'].toDate() as DateTime;
      final now = DateTime.now();
      final daysRemaining = endDate.difference(now).inDays;
      return daysRemaining <= 30 && daysRemaining > 0;
    } catch (e) {
      return false;
    }
  }

  String _formatDate(dynamic timestamp) {
    if (timestamp == null) return 'N/A';

    try {
      final date = timestamp.toDate() as DateTime;
      return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
    } catch (e) {
      return 'N/A';
    }
  }

  /// 🎯 Actions
  void _viewVehicleDetails(Map<String, dynamic> vehicle) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: ({vehicle['immatriculation']}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildInfoSection('🚗 Véhicule', [
                'Immatriculation: ${vehicle['immatriculation'] ?? 'N/A'}',
                'Marque: ${vehicle['marque'] ?? 'N/A'}',
                'Modèle: ${vehicle['modele'] ?? 'N/A'}',
                'Année: ${vehicle['annee']?.toString() ?? 'N/A'}',
                'Couleur: ${vehicle['couleur'] ?? 'N/A'}',
                'Énergie: ${vehicle['energie'] ?? 'N/A'}',
                'Puissance: ${vehicle['puissance']?.toString() ?? 'N/A'} CV',
                'Usage: ${vehicle['usage'] ?? 'N/A'}',
              ),

              if (vehicle['assurance'] != null) ...[
                const SizedBox(height: 16),
                _buildInfoSection('🛡️ Assurance', [
                  'Compagnie: ${vehicle['assurance']['compagnie'] ?? 'N/A'}',
                  'Contrat: ${vehicle['assurance']['numeroContrat'] ?? 'N/A'}',
                  'Agence: ${vehicle['assurance']['agence'] ?? 'N/A'}',
                  'Agent: ${vehicle['assurance']['agent'] ?? 'N/A'}',
                  'Début: ${_formatDate(vehicle['assurance']['dateDebut'])}',
                  'Fin: ${_formatDate(vehicle['assurance']['dateFin'])}',
                  'Statut: ${vehicle['assurance']['status'] ?? 'N/A'}',
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection(String title, List<String> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        ),
        const SizedBox(height: 8),
        ...items.map((item) => (1),
          ),
        )),
      ],
    );
  }

  void _contactAgent(Map<String, dynamic> vehicle) {
    final assurance = vehicle['assurance'] as Map<String, dynamic>?;
    if (assurance == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Contacter l\'agent'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ({assurance['agent'] ?? 'N/A'}',
              style: ,
            ),
            const SizedBox(height: 8),
            ({assurance['agence'] ?? 'N/A'}'),
            ({assurance['compagnie'] ?? 'N/A'}'),
            const SizedBox(height: 16),
            ,
            ),
            const SizedBox(height: 8),
            const Text('• Renouveler votre contrat'),
            const Text('• Modifier vos garanties'),
            const Text('• Déclarer un sinistre'),
            const Text('• Poser des questions'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
          ElevatedButton.const Icon(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implémenter l'appel ou l'envoi d'email
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: const Text('Fonctionnalité de contact à implémenter'),
                ),
              );
            },
            icon: const Icon(Icons.info),
            label: const Text('Appeler
