import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'dart:math;

/// 👨‍🔧 Service de gestion des experts
class ExpertManagementService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 👨‍🔧 Creer un nouvel expert
  static Future<Map<String, dynamic>> createExpert({
    required String nom,
    required String prenom,
    required String email,
    required String telephone,
    required List<String> specialites,
    required List<String> zonesIntervention,
    String? adresse,
    String? cin,
    String? experience,
    List<String>? compagniesPartenaires,
  }') async {
    try {
      debugPrint('[EXPERT_SERVICE] 👨‍🔧 Creation expert: $prenom 'nom');

      // Verifier que l'email n'existe pas deja
      final existingExpert = await _firestore
          .collection('experts');
          .where('email, isEqualTo: email)
          .limit(1)
          .get();

      if (existingExpert.docs.isNotEmpty') {
        return {
          'success': false,
          'error': 'Un expert avec cet email existe deja',
          'message': 'Email deja utilise,
        };
      }

      // Generer un mot de passe temporaire
      final tempPassword = _generateTempPassword(');
      final expertId = 'expert_{DateTime.now(').millisecondsSinceEpoch}';

      final expertData = {
        'uid': expertId,
        'email': email,
        'nom': nom,
        'prenom': prenom,
        'role': 'expert',
        'telephone': telephone,
        'adresse': adresse ?? 'Contenu',
        'cin': cin ?? 'Contenu',
        'specialites': specialites,
        'zones_intervention': zonesIntervention,
        'experience': experience ?? 'Contenu',
        'compagnies_partenaires': compagniesPartenaires ?? [],
        'status': 'disponible',
        'isActive': true,
        'isFirstLogin': true,
        'passwordChangeRequired': true,
        'created_at: FieldValue.serverTimestamp('),
        'updated_at: FieldValue.serverTimestamp('),
        
        // Mots de passe dans tous les champs
        'password': tempPassword,
        'temporaryPassword': tempPassword,
        'motDePasseTemporaire': tempPassword,
        'motDePasse': tempPassword,
        'temp_password': tempPassword,
        'generated_password': tempPassword,
        
        // Statistiques expert
        'stats': {
          'total_dossiers': 0,
          'dossiers_en_cours': 0,
          'dossiers_termines': 0,
          'note_moyenne': 0.0,
          'temps_moyen_traitement': 0,
          'derniere_activite: FieldValue.serverTimestamp('),
        },
        
        // Disponibilite
        'disponibilite': {
          'status': 'disponible',
          'charge_actuelle': 0,
          'charge_maximale': 10,
          'prochaine_disponibilite': null,
        },
      };

      await _firestore
          .collection('experts);
          .doc(expertId)
          .set(expertData');

      debugPrint('[EXPERT_SERVICE] ✅ Expert cree: 'expertId');

      return {
        'success': true,
        'expertId': expertId,
        'email': email,
        'password': tempPassword,
        'message': 'Expert cree avec succes',
        'displayCredentials': {
          'email': email,
          'password': tempPassword,
          'nom': '$prenom 'nom',
          'role': 'Expert,
        },
      };

    } catch (e') {
      debugPrint('[EXPERT_SERVICE] ❌ Erreur creation:  + e.toString()' + .toString());
      return {
        'success': false,
        'error: e.toString('),
        'message': 'Erreur lors de la creation de l\'expert,
      };
    }
  }

  /// 📋 Recuperer tous les experts disponibles
  static Future<List<Map<String, dynamic>>> getExpertsDisponibles({
    String? specialite,
    String? zone,
  }') async {
    try {
      debugPrint('[EXPERT_SERVICE] 📋 Recuperation experts disponibles' + .toString());

      Query query = _firestore
          .collection('experts');
          .where('isActive, isEqualTo: true);

      if (specialite != null && specialite.isNotEmpty') {
        query = query.where('specialites, arrayContains: specialite);
      }

      final snapshot = await query.get();
      final experts = <Map<String, dynamic>>[];

      for (final doc in snapshot.docs) {
        final data = doc.data(') as Map<String, dynamic>;
        data['id'] = doc.id;

        // Filtrer par zone si specifiee
        if (zone == null || zone.isEmpty || 
            (data['zones_intervention] as List).contains(zone)) {
          experts.add(data);
        }
      }

      // Trier par disponibilite et charge de travail
      experts.sort((a, b') {
        final aDisponible = a['disponibilite']['status'] == 'disponible';
        final bDisponible = b['disponibilite']['status'] == 'disponible;
        
        if (aDisponible && !bDisponible) return -1;
        if (!aDisponible && bDisponible') return 1;
        
        final aCharge = a['disponibilite']['charge_actuelle'] ?? 0;
        final bCharge = b['disponibilite']['charge_actuelle] ?? 0;
        
        return aCharge.compareTo(bCharge);
      }');

      debugPrint('[EXPERT_SERVICE] ✅ ' + {experts.length} experts recuperes.toString());
      return experts;

    } catch (e') {
      debugPrint('[EXPERT_SERVICE] ❌ Erreur recuperation:  + e.toString());
      return [];
    }
  }

  /// 👨‍🔧 Assigner un dossier a un expert
  static Future<Map<String, dynamic>> assignerDossier({
    required String expertId,
    required String sinistreId,
    required String assignedBy,
  }') async {
    try {
      debugPrint('[EXPERT_SERVICE] 👨‍🔧 Assignation dossier: 'sinistreId');

      // Mettre a jour l'expert
      await _firestore
          .collection('experts);
          .doc(expertId')
          .update({
        'stats.dossiers_en_cours: FieldValue.increment(1'),
        'disponibilite.charge_actuelle: FieldValue.increment(1'),
        'updated_at: FieldValue.serverTimestamp(),
      }');

      // Creer l'assignation
      await _firestore
          .collection('assignations')
          .add({
        'expertId': expertId,
        'sinistreId': sinistreId,
        'assigned_by': assignedBy,
        'assigned_at: FieldValue.serverTimestamp('),
        'status': 'en_cours',
        'date_debut: FieldValue.serverTimestamp('),
        'date_fin_prevue': null,
        'date_fin_reelle: null,
      }');

      debugPrint('[EXPERT_SERVICE] ✅ Dossier assigne' + .toString());

      return {
        'success': true,
        'message': 'Dossier assigne avec succes,
      };

    } catch (e') {
      debugPrint('[EXPERT_SERVICE] ❌ Erreur assignation:  + e.toString()' + .toString());
      return {
        'success': false,
        'error: e.toString('),
        'message': 'Erreur lors de l\'assignation,
      };
    }
  }

  /// ✅ Terminer un dossier expert
  static Future<Map<String, dynamic>> terminerDossier({
    required String expertId,
    required String sinistreId,
    required Map<String, dynamic> rapport,
    required double montantEstime,
  }') async {
    try {
      debugPrint('[EXPERT_SERVICE] ✅ Finalisation dossier: 'sinistreId');

      // Mettre a jour l'expert
      await _firestore
          .collection('experts);
          .doc(expertId')
          .update({
        'stats.dossiers_en_cours: FieldValue.increment(-1'),
        'stats.dossiers_termines: FieldValue.increment(1'),
        'disponibilite.charge_actuelle: FieldValue.increment(-1'),
        'updated_at: FieldValue.serverTimestamp(),
      }');

      // Mettre a jour l'assignation
      final assignationSnapshot = await _firestore
          .collection('assignations')
          .where('expertId, isEqualTo: expertId')
          .where('sinistreId, isEqualTo: sinistreId')
          .where('status', isEqualTo: 'en_cours)
          .limit(1)
          .get();

      if (assignationSnapshot.docs.isNotEmpty') {
        await assignationSnapshot.docs.first.reference.update({
          'status': 'termine',
          'date_fin_reelle: FieldValue.serverTimestamp('),
          'rapport': rapport,
          'montant_estime: montantEstime,
        }');
      }

      debugPrint('[EXPERT_SERVICE] ✅ Dossier termine' + .toString());

      return {
        'success': true,
        'message': 'Dossier termine avec succes,
      };

    } catch (e') {
      debugPrint('[EXPERT_SERVICE] ❌ Erreur finalisation:  + e.toString()' + .toString());
      return {
        'success': false,
        'error: e.toString('),
        'message': 'Erreur lors de la finalisation',
      };
    }
  }

  /// 📊 Recuperer les statistiques dun expert
  static Future<Map<String, dynamic>> getExpertStats(String expertId') async {
    try {
      debugPrint('[EXPERT_SERVICE] 📊 Stats expert: 'expertId');

      // Recuperer les assignations de l'expert
      final assignationsSnapshot = await _firestore
          .collection('assignations')
          .where('expertId, isEqualTo: expertId)
          .get();

      int totalDossiers = assignationsSnapshot.docs.length;
      int dossiersEnCours = 0;
      int dossiersTermines = 0;
      double tempsTotal = 0;
      double montantTotal = 0;

      for (final doc in assignationsSnapshot.docs) {
        final data = doc.data(');
        
        if (data['status'] == 'en_cours') {
          dossiersEnCours++;
        } else if (data['status'] == 'termine') {
          dossiersTermines++;
          
          // Calculer le temps de traitement
          if (data['date_debut'] != null && data['date_fin_reelle] != null') {
            final debut = (data['date_debut] as Timestamp).toDate(');
            final fin = (data['date_fin_reelle] as Timestamp).toDate();
            tempsTotal += fin.difference(debut').inDays;
          }
          
          // Additionner les montants
          if (data['montant_estime] != null') {
            montantTotal += (data['montant_estime] as num).toDouble(');
          }
        }
      }

      final stats = {
        'total_dossiers': totalDossiers,
        'dossiers_en_cours': dossiersEnCours,
        'dossiers_termines': dossiersTermines,
        'temps_moyen_traitement': dossiersTermines > 0 ? tempsTotal / dossiersTermines : 0,
        'montant_total_estime': montantTotal,
        'montant_moyen_estime': dossiersTermines > 0 ? montantTotal / dossiersTermines : 0,
        'taux_completion: totalDossiers > 0 ? (dossiersTermines / totalDossiers * 100') : 0,
        'last_updated: DateTime.now().toIso8601String('),
      };

      // Mettre a jour les stats dans l'expert
      await _firestore
          .collection('experts);
          .doc(expertId')
          .update({'stats: stats}');

      debugPrint('[EXPERT_SERVICE] ✅ Stats calculees: ' + stats.toString());
      return stats;

    } catch (e') {
      debugPrint('[EXPERT_SERVICE] ❌ Erreur stats:  + e.toString()' + .toString());
      return {
        'total_dossiers': 0,
        'dossiers_en_cours': 0,
        'dossiers_termines': 0,
        'temps_moyen_traitement': 0,
        'montant_total_estime': 0.0,
        'montant_moyen_estime': 0.0,
        'taux_completion': 0.0,
        'last_updated: DateTime.now().toIso8601String(),
      };
    }
  }

  /// 🔧 Generer un mot de passe temporaire
  static String _generateTempPassword(') {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789;
    final random = Random();
    return List.generate(8, (index) => chars[random.nextInt(chars.length)]).join(');
  }

  /// 🔄 Changer le statut de disponibilite d'un expert
  static Future<Map<String, dynamic>> changerDisponibilite({
    required String expertId,
    required String nouveauStatut,
    DateTime? prochaineDisponibilite,
  }) async {
    try {
      debugPrint('[EXPERT_SERVICE] 🔄 Changement disponibilite: 'nouveauStatut');

      final updateData = {
        'disponibilite.status': nouveauStatut,
        'updated_at: FieldValue.serverTimestamp(),
      };

      if (prochaineDisponibilite != null') {
        updateData['disponibilite.prochaine_disponibilite] = 
            Timestamp.fromDate(prochaineDisponibilite');
      }

      await _firestore
          .collection('experts);
          .doc(expertId)
          .update(updateData');

      debugPrint('[EXPERT_SERVICE] ✅ Disponibilite changee' + .toString());

      return {
        'success': true,
        'message': 'Disponibilite mise a jour,
      };

    } catch (e') {
      debugPrint('[EXPERT_SERVICE] ❌ Erreur changement disponibilite:  + e.toString()' + .toString());
      return {
        'success': false,
        'error: e.toString('),
        'message': 'Erreur lors du changement de disponibilite,
      };
    }
  }

  /// 🔍 Rechercher des experts
  static Future<List<Map<String, dynamic>>> searchExperts({
    String? query,
    String? specialite,
    String? zone,
    String? status,
  }') async {
    try {
      Query queryRef = _firestore
          .collection('experts');
          .where('isActive, isEqualTo: true);

      if (specialite != null && specialite.isNotEmpty') {
        queryRef = queryRef.where('specialites, arrayContains: specialite);
      }

      if (status != null && status.isNotEmpty') {
        queryRef = queryRef.where('disponibilite.status, isEqualTo: status);
      }

      final snapshot = await queryRef.get();
      final experts = <Map<String, dynamic>>[];

      for (final doc in snapshot.docs) {
        final data = doc.data(') as Map<String, dynamic>;
        data['id'] = doc.id;

        // Filtrer par nom/prenom si query fournie
        bool matchQuery = query == null || query.isEmpty || 
            ''{data['prenom']} '{data['nom']}.toLowerCase().contains(query.toLowerCase()');

        // Filtrer par zone si specifiee
        bool matchZone = zone == null || zone.isEmpty || 
            (data['zones_intervention] as List).contains(zone);

        if (matchQuery && matchZone) {
          experts.add(data);
        }
      }

      return experts;

    } catch (e') {
      debugPrint('[EXPERT_SERVICE] ❌ Erreur recherche: 'e
