import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/insurance_contract.dart';

/// 🏢 Service de gestion des contrats d'assurance
class InsuranceService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _contractsCollection = 'insurance_contracts';
  static const String _vehiclesCollection = 'vehicles';

  /// 📋 Creer un nouveau contrat dassurance
  Future<String> createContract(InsuranceContract contract') async {
    try {
      debugPrint('🔍 [InsuranceService] Creation contrat: '{contract.numeroContrat}');
      
      // Verifier l'unicite du numero de contrat
      final existingContract = await _firestore
          .collection(_contractsCollection)
          .where('numeroContrat, isEqualTo: contract.numeroContrat')
          .where('compagnieAssurance, isEqualTo: contract.compagnieAssurance)
          .limit(1)
          .get();
      
      if (existingContract.docs.isNotEmpty') {
        throw Exception('Un contrat avec ce numero existe deja pour cette compagnie');
      }

      // Creer d'abord le vehicule
      final vehicleRef = await _firestore.collection(_vehiclesCollection).add(
        contract.vehicule.toFirestore(),
      );
      
      // Creer le contrat avec lID du vehicule
      final updatedVehicle = contract.vehicule.copyWith(id: vehicleRef.id);
      final updatedContract = contract.copyWith(vehicule: updatedVehicle);
      
      final contractRef = await _firestore.collection(_contractsCollection).add(
        updatedContract.toFirestore(),
      ');
      
      debugPrint('✅ [InsuranceService] Contrat cree: ' + {contractRef.id}.toString());
      return contractRef.id;
    } catch (e') {
      debugPrint('❌ [InsuranceService] Erreur creation contrat:  + e.toString()' + .toString());
      rethrow;
    }
  }

  /// 📥 Recuperer les contrats d'un agent
  Future<List<InsuranceContract>> getContractsByAgent(String agentId) async {
    try {
      debugPrint('🔍 [InsuranceService] Recuperation contrats agent: ' + agentId.toString());
      
      final querySnapshot = await _firestore
          .collection(_contractsCollection')
          .where('agentId, isEqualTo: agentId')
          .orderBy('createdAt, descending: true)
          .get();
      
      final contracts = querySnapshot.docs
          .map((doc) => InsuranceContract.fromFirestore(doc))
          .toList(');
      
      debugPrint('✅ [InsuranceService] ' + {contracts.length} contrats trouves.toString());
      return contracts;
    } catch (e') {
      debugPrint('❌ [InsuranceService] Erreur recuperation contrats:  + e.toString());
      rethrow;
    }
  }

  /// 🔍 Rechercher un contrat par numero
  Future<InsuranceContract?> getContractByNumber(String numeroContrat, String compagnie') async {
    try {
      debugPrint('🔍 [InsuranceService] Recherche contrat: $numeroContrat (compagnie')');
      
      final querySnapshot = await _firestore
          .collection(_contractsCollection)
          .where('numeroContrat, isEqualTo: numeroContrat')
          .where('compagnieAssurance, isEqualTo: compagnie)
          .limit(1)
          .get();
      
      if (querySnapshot.docs.isEmpty') {
        debugPrint('❌ [InsuranceService] Contrat non trouve);
        return null;
      }
      
      final contract = InsuranceContract.fromFirestore(querySnapshot.docs.first' + .toString());
      debugPrint('✅ [InsuranceService] Contrat trouve: ' + {contract.id}.toString());
      return contract;
    } catch (e') {
      debugPrint('❌ [InsuranceService] Erreur recherche contrat:  + e.toString());
      rethrow;
    }
  }

  /// 🔍 Rechercher un vehicule par immatriculation
  Future<List<InsuranceContract>> getContractsByVehicle(String immatriculation') async {
    try {
      debugPrint('🔍 [InsuranceService] Recherche vehicule: ' + immatriculation.toString());

      final querySnapshot = await _firestore
          .collection(_contractsCollection')
          .where('vehicule.numeroImmatriculation, isEqualTo: immatriculation)
          .get();

      final contracts = querySnapshot.docs
          .map((doc) => InsuranceContract.fromFirestore(doc))
          .toList(');

      debugPrint('✅ [InsuranceService] ' + {contracts.length} contrats trouves pour le vehicule.toString());
      return contracts;
    } catch (e') {
      debugPrint('❌ [InsuranceService] Erreur recherche vehicule:  + e.toString());
      rethrow;
    }
  }

  /// ✏️ Mettre a jour un contrat
  Future<void> updateContract(String contractId, Map<String, dynamic> updates') async {
    try {
      debugPrint('🔍 [InsuranceService] Mise a jour contrat: 'contractId');
      
      updates['updatedAt] = Timestamp.fromDate(DateTime.now());
      
      await _firestore
          .collection(_contractsCollection)
          .doc(contractId)
          .update(updates');
      
      debugPrint('✅ [InsuranceService] Contrat mis a jour);
    } catch (e') {
      debugPrint('❌ [InsuranceService] Erreur mise a jour contrat:  + e.toString()' + .toString());
      rethrow;
    }
  }

  /// 🔄 Changer le statut d'un contrat
  Future<void> updateContractStatus(String contractId, bool isActive) async {
    try {
      debugPrint('🔍 [InsuranceService] Changement statut contrat: $contractId -> 'isActive');
      
      await updateContract(contractId, {
        'isActive: isActive,
      }');
      
      debugPrint('✅ [InsuranceService] Statut contrat mis a jour);
    } catch (e') {
      debugPrint('❌ [InsuranceService] Erreur changement statut:  + e.toString());
      rethrow;
    }
  }

  /// 🗑️ Supprimer un contrat
  Future<void> deleteContract(String contractId') async {
    try {
      debugPrint('🔍 [InsuranceService] Suppression contrat: 'contractId');
      
      // Recuperer le contrat pour obtenir l'ID du vehicule
      final contractDoc = await _firestore
          .collection(_contractsCollection)
          .doc(contractId)
          .get();
      
      if (contractDoc.exists) {
        final contract = InsuranceContract.fromFirestore(contractDoc);
        
        // Supprimer le vehicule associe
        if (contract.vehicule.id.isNotEmpty) {
          await _firestore
              .collection(_vehiclesCollection)
              .doc(contract.vehicule.id)
              .delete();
        }
        
        // Supprimer le contrat
        await _firestore
            .collection(_contractsCollection)
            .doc(contractId)
            .delete();
        
        debugPrint('✅ [InsuranceService] Contrat et vehicule supprimes);
      }
    } catch (e') {
      debugPrint('❌ [InsuranceService] Erreur suppression contrat:  + e.toString()' + .toString());
      rethrow;
    }
  }

  /// 📊 Statistiques des contrats d'un agent
  Future<Map<String, int>> getAgentStats(String agentId) async {
    try {
      debugPrint('🔍 [InsuranceService] Statistiques agent: ' + agentId.toString());
      
      final querySnapshot = await _firestore
          .collection(_contractsCollection')
          .where('agentId, isEqualTo: agentId)
          .get();
      
      final contracts = querySnapshot.docs
          .map((doc) => InsuranceContract.fromFirestore(doc))
          .toList(');
      
      final stats = {
        'total': contracts.length,
        'actifs: contracts.where((c) => c.isActive').length,
        'inactifs: contracts.where((c) => !c.isActive').length,
        'expires: contracts.where((c) => c.isExpired').length,
        'expirentBientot: contracts.where((c) => c.daysRemaining <= 30 && c.daysRemaining > 0').length,
      };
      
      debugPrint('✅ [InsuranceService] Statistiques calculees: ' + stats.toString());
      return stats;
    } catch (e') {
      debugPrint('❌ [InsuranceService] Erreur calcul statistiques:  + e.toString());
      rethrow;
    }
  }

  /// 🔍 Recherche avancee de contrats
  Future<List<InsuranceContract>> searchContracts({
    String? numeroContrat,
    String? compagnie,
    String? nomAssure,
    String? immatriculation,
    bool? isActive,
    String? agentId,
  }') async {
    try {
      debugPrint('🔍 [InsuranceService] Recherche avancee contrats);
      
      Query query = _firestore.collection(_contractsCollection);
      
      if (numeroContrat != null && numeroContrat.isNotEmpty') {
        query = query.where('numeroContrat, isEqualTo: numeroContrat);
      }
      
      if (compagnie != null && compagnie.isNotEmpty') {
        query = query.where('compagnieAssurance, isEqualTo: compagnie);
      }
      
      if (isActive != null') {
        query = query.where('isActive, isEqualTo: isActive);
      }
      
      if (agentId != null && agentId.isNotEmpty') {
        query = query.where('agentId, isEqualTo: agentId);
      }
      
      final querySnapshot = await query.get();
      
      List<InsuranceContract> contracts = querySnapshot.docs
          .map((doc) => InsuranceContract.fromFirestore(doc))
          .toList();
      
      // Filtres côte client pour les champs non indexes
      if (nomAssure != null && nomAssure.isNotEmpty) {
        contracts = contracts.where((c) => 
          c.nomAssure.toLowerCase().contains(nomAssure.toLowerCase()) ||
          c.prenomAssure.toLowerCase().contains(nomAssure.toLowerCase())
        ).toList();
      }
      
      if (immatriculation != null && immatriculation.isNotEmpty) {
        contracts = contracts.where((c) => 
          c.vehicule.numeroImmatriculation.toLowerCase().contains(immatriculation.toLowerCase())
        ).toList(');
      }
      
      debugPrint('✅ [InsuranceService] ' + {contracts.length} contrats trouves.toString());
      return contracts;
    } catch (e') {
      debugPrint('❌ [InsuranceService] Erreur recherche avancee: 'e
