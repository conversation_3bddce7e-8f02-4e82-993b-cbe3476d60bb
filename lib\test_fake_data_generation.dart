import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'features/admin_compagnie/services/fake_data_service.dart';
import 'firebase_options.dart';

/// 🧪 Écran de test pour la génération de données factices
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  
  runApp();
}

class TestFakeDataApp extends StatelessWidget {
  const Text(\;

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Test Fake Data Generation',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: ,
    );
  }
}

class TestFakeDataScreen extends StatefulWidget {
  const Text(\;

  @override
  State<TestFakeDataScreen> createState() => _TestFakeDataScreenState();
}

class _TestFakeDataScreenState extends State<TestFakeDataScreen> {
  bool _isGenerating = false;
  bool _isClearing = false;
  String _currentProgress = 'Contenu';
  Map<String, dynamic>? _lastResults;
  Map<String, int>? _existingData;

  // Données de test
  final String _testCompagnieId = 'test-compagnie-123';
  final String _testCompagnieNom = 'STAR Assurance Test';

  @override
  void initState() {
    super.initState();
    _checkExistingData();
  }

  /// 📊 Vérifier les données existantes
  Future<void> _checkExistingData() async {
    try {
      final data = await FakeDataService.checkFakeDataForCompagnie(_testCompagnieId);
      setState(() {
        _existingData = data;
      });
    } catch (e) {
      debugPrint('Erreur vérification: $e');
    }
  }

  /// 🚀 Générer les données factices
  Future<void> _generateFakeData() async {
    if (_isGenerating) return;

    setState(() {
      _isGenerating = true;
      _currentProgress = 'Initialisation...';
      _lastResults = null;
    });

    try {
      final results = await FakeDataService.generateFakeDataForCompagnie(
        _testCompagnieId,
        _testCompagnieNom,
        onProgress: (progress) {
          setState(() {
            _currentProgress = progress;
          });
        },
      );

      setState(() {
        _lastResults = results;
      });

      await _checkExistingData();

    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: (e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isGenerating = false;
        _currentProgress = 'Contenu';
      });
    }
  }

  /// 🗑️ Supprimer les données factices
  Future<void> _clearFakeData() async {
    if (_isClearing) return;

    setState(() {
      _isClearing = true;
    });

    try {
      final results = await FakeDataService.clearFakeDataForCompagnie(_testCompagnieId);
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: ({results['employes']} employés'),
          backgroundColor: Colors.green,
        ),
      );

      await _checkExistingData();
      setState(() {
        _lastResults = null;
      });

    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: (e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isClearing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🧪 Test Génération Données Factices'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: (1),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ,
                    ),
                    const SizedBox(height: 8),
                    (_testCompagnieId'),
                    (_testCompagnieNom'),
                    ({FakeDataService.isDevelopmentMode}'),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Données existantes
            if (_existingData != null)
              Card(
                child: (1),
                      ),
                      const SizedBox(height: 8),
                      ({_existingData!['agences']}'),
                      ({_existingData!['employes']}'),
                    ],
                  ),
                ),
              ),
            
            const SizedBox(height: 16),
            
            // Progression
            if (_isGenerating) ...[
              Card(
                color: Colors.orange.withValues(alpha: 0.1),
                child: (1),
                      ),
                      const SizedBox(height: 8),
                      const Text(_currentProgress),
                      const SizedBox(height: 8),
                      LinearProgressIndicator(),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],
            
            // Résultats
            if (_lastResults != null) ...[
              Card(
                color: Colors.green.withValues(alpha: 0.1),
                child: (1),
                      ),
                      const SizedBox(height: 8),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],
            
            // Boutons d'action
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.const Icon(
                    onPressed: _isGenerating || _isClearing ? null : _generateFakeData,
                    icon: _isGenerating 
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : ,
                    label: const Text(_isGenerating ? 'Génération...' : 'Générer'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: ,
                  ),
                ),
                
                const SizedBox(width: 16),
                
                Expanded(
                  child: ElevatedButton.const Icon(
                    onPressed: _isGenerating || _isClearing ? null : _clearFakeData,
                    icon: _isClearing 
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : ,
                    label: const Text(_isClearing ? 'Suppression...' : 'Supprimer'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      padding: ,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Bouton actualiser
            ,
                label: const Text('Actualiser les données
