import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

/// 📸 Widget pour capturer les photos de l'accident
class PhotoCaptureWidget extends StatefulWidget {
  final Function(List<String>) onPhotosChanged;

  ;

  @override
  State<PhotoCaptureWidget> createState() => _PhotoCaptureWidgetState();
}

class _PhotoCaptureWidgetState extends State<PhotoCaptureWidget> {
  final ImagePicker _picker = ImagePicker();
  final List<XFile> _photos = [];
  
  final List<Map<String, dynamic>> _photoTypes = [
    {
      'title': 'Vue d\'ensemble',
      'description': 'Photo générale de la scène d\'accident',
      'icon': Icons.landscape,
      'required': true,
    },
    {
      'title': 'Dégâts véhicule',
      'description': 'Photos détaillées des dégâts sur votre véhicule',
      'icon': Icons.directions_car,
      'required': true,
    },
    {
      'title': 'Autre véhicule',
      'description': 'Photos de l\'autre véhicule impliqué',
      'icon': Icons.car_crash,
      'required': false,
    },
    {
      'title': 'Environnement',
      'description': 'Signalisation, route, obstacles...',
      'icon': Icons.traffic,
      'required': false,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: ,
          
          const SizedBox(height: 20),
          
          // Guide photos
          _buildPhotoGuide(),
          
          const SizedBox(height: 20),
          
          // Photos prises
          _buildPhotoGrid(),
          
          const SizedBox(height: 20),
          
          // Boutons d'action
          _buildActionButtons(),
        ],
      ),
    );
  }

  /// 📋 En-tête
  Widget _buildHeader() {
    return Container(
      padding: ,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: ,
                ),
                child: ,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ,
                    ),
                    ,
                    ),
                  ],
                ),
              ),
              Container(
                padding: ,
                ),
                child: ({_photos.length}/8',
                  style: ,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ,
          ),
        ],
      ),
    );
  }

  /// 📋 Guide des types de photos
  Widget _buildPhotoGuide() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        ),
        const SizedBox(height: 12),
        ...(_photoTypes.map((type) => _buildPhotoTypeCard(type))),
      ],
    );
  }

  /// 🎯 Carte type de photo
  Widget _buildPhotoTypeCard(Map<String, dynamic> type) {
    return Container(
      margin: ,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: type['required'] ? Colors.orange[200]! : Colors.grey[200]!,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: ,
            ),
            child: ,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    ,
                    ),
                    if (type['required'])
                      Container(
                        margin: ,
                        decoration: BoxDecoration(
                          color: Colors.orange,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: ,
                        ),
                      ),
                  ],
                ),
                ,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 🖼️ Grille des photos
  Widget _buildPhotoGrid() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(\',
          style: ,
        ),
        const SizedBox(height: 12),
        if (_photos.isEmpty)
          _buildEmptyState()
        else
          GridView.builder(
            shrinkWrap: true,
            physics: ,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio: 1,
            ),
            itemCount: _photos.length,
            itemBuilder: (context, index) {
              return _buildPhotoItem(_photos[index], index);
            },
          ),
      ],
    );
  }

  /// 🚫 État vide
  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      padding: ,
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          ,
          const SizedBox(height: 16),
          ,
          ),
          const SizedBox(height: 8),
          ,
          ),
        ],
      ),
    );
  }

  /// 📸 Item photo
  Widget _buildPhotoItem(XFile photo, int index) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.file(
              File(photo.path),
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          top: 4,
          right: 4,
          child: GestureDetector(
            onTap: () => _removePhoto(index),
            child: Container(
              padding: ,
              child: ,
            ),
          ),
        ),
        Positioned(
          bottom: 4,
          left: 4,
          child: Container(
            padding: ,
              borderRadius: BorderRadius.circular(4),
            ),
            child: ({index + 1}',
              style: ,
            ),
          ),
        ),
      ],
    );
  }

  /// 🎬 Boutons d'action
  Widget _buildActionButtons() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: ElevatedButton.const Icon(
                onPressed: () => _takePhoto(ImageSource.camera),
                icon: const Icon(Icons.info),
                label: const Text('Prendre Photo'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: ,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.const Icon(
                onPressed: () => _takePhoto(ImageSource.gallery),
                icon: const Icon(Icons.info),
                label: const Text('Galerie'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                  padding: ,
              ),
            ),
          ],
        ),
        
        if (_photos.isNotEmpty) ...[
          const SizedBox(height: 12),
          ,
              label: ,
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// 📸 Prendre une photo
  void _takePhoto(ImageSource source) async {
    try {
      final XFile? photo = await _picker.pickImage(
        source: source,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );
      
      if (photo != null) {
        setState(() {
          _photos.add(photo);
        });
        _updatePhotos();
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: const Text('📸 Photo ajoutée avec succès !'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: (e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 🗑️ Supprimer une photo
  void _removePhoto(int index) {
    setState(() {
      _photos.removeAt(index);
    });
    _updatePhotos();
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: const Text('🗑️ Photo supprimée'),
        backgroundColor: Colors.orange,
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// 🧹 Supprimer toutes les photos
  void _clearAllPhotos() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmation'),
        content: const Text('Voulez-vous vraiment supprimer toutes les photos ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Supprimer
