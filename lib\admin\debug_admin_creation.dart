import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../services/unified_compagnie_service.dart';

/// 🔧 Écran de debug pour créer des Admin Compagnie de test
class DebugAdminCreationScreen extends StatefulWidget {
  const DebugAdminCreationScreen({Key? key}) : super(key: key);

  @override
  State<DebugAdminCreationScreen> createState() => _DebugAdminCreationScreenState();
}

class _DebugAdminCreationScreenState extends State<DebugAdminCreationScreen> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  bool _isLoading = false;
  String _status = 'Contenu';

  /// 🏢 Diagnostiquer les compagnies (doublons, etc.)
  Future<void> _diagnoseCompagnies() async {
    setState(() {
      _isLoading = true;
      _status = 'Diagnostic des compagnies...';
    });

    try {
      // Utiliser le service unifié pour diagnostiquer
      final cleanResult = await UnifiedCompagnieService.cleanDuplicates();
      final stats = await UnifiedCompagnieService.getCompagniesStats();

      setState(() {
        _status = '🏢 DIAGNOSTIC DES COMPAGNIES\n\n';
        _status += '📊 STATISTIQUES:\n';
        _status += '• Total compagnies unifiées: ${stats['total']}\n';
        _status += '• Collections trouvées: ${(stats['par_collection'] as Map).keys.join(', ')}\n\n';

        _status += '🔍 ANALYSE DES DOUBLONS:\n';
        _status += '• Total documents trouvés: ${cleanResult['total_found']}\n';
        _status += '• Groupes uniques: ${cleanResult['unique_groups']}\n';
        _status += '• Doublons détectés: ${cleanResult['duplicates_detected']}\n\n';

        if (cleanResult['duplicates_detected'] > 0) {
          _status += '⚠️ DOUBLONS TROUVÉS:\n';
          final duplicates = cleanResult['duplicates'] as Map<String, dynamic>;
          for (final entry in duplicates.entries) {
            _status += '• ${entry.key}: ${(entry.value as List).length} copies\n';
          }
        } else {
          _status += '✅ Aucun doublon détecté !';
        }

        _status += '\n💡 RECOMMANDATION:\n';
        _status += 'Utilisez le service UnifiedCompagnieService pour éviter les doublons.';
      });
    } catch (e) {
      setState(() {
        _status = '❌ Erreur diagnostic: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 🔍 Vérifier les Admin Compagnie existants
  Future<void> _checkExistingAdmins() async {
    setState(() {
      _isLoading = true;
      _status = 'Vérification des Admin Compagnie existants...';
    });

    try {
      final adminsQuery = await _firestore
          .collection('users')
          .where('role', isEqualTo: 'admin_compagnie')
          .get();

      setState(() {
        _status = 'Trouvé ${adminsQuery.docs.length} Admin Compagnie(s):\n\n';
        
        for (final doc in adminsQuery.docs) {
          final data = doc.data();
          final password = data['password'] ??
                          data['temporaryPassword'] ??
                          data['motDePasseTemporaire'];

          final status = data['status'] as String?;
          final isActive = data['isActive'] as bool?;
          final isDeleted = status == 'supprime' || status == 'deleted';

          _status += '📧 ${data['email']}\n';
          _status += '🏢 ${data['compagnieNom'] ?? 'Non définie'}\n';
          _status += '🔑 Mot de passe: ${password ?? '❌ NON DÉFINI'}\n';
          _status += '📊 Statut: ${status ?? 'Non défini'} ${isDeleted ? '❌ SUPPRIMÉ' : 'Contenu'}\n';
          _status += '🔄 Actif: ${isActive ?? false}\n';

          if (isDeleted) {
            _status += '⚠️  COMPTE SUPPRIMÉ - Ne peut pas se connecter\n';
          }

          _status += '🔧 Champs mot de passe:\n';
          _status += '   - password: ${data['password'] ?? '❌'}\n';
          _status += '   - temporaryPassword: ${data['temporaryPassword'] ?? '❌'}\n';
          _status += '   - motDePasseTemporaire: ${data['motDePasseTemporaire'] ?? '❌'}\n';
          _status += '   - motDePasse: ${data['motDePasse'] ?? '❌'}\n';
          _status += '   - temp_password: ${data['temp_password'] ?? '❌'}\n';
          _status += '   - generated_password: ${data['generated_password'] ?? '❌'}\n';
          _status += '---\n';
        }
        
        if (adminsQuery.docs.isEmpty) {
          _status += '❌ Aucun Admin Compagnie trouvé !';
        }
      });
    } catch (e) {
      setState(() {
        _status = '❌ Erreur: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 🏗️ Créer des Admin Compagnie de test
  Future<void> _createTestAdmins() async {
    setState(() {
      _isLoading = true;
      _status = 'Création des Admin Compagnie de test...';
    });

    try {
      // Créer d'abord les compagnies
      final compagnies = [
        {
          'id': 'star_assurance',
          'nom': 'STAR Assurance',
          'code': 'STAR',
          'email': '<EMAIL>',
          'password': 'Star123',
        },
        {
          'id': 'comar_assurance',
          'nom': 'COMAR Assurance',
          'code': 'COMAR',
          'email': '<EMAIL>',
          'password': 'Comar123',
        },
        {
          'id': 'maghrebia_assurance',
          'nom': 'Maghrebia Assurance',
          'code': 'MAGHREBIA',
          'email': '<EMAIL>',
          'password': 'Maghrebia123',
        },
      ];

      int created = 0;
      for (final compagnie in compagnies) {
        // 1. Créer la compagnie
        await _firestore
            .collection('compagnies_assurance')
            .doc(compagnie['id'])
            .set({
          'nom': compagnie['nom'],
          'code': compagnie['code'],
          'status': 'actif',
          'created_at': FieldValue.serverTimestamp(),
          'created_by': 'debug_script',
          'adresse': 'Tunis, Tunisie',
          'telephone': '+216 71 123 456',
          'email': compagnie['email'],
        });

        // 2. Créer l'admin compagnie
        final adminId = 'admin_${compagnie['id']}';
        await _firestore
            .collection('users')
            .doc(adminId)
            .set({
          'uid': adminId,
          'email': compagnie['email'],
          'nom': 'Admin',
          'prenom': compagnie['nom'],
          'role': 'admin_compagnie',
          'status': 'actif',
          'isActive': true,
          'compagnieId': compagnie['id'],
          'compagnieNom': compagnie['nom'],
          'password': compagnie['password'], // Mot de passe simple pour test
          'temporaryPassword': compagnie['password'], // Aussi dans ce champ
          'created_at': FieldValue.serverTimestamp(),
          'created_by': 'debug_script',
          'source': 'debug_creation',
          'phone': '+216 71 123 456',
          'address': 'Tunis, Tunisie',
          'isLegitimate': true,
          'accountType': 'admin_system',
        });

        created++;
        setState(() {
          _status = 'Créé $created/${compagnies.length} Admin Compagnie...';
        });
      }

      setState(() {
        _status = '✅ $created Admin Compagnie créés avec succès !\n\n';
        _status += 'Identifiants créés:\n';
        for (final compagnie in compagnies) {
          _status += '\n🏢 ${compagnie['nom']}\n';
          _status += '📧 ${compagnie['email']}\n';
          _status += '🔑 ${compagnie['password']}\n';
        }
      });
    } catch (e) {
      setState(() {
        _status = '❌ Erreur lors de la création: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 🔄 Réactiver les comptes supprimés
  Future<void> _reactivateDeletedAdmins() async {
    setState(() {
      _isLoading = true;
      _status = 'Réactivation des comptes supprimés...';
    });

    try {
      // Trouver tous les admins compagnie supprimés
      final adminsQuery = await _firestore
          .collection('users')
          .where('role', isEqualTo: 'admin_compagnie')
          .get();

      int reactivated = 0;
      for (final doc in adminsQuery.docs) {
        final data = doc.data();
        final status = data['status'] as String?;

        if (status == 'supprime' || status == 'deleted') {
          // Réactiver le compte
          await doc.reference.update({
            'status': 'actif',
            'isActive': true,
            'deletedAt': FieldValue.delete(), // Supprimer le champ deletedAt
            'reactivatedAt': FieldValue.serverTimestamp(),
            'reactivatedBy': 'debug_script',
            'updated_at': FieldValue.serverTimestamp(),
          });

          reactivated++;
          setState(() {
            _status = 'Réactivé $reactivated comptes...';
          });
        }
      }

      setState(() {
        _status = '✅ $reactivated comptes réactivés !\n\n';
        if (reactivated > 0) {
          _status += 'Les comptes peuvent maintenant se connecter.\n';
          _status += 'Utilisez "Vérifier Admin Existants" pour voir les changements.';
        } else {
          _status += 'Aucun compte supprimé trouvé.';
        }
      });
    } catch (e) {
      setState(() {
        _status = '❌ Erreur lors de la réactivation: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 🔄 Synchroniser tous les champs de mot de passe
  Future<void> _syncPasswordFields() async {
    setState(() {
      _isLoading = true;
      _status = 'Synchronisation des champs de mot de passe...';
    });

    try {
      // Trouver tous les admins compagnie
      final adminsQuery = await _firestore
          .collection('users')
          .where('role', isEqualTo: 'admin_compagnie')
          .get();

      int synced = 0;
      for (final doc in adminsQuery.docs) {
        final data = doc.data();

        // Trouver le mot de passe dans n'importe quel champ
        final password = data['password'] ??
                        data['temporaryPassword'] ??
                        data['motDePasseTemporaire'] ??
                        data['motDePasse'] ??
                        data['temp_password'] ??
                        data['generated_password'];

        if (password != null) {
          // Synchroniser tous les champs avec le même mot de passe
          await doc.reference.update({
            'password': password,
            'temporaryPassword': password,
            'motDePasseTemporaire': password,
            'motDePasse': password,
            'temp_password': password,
            'generated_password': password,
            'password_synced_at': FieldValue.serverTimestamp(),
            'password_synced_by': 'debug_sync',
            'updated_at': FieldValue.serverTimestamp(),
          });

          synced++;
          setState(() {
            _status = 'Synchronisé $synced comptes...';
          });
        }
      }

      setState(() {
        _status = '✅ $synced comptes synchronisés !\n\n';
        if (synced > 0) {
          _status += 'Tous les champs de mot de passe sont maintenant identiques.\n';
          _status += 'Les connexions devraient maintenant fonctionner.';
        } else {
          _status += 'Aucun mot de passe trouvé à synchroniser.';
        }
      });
    } catch (e) {
      setState(() {
        _status = '❌ Erreur lors de la synchronisation: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 🔧 Corriger les Admin Compagnie sans mot de passe
  Future<void> _fixPasswordlessAdmins() async {
    setState(() {
      _isLoading = true;
      _status = 'Correction des Admin Compagnie sans mot de passe...';
    });

    try {
      // Trouver tous les admins compagnie
      final adminsQuery = await _firestore
          .collection('users')
          .where('role', isEqualTo: 'admin_compagnie')
          .get();

      int fixed = 0;
      for (final doc in adminsQuery.docs) {
        final data = doc.data();
        final password = data['password'] ??
                        data['temporaryPassword'] ??
                        data['motDePasseTemporaire'];

        if (password == null) {
          // Générer un mot de passe basé sur le nom de la compagnie
          final compagnieNom = data['compagnieNom'] as String?;
          final defaultPassword = compagnieNom != null
              ? '${compagnieNom.replaceAll(' ', 'Contenu')}123'
              : 'Admin123';

          // Mettre à jour avec un mot de passe
          await doc.reference.update({
            'password': defaultPassword,
            'temporaryPassword': defaultPassword,
            'motDePasseTemporaire': defaultPassword,
            'updated_at': FieldValue.serverTimestamp(),
            'updated_by': 'debug_fix',
          });

          fixed++;
          setState(() {
            _status = 'Corrigé $fixed comptes sans mot de passe...';
          });
        }
      }

      setState(() {
        _status = '✅ $fixed Admin Compagnie corrigés !\n\n';
        if (fixed > 0) {
          _status += 'Mots de passe générés automatiquement.\n';
          _status += 'Utilisez "Vérifier Admin Existants" pour voir les nouveaux mots de passe.';
        } else {
          _status += 'Tous les Admin Compagnie ont déjà un mot de passe.';
        }
      });
    } catch (e) {
      setState(() {
        _status = '❌ Erreur lors de la correction: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 🗑️ Supprimer tous les Admin Compagnie de test
  Future<void> _deleteTestAdmins() async {
    setState(() {
      _isLoading = true;
      _status = 'Suppression des Admin Compagnie de test...';
    });

    try {
      // Supprimer les utilisateurs
      final adminsQuery = await _firestore
          .collection('users')
          .where('role', isEqualTo: 'admin_compagnie')
          .where('source', isEqualTo: 'debug_creation')
          .get();

      for (final doc in adminsQuery.docs) {
        await doc.reference.delete();
      }

      // Supprimer les compagnies
      final compagniesQuery = await _firestore
          .collection('compagnies_assurance')
          .where('created_by', isEqualTo: 'debug_script')
          .get();

      for (final doc in compagniesQuery.docs) {
        await doc.reference.delete();
      }

      setState(() {
        _status = '✅ Admin Compagnie de test supprimés !';
      });
    } catch (e) {
      setState(() {
        _status = '❌ Erreur lors de la suppression: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🔧 Debug Admin Compagnie'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
      body: (1),
              label: const Text('Diagnostiquer Compagnies'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.indigo,
                foregroundColor: Colors.white,
                padding: ,
            ),
            const SizedBox(height: 12),

            ElevatedButton.const Icon(
              onPressed: _isLoading ? null : _checkExistingAdmins,
              icon: const Icon(Icons.info),
              label: const Text('Vérifier Admin Existants'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: ,
            ),
            const SizedBox(height: 12),

            ElevatedButton.const Icon(
              onPressed: _isLoading ? null : _createTestAdmins,
              icon: const Icon(Icons.info),
              label: const Text('Créer Admin de Test'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: ,
            ),
            const SizedBox(height: 12),

            ElevatedButton.const Icon(
              onPressed: _isLoading ? null : _syncPasswordFields,
              icon: const Icon(Icons.info),
              label: const Text('Synchroniser Mots de Passe'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal,
                foregroundColor: Colors.white,
                padding: ,
            ),
            const SizedBox(height: 12),

            ElevatedButton.const Icon(
              onPressed: _isLoading ? null : _reactivateDeletedAdmins,
              icon: const Icon(Icons.info),
              label: const Text('Réactiver Comptes Supprimés'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                foregroundColor: Colors.white,
                padding: ,
            ),
            const SizedBox(height: 12),

            ElevatedButton.const Icon(
              onPressed: _isLoading ? null : _fixPasswordlessAdmins,
              icon: const Icon(Icons.info),
              label: const Text('Corriger Mots de Passe Manquants'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: ,
            ),
            const SizedBox(height: 12),

            ElevatedButton.const Icon(
              onPressed: _isLoading ? null : _deleteTestAdmins,
              icon: const Icon(Icons.info),
              label: const Text('Supprimer Admin de Test'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: ,
            ),
            const SizedBox(height: 20),

            // Zone de statut
            Expanded(
              child: Container(
                padding: ,
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: SingleChildScrollView(
                  child: _isLoading
                      ? ,
                              const SizedBox(height: 16),
                              const Text('Chargement...
