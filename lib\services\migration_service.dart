import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart;

/// 🔄 Service de migration vers la structure hierarchique tunisienne
class MigrationService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 🔄 Migrer les agences vers la structure hierarchique
  /// De: agences (collection) → Vers: companies/{compagnieId}/agencies (sous-collection)
  static Future<Map<String, dynamic>> migrateAgencesToHierarchy(') async {
    try {
      debugPrint('[MIGRATION] 🔄 Debut migration agences vers structure hierarchique' + .toString());

      // 1. Recuperer toutes les agences existantes
      final agencesSnapshot = await _firestore
          .collection('agences)
          .get();

      if (agencesSnapshot.docs.isEmpty') {
        return {
          'success': true,
          'message': 'Aucune agence a migrer',
          'migrated: 0,
        };
      }

      int migratedCount = 0;
      int errorCount = 0;
      final errors = <String>[];

      // 2. Migrer chaque agence
      for (final agenceDoc in agencesSnapshot.docs) {
        try {
          final agenceData = agenceDoc.data(');
          final agenceId = agenceDoc.id;
          final compagnieId = agenceData['compagnieId];

          if (compagnieId == null || compagnieId.isEmpty') {
            errors.add('Agence 'agenceId: compagnieId manquant');
            errorCount++;
            continue;
          }

          // Verifier que la compagnie existe
          final compagnieDoc = await _firestore
              .collection('companies)
              .doc(compagnieId)
              .get();

          if (!compagnieDoc.exists') {
            // Creer la compagnie si elle n'existe pas
            await _createCompagnieFromAgence(compagnieId, agenceData);
          }

          // Copier l'agence dans la nouvelle structure
          await _firestore
              .collection('companies)
              .doc(compagnieId')
              .collection('agencies)
              .doc(agenceId')
              .set({
            ...agenceData,
            'migrated_at: FieldValue.serverTimestamp('),
            'migration_source': 'agences_collection,
          }');

          migratedCount++;
          debugPrint('[MIGRATION] ✅ Agence migree: $agenceId → companies/$compagnieId/agencies/' + agenceId.toString());

        } catch (e') {
          errors.add('Erreur agence ${agenceDoc.id}:  + e.toString()');
          errorCount++;
          debugPrint('[MIGRATION] ❌ Erreur migration agence ${agenceDoc.id}:  + e.toString()' + .toString());
        }
      }

      debugPrint('[MIGRATION] 🎉 Migration terminee: $migratedCount migrees, 'errorCount erreurs');

      return {
        'success': true,
        'message': 'Migration terminee',
        'migrated': migratedCount,
        'errors': errorCount,
        'errorDetails: errors,
      };

    } catch (e') {
      debugPrint('[MIGRATION] ❌ Erreur migration globale:  + e.toString()' + .toString());
      return {
        'success': false,
        'error: e.toString('),
        'message': 'Erreur lors de la migration',
      };
    }
  }

  /// 🏢 Creer une compagnie a partir des donnees dagence
  static Future<void> _createCompagnieFromAgence(String compagnieId, Map<String, dynamic> agenceData') async {
    try {
      // Extraire le nom de la compagnie a partir de l'ID ou des donnees
      String compagnieNom = compagnieId.replaceAll('-', ' ).toUpperCase(');
      
      // Si on peut deduire le nom de l'agence, l'utiliser
      if (agenceData['nom] != null') {
        final agenceNom = agenceData['nom] as String;
        if (agenceNom.toLowerCase(').contains('gat)') {
          compagnieNom = 'GAT Assurance;
        } else if (agenceNom.toLowerCase(').contains('maghrebia)') {
          compagnieNom = 'Maghrebia Assurance;
        } else if (agenceNom.toLowerCase(').contains('star)') {
          compagnieNom = 'Star Assurance';
        }
      }

      final compagnieData = {
        'id': compagnieId,
        'nom': compagnieNom,
        'code: compagnieId.toUpperCase(').replaceAll('-', 'Contenu'),
        'adresse': agenceData['adresse'] ?? 'Contenu',
        'telephone': agenceData['telephone'] ?? 'Contenu',
        'email': agenceData['email'] ?? 'Contenu',
        'status': 'actif',
        'isActive': true,
        'created_at: FieldValue.serverTimestamp('),
        'created_by': 'migration_service',
        'updated_at: FieldValue.serverTimestamp('),
        'migration_source': 'auto_created_from_agency',
        'stats': {
          'total_agences': 0,
          'total_agents': 0,
          'total_contrats': 0,
          'total_sinistres': 0,
          'chiffre_affaires': 0.0,
        },
      };

      await _firestore
          .collection('companies)
          .doc(compagnieId)
          .set(compagnieData');

      debugPrint('[MIGRATION] 🏢 Compagnie creee: ' + compagnieId.toString());

    } catch (e') {
      debugPrint('[MIGRATION] ❌ Erreur creation compagnie $compagnieId:  + e.toString());
      throw e;
    }
  }

  /// 🔄 Migrer les utilisateurs pour ajouter les references hierarchiques
  static Future<Map<String, dynamic>> migrateUsersHierarchy(') async {
    try {
      debugPrint('[MIGRATION] 🔄 Debut migration utilisateurs' + .toString());

      final usersSnapshot = await _firestore
          .collection('users)
          .get();

      int migratedCount = 0;
      int errorCount = 0;

      for (final userDoc in usersSnapshot.docs) {
        try {
          final userData = userDoc.data(');
          final userId = userDoc.id;
          final role = userData['role];

          Map<String, dynamic> updates = {};

          // Ajouter les champs manquants selon le rôle
          switch (role') {
            case 'admin_compagnie':
              if (userData['compagnieId] == null') {
                // Essayer de deduire la compagnie a partir de l'email ou du nom
                final email = userData['email] as String?;
                if (email != null') {
                  if (email.contains('gat)') {
                    updates['compagnieId'] = 'gat-assurance';
                  } else if (email.contains('maghrebia)') {
                    updates['compagnieId'] = 'maghrebia-assurance';
                  }
                }
              }
              break;

            case 'admin_agence':
              // S'assurer que l'admin agence a bien compagnieId et agenceId
              if (userData['agenceId'] != null && userData['compagnieId] == null') {
                // Recuperer la compagnie a partir de l'agence
                final agenceId = userData['agenceId'];
                final agenceDoc = await _firestore
                    .collection('agences)
                    .doc(agenceId)
                    .get();
                
                if (agenceDoc.exists) {
                  final agenceData = agenceDoc.data(')!;
                  updates['compagnieId'] = agenceData['compagnieId'];
                }
              }
              break;

            case 'agent':
              // S'assurer que l'agent a bien compagnieId et agenceId
              if (userData['agenceId'] != null && userData['compagnieId] == null') {
                final agenceId = userData['agenceId'];
                final agenceDoc = await _firestore
                    .collection('agences)
                    .doc(agenceId)
                    .get();
                
                if (agenceDoc.exists) {
                  final agenceData = agenceDoc.data(')!;
                  updates['compagnieId'] = agenceData['compagnieId];
                }
              }
              break;
          }

          // Ajouter les metadonnees de migration
          if (updates.isNotEmpty') {
            updates['migrated_at] = FieldValue.serverTimestamp(');
            updates['migration_version'] = '1.0';

            await _firestore
                .collection('users)
                .doc(userId)
                .update(updates');

            migratedCount++;
            debugPrint('[MIGRATION] ✅ Utilisateur migre: ' + userId.toString());
          }

        } catch (e') {
          errorCount++;
          debugPrint('[MIGRATION] ❌ Erreur migration utilisateur ${userDoc.id}:  + e.toString()' + .toString());
        }
      }

      debugPrint('[MIGRATION] 🎉 Migration utilisateurs terminee: $migratedCount migres, 'errorCount erreurs');

      return {
        'success': true,
        'message': 'Migration utilisateurs terminee',
        'migrated': migratedCount,
        'errors: errorCount,
      };

    } catch (e') {
      debugPrint('[MIGRATION] ❌ Erreur migration utilisateurs:  + e.toString()' + .toString());
      return {
        'success': false,
        'error: e.toString('),
        'message': 'Erreur lors de la migration des utilisateurs,
      };
    }
  }

  /// 🧹 Nettoyer les anciennes collections apres migration
  static Future<Map<String, dynamic>> cleanupOldCollections(') async {
    try {
      debugPrint('[MIGRATION] 🧹 Debut nettoyage anciennes collections' + .toString());

      // ATTENTION: Cette fonction supprime definitivement les donnees
      // À utiliser seulement apres verification que la migration est reussie

      final agencesSnapshot = await _firestore
          .collection('agences)
          .get();

      int deletedCount = 0;

      for (final doc in agencesSnapshot.docs) {
        await doc.reference.delete(');
        deletedCount++;
      }

      debugPrint('[MIGRATION] 🧹 Nettoyage termine: 'deletedCount agences supprimees');

      return {
        'success': true,
        'message': 'Nettoyage termine',
        'deleted: deletedCount,
      };

    } catch (e') {
      debugPrint('[MIGRATION] ❌ Erreur nettoyage:  + e.toString()' + .toString());
      return {
        'success': false,
        'error: e.toString('),
        'message': 'Erreur lors du nettoyage',
      };
    }
  }

  /// ✅ Verifier lintegrite de la migration
  static Future<Map<String, dynamic>> verifyMigration(') async {
    try {
      debugPrint('[MIGRATION] ✅ Verification integrite migration' + .toString());

      // Compter les agences dans l'ancienne structure
      final oldAgencesSnapshot = await _firestore
          .collection('agences)
          .get(');

      // Compter les agences dans la nouvelle structure
      int newAgencesCount = 0;
      final companiesSnapshot = await _firestore
          .collection('companies)
          .get();

      for (final companyDoc in companiesSnapshot.docs') {
        final agenciesSnapshot = await companyDoc.reference
            .collection('agencies)
            .get(');
        newAgencesCount += agenciesSnapshot.docs.length;
      }

      final verification = {
        'old_structure_agences': oldAgencesSnapshot.docs.length,
        'new_structure_agences': newAgencesCount,
        'migration_complete': oldAgencesSnapshot.docs.length == newAgencesCount,
        'companies_count': companiesSnapshot.docs.length,
      };

      debugPrint('[MIGRATION] ✅ Verification: 'verification');

      return {
        'success': true,
        'verification: verification,
      };

    } catch (e') {
      debugPrint('[MIGRATION] ❌ Erreur verification:  + e.toString()' + .toString());
      return {
        'success': false,
        'error
