import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../shared/models/compagnie_assurance_model.dart';
import '../../../../shared/models/agence_assurance_model.dart';
import '../providers/compagnie_agence_provider.dart';

/// 🏢 Widget dropdown pour sélectionner une compagnie d'assurance
class CompagnieDropdown extends ConsumerWidget {
  final String? selectedCompagnieId;
  final ValueChanged<CompagnieAssuranceModel?> onChanged;
  final String? hintText;
  final bool enabled;

   ) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final compagniesAsync = ref.watch(compagniesProvider);
    final state = ref.watch(compagnieAgenceProvider);

    return compagniesAsync.when(
      data: (compagnies) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: DropdownButtonFormField<CompagnieAssuranceModel>(
            value: selectedCompagnieId != null 
                ? compagnies.where((c) => c.id == selectedCompagnieId).firstOrNull
                : null,
            decoration: InputDecoration(
              labelText: 'Compagnie d\'assurance *',
              hintText: hintText ?? 'Sélectionnez une compagnie',
              prefixIcon: ,
              border: InputBorder.none,
              contentPadding: ,
            items: compagnies.map((compagnie) {
              return DropdownMenuItem<CompagnieAssuranceModel>(
                value: compagnie,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ,
                    ),
                    ({compagnie.ville}',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
            onChanged: enabled ? (compagnie) {
              ref.read(compagnieAgenceProvider.notifier).selectCompagnie(compagnie);
              onChanged(compagnie);
            } : null,
            validator: (value) {
              if (value == null) {
                return 'Veuillez sélectionner une compagnie';
              }
              return null;
            },
            isExpanded: true,
            icon: const Icon(Icons.info),
          ),
        );
      },
      loading: () => Container(
        height: 60,
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: ,
              ),
              const SizedBox(width: 12),
              const Text('Chargement des compagnies...'),
            ],
          ),
        ),
      ),
      error: (error, stack) => Container(
        height: 60,
        decoration: BoxDecoration(
          color: Colors.red.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.red.shade300),
        ),
        child: (error',
            style: TextStyle(color: Colors.red.shade700),
          ),
        ),
      ),
    );
  }
}

/// 🏬 Widget dropdown pour sélectionner une agence
class AgenceDropdown extends ConsumerWidget {
  final String? selectedAgenceId;
  final ValueChanged<AgenceAssuranceModel?> onChanged;
  final String? hintText;
  final bool enabled;
  final bool required;

   ) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(compagnieAgenceProvider);

    if (state.selectedCompagnie == null) {
      return Container(
        height: 60,
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: ,
          ),
        ),
      );
    }

    if (state.isLoadingAgences) {
      return Container(
        height: 60,
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: ,
              ),
              const SizedBox(width: 12),
              const Text('Chargement des agences...'),
            ],
          ),
        ),
      );
    }

    if (state.agences.isEmpty) {
      return Container(
        height: 60,
        decoration: BoxDecoration(
          color: Colors.orange.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.orange.shade300),
        ),
        child: ,
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: DropdownButtonFormField<AgenceAssuranceModel>(
        value: selectedAgenceId != null 
            ? state.agences.where((a) => a.id == selectedAgenceId).firstOrNull
            : null,
        decoration: InputDecoration(
          labelText: required ? 'Agence *' : 'Agence',
          hintText: hintText ?? 'Sélectionnez une agence',
          prefixIcon: ,
          border: InputBorder.none,
          contentPadding: ,
        items: state.agences.map((agence) {
          return DropdownMenuItem<AgenceAssuranceModel>(
            value: agence,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                ,
                ),
                ({agence.gouvernorat}',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          );
        }).toList(),
        onChanged: enabled ? (agence) {
          ref.read(compagnieAgenceProvider.notifier).selectAgence(agence);
          onChanged(agence);
        } : null,
        validator: required ? (value) {
          if (value == null) {
            return 'Veuillez sélectionner une agence
