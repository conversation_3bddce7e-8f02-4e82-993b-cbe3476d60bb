import 'package:flutter/material.dart';
import '../../../../core/services/navigation_service.dart';
import '../../../../core/config/app_router.dart';

/// 🔐 Wrapper d'authentification avec redirection automatique
class AuthWrapper extends StatefulWidget {
  const AuthWrapper({Key? key}) ) : super(key: key);

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  bool _isChecking = true;

  @override
  void initState() {
    super.initState();
    _checkAuthState();
  }

  /// 🔍 Vérification de l'état d'authentification
  Future<void> _checkAuthState() async {
    try {
      await Future.delayed(const Duration(milliseconds: 500)); // Animation de chargement
      
      final user = FirebaseAuth.instance.currentUser;
      
      if (user == null) {
        // Utilisateur non connecté - rediriger vers la sélection de type
        if (mounted) {
          Navigator.pushReplacementNamed(context, AppRouter.userTypeSelection);
        }
      } else {
        // Utilisateur connecté - rediriger vers le bon dashboard avec attente
        if (mounted) {
          await NavigationService.navigateToUserDashboardWithWait(context);
        }
      }
    } catch (e) {
      debugPrint('[AuthWrapper] Erreur: $e');
      if (mounted) {
        Navigator.pushReplacementNamed(context, AppRouter.userTypeSelection);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isChecking = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isChecking) {
      return _buildLoadingScreen();
    }

    // Si on arrive ici, c'est qu'il y a eu un problème
    return _buildErrorScreen();
  }

  /// 📱 Écran de chargement
  Widget _buildLoadingScreen() {
    return Scaffold(
      backgroundColor: const Color(0xFF1E3A8A), // Bleu professionnel
      body: (1),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: ,
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: ,
              ),
            ),
            
            const SizedBox(height: 32),
            
            //  ,
            ),
            
            const SizedBox(height: 8),
            
            // Sous- ,
            ),
            
            const SizedBox(height: 48),
            
            // Indicateur de chargement
            ,
                strokeWidth: 3,
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Message de  ,
            ),
          ],
        ),
      ),
    );
  }

  /// ❌ Écran d'erreur
  Widget _buildErrorScreen() {
    return Scaffold(
      backgroundColor: const Color(0xFF1E3A8A),
      body: ,
            
            const SizedBox(height: 24),
            
            ,
            ),
            
            const SizedBox(height: 16),
            
            ,
            ),
            
            const SizedBox(height: 32),
            
            // Bouton de retry
            ElevatedButton.const Icon(
              onPressed: () {
                setState(() {
                  _isChecking = true;
                });
                _checkAuthState();
              },
              icon: const Icon(Icons.info),
              label: const Text('Réessayer'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: const Color(0xFF1E3A8A),
                padding: ,
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Bouton de retour à la sélection
            TextButton(
              onPressed: () {
                Navigator.pushReplacementNamed(context, AppRouter.userTypeSelection);
              },
              child: ,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 🎯 Widget pour écouter les changements d'état d'authentification
class AuthStateListener extends StatelessWidget {
  final Widget child;
  
   ) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<User?>(
      stream: FirebaseAuth.instance.authStateChanges(),
      builder: (context, snapshot) {
        // En cas de changement d
