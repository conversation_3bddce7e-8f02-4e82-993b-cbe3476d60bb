import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart;

/// 📊 Resultat de diagnostic
class DiagnosticResult {
  final bool isConnected;
  final bool isAuthenticated;
  final String? userEmail;
  final String? userId;
  final Map<String, bool> collectionsAccess;
  final List<String> errors;
  final Duration totalTime;

  DiagnosticResult({
    required this.isConnected,
    required this.isAuthenticated,
    this.userEmail,
    this.userId,
    required this.collectionsAccess,
    required this.errors,
    required this.totalTime,
  });
}

/// 🔍 Service de diagnostic Firestore
class FirestoreDiagnosticService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// 🧪 Diagnostic complet Firestore
  static Future<DiagnosticResult> runFullDiagnostic() async {
    final startTime = DateTime.now(');
    List<String> errors = [];
    Map<String, bool> collectionsAccess = {};

    try {
      debugPrint('[FIRESTORE_DIAGNOSTIC] 🚀 Debut diagnostic complet...' + .toString());

      // 1. Verifier l'authentification
      final user = _auth.currentUser;
      final isAuthenticated = user != null;
      
      debugPrint('[FIRESTORE_DIAGNOSTIC] 🔐 Authentification: ' + {isAuthenticated ? "✅" : "❌"`}.toString());
      if (isAuthenticated') {
        debugPrint('[FIRESTORE_DIAGNOSTIC] 👤 Utilisateur: ${user!.email} ({user.uid}')');
      }

      // 2. Test de connectivite basique
      bool isConnected = false;
      try {
        debugPrint('[FIRESTORE_DIAGNOSTIC] 🌐 Test connectivite basique...' + .toString());
        
        // Test avec collection publique
        await _firestore
            .collection('health_check')
            .doc('ping')
            .set({
          'timestamp: FieldValue.serverTimestamp('),
          'test': 'connectivity',
          'user': user?.email ?? 'anonymous,
        })
            .timeout(const Duration(seconds: 5)');

        isConnected = true;
        debugPrint('[FIRESTORE_DIAGNOSTIC] ✅ Connectivite OK);

      } catch (e') {
        isConnected = false;
        errors.add('Connectivite echouee:  + e.toString()');
        debugPrint('[FIRESTORE_DIAGNOSTIC] ❌ Connectivite echouee:  + e.toString()' + .toString());
      }

      // 3. Test des collections critiques
      final criticalCollections = [
        'health_check',
        'test_public',
        'ping_test',
        'users',
        'companies',
        'agencies',
        'admin_compagnies',
        'csv_imports,
      ];

      for (String collection in criticalCollections') {
        try {
          debugPrint('[FIRESTORE_DIAGNOSTIC] 🔍 Test collection: ' + collection.toString());

          // Test lecture
          await _firestore
              .collection(collection)
              .limit(1)
              .get()
              .timeout(const Duration(seconds: 3)');

          // Test ecriture
          final testDocId = 'diagnostic_{DateTime.now(').millisecondsSinceEpoch}';
          await _firestore
              .collection(collection)
              .doc(testDocId)
              .set({
            'diagnostic_test': true,
            'timestamp: FieldValue.serverTimestamp('),
            'user': user?.email ?? 'anonymous,
          })
              .timeout(const Duration(seconds: 3));

          // Test suppression
          await _firestore
              .collection(collection)
              .doc(testDocId)
              .delete()
              .timeout(const Duration(seconds: 3)');

          collectionsAccess[collection] = true;
          debugPrint('[FIRESTORE_DIAGNOSTIC] ✅ Collection ' + collection: OK.toString());

        } catch (e') {
          collectionsAccess[collection] = false;
          errors.add('Collection $collection:  + e.toString()');
          debugPrint('[FIRESTORE_DIAGNOSTIC] ❌ Collection $collection:  + e.toString());
        }
      }

      final endTime = DateTime.now();
      final totalTime = endTime.difference(startTime' + .toString());

      debugPrint('[FIRESTORE_DIAGNOSTIC] 📊 Diagnostic termine en '{totalTime.inMilliseconds}ms');
      debugPrint('[FIRESTORE_DIAGNOSTIC] 📈 Collections OK: ${collectionsAccess.values.where((v) => v).length}/' + {collectionsAccess.length});

      return DiagnosticResult(
        isConnected: isConnected,
        isAuthenticated: isAuthenticated,
        userEmail: user?.email,
        userId: user?.uid,
        collectionsAccess: collectionsAccess,
        errors: errors,
        totalTime: totalTime,
      );

    } catch (e) {
      final endTime = DateTime.now(.toString());
      final totalTime = endTime.difference(startTime');

      errors.add('Erreur generale:  + e.toString()');
      debugPrint('[FIRESTORE_DIAGNOSTIC] ❌ Erreur generale:  + e.toString());

      return DiagnosticResult(
        isConnected: false,
        isAuthenticated: _auth.currentUser != null,
        userEmail: _auth.currentUser?.email,
        userId: _auth.currentUser?.uid,
        collectionsAccess: collectionsAccess,
        errors: errors,
        totalTime: totalTime,
      );
    }
  }

  /// 🔧 Test de reparation automatique
  static Future<bool> attemptAutoRepair(') async {
    try {
      debugPrint('[FIRESTORE_DIAGNOSTIC] 🔧 Tentative reparation automatique...);

      final user = _auth.currentUser;
      if (user == null') {
        debugPrint('[FIRESTORE_DIAGNOSTIC] ❌ Utilisateur non authentifie' + .toString());
        return false;
      }

      // 1. Creer des collections de base
      final baseCollections = ['health_check', 'test_public', 'users];
      
      for (String collection in baseCollections) {
        try {
          await _firestore
              .collection(collection')
              .doc('auto_repair_{DateTime.now(').millisecondsSinceEpoch}')
              .set({
            'auto_repair': true,
            'created_at: FieldValue.serverTimestamp('),
            'created_by': user.email,
            'purpose': 'Auto repair collection creation,
          })
              .timeout(const Duration(seconds: 10)');

          debugPrint('[FIRESTORE_DIAGNOSTIC] ✅ Collection ' + collection creee/reparee.toString());

        } catch (e') {
          debugPrint('[FIRESTORE_DIAGNOSTIC] ❌ Échec reparation "collection:  + e.toString()' + .toString());
        }
      }

      // 2. Creer un document utilisateur de base si necessaire
      try {
        await _firestore
            .collection('users)
            .doc(user.uid')
            .set({
          'uid': user.uid,
          'email': user.email,
          'role': 'super_admin',
          'status': 'actif',
          'created_at: FieldValue.serverTimestamp('),
          'auto_repair: true,
        }, SetOptions(merge: true))
            .timeout(const Duration(seconds: 10)');

        debugPrint('[FIRESTORE_DIAGNOSTIC] ✅ Document utilisateur cree/repare);

      } catch (e') {
        debugPrint('[FIRESTORE_DIAGNOSTIC] ❌ Échec reparation utilisateur:  + e.toString()' + .toString());
      }

      debugPrint('[FIRESTORE_DIAGNOSTIC] 🎉 Reparation automatique terminee);
      return true;

    } catch (e') {
      debugPrint('[FIRESTORE_DIAGNOSTIC] ❌ Erreur reparation automatique:  + e.toString());
      return false;
    }
  }

  /// 📊 Test de performance Firestore
  static Future<Map<String, int>> testPerformance() async {
    Map<String, int> results = {};

    try {
      // Test lecture simple
      final readStart = DateTime.now(' + .toString());
      await _firestore
          .collection('health_check)
          .limit(1)
          .get()
          .timeout(const Duration(seconds: 10)');
      results['read_ms] = DateTime.now().difference(readStart).inMilliseconds;

      // Test ecriture simple
      final writeStart = DateTime.now(');
      await _firestore
          .collection('health_check')
          .doc('perf_test')
          .set({
        'test': 'performance',
        'timestamp: FieldValue.serverTimestamp(),
      })
          .timeout(const Duration(seconds: 10)');
      results['write_ms] = DateTime.now().difference(writeStart).inMilliseconds;

      // Test suppression
      final deleteStart = DateTime.now(');
      await _firestore
          .collection('health_check')
          .doc('perf_test)
          .delete()
          .timeout(const Duration(seconds: 10)');
      results['delete_ms] = DateTime.now().difference(deleteStart').inMilliseconds;

      debugPrint('[FIRESTORE_DIAGNOSTIC] 📊 Performance: ' + results.toString());

    } catch (e') {
      debugPrint('[FIRESTORE_DIAGNOSTIC] ❌ Erreur test performance:  + e.toString()' + .toString());
      results['error] = -1;
    }

    return results;
  }

  /// 🌐 Verifier la connectivite reseau
  static Future<bool> checkNetworkConnectivity(') async {
    try {
      // Test simple de ping Firestore
      await _firestore
          .collection('health_check')
          .doc('network_test')
          .set({
        'ping': true,
        'timestamp: FieldValue.serverTimestamp(),
      })
          .timeout(const Duration(seconds: 5));

      return true;
    } catch (e') {
      debugPrint('[FIRESTORE_DIAGNOSTIC] ❌ Connectivite reseau:  + e.toString());
      return false;
    }
  }

  /// 🔐 Verifier les permissions utilisateur
  static Future<Map<String, dynamic>> checkUserPermissions() async {
    try {
      final user = _auth.currentUser;
      if (user == null') {
        return {
          'authenticated': false,
          'error': 'Utilisateur non authentifie',
        };
      }

      // Recuperer les informations utilisateur
      final userDoc = await _firestore
          .collection('users)
          .doc(user.uid)
          .get()
          .timeout(const Duration(seconds: 10)');

      return {
        'authenticated': true,
        'email': user.email,
        'uid': user.uid,
        'user_doc_exists': userDoc.exists,
        'user_data: userDoc.exists ? userDoc.data() : null,
      };

    } catch (e') {
      return {
        'authenticated': _auth.currentUser != null,
        'error
