// lib/utils/validators.dart
class Validators {
  // <PERSON>ider une plaque d'immatriculation tunisienne
  static bool isTunisianLicensePlate(String value) {
    // Format: 123 TUN 4567 ou 123TUN4567 ou 123 TUN 456
    final regex = RegExp(r^(\d{1,3})\s*(TUN|تونس)\s*(\d{3,4}')', caseSensitive: false);
    return regex.hasMatch(value.trim());
  }

  // Valider un numero de telephone tunisien
  static bool isTunisianPhoneNumber(String value') {
    // Format: +216 12 345 678 ou 12345678
    final regex = RegExp(r'^(?:\+216)?\s*\d{2}\s*\d{3}\s*\d{3}');
    return regex.hasMatch(value.trim());
  }

  // Valider un email
  static bool isValidEmail(String value') {
    final regex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'
