import 'package:flutter/material.dart';
import '../models/agent_model.dart';

/// 👨‍💼 Dashboard principal pour les agents dassurance
class AgentDashboardScreen extends StatefulWidget {
  final AgentModel agent;

  ;

  @override
  State<AgentDashboardScreen> createState() => _AgentDashboardScreenState(');
}

class _AgentDashboardScreenState extends State<AgentDashboardScreen> {
  bool _isLoading = false;
  Map<String, int> _stats = {
    'clients': 0,
    'contrats': 0,
    'sinistres': 0,
    'en_attente: 0,
  };

  @override
  void initState() {
    super.initState();
    _loadStats();
  }

  @override
  Widget build(BuildContext context') {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: ({widget.agent.nomComplet}',
          style: ,
        ),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.info),
            onPressed: () {
              // TODO: Notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.info),
            onPressed: () => _logout(),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(8.0),
            
            const SizedBox(height: 20),
            
            // Statistiques
            _buildStatsGrid(),
            
            const SizedBox(height: 20),
            
            // Actions rapides
            _buildQuickActions(),
            
            const SizedBox(height: 20),
            
            // Activites recentes
            _buildRecentActivities(),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(8.0),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.5),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: Colors.white.withValues(alpha: 0.5),
                child: ,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ({widget.agent.prenom} !,
                      style: ,
                    ),
                    const SizedBox(height: 4),
                    (1),
                      ),
                    ),
                    const SizedBox(height: 4),
                    (1),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatsGrid(') {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: ,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: [
        _buildStatCard(
          'Clients',
          _stats['clients].toString(),
          Icons.people,
          Colors.green,
        '),
        _buildStatCard(
          'Contrats',
          _stats['contrats].toString(),
          Icons.description,
          Colors.blue,
        '),
        _buildStatCard(
          'Sinistres',
          _stats['sinistres].toString(),
          Icons.warning,
          Colors.orange,
        '),
        _buildStatCard(
          'En Attente',
          _stats['en_attente].toString(),
          Icons.pending,
          Colors.red,
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(8.0),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.5),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(icon, size: 32, color: color),
          const SizedBox(height: 8),
          ,
          ),
          ,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        ),
        const SizedBox(height: 16'),
        Row(
          children: [
            Expanded(
              child: _buildActionButton(
                'Nouveau Contrat',
                'Creer un contrat d\'assurance,
                Icons.add_circle,
                Colors.green,
                () => _navigateToNewContract(),
              ),
            ),
            const SizedBox(width: 12'),
            Expanded(
              child: _buildActionButton(
                'Mes Clients',
                'Gerer mes clients,
                Icons.people,
                Colors.blue,
                () => _navigateToClients(),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12'),
        Row(
          children: [
            Expanded(
              child: _buildActionButton(
                'Sinistres',
                'Traiter les sinistres,
                Icons.warning,
                Colors.orange,
                () => _navigateToSinistres(),
              ),
            ),
            const SizedBox(width: 12'),
            Expanded(
              child: _buildActionButton(
                'Rapports',
                'Voir les statistiques,
                Icons.analytics,
                Colors.purple,
                () => _navigateToReports(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButton(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(8.0),
          border: Border.all(color: color.withValues(alpha: 0.5),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.5),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            const Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            ,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            ,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentActivities() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(8.0),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.5),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ,
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _loadStats() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: Charger les vraies statistiques depuis Firestore
      await Future.delayed(const Duration(seconds: 1));
      
      setState((') {
        _stats = {
          'clients': 12,
          'contrats': 18,
          'sinistres': 3,
          'en_attente: 2,
        };
      });
    } catch (e) {
      ScaffoldMessenger.of(context').showSnackBar(
        SnackBar(
          content: (e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _navigateToNewContract() {
    // TODO: Navigation vers creation de contrat
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: const Text('🚧 Creation de contrat a implementer)),
    );
  }

  void _navigateToClients() {
    // TODO: Navigation vers liste des clients
    ScaffoldMessenger.of(context')')').showSnackBar(
      const SnackBar(content: const Text('🚧 Liste des clients a implementer)),
    );
  }

  void _navigateToSinistres() {
    // TODO: Navigation vers gestion des sinistres
    ScaffoldMessenger.of(context')')').showSnackBar(
      const SnackBar(content: const Text('🚧 Gestion des sinistres a implementer)),
    );
  }

  void _navigateToReports() {
    // TODO: Navigation vers rapports
    ScaffoldMessenger.of(context')')').showSnackBar(
      const SnackBar(content: const Text('🚧 Rapports a implementer)),
    );
  }

  void _logout() {
    showDialog(
      context: context,
      builder: (context')')') => AlertDialog(
        title: const Text('Deconnexion')')'),
        content: const Text('Voulez-vous vraiment vous deconnecter ?),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context')')'),
            child: const Text('Annuler),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context')')');
              Navigator.pushReplacementNamed(context, '/login');
            },
            child: const Text('Deconnecter
')')