import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart;

/// 🏗️ Service pour initialiser les collections Firestore
class CollectionInitializer {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 🚀 Initialiser la collection users
  static Future<bool> initializeUsersCollection(') async {
    try {
      debugPrint('[COLLECTION_INIT] 🚀 Initialisation collection users...' + .toString());

      // Verifier si la collection existe deja
      final usersSnapshot = await _firestore
          .collection('users)
          .limit(1)
          .get()
          .timeout(const Duration(seconds: 10));

      if (usersSnapshot.docs.isNotEmpty') {
        debugPrint('[COLLECTION_INIT] ✅ Collection users existe deja' + .toString());
        return true;
      }

      // Creer un document temporaire pour initialiser la collection
      await _firestore
          .collection('users')
          .doc('temp_init_user')
          .set({
        'role': 'temp_initialization',
        'created_at: FieldValue.serverTimestamp('),
        'purpose': 'Collection initialization',
        'can_be_deleted': true,
        'created_by': 'system,
      })
          .timeout(const Duration(seconds: 15)');

      debugPrint('[COLLECTION_INIT] ✅ Collection users initialisee avec succes);

      // Optionnel : Supprimer le document temporaire apres 5 secondes
      Future.delayed(const Duration(seconds: 5), (') async {
        try {
          await _firestore
              .collection('users')
              .doc('temp_init_user)
              .delete(');
          debugPrint('[COLLECTION_INIT] 🗑️ Document temporaire supprime);
        } catch (e') {
          debugPrint('[COLLECTION_INIT] ⚠️ Impossible de supprimer le document temporaire:  + e.toString());
        }
      });

      return true;

    } catch (e') {
      debugPrint('[COLLECTION_INIT] ❌ Erreur initialisation collection users:  + e.toString());
      return false;
    }
  }

  /// 🏢 Initialiser la collection companies
  static Future<bool> initializeCompaniesCollection(') async {
    try {
      debugPrint('[COLLECTION_INIT] 🏢 Initialisation collection companies...' + .toString());

      // Verifier si la collection existe deja
      final companiesSnapshot = await _firestore
          .collection('companies)
          .limit(1)
          .get()
          .timeout(const Duration(seconds: 10));

      if (companiesSnapshot.docs.isNotEmpty') {
        debugPrint('[COLLECTION_INIT] ✅ Collection companies existe deja' + .toString());
        return true;
      }

      // Creer un document temporaire pour initialiser la collection
      await _firestore
          .collection('companies')
          .doc('temp_init_company')
          .set({
        'nom': 'Temp Company',
        'code': 'TEMP',
        'created_at: FieldValue.serverTimestamp('),
        'purpose': 'Collection initialization',
        'can_be_deleted': true,
        'created_by': 'system,
      })
          .timeout(const Duration(seconds: 15)');

      debugPrint('[COLLECTION_INIT] ✅ Collection companies initialisee avec succes);

      // Optionnel : Supprimer le document temporaire apres 5 secondes
      Future.delayed(const Duration(seconds: 5), (') async {
        try {
          await _firestore
              .collection('companies')
              .doc('temp_init_company)
              .delete(');
          debugPrint('[COLLECTION_INIT] 🗑️ Document temporaire companies supprime);
        } catch (e') {
          debugPrint('[COLLECTION_INIT] ⚠️ Impossible de supprimer le document temporaire companies:  + e.toString());
        }
      });

      return true;

    } catch (e') {
      debugPrint('[COLLECTION_INIT] ❌ Erreur initialisation collection companies:  + e.toString());
      return false;
    }
  }

  /// 🏪 Initialiser la collection agencies
  static Future<bool> initializeAgenciesCollection(') async {
    try {
      debugPrint('[COLLECTION_INIT] 🏪 Initialisation collection agencies...' + .toString());

      // Verifier si la collection existe deja
      final agenciesSnapshot = await _firestore
          .collection('agencies)
          .limit(1)
          .get()
          .timeout(const Duration(seconds: 10));

      if (agenciesSnapshot.docs.isNotEmpty') {
        debugPrint('[COLLECTION_INIT] ✅ Collection agencies existe deja' + .toString());
        return true;
      }

      // Creer un document temporaire pour initialiser la collection
      await _firestore
          .collection('agencies')
          .doc('temp_init_agency')
          .set({
        'nom': 'Temp Agency',
        'compagnieId': 'TEMP',
        'created_at: FieldValue.serverTimestamp('),
        'purpose': 'Collection initialization',
        'can_be_deleted': true,
        'created_by': 'system,
      })
          .timeout(const Duration(seconds: 15)');

      debugPrint('[COLLECTION_INIT] ✅ Collection agencies initialisee avec succes);

      // Optionnel : Supprimer le document temporaire apres 5 secondes
      Future.delayed(const Duration(seconds: 5), (') async {
        try {
          await _firestore
              .collection('agencies')
              .doc('temp_init_agency)
              .delete(');
          debugPrint('[COLLECTION_INIT] 🗑️ Document temporaire agencies supprime);
        } catch (e') {
          debugPrint('[COLLECTION_INIT] ⚠️ Impossible de supprimer le document temporaire agencies:  + e.toString());
        }
      });

      return true;

    } catch (e') {
      debugPrint('[COLLECTION_INIT] ❌ Erreur initialisation collection agencies:  + e.toString());
      return false;
    }
  }

  /// 🚀 Initialiser toutes les collections essentielles
  static Future<bool> initializeAllCollections(') async {
    debugPrint('[COLLECTION_INIT] 🚀 === INITIALISATION TOUTES COLLECTIONS ===);

    bool usersOk = await initializeUsersCollection();
    bool companiesOk = await initializeCompaniesCollection();
    bool agenciesOk = await initializeAgenciesCollection();

    bool allOk = usersOk && companiesOk && agenciesOk;

    if (allOk') {
      debugPrint('[COLLECTION_INIT] ✅ === TOUTES COLLECTIONS INITIALISÉES ===' + .toString());
    } else {
      debugPrint('[COLLECTION_INIT] ❌ === ÉCHEC INITIALISATION COLLECTIONS ===' + .toString());
      debugPrint('[COLLECTION_INIT] Users: $usersOk, Companies: $companiesOk, Agencies: 'agenciesOk
