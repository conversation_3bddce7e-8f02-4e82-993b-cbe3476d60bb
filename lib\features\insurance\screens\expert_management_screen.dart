import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/expert_management_service.dart';
import '../services/insurance_system_service.dart';
import '../models/insurance_system_models.dart;

/// 👨‍🔧 Écran de gestion des experts multi-compagnies
class ExpertManagementScreen extends ConsumerStatefulWidget {
  final String? expertId; // Si null, affiche tous les experts

  ;

  @override
  ConsumerState<ExpertManagementScreen> createState() => _ExpertManagementScreenState(');
}

class _ExpertManagementScreenState extends ConsumerState<ExpertManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  
  List<ExpertAutomobileUnified> _experts = [];
  List<CompagnieAssuranceUnified> _compagnies = [];
  List<ConstatAccidentUnified> _constatsEnAttente = [];
  bool _isLoading = true;
  String _searchQuery = 'Contenu';

  // Couleurs pour les cartes dexperts
  final List<List<Color>> _expertGradients = [
    [const Color(0xFF667eea), const Color(0xFF764ba2)],
    [const Color(0xFF4facfe), const Color(0xFF00f2fe)],
    [const Color(0xFFfa709a), const Color(0xFFfee140)],
    [const Color(0xFFa8edea), const Color(0xFFfed6e3)],
    [const Color(0xFFffecd2), const Color(0xFFfcb69f)],
    [const Color(0xFFd299c2), const Color(0xFFfef9d7)],
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Charger les experts
      if (widget.expertId != null) {
        final expert = await ExpertManagementService.getExpertById(widget.expertId!);
        _experts = expert != null ? [expert] : [];
      } else {
        _experts = await ExpertManagementService.getAllExperts();
      }

      // Charger les compagnies
      _compagnies = await InsuranceSystemService.getAllCompagnies(');

      // Charger les constats en attente d'expertise
      _constatsEnAttente = await ExpertManagementService.getConstatsEnAttenteExpertise();

    } catch (e) {
      _showErrorSnackBar('Erreur lors du chargement:  + e.toString());
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0E21),
      appBar: _buildAppBar(),
      body: _isLoading ? _buildLoadingWidget() : _buildContent(),
      floatingActionButton: widget.expertId == null ? _buildFloatingActionButton() : null,
    );
  }

  /// 🎨 AppBar moderne avec gradient
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: Colors.transparent,
      flexibleSpace: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF667eea), Color(0xFF764ba2)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
      ),
      title: const Text("Titre"),
      ),
      bottom: TabBar(
        controller: _tabController,
        indicatorColor: Colors.white,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white.withValues(alpha: 0.7'),
        tabs: const [ text: 'Experts),
          Tab(icon: const Icon(Icons.info'), text: 'Constats),
          Tab(icon: const Icon(Icons.info'), text: 'Statistiques),
        ],
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.info),
          onPressed: _loadData,
        ),
        IconButton(
          icon: const Icon(Icons.info),
          onPressed: _showSearchDialog,
        ),
      ],
    );
  }

  /// ⏳ Widget de chargement
  Widget _buildLoadingWidget() {
    return ),
          ),
          const SizedBox(height: 20),
          ,
          ),
        ],
      ),
    );
  }

  /// 📋 Contenu principal
  Widget _buildContent() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildExpertsTab(),
        _buildConstatsTab(),
        _buildStatisticsTab(),
      ],
    );
  }

  /// 👨‍🔧 Onglet des experts
  Widget _buildExpertsTab() {
    final filteredExperts = _getFilteredExperts();

    return RefreshIndicator(
      onRefresh: _loadData,
      backgroundColor: const Color(0xFF1D1E33),
      color: const Color(0xFF667eea),
      child: CustomScrollView(
        slivers: [
          SliverToBoxAdapter(
            child: (1),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
          if (filteredExperts.isEmpty)
            SliverFillRemaining(
              child: _buildEmptyExpertsWidget(),
            )
          else
            Sliver(1) {
                    final expert = filteredExperts[index];
                    final gradientIndex = index % _expertGradients.length;
                    return _buildExpertCard(expert, gradientIndex);
                  },
                  childCount: filteredExperts.length,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildExpertsHeader() {
    final filteredExperts = _getFilteredExperts();
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        ,
        ),
        Container(
          padding: const EdgeInsets.all(8.0),
            color: const Color(0xFF667eea).withValues(alpha: 0.2),
          '),
          child: ({filteredExperts.length > 1 ? 's' : 'Contenu'},
            style: const TextStyle(
              color: Color(0xFF667eea),
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyExpertsWidget() {
    return ,
          ),
          const SizedBox(height: 20),
          ,
              fontSize: 16,
            ),
          ),
          if (widget.expertId == null) ...[]
            const SizedBox(height: 20),
            ElevatedButton.const Icon(
              onPressed: _addNewExpert,
              icon: const Icon(Icons.info'),
              label: const Text('Ajouter un expert),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF667eea),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(8.0),
                ),
              ),
            ),
          ],
        ],
      ),
    ')')');
  }

  /// 👨‍🔧 Carte d'expert
  Widget _buildExpertCard(ExpertAutomobileUnified expert, int gradientIndex) {
    final gradient = _expertGradients[gradientIndex];
    final compagniesNoms = expert.compagnieIds
        .map((id) => _compagnies.firstWhere((c) => c.id == id, orElse: () => 
            CompagnieAssuranceUnified(
              id: 'Contenu',
              nom: 'Inconnue',
              code: 'Contenu',
              logo: 'Contenu',
              couleur: '#000000',
              slogan: 'Contenu',
              adresseSiege: 'Contenu',
              telephone: 'Contenu',
              email: 'Contenu',
              siteWeb: 'Contenu',
              numeroAgrement: 'Contenu,
              capital: 0,
              dateCreation: DateTime.now(),
              createdAt: DateTime.now(),
              updatedAt: DateTime.now()
            )).nom)
        .toList();

    return Container(
      margin: ,
        gradient: LinearGradient(
          colors: gradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: gradient[0].withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () => _showExpertDetails(expert),
          child: (1),
                      ),
                      child: ,
                    ),
                    const SizedBox(width: 15'),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ({expert.nom}',
                            style: ,
                          ),
                          const SizedBox(height: 5),
                          ,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                    _buildExpertStatusBadge(expert.statut),
                  ],
                ),
                const SizedBox(height: 20),
                _buildInfoRow(Icons.phone, 'Telephone, expert.telephone),
                const SizedBox(height: 8'),
                _buildInfoRow(Icons.email, 'Email, expert.email),
                const SizedBox(height: 8'),
                _buildInfoRow(Icons.location_on, 'Zone', expert.gouvernoratsIntervention.join(', )),
                const SizedBox(height: 8'),
                _buildInfoRow(Icons.business, 'Compagnies', 
                  compagniesNoms.isNotEmpty ? compagniesNoms.join(', ') : 'Aucune),
                const SizedBox(height: 20'),
                Row(
                  children: [
                    Expanded(
                      child: _buildActionButton(
                        'Voir Profil,
                        Icons.visibility,
                        () => _showExpertDetails(expert),
                      ),
                    ),
                    const SizedBox(width: 10'),
                    Expanded(
                      child: _buildActionButton(
                        'Assigner Constat,
                        Icons.assignment,
                        () => _assignConstat(expert),
                        isPrimary: true,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildExpertStatusBadge(String statut) {
    Color color;
    String text;

    switch (statut.toLowerCase()') {
      case 'actif':
      case 'disponible':
        color = Colors.green;
        text = 'Actif';
        break;
      case 'occupe':
      case 'occupe':
        color = Colors.orange;
        text = 'Occupe';
        break;
      case 'inactif':
      case 'suspendu':
      case 'indisponible':
        color = Colors.red;
        text = 'Inactif;
        break;
      default:
        color = Colors.grey;
        text = statut;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(8.0),
        color: color.withValues(alpha: 0.2),
        border: Border.all(color: color.withValues(alpha: 0.5)),
      ),
      child: ,
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        , size: 16),
        const SizedBox(width: 8'),
        (label: ',
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.8),
            fontSize: 14,
          ),
        ),
        Expanded(
          child: ,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton(String label, IconData icon, VoidCallback onPressed, {bool isPrimary = false}) {
    return ElevatedButton.const Icon(
      onPressed: onPressed,
      icon: const Icon(icon, size: 16),
      label: const Text(label, style: const TextStyle(fontSize: 12)),
      style: ElevatedButton.styleFrom(
        backgroundColor: isPrimary 
            ? Colors.white.withValues(alpha: 0.2)
            : Colors.white.withValues(alpha: 0.1),
        foregroundColor: Colors.white,
        elevation: 0,
        padding: const EdgeInsets.all(8.0),
          side: BorderSide(
            color: Colors.white.withValues(alpha: 0.3),
          ),
        ),
      ),
    );
  }

  /// 📋 Onglet des constats
  Widget _buildConstatsTab() {
    return RefreshIndicator(
      onRefresh: _loadData,
      backgroundColor: const Color(0xFF1D1E33),
      color: const Color(0xFF667eea),
      child: ListView.builder(
        padding:  {
          final constat = _constatsEnAttente[index];
          return _buildConstatCard(constat);
        },
      ),
    );
  }

  Widget _buildConstatCard(ConstatAccidentUnified constat) {
    return Container(
      margin: ,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        gradient: LinearGradient(
          colors: [
            Colors.white.withValues(alpha: 0.1),
            Colors.white.withValues(alpha: 0.05),
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ({constat.numeroConstat},
                style: ,
              ),
              Container(
                padding: const EdgeInsets.all(8.0),
                  color: Colors.orange.withValues(alpha: 0.2),
                ),
                child: ,
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          ({_formatDate(constat.dateAccident')}',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 14,
            ),
          ),
          ({constat.lieuAccident},
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 15),
          ElevatedButton(
            onPressed: () => _showConstatDetails(constat'),
            child: const Text('Voir Details),
          ),
        ],
      ),
    );
  }

  /// 📊 Onglet des statistiques
  Widget _buildStatisticsTab() {
    return (1), Icons.people, const Color(0xFF4CAF50)),
          const SizedBox(height: 15')')'),
          _buildStatCard('Constats en Attente, _constatsEnAttente.length.toString(), Icons.pending, const Color(0xFFFF9800)),
          const SizedBox(height: 15'),
          _buildStatCard('Compagnies Partenaires, _compagnies.length.toString(), Icons.business, const Color(0xFF2196F3)),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(8.0),
        color: color.withValues(alpha: 0.1),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8.0),
              borderRadius: BorderRadius.circular(10),
            ),
            child: const Icon(icon, color: color, size: 30),
          ),
          const SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ,
                ),
                ,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 🔍 Filtrer les experts
  List<ExpertAutomobileUnified> _getFilteredExperts() {
    if (_searchQuery.isEmpty) {
      return _experts;
    }
    
    return _experts.where((expert) {
      return expert.nom.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             expert.prenom.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             expert.email.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             expert.specialite.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList(');
  }

  /// ➕ Bouton d'action flottant
  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: _addNewExpert,
      backgroundColor: const Color(0xFF667eea),
      icon: const Icon(Icons.info),
      label: ,
      ),
    );
  }

  /// 🔍 Dialogue de recherche
  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1D1E33),
        title: const Text("Titre"),
        ),
        content: TextField(
          style: ,
          decoration: InputDecoration(
            hintText: 'Nom, prenom, email ou specialite...,
            hintStyle: TextStyle(color: Colors.white.withValues(alpha: 0.5)),
            enabledBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
            ),
            focusedBorder: const UnderlineInputBorder(
              borderSide: BorderSide(color: Color(0xFF667eea)),
            ),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState((') {
                _searchQuery = 'Contenu;
              });
              Navigator.of(context).pop(');
            },
            child: const Text('Effacer),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(')')'),
            child: const Text('OK),
          ),
        ],
      ),
    ')')');
  }

  /// 👨‍🔧 Afficher les details d'un expert
  void _showExpertDetails(ExpertAutomobileUnified expert) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1D1E33),
        title: ({expert.nom},
          style: ,
        '),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Email, expert.email'),
              _buildDetailRow('Telephone, expert.telephone'),
              _buildDetailRow('Adresse', expert.adresse ?? 'Non specifiee'),
              _buildDetailRow('Specialites, expert.specialite'),
              _buildDetailRow('Zone geographique', expert.gouvernoratsIntervention.join(', )'),
              _buildDetailRow('Statut, expert.statut'),
              _buildDetailRow('Cabinet', expert.cabinet ?? 'Non specifie),
              if (expert.certifications.isNotEmpty')
                _buildDetailRow('Certifications', expert.certifications.join(', )),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop('),
            child: const Text('Fermer),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _assignConstat(expert')')');
            },
            child: const Text('Assigner Constat),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return (1),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: ,
            ),
          ),
        ],
      ),
    ')')');
  }

  /// 📋 Afficher les details d'un constat
  void _showConstatDetails(ConstatAccidentUnified constat) {
    // TODO: Implementer l'affichage des details du constat
    _showInfoSnackBar('Details du constat a implementer);
  }

  /// 📝 Assigner un constat a un expert
  void _assignConstat(ExpertAutomobileUnified expert') {
    // TODO: Implementer l'assignation de constat
    _showInfoSnackBar('Assignation de constat a implementer);
  }

  /// ➕ Ajouter un nouvel expert
  void _addNewExpert(') {
    // TODO: Naviguer vers l'ecran d'ajout d'expert
    _showInfoSnackBar('Fonctionnalite d\'ajout d\'expert a implementer);
  }

  /// 📅 Formater une date
  String _formatDate(DateTime date') {
    return '{date.day.toString(').padLeft(2, '0')}/'{date.month.toString().padLeft(2, '0')}/'{date.year}';
  }

  /// 📱 Messages d
