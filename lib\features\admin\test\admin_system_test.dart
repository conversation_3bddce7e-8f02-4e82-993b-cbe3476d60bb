import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/admin_firestore_service.dart';

/// 🧪 Page de test pour vérifier le système admin
class AdminSystemTestPage extends ConsumerStatefulWidget {
  const Text(\;

  @override
  ConsumerState<AdminSystemTestPage> createState() => _AdminSystemTestPageState();
}

class _AdminSystemTestPageState extends ConsumerState<AdminSystemTestPage> {
  final List<String> _testResults = [];
  bool _isRunningTests = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🧪 Tests Système Admin'),
        backgroundColor: const Color(0xFF3B82F6),
        foregroundColor: Colors.white,
      ),
      body: (1),
            const SizedBox(height: 20),
            Expanded(child: _buildTestResults()),
          ],
        ),
      ),
    );
  }

  /// 🎮 Contrôles de test
  Widget _buildTestControls() {
    return Card(
      child: (1),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.const Icon(
                    onPressed: _isRunningTests ? null : _runAllTests,
                    icon: _isRunningTests 
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : ,
                    label: const Text(_isRunningTests ? 'Tests en cours...' : 'Lancer tous les tests'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF10B981),
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton.const Icon(
                  onPressed: _clearResults,
                  icon: const Icon(Icons.info),
                  label: const Text('Effacer'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF64748B),
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 📋 Résultats des tests
  Widget _buildTestResults() {
    return Card(
      child: (1),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: _testResults.isEmpty
                  ? ,
                      ),
                    )
                  : ListView.builder(
                      itemCount: _testResults.length,
                      itemBuilder: (context, index) {
                        final result = _testResults[index];
                        final isSuccess = result.startsWith('✅');
                        final isError = result.startsWith('❌');
                        
                        return Container(
                          margin: ,
                          decoration: BoxDecoration(
                            color: isSuccess 
                                ? Colors.green.withValues(alpha: 0.1)
                                : isError 
                                    ? Colors.red.withValues(alpha: 0.1)
                                    : Colors.blue.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: isSuccess 
                                  ? Colors.green
                                  : isError 
                                      ? Colors.red
                                      : Colors.blue,
                              width: 1,
                            ),
                          ),
                          child: ,
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  /// 🧪 Lancer tous les tests
  Future<void> _runAllTests() async {
    setState(() {
      _isRunningTests = true;
      _testResults.clear();
    });

    _addResult('🚀 Début des tests du système admin...');
    
    try {
      await _testCompagniesCollection();
      await _testAgencesCollection();
      await _testUsersCollection();
      await _testContractsCollection();
      await _testDataRelationships();
      await _testRoleBasedFiltering();
      
      _addResult('🎉 Tous les tests terminés avec succès !');
    } catch (e) {
      _addResult('❌ Erreur générale: $e');
    } finally {
      setState(() {
        _isRunningTests = false;
      });
    }
  }

  /// 🏢 Test de la collection compagnies
  Future<void> _testCompagniesCollection() async {
    _addResult('📋 Test: Collection compagnies_assurance...');
    
    try {
      final compagnies = await AdminFirestoreService.getAllCompagnies().first;
      _addResult('✅ Compagnies chargées: ${compagnies.length} trouvées');
      
      if (compagnies.isNotEmpty) {
        final firstCompagnie = compagnies.first;
        _addResult('✅ Première compagnie: ${firstCompagnie.nom} (ID: ${firstCompagnie.id})');
      }
    } catch (e) {
      _addResult('❌ Erreur compagnies: $e');
    }
  }

  /// 🏪 Test de la collection agences
  Future<void> _testAgencesCollection() async {
    _addResult('📋 Test: Collection agences_assurance...');
    
    try {
      final agences = await AdminFirestoreService.getAllAgences().first;
      _addResult('✅ Agences chargées: ${agences.length} trouvées');
      
      if (agences.isNotEmpty) {
        final firstAgence = agences.first;
        _addResult('✅ Première agence: ${firstAgence.nom} (Compagnie: ${firstAgence.compagnieId})');
      }
    } catch (e) {
      _addResult('❌ Erreur agences: $e');
    }
  }

  /// 👥 Test de la collection utilisateurs
  Future<void> _testUsersCollection() async {
    _addResult('📋 Test: Collection users...');
    
    try {
      final users = await AdminFirestoreService.getAllUsers().first;
      _addResult('✅ Utilisateurs chargés: ${users.length} trouvés');
      
      // Compter par rôle
      final roleCount = <String, int>{};
      for (final user in users) {
        roleCount[user.role] = (roleCount[user.role] ?? 0) + 1;
      }
      
      roleCount.forEach((role, count) {
        _addResult('✅ Rôle $role: $count utilisateurs');
      });
    } catch (e) {
      _addResult('❌ Erreur utilisateurs: $e');
    }
  }

  /// 📋 Test de la collection contrats
  Future<void> _testContractsCollection() async {
    _addResult('📋 Test: Collection contrats_assurance...');
    
    try {
      final contrats = await AdminFirestoreService.getAllContracts().first;
      _addResult('✅ Contrats chargés: ${contrats.length} trouvés');
      
      if (contrats.isNotEmpty) {
        final firstContrat = contrats.first;
        _addResult('✅ Premier contrat: ${firstContrat.numeroContrat} (Agence: ${firstContrat.agenceId})');
      }
    } catch (e) {
      _addResult('❌ Erreur contrats: $e');
    }
  }

  /// 🔗 Test des relations entre données
  Future<void> _testDataRelationships() async {
    _addResult('📋 Test: Relations entre les données...');
    
    try {
      final compagnies = await AdminFirestoreService.getAllCompagnies().first;
      final agences = await AdminFirestoreService.getAllAgences().first;
      
      if (compagnies.isNotEmpty && agences.isNotEmpty) {
        final firstCompagnie = compagnies.first;
        final agencesDeCompagnie = agences.where((a) => a.compagnieId == firstCompagnie.id).toList();
        
        _addResult('✅ Relation Compagnie-Agences: ${firstCompagnie.nom} a ${agencesDeCompagnie.length} agences');
        
        if (agencesDeCompagnie.isNotEmpty) {
          final firstAgence = agencesDeCompagnie.first;
          final users = await AdminFirestoreService.getAllUsers().first;
          final usersDeAgence = users.where((u) => u.agenceId == firstAgence.id).toList();
          
          _addResult('✅ Relation Agence-Users: ${firstAgence.nom} a ${usersDeAgence.length} utilisateurs');
        }
      }
    } catch (e) {
      _addResult('❌ Erreur relations: $e');
    }
  }

  /// 🔐 Test du filtrage par rôle
  Future<void> _testRoleBasedFiltering() async {
    _addResult('📋 Test: Filtrage par rôle...');
    
    try {
      final compagnies = await AdminFirestoreService.getAllCompagnies().first;
      
      if (compagnies.isNotEmpty) {
        final firstCompagnie = compagnies.first;
        final agencesFiltrees = await AdminFirestoreService.getAgencesByCompagnie(firstCompagnie.id).first;
        
        _addResult('✅ Filtrage agences par compagnie: ${agencesFiltrees.length} agences pour ${firstCompagnie.nom}');
        
        if (agencesFiltrees.isNotEmpty) {
          final firstAgence = agencesFiltrees.first;
          final usersFiltres = await AdminFirestoreService.getUsersByAgence(firstAgence.id).first;
          
          _addResult('✅ Filtrage users par agence: ${usersFiltres.length} utilisateurs pour ${firstAgence.nom}');
        }
      }
    } catch (e) {
      _addResult('❌ Erreur filtrage: $e
