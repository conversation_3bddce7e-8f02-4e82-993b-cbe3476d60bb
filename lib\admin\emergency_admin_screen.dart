import 'package:flutter/material.dart';
import '../services/admin_fallback_service.dart';

/// 🚨 Interface d'Urgence pour Création d'Admins
class EmergencyAdminScreen extends StatefulWidget {
  const EmergencyAdminScreen({Key? key}) : super(key: key);

  @override
  State<EmergencyAdminScreen> createState() => _EmergencyAdminScreenState();
}

class _EmergencyAdminScreenState extends State<EmergencyAdminScreen> {
  bool _isLoading = false;
  Map<String, dynamic>? _adminStatus;
  final _emailController = TextEditingController();
  final _compagnieController = TextEditingController();
  final _compagnieIdController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadAdminStatus();
  }

  Future<void> _loadAdminStatus() async {
    setState(() => _isLoading = true);
    
    try {
      final status = await AdminFallbackService.getAdminStatus();
      setState(() => _adminStatus = status);
    } catch (e) {
      _showError('Erreur chargement: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _ensureBasicAdmins() async {
    setState(() => _isLoading = true);
    
    try {
      final success = await AdminFallbackService.ensureBasicAdminsExist();
      
      if (success) {
        _showSuccess('Admins de base créés avec succès !');
        await _loadAdminStatus();
      } else {
        _showError('Échec création admins de base');
      }
    } catch (e) {
      _showError('Erreur: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _forceCreateAdmin() async {
    if (_emailController.text.isEmpty || 
        _compagnieController.text.isEmpty || 
        _compagnieIdController.text.isEmpty) {
      _showError('Tous les champs sont requis');
      return;
    }

    setState(() => _isLoading = true);
    
    try {
      final success = await AdminFallbackService.forceCreateAdmin(
        email: _emailController.text.trim(),
        compagnieId: _compagnieIdController.text.trim(),
        compagnieNom: _compagnieController.text.trim(),
      );
      
      if (success) {
        _showSuccess('Admin créé avec succès !');
        _clearForm();
        await _loadAdminStatus();
      } else {
        _showError('Échec création admin');
      }
    } catch (e) {
      _showError('Erreur: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _cleanDuplicates() async {
    setState(() => _isLoading = true);
    
    try {
      final success = await AdminFallbackService.cleanDuplicateAdmins();
      
      if (success) {
        _showSuccess('Doublons nettoyés !');
        await _loadAdminStatus();
      } else {
        _showError('Échec nettoyage');
      }
    } catch (e) {
      _showError('Erreur: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _clearForm() {
    _emailController.clear();
    _compagnieController.clear();
    _compagnieIdController.clear();
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🚨 Interface d\'Urgence Admins'),
        backgroundColor: Colors.red.shade700,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: ,
                  const SizedBox(height: 20),
                  
                  // Actions rapides
                  _buildQuickActions(),
                  const SizedBox(height: 20),
                  
                  // Création manuelle
                  _buildManualCreation(),
                  const SizedBox(height: 20),
                  
                  // Liste des admins
                  _buildAdminsList(),
                ],
              ),
            ),
    );
  }

  Widget _buildStatusCard() {
    final hasAdmins = _adminStatus?['hasAdmins'] ?? false;
    final total = _adminStatus?['total'] ?? 0;
    
    return Card(
      color: hasAdmins ? Colors.green.shade50 : Colors.red.shade50,
      child: (1),
            const SizedBox(height: 8),
            .textTheme.headlineSmall?.copyWith(
                color: hasAdmins ? Colors.green.shade700 : Colors.red.shade700,
                fontWeight: FontWeight.bold,
              ),
            ),
            (total admin(s)',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Card(
      child: (1).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            ElevatedButton.const Icon(
              onPressed: _ensureBasicAdmins,
              icon: const Icon(Icons.info),
              label: const Text('Créer Admins de Base'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: ,
            ),
            const SizedBox(height: 8),
            
            ElevatedButton.const Icon(
              onPressed: _cleanDuplicates,
              icon: const Icon(Icons.info),
              label: const Text('Nettoyer Doublons'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: ,
            ),
            const SizedBox(height: 8),
            
            ElevatedButton.const Icon(
              onPressed: _loadAdminStatus,
              icon: const Icon(Icons.info),
              label: const Text('Actualiser'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey,
                foregroundColor: Colors.white,
                padding: ,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildManualCreation() {
    return Card(
      child: (1).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            TextField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'Email Admin',
                border: OutlineInputBorder(),
                prefixIcon: ,
              ),
              keyboardType: TextInputType.emailAddress,
            ),
            const SizedBox(height: 12),
            
            TextField(
              controller: _compagnieController,
              decoration: const InputDecoration(
                labelText: 'Nom Compagnie',
                border: OutlineInputBorder(),
                prefixIcon: ,
              ),
            ),
            const SizedBox(height: 12),
            
            TextField(
              controller: _compagnieIdController,
              decoration: const InputDecoration(
                labelText: 'ID Compagnie',
                border: OutlineInputBorder(),
                prefixIcon: ,
              ),
            ),
            const SizedBox(height: 16),
            
            ElevatedButton.const Icon(
              onPressed: _forceCreateAdmin,
              icon: const Icon(Icons.info),
              label: const Text('Créer Admin'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: ,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdminsList() {
    final admins = _adminStatus?['admins'] as List? ?? [];
    
    if (admins.isEmpty) {
      return Card(
        child: (1),
          ),
        ),
      );
    }
    
    return Card(
      child: (1)',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            ...admins.map((admin) => Card(
              margin: .substring(0, 1) ?? '?',
                    style: ,
                  ),
                ),
                title: const Text(admin['email'] ?? 'Email inconnu'),
                subtitle: const Text(admin['compagnieNom'] ?? 'Compagnie inconnue'),
                trailing: Chip(
                  label: const Text(admin['status'] ?? 'inconnu'),
                  backgroundColor: admin['status'] == 'actif
