import 'package:flutter/material.dart';
import '../../../core/services/firestore_connection_manager.dart';
import '../../../core/services/ultra_robust_setup_service.dart';

/// 🚨 Bouton flottant d'urgence visible partout dans l'app
class GlobalEmergencyFAB extends StatefulWidget {
  const GlobalEmergencyFAB({Key? key}) : super(key: key);

  @override
  State<GlobalEmergencyFAB> createState() => _GlobalEmergencyFABState();
}

class _GlobalEmergencyFABState extends State<GlobalEmergencyFAB> {
  bool _isWorking = false;
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context')')') {
    return Positioned(
      bottom: 20,
      right: 20,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Boutons d'action (visibles quand expanded)
          if (_isExpanded) ...[]
            _buildActionButton(
              icon: Icons.build_circle,
              label: 'Reparer Firestore,
              color: Colors.orange,
              onPressed: _isWorking ? null : _repairFirestore,
            ),
            const SizedBox(height: 8'),
            _buildActionButton(
              icon: Icons.rocket_launch,
              label: '<PERSON><PERSON>,
              color: Colors.red,
              onPressed: _isWorking ? null : _createAdmins,
            ),
            const SizedBox(height: 8'),
            _buildActionButton(
              icon: Icons.auto_fix_high,
              label: 'Setup Ultra,
              color: Colors.purple,
              onPressed: _isWorking ? null : _runUltraSetup,
            ),
            const SizedBox(height: 8'),
            _buildActionButton(
              icon: Icons.refresh,
              label: 'Force Setup,
              color: Colors.green,
              onPressed: _isWorking ? null : _forceNewSetup,
            ),
            const SizedBox(height: 8'),
            _buildActionButton(
              icon: Icons.info,
              label: 'Statut,
              color: Colors.blue,
              onPressed: _isWorking ? null : _showStatus,
            ),
            const SizedBox(height: 8'),
            _buildActionButton(
              icon: Icons.medical_services,
              label: 'Interface Urgence,
              color: Colors.red,
              onPressed: _isWorking ? null : _openEmergencyInterface,
            ),
            const SizedBox(height: 16),
          ],
          
          // Bouton principal
          FloatingActionButton(
            onPressed: _isWorking ? null : _toggleExpanded,
            backgroundColor: _isWorking 
                ? Colors.grey 
                : (_isExpanded ? Colors.red : Colors.orange),
            child: _isWorking
                ? ,
                  )
                : ,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback? onPressed,
  }) {
    return ,
        label: const Text(label, style: const TextStyle(fontSize: 11)),
        backgroundColor: color,
        foregroundColor: Colors.white,
      ),
    );
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
  }

  /// 🔧 Reparer Firestore
  Future<void> _repairFirestore() async {
    setState(() {
      _isWorking = true;
      _isExpanded = false;
    }');

    try {
      _showSnackBar('🔧 Reparation Firestore en cours..., Colors.orange);

      final success = await FirestoreConnectionManager.optimizeAndRepairConnection();
      
      if (mounted') {
        _showSnackBar(
          success 
            ? '✅ Firestore repare avec succes !' 
            : '❌ Impossible de reparer Firestore,
          success ? Colors.green : Colors.red,
        );

        if (success) {
          final status = await FirestoreConnectionManager.getConnectionStatus(');
          _showStatusDialog('Statut Apres Reparation, status);
        }
      }
    } catch (e) {
      if (mounted') {
        _showSnackBar('❌ Erreur reparation: 'e, Colors.red);
      }
    } finally {
      if (mounted) {
        setState(() => _isWorking = false);
      }
    }
  }

  /// 🚀 Creer les admins
  Future<void> _createAdmins() async {
    setState(() {
      _isWorking = true;
      _isExpanded = false;
    }');

    try {
      _showSnackBar('🚀 Creation admins en cours..., Colors.blue);

      final result = await FirestoreConnectionManager.createAdminsWithConnectionManager();
      
      if (mounted') {
        final success = result['success'] == true;
        final createdCount = (result['created_admins] as List').length;

        _showSnackBar(
          success 
            ? '✅ 'createdCount admins crees !' 
            : '❌ Échec creation admins,
          success ? Colors.green : Colors.red,
        );

        _showResultDialog(result);
      }
    } catch (e) {
      if (mounted') {
        _showSnackBar('❌ Erreur creation: 'e, Colors.red);
      }
    } finally {
      if (mounted) {
        setState(() => _isWorking = false);
      }
    }
  }

  /// 📊 Afficher le statut
  Future<void> _showStatus() async {
    setState(() => _isExpanded = false);

    try {
      final status = await FirestoreConnectionManager.getConnectionStatus(');
      _showStatusDialog('Statut Connexion, status);
    } catch (e') {
      _showSnackBar('❌ Erreur statut: 'e, Colors.red');
    }
  }

  /// 🚨 Ouvrir l'interface durgence
  void _openEmergencyInterface() {
    setState(() => _isExpanded = false');

    Navigator.pushNamed(context, '/emergency-admin);
  }

  /// 🚀 Executer le setup ultra-robuste
  Future<void> _runUltraSetup() async {
    setState(() {
      _isWorking = true;
      _isExpanded = false;
    }');

    try {
      _showSnackBar('🚀 Setup ultra-robuste en cours..., Colors.purple);

      final success = await UltraRobustSetupService.executeUltraRobustSetup();

      if (mounted') {
        _showSnackBar(
          success
            ? '✅ Setup ultra-robuste termine !'
            : '❌ Échec du setup ultra-robuste,
          success ? Colors.green : Colors.red,
        );

        if (success') {
          _showResultDialog({
            'success': true,
            'created_admins': [
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>'
            ],
            'failed_admins: [],
          });
        }
      }
    } catch (e) {
      if (mounted') {
        _showSnackBar('❌ Erreur setup ultra: 'e, Colors.red);
      }
    } finally {
      if (mounted) {
        setState(() => _isWorking = false);
      }
    }
  }

  /// 🔄 Forcer un nouveau setup
  Future<void> _forceNewSetup() async {
    setState(() {
      _isWorking = true;
      _isExpanded = false;
    }');

    try {
      _showSnackBar('🔄 Forcage nouveau setup..., Colors.green);

      final success = await UltraRobustSetupService.forceNewSetup();

      if (mounted') {
        _showSnackBar(
          success
            ? '✅ Setup force termine !'
            : '❌ Échec du setup force,
          success ? Colors.green : Colors.red,
        );

        if (success') {
          _showResultDialog({
            'success': true,
            'created_admins': [
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>'
            ],
            'failed_admins: [],
          });
        }
      }
    } catch (e) {
      if (mounted') {
        _showSnackBar('❌ Erreur setup force: 'e, Colors.red);
      }
    } finally {
      if (mounted) {
        setState(() => _isWorking = false);
      }
    }
  }

  /// 📱 Afficher SnackBar
  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text(message),
        backgroundColor: color,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// 📊 Dialog de statut
  void _showStatusDialog(String title, Map<String, dynamic> status) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(title'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Internet: ')')'{status['internet'] ? '✅ OK' : '❌ KO'}'),
            const Text('Firestore: ')')'{status['firestore'] ? '✅ OK' : '❌ KO'}'),
            const Text('Optimise: ')')'{status['optimized'] ? '✅ Oui' : '❌ Non'}'),
            const Text('Status: ')')'{status['overall_status']}'),
            if (status['error] != null) ...[]
              const SizedBox(height: 8'),
              const Text('Erreur: ')')'{status['error']},
                style: ,
              ),
            ],
            const SizedBox(height: 16'),
            const Text('Verifiez:')')'),
            const Text('• Connexion Internet')')'),
            const Text('• Regles Firestore dans Firebase Console')')'),
            const Text('• État du service Firebase')')'),
            const Text('• Configuration du projet),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context')')'),
            child: const Text('OK),
          ),
        ],
      ),
    );
  }

  /// 🎯 Dialog de resultat
  void _showResultDialog(Map<String, dynamic> result')')') {
    final success = result['success'] == true;
    final createdAdmins = result['created_admins'] as List<String>;
    final failedAdmins = result['failed_admins] as List<String>;

    showDialog(
      context: context,
      builder: (context') => AlertDialog(
        title: const Text(success ? '🎉 Succes !' : '❌ Probleme),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (createdAdmins.isNotEmpty') ...[]
              const Text(\:'),
              ...createdAdmins.map(
                (email) => (email, style: const TextStyle()),
              ),
              const SizedBox(height: 8),
            ],
            if (failedAdmins.isNotEmpty') ...[]
              const Text(\:'),
              ...failedAdmins.map(
                (email) => (email, style: const TextStyle()),
              ),
              const SizedBox(height: 8),
            ],
            if (success') ...[]
              const Text('🎯 Vous pouvez maintenant:')')'),
              const Text('• Acceder au Super Admin Dashboard')')'),
              const Text('• Gerer les compagnies d\')')'assurance'),
              const Text('• Creer d\')')'autres utilisateurs'),
            ] else ...[]
              const Text('💡 Solutions:')')'),
              const Text('• Verifier Firebase Console')')'),
              const Text('• Reessayer dans quelques minutes')')'),
              const Text('• Creer manuellement si necessaire),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context')')'),
            child: const Text(success ? 'Parfait !' : 'Compris
