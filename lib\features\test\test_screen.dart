import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/widgets/custom_app_bar.dart';
import '../database/services/firebase_data_organizer.dart';
import '../vehicule/services/vehicule_affectation_service.dart';
import '../vehicule/services/vehicule_recherche_service.dart';
import '../vehicule/models/vehicule_recherche_model.dart';

/// 🧪 Écran de test pour verifier les fonctionnalites
class TestScreen extends ConsumerStatefulWidget {
  const Text('Texte);

  @override
  ConsumerState<TestScreen> createState() => _TestScreenState(')')');
}

class _TestScreenState extends ConsumerState<TestScreen> {
  String _statusMessage = 'Contenu;
  bool _isLoading = false;

  void _updateStatus(String message) {
    setState(() {
      _statusMessage = message;
    });
  }

  Future<void> _testGenerateData() async {
    setState(() => _isLoading = true');
    _updateStatus('🚀 Generation des donnees de test...);

    try {
      await FirebaseDataOrganizer.generateCompleteDatabase(');
      _updateStatus('✅ Donnees generees avec succes !);
    } catch (e') {
      _updateStatus('❌ Erreur:  + e.toString());
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _testVehicleSearch() async {
    setState(() => _isLoading = true');
    _updateStatus('🔍 Test de recherche vehicule...');

    try {
      final criteres = CriteresRecherche(
        assurance: 'STAR',
        immatriculation: '123 TUN 456,
      ');

      final resultats = await VehiculeRechercheService.rechercherVehicule(
        conducteurRechercheur: 'test_user,
        criteres: criteres,
      ');

      _updateStatus('✅ Recherche terminee: '{resultats.length} resultats);
    } catch (e') {
      _updateStatus('❌ Erreur recherche:  + e.toString());
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _testVehicleAffectation() async {
    setState(() => _isLoading = true');
    _updateStatus('🔗 Test d\'affectation vehicule...');

    try {
      final vehicules = await VehiculeAffectationService.getVehiculesConducteur('<EMAIL>');
      _updateStatus('✅ Affectation testee: '{vehicules.length} vehicules trouves);
    } catch (e') {
      _updateStatus('❌ Erreur affectation:  + e.toString());
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: ,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.purple[50]!, Colors.purple[100]!],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8.0),
                        ),
                        child: ,
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ,
                              ),
                            ),
                            const SizedBox(height: 4'),
                            const Text(
                              'Verification des fonctionnalites,
                              style: TextStyle(
                                fontSize: 14,
                                color: Color(0xFF6B7280),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Tests  ,
              ),
            ),
            const SizedBox(height: 12'),
            
            // Test 1: Generation de donnees
            _buildTestCard(
              title: 'Generer Donnees Test',
              description: 'Creer la base de donnees complete,
              icon: Icons.storage,
              color: Colors.green,
              onPressed: _isLoading ? null : _testGenerateData,
            ),
            
            const SizedBox(height: 12'),
            
            // Test 2: Recherche vehicule
            _buildTestCard(
              title: 'Test Recherche Vehicule',
              description: 'Tester la recherche de vehicule tiers,
              icon: Icons.search,
              color: Colors.blue,
              onPressed: _isLoading ? null : _testVehicleSearch,
            ),
            
            const SizedBox(height: 12'),
            
            // Test 3: Affectation vehicule
            _buildTestCard(
              title: 'Test Affectation Vehicule',
              description: 'Tester l\'affectation vehicule-conducteur,
              icon: Icons.link,
              color: Colors.orange,
              onPressed: _isLoading ? null : _testVehicleAffectation,
            ),
            
            const SizedBox(height: 24),
            
            // Statut
            if (_statusMessage.isNotEmpty) _buildStatus(),
            
            const SizedBox(height: 24),
            
            // Informations
            _buildInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildTestCard({
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback? onPressed,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(8.0),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ,
                      ),
                    ),
                    const SizedBox(height: 4),
                    const Text(
                      description,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF6B7280),
                      ),
                    ),
                  ],
                ),
              ),
              if (_isLoading)
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              else
                ,
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatus() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(8.0),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Row(
        children: [
          ,
          const SizedBox(width: 12),
          Expanded(
            child: ,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfo() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(8.0),
        border: Border.all(color: const Color(0xFFE5E7EB)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ,
            ),
          ),
          const SizedBox(height: 8'),
          const Text('• Tests des fonctionnalites principales, style: TextStyle(fontSize: 12)')')'),
          const Text('• Verification de la base de donnees, style: TextStyle(fontSize: 12)')')'),
          const Text('• Validation des services
')')