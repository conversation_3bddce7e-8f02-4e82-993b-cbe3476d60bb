import 'package:flutter/material.dart';
import '../../vehicules/models/vehicule_assure_model.dart';
import '../../auth/providers/auth_provider.dart';
import '../widgets/accident_form_widget.dart';
import '../widgets/photo_capture_widget.dart';
import '../widgets/ai_analysis_widget.dart';

/// 📋 Écran de déclaration d'accident
class AccidentDeclarationScreen extends StatefulWidget {
  final VehiculeAssureModel selectedVehicle;

  ;

  @override
  State<AccidentDeclarationScreen> createState() => _AccidentDeclarationScreenState();
}

class _AccidentDeclarationScreenState extends State<AccidentDeclarationScreen>
    with TickerProviderStateMixin {
  
  late TabController _tabController;
  int _currentStep = 0;
  
  // Données du formulaire
  final Map<String, dynamic> _accidentData = {};
  final List<String> _photos = [];
  Map<String, dynamic>? _aiAnalysis;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    
    // Initialiser avec les données du véhicule
    _accidentData['vehicule'] = widget.selectedVehicle.toMap();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('📋 Déclaration d\'Accident'),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [ text: 'Infos'),
            Tab(icon: const Icon(Icons.info), text: 'Photos'),
            Tab(icon: const Icon(Icons.info), text: 'IA'),
            Tab(icon: const Icon(Icons.info), text: 'Envoi'),
          ],
        ),
      ),
      body: Column(
        children: [
          // Informations du véhicule sélectionné
          _buildVehicleHeader(),
          
          // Contenu des onglets
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAccidentInfoTab(),
                _buildPhotosTab(),
                _buildAIAnalysisTab(),
                _buildSummaryTab(),
              ],
            ),
          ),
          
          // Barre de navigation
          _buildNavigationBar(),
        ],
      ),
    );
  }

  /// 🚗 En-tête avec informations du véhicule
  Widget _buildVehicleHeader() {
    final vehicule = widget.selectedVehicle;
    
    return Container(
      width: double.infinity,
      padding: ,
      ),
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: Colors.purple,
              borderRadius: BorderRadius.circular(8),
            ),
            child: ,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ({vehicule.vehicule.modele}',
                  style: ,
                ),
                ({_getAssureurName(vehicule.assureurId)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: ,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                ,
                const SizedBox(width: 4),
                ,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 📝 Onglet informations accident
  Widget _buildAccidentInfoTab() {
    return AccidentFormWidget(
      vehicule: widget.selectedVehicle,
      onDataChanged: (data) {
        setState(() {
          _accidentData.addAll(data);
        });
      },
    );
  }

  /// 📸 Onglet photos
  Widget _buildPhotosTab() {
    return PhotoCaptureWidget(
      onPhotosChanged: (photos) {
        setState(() {
          _photos.clear();
          _photos.addAll(photos);
        });
      },
    );
  }

  /// 🧠 Onglet analyse IA
  Widget _buildAIAnalysisTab() {
    return AIAnalysisWidget(
      photos: _photos,
      accidentData: _accidentData,
      onAnalysisComplete: (analysis) {
        setState(() {
          _aiAnalysis = analysis;
        });
      },
    );
  }

  /// 📋 Onglet résumé
  Widget _buildSummaryTab() {
    return SingleChildScrollView(
      padding: ,
          
          const SizedBox(height: 16),
          
          // Photos
          _buildSummarySection(
            'Photos de l\'Accident',
            [
              'Nombre de photos: ${_photos.length}',
              if (_photos.isEmpty) 'Aucune photo ajoutée',
            ],
            Icons.camera_alt,
          ),
          
          const SizedBox(height: 16),
          
          // Analyse IA
          if (_aiAnalysis != null)
            _buildSummarySection(
              'Analyse IA',
              [
                'Véhicules détectés: ${_aiAnalysis!['vehicules_detectes'] ?? 0}',
                'Gravité estimée: ${_aiAnalysis!['gravite'] ?? 'Non déterminée'}',
                'Confiance: ${(_aiAnalysis!['confiance'] ?? 0) * 100}%',
              ],
              Icons.smart_toy,
            ),
          
          const SizedBox(height: 24),
          
          // Bouton de soumission
           ? _submitAccidentReport : null,
              icon: const Icon(Icons.info),
              label: const Text('Soumettre la Déclaration'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                foregroundColor: Colors.white,
                padding: ,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 📊 Section de résumé
  Widget _buildSummarySection(String title, List<String> items, IconData icon) {
    return Container(
      width: double.infinity,
      padding: ,
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              ,
              const SizedBox(width: 8),
              ,
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...items.map((item) => (1)),
          )),
        ],
      ),
    );
  }

  /// 🧭 Barre de navigation
  Widget _buildNavigationBar() {
    return Container(
      padding: ,
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (_tabController.index > 0)
            Expanded(
              child: TextButton.const Icon(
                onPressed: () {
                  _tabController.animateTo(_tabController.index - 1);
                },
                icon: const Icon(Icons.info),
                label: const Text('Précédent'),
              ),
            ),
          
          if (_tabController.index > 0) const SizedBox(width: 12),
          
          if (_tabController.index < 3)
            Expanded(
              child: ElevatedButton.const Icon(
                onPressed: () {
                  _tabController.animateTo(_tabController.index + 1);
                },
                icon: const Icon(Icons.info),
                label: const Text('Suivant'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// ✅ Vérifier si on peut soumettre
  bool _canSubmit() {
    return _accidentData.isNotEmpty && 
           _photos.isNotEmpty;
  }

  /// 📤 Soumettre la déclaration
  void _submitAccidentReport() async {
    try {
      // TODO: Implémenter la soumission
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: const Text('🎉 Déclaration soumise avec succès !'),
          backgroundColor: Colors.green,
        ),
      );
      
      // Retourner à l'écran précédent
      Navigator.of(context).pop();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: (e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 🏢 Nom de l'assureur
  String _getAssureurName(String assureurId) {
    switch (assureurId.toUpperCase()) {
      case 'STAR':
        return 'STAR Assurances';
      case 'MAGHREBIA':
        return 'Maghrebia Assurances';
      case 'GAT':
        return 'GAT Assurances
