import 'package:flutter/material.dart';
import 'core/config/app_routes.dart';

/// 🧪 Écran de test pour accéder directement au formulaire
class TestFormulaireScreen extends StatelessWidget {
  const Text(\;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🧪 Test Formulaire'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
      ),
      body: (1),
              const SizedBox(height: 24),
              ,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              const Text(
                'Cliquez sur le bouton ci-dessous pour accéder au formulaire de demande de compte professionnel',
                style: TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
               {
                    Navigator.pushNamed(context, AppRoutes.professionalRequest);
                  },
                  icon: const Icon(Icons.info),
                  label: const Text('Ouvrir le Formulaire'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue[600],
                    foregroundColor: Colors.white,
                    padding: ,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              OutlinedButton.const Icon(
                onPressed: () {
                  Navigator.pushNamed(context, '/user-type-selection');
                },
                icon: const Icon(Icons.info),
                label: const Text('Retour à la sélection
