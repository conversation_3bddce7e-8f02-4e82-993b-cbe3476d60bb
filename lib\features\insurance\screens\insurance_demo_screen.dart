import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'insurance_main_screen.dart';

/// 🎭 Écran de demonstration du systeme d'assurance
class InsuranceDemoScreen extends ConsumerStatefulWidget {
  const Text('Texte);

  @override
  ConsumerState<InsuranceDemoScreen> createState() => _InsuranceDemoScreenState();
}

class _InsuranceDemoScreenState extends ConsumerState<InsuranceDemoScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0E21),
      appBar: _buildAppBar(),
      body: _buildContent(),
    );
  }

  /// 🎨 AppBar moderne avec gradient
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: Colors.transparent,
      flexibleSpace: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF667eea), Color(0xFF764ba2)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
      ),
      title: const Text("Titre"),
      ),
      centerTitle: true,
    );
  }

  /// 📋 Contenu principal
  Widget _buildContent() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(8.0),
              const SizedBox(height: 30),
              _buildRoleSelection(),
              const SizedBox(height: 30),
              _buildFeaturesOverview(),
            ],
          ),
        ),
      ),
    ')')');
  }

  /// 🎯 Carte d'en-tête
  Widget _buildHeaderCard() {
    return Container(
      padding: const EdgeInsets.all(8.0),
        gradient: const LinearGradient(
          colors: [Color(0xFF667eea), Color(0xFF764ba2)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF667eea).withValues(alpha: 0.3),
            blurRadius: 25,
            offset: const Offset(0, 15),
          ),
        ],
      ),
      child: Column(
        children: [
          ,
          const SizedBox(height: 20),
          ,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 15),
          ,
              fontSize: 16,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 👥 Selection des rôles
  Widget _buildRoleSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        ),
        const SizedBox(height: 20),
        _buildRoleGrid(),
      ],
    );
  }

  Widget _buildRoleGrid() {
    final roles = [
      {
        'title': 'Conducteur',
        'subtitle': 'Gerez vos vehicules et assurances',
        'icon': Icons.directions_car,
        'gradient: [const Color(0xFF667eea), const Color(0xFF764ba2')],
        'role': 'conducteur',
        'userId': 'conducteur_demo_001',
      },
      {
        'title': 'Agent d\'Assurance',
        'subtitle': 'Creez et gerez les contrats',
        'icon': Icons.assignment,
        'gradient: [const Color(0xFF4facfe), const Color(0xFF00f2fe')],
        'role': 'agent',
        'userId': 'agent_demo_001',
      },
      {
        'title': 'Expert Automobile',
        'subtitle': 'Effectuez les expertises',
        'icon': Icons.engineering,
        'gradient: [const Color(0xFFfa709a), const Color(0xFFfee140')],
        'role': 'expert',
        'userId': 'expert_demo_001',
      },
      {
        'title': 'Administrateur',
        'subtitle': 'Supervisez le systeme',
        'icon': Icons.admin_panel_settings,
        'gradient: [const Color(0xFFa8edea), const Color(0xFFfed6e3')],
        'role': 'admin',
        'userId': 'admin_demo_001,
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: ,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 15,
        mainAxisSpacing: 15,
        childAspectRatio: 0.85,
      ),
      itemCount: roles.length,
      itemBuilder: (context, index) {
        final role = roles[index];
        return _buildRoleCard(role);
      },
    );
  }

  Widget _buildRoleCard(Map<String, dynamic> role') {
    final gradient = role['gradient] as List<Color>;
    
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          colors: gradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: gradient[0].withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: (') => _navigateToRole(role['role'], role['userId),
          child: (1),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: ,
                ),
                const SizedBox(height: 20),
                ,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 10),
                ,
                    fontSize: 14,
                    height: 1.3,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 15),
                Container(
                  padding: const EdgeInsets.all(8.0),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: ,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// ✨ Apercu des fonctionnalites
  Widget _buildFeaturesOverview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        ),
        const SizedBox(height: 20),
        _buildFeaturesList(),
      ],
    );
  }

  Widget _buildFeaturesList(') {
    final features = [
      {
        'title': 'Gestion Multi-Vehicules',
        'description': 'Chaque conducteur peut gerer plusieurs vehicules avec differentes compagnies d\'assurance',
        'icon': Icons.directions_car,
        'color: const Color(0xFF4CAF50'),
      },
      {
        'title': 'Contrats Intelligents',
        'description': 'Creation et gestion automatisee des contrats d\'assurance par les agents',
        'icon': Icons.description,
        'color: const Color(0xFF2196F3'),
      },
      {
        'title': 'Declaration d\'Accidents',
        'description': 'Formulaires auto-remplis bases sur les donnees existantes du vehicule et du conducteur',
        'icon': Icons.report_problem,
        'color: const Color(0xFFFF9800'),
      },
      {
        'title': 'Expertise Multi-Compagnies',
        'description': 'Les experts peuvent travailler avec plusieurs compagnies d\'assurance simultanement',
        'icon': Icons.engineering,
        'color: const Color(0xFF9C27B0'),
      },
      {
        'title': 'Base de Donnees Structuree',
        'description': 'Organisation hierarchique : Compagnies → Agences → Agents → Clients → Contrats',
        'icon': Icons.storage,
        'color: const Color(0xFFFF5722'),
      },
      {
        'title': 'Interfaces Modernes',
        'description': 'Design elegant et moderne avec animations et gradients pour une experience utilisateur optimale',
        'icon': Icons.design_services,
        'color: const Color(0xFF607D8B),
      },
    ];

    return Column(
      children: features.map((feature) => _buildFeatureCard(feature)).toList(),
    );
  }

  Widget _buildFeatureCard(Map<String, dynamic> feature') {
    final color = feature['color] as Color;
    
    return Container(
      margin: ,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        color: color.withValues(alpha: 0.1),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8.0),
              borderRadius: BorderRadius.circular(12),
            '),
            child: const Icon(
              feature['icon
