import 'package:flutter/material.dart';

import '../../models/onboarding_page_model.dart';

/// 📄 Widget pour une page donboarding
class OnboardingPageWidget extends StatefulWidget {
  final OnboardingPageModel page;
  final bool isActive;

   ) : super(key: key);

  @override
  State<OnboardingPageWidget> createState() => _OnboardingPageWidgetState();
}

class _OnboardingPageWidgetState extends State<OnboardingPageWidget>
    with TickerProviderStateMixin {
  late AnimationController _iconController;
  late AnimationController _textController;
  late AnimationController _featuresController;
  
  late Animation<double> _iconScale;
  late Animation<double> _iconRotation;
  late Animation<double> _textOpacity;
  late Animation<Offset> _textSlide;
  late Animation<double> _featuresOpacity;
  late Animation<Offset> _featuresSlide;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    
    if (widget.isActive) {
      _startAnimations();
    }
  }

  @override
  void didUpdateWidget(OnboardingPageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isActive && !oldWidget.isActive) {
      _startAnimations();
    } else if (!widget.isActive && oldWidget.isActive) {
      _resetAnimations();
    }
  }

  /// 🎬 Initialisation des animations
  void _initializeAnimations(') {
    // Animation de l
