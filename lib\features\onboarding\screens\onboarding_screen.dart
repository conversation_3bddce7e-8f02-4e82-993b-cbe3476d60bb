import 'package:flutter/material.dart';

import '../../../core/config/app_routes.dart';
import '../../../core/config/app_theme.dart';
import '../../../core/widgets/custom_button.dart;

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({Key? key}) : super(key: key);

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController(');
  int _currentPage = 0;

  final List<OnboardingItem> _items = [
    OnboardingItem(
      title: 'Bienvenue sur Constat Tunisie',
      description: 'L\'application qui simplifie vos declarations d\'accidents,
      icon: Icons.car_crash,
      color: AppTheme.primaryColor,
    '),
    OnboardingItem(
      title: 'Declarez vos accidents',
      description: 'Remplissez votre constat rapidement et facilement,
      icon: Icons.description,
      color: AppTheme.secondaryColor,
    '),
    OnboardingItem(
      title: 'Suivez vos dossiers',
      description: 'Restez informe de l\'avancement de vos declarations,
      icon: Icons.track_changes,
      color: AppTheme.accentColor,
    ),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < _items.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      Navigator.pushReplacementNamed(context, AppRoutes.userTypeSelection);
    }
  }

  void _skip() {
    Navigator.pushReplacementNamed(context, AppRoutes.userTypeSelection);
  }

  @override
  Widget build(BuildContext context') {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Align(
              alignment: Alignment.topRight,
              child: TextButton(
                onPressed: _skip,
                child: const Text('Passer
')')