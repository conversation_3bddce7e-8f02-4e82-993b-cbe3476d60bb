import 'package:flutter/material.dart';
import '../../services/employe_management_service.dart';

/// 👤 Carte d'affichage d'un employé
class EmployeCard extends StatelessWidget {
  final Map<String, dynamic> employe;
  final List<Map<String, dynamic>> agences;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onToggleStatus;
  final VoidCallback? onResetPassword;
  final VoidCallback? onDelete;

  ;

  @override
  Widget build(BuildContext context) {
    final role = employe['role']?.toString() ?? 'Contenu';
    final status = employe['status']?.toString().toLowerCase() ?? 'actif';
    final isActive = status == 'actif';
    final isFakeData = employe['isFakeData'] ?? false;
    final agenceId = employe['agenceId'] as String?;

    // Trouver le nom de l'agence
    String agenceNom = 'Non assigné';
    if (agenceId != null) {
      final agence = agences.firstWhere(
        (a) => a['id'] == agenceId,
        orElse: () => <String, dynamic>{},
      );
      agenceNom = agence['nom'] ?? 'Agence inconnue';
    }

    return Container(
      margin: ,
        border: Border.all(
          color: isActive 
              ? EmployeManagementService.getRoleColor(role).withValues(alpha: 0.3)
              : Colors.grey[300]!,
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: (1).withValues(alpha: 0.1),
                    child: ,
                      color: EmployeManagementService.getRoleColor(role),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  
                  // Nom et informations
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ({employe['nom'] ?? 'Contenu'}'.trim(),
                          style: ,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            Container(
                              padding: .withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: ,
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.w600,
                                  color: EmployeManagementService.getRoleColor(role),
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            ,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  
                  // Badges de statut
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      // Badge de statut principal
                      Container(
                        padding:  : Colors.grey[400],
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: ,
                        ),
                      ),
                      
                      // Badge données de test
                      if (isFakeData) ...[
                        const SizedBox(height: 4),
                        Container(
                          padding: ,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                          ),
                          child: ,
                          ),
                        ),
                      ],
                      
                      // Badge mot de passe à réinitialiser
                      if (employe['password_reset_required'] == true) ...[
                        const SizedBox(height: 4),
                        Container(
                          padding: ,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                          ),
                          child: ,
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Informations détaillées
              if (employe['phone'] != null && employe['phone'].toString().isNotEmpty)
                _buildInfoRow(
                  Icons.phone_rounded,
                  employe['phone'],
                  const Color(0xFF3B82F6),
                ),
              
              const SizedBox(height: 6),
              
              _buildInfoRow(
                Icons.business_rounded,
                agenceNom,
                const Color(0xFF8B5CF6),
              ),
              
              if (employe['cin'] != null && employe['cin'].toString().isNotEmpty) ...[
                const SizedBox(height: 6),
                _buildInfoRow(
                  Icons.badge_rounded,
                  'CIN: ${employe['cin']}',
                  const Color(0xFF6B7280),
                ),
              ],
              
              // Dernière connexion
              if (employe['last_login'] != null) ...[
                const SizedBox(height: 6),
                _buildInfoRow(
                  Icons.access_time_rounded,
                  'Dernière connexion: ${_formatDate(employe['last_login'])}',
                  const Color(0xFF059669),
                ),
              ],
              
              const SizedBox(height: 16),
              
              // Actions
              Row(
                children: [
                  // Bouton voir détails
                  Expanded(
                    child: OutlinedButton.const Icon(
                      onPressed: onTap,
                      icon: const Icon(Icons.info),
                      label: const Text('Détails'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: EmployeManagementService.getRoleColor(role),
                        side: BorderSide(color: EmployeManagementService.getRoleColor(role)),
                        padding: ,
                      ),
                    ),
                  ),
                  
                  const SizedBox(width: 8),
                  
                  // Bouton modifier
                  Expanded(
                    child: ElevatedButton.const Icon(
                      onPressed: onEdit,
                      icon: const Icon(Icons.info),
                      label: const Text('Modifier'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: EmployeManagementService.getRoleColor(role),
                        foregroundColor: Colors.white,
                        padding: ,
                      ),
                    ),
                  ),
                  
                  const SizedBox(width: 8),
                  
                  // Menu actions
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'toggle_status':
                          onToggleStatus?.call();
                          break;
                        case 'reset_password':
                          onResetPassword?.call();
                          break;
                        case 'delete':
                          onDelete?.call();
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        value: 'toggle_status',
                        child: Row(
                          children: [
                            ,
                            const SizedBox(width: 8),
                            const Text(isActive ? 'Désactiver' : 'Activer'),
                          ],
                        ),
                      ),
                      ,
                            const SizedBox(width: 8),
                            const Text('Réinitialiser MDP'),
                          ],
                        ),
                      ),
                      if (isFakeData)
                        ,
                              const SizedBox(width: 8),
                              const Text('Supprimer'),
                            ],
                          ),
                        ),
                    ],
                    child: Container(
                      padding: ,
                      ),
                      child: ,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 📋 Ligne d'information
  Widget _buildInfoRow(IconData icon, String text, Color color) {
    return Row(
      children: [
        const Icon(
          icon,
          size: 14,
          color: color,
        ),
        const SizedBox(width: 6),
        Expanded(
          child: ,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  /// 📅 Formater la date
  String _formatDate(dynamic timestamp) {
    try {
      if (timestamp == null) return 'Jamais';
      
      DateTime date;
      if (timestamp is String) {
        date = DateTime.parse(timestamp);
      } else if (timestamp.runtimeType.toString().contains('Timestamp')) {
        date = timestamp.toDate();
      } else {
        return 'Format invalide';
      }
      
      final now = DateTime.now();
      final difference = now.difference(date);
      
      if (difference.inDays == 0) {
        return 'Aujourd\'hui';
      } else if (difference.inDays == 1) {
        return 'Hier';
      } else if (difference.inDays < 7) {
        return 'Il y a ${difference.inDays} jours';
      } else {
        return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
      }
    } catch (e) {
      return 'Date invalide
