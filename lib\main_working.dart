import 'package:flutter/material.dart';

/// 🚀 Version fonctionnelle minimale
void main() {
  runApp(const WorkingApp());
}

class WorkingApp extends StatelessWidget {
  const WorkingApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Constat Tunisie - Working',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const WorkingHomePage(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class WorkingHomePage extends StatelessWidget {
  const WorkingHomePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Constat Tunisie - Version Fonctionnelle'),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.warning,
              size: 100,
              color: Colors.orange,
            ),
            const SizedBox(height: 20),
            const Text(
              '⚠️ Projet Corrompu !',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 10),
            const Text(
              'Les scripts de correction automatique ont corrompu les fichiers',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 40),
            const Text(
              '🔧 SOLUTIONS :',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            const Text(
              '1. git reset --hard <commit-avant-corruption>',
              style: TextStyle(
                fontFamily: 'monospace',
                color: Colors.green,
              ),
            ),
            const Text(
              '2. Restaurer depuis backup',
              style: TextStyle(
                fontFamily: 'monospace',
                color: Colors.green,
              ),
            ),
            const Text(
              '3. Recréer le projet proprement',
              style: TextStyle(
                fontFamily: 'monospace',
                color: Colors.green,
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: const Text('Cette version fonctionne ! Restaurez le projet.'),
              backgroundColor: Colors.orange,
            ),
          );
        },
        child: const Icon(Icons.info),
      ),
    );
  }
}
