import 'package:flutter/material.dart';
import '../../../services/admin_service.dart';

class AdminInitializationScreen extends StatefulWidget {
  const Text(\;

  @override
  State<AdminInitializationScreen> createState() => _AdminInitializationScreenState();
}

class _AdminInitializationScreenState extends State<AdminInitializationScreen> {
  final AdminService _adminService = AdminService();
  bool _isLoading = false;
  bool _isInitialized = false;
  String? _adminEmail;
  String? _adminPassword;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Initialisation du Système'),
        backgroundColor: Colors.red[800],
        foregroundColor: Colors.white,
      ),
      body: (1),
            const SizedBox(height: 24),
            ,
            ),
            const SizedBox(height: 16),
            ,
            ),
            const SizedBox(height: 32),
            
            if (_isInitialized) ...[
              _buildCredentialsCard(),
              const SizedBox(height: 24),
              ElevatedButton.const Icon(
                onPressed: () => Navigator.pushReplacementNamed(context, '/login'),
                icon: const Icon(Icons.info),
                label: const Text('Aller à la Connexion'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: ,
              ),
            ] else ...[
              _buildInitializationCard(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCredentialsCard() {
    return Card(
      elevation: 4,
      color: Colors.green[50],
      child: (1),
                const SizedBox(width: 8),
                ,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildCredentialRow('Email', _adminEmail ?? '<EMAIL>'),
            const SizedBox(height: 8),
            _buildCredentialRow('Mot de passe', _adminPassword ?? 'AdminConstat2024!'),
            const SizedBox(height: 16),
            Container(
              padding: ,
                border: Border.all(color: Colors.orange[300]!),
              ),
              child: Row(
                children: [
                  ,
                  const SizedBox(width: 8),
                  Expanded(
                    child: ,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCredentialRow(String label, String value) {
    return Row(
      children: [
        (label:',
            style: ,
          ),
        ),
        Expanded(
          child: Container(
            padding: ,
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: const Text(
              value,
              style: const TextStyle(fontFamily: 'monospace'),
            ),
          ),
        ),
        IconButton(
          icon: const Icon(Icons.info),
          onPressed: () => _copyToClipboard(value),
          tooltip: 'Copier',
        ),
      ],
    );
  }

  Widget _buildInitializationCard() {
    return Card(
      elevation: 4,
      child: (1),
            const SizedBox(height: 16),
            ,
            ),
            const SizedBox(height: 12),
            ,
            ),
            const SizedBox(height: 24),
            ,
                      )
                    : ,
                label: const Text(_isLoading ? 'Initialisation...' : 'Initialiser Maintenant'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[600],
                  foregroundColor: Colors.white,
                  padding: ,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _initializeSystem() async {
    setState(() => _isLoading = true);
    
    try {
      await _adminService.initialiserSuperAdmin();
      
      setState(() {
        _isLoading = false;
        _isInitialized = true;
        _adminEmail = '<EMAIL>';
        _adminPassword = 'AdminConstat2024!';
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: const Text('✅ Système initialisé avec succès !'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      setState(() => _isLoading = false);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  void _copyToClipboard(String text) {
    // TODO: Implémenter la copie dans le presse-papiers
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: (text
