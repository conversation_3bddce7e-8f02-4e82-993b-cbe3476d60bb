import 'package:flutter/material.dart';
import 'ai_accident_analysis_widget.dart';
import '../models/accident_analysis_model.dart';
import 'dart:math' as math;

/// 🎯 Widget de démonstration pour l'intégration IA
/// Montre comment intégrer l'analyse IA dans le formulaire de constat
class AIIntegrationDemo extends StatefulWidget {
  final String sessionId;
  final bool isCollaborative;

   ) : super(key: key);

  @override
  State<AIIntegrationDemo> createState() => _AIIntegrationDemoState();
}

class _AIIntegrationDemoState extends State<AIIntegrationDemo>
    with TickerProviderStateMixin {
  AccidentAnalysis? _analysis;
  bool _showAnalysis = false;
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 15),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🤖 Analyse IA d\'Accident'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: ,
            const SizedBox(height: 24),

            // Widget d'analyse IA
            AIAccidentAnalysisWidget(
              sessionId: widget.sessionId,
              isCollaborative: widget.isCollaborative,
              onAnalysisComplete: (analysis) {
                setState(() {
                  _analysis = analysis;
                  _showAnalysis = true;
                });
              },
            ),

            // Résultats de l'analyse
            if (_showAnalysis && _analysis != null) ...[
              const SizedBox(height: 24),
              _buildAnalysisResults(),
            ],

            // Guide d'utilisation
            const SizedBox(height: 24),
            _buildUsageGuide(),
          ],
        ),
      ),
    );
  }

  /// 📋 En-tête explicatif
  Widget _buildHeader() {
    return Card(
      child: (1),
                const SizedBox(width: 12),
                ,
                ),
              ],
            ),
            const SizedBox(height: 12),
            ,
            ),
            const SizedBox(height: 8),
            _buildFeatureList(),
          ],
        ),
      ),
    );
  }

  /// ✅ Liste des fonctionnalités gratuites
  Widget _buildFeatureList() {
    final features = [
      '📸 Analyse d\'images avec algorithmes simples',
      '🎤 Reconnaissance vocale native',
      '🧠 Traitement de texte basique',
      '📊 Génération de rapports automatiques',
      '💾 Sauvegarde dans Firebase',
    ];

    return Column(
      children: features.map((feature) => (1),
            const SizedBox(width: 8),
            Expanded(child: const Text(feature, style: const TextStyle(fontSize: 14))),
          ],
        ),
      )).toList(),
    );
  }

  /// 📊 Résultats de l'analyse
  Widget _buildAnalysisResults() {
    return Card(
      child: (1),
                const SizedBox(width: 12),
                ,
                ),
                ,
                Chip(
                  label: ({(_analysis!.reconstruction.confidence * 100).toInt()}% confiance'),
                  backgroundColor: Colors.green.shade100,
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Véhicules détectés
            _buildVehicleResults(),
            const SizedBox(height: 12),

            // Dégâts détectés
            _buildDamageResults(),
            const SizedBox(height: 12),

            // Impact analysé
            _buildImpactResults(),
            const SizedBox(height: 12),

            // Description traitée
            if (_analysis!.description.originalText.isNotEmpty)
              _buildDescriptionResults(),

            const SizedBox(height: 20),

            // 🎬 BOUTONS D'ACTION VIDÉO
            _buildVideoActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildVehicleResults() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(\',
          style: ,
        ),
        const SizedBox(height: 8),
        ...(_analysis!.imageAnalysis.vehicles.map((vehicle) => (1)'),
        ))),
      ],
    );
  }

  Widget _buildDamageResults() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(\',
          style: ,
        ),
        const SizedBox(height: 8),
        ...(_analysis!.imageAnalysis.damages.map((damage) => (1),
        ))),
      ],
    );
  }

  Widget _buildImpactResults() {
    final impact = _analysis!.imageAnalysis.impact;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        ),
        const SizedBox(height: 8),
        (1),
              ({impact.angle}'),
              ({impact.speed}'),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDescriptionResults() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        ),
        const SizedBox(height: 8),
        Container(
          padding: ,
          ),
          child: ,
          ),
        ),
        if (_analysis!.description.keyWords.isNotEmpty) ...[
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: _analysis!.description.keyWords.map((keyword) => Chip(
              label: const Text(keyword),
              backgroundColor: Colors.blue.shade100,
            )).toList(),
          ),
        ],
      ],
    );
  }

  /// 🎬 Boutons d'action pour la vidéo de reconstitution
  Widget _buildVideoActionButtons() {
    return Column(
      children: [
        // Séparateur
        Divider(color: Colors.grey.shade300, thickness: 1),
        const SizedBox(height: 16),

        // Titre de section
        Row(
          children: [
            ,
            const SizedBox(width: 8),
            ,
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Boutons d'action
        Row(
          children: [
            Expanded(
              child: ElevatedButton.const Icon(
                onPressed: () => _showReconstructionVideo(),
                icon: const Icon(Icons.info),
                label: const Text('Voir la reconstitution'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                  padding: ,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: OutlinedButton.const Icon(
                onPressed: () => _showVideoDetails(),
                icon: const Icon(Icons.info),
                label: const Text('Détails'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.purple,
                  side: ,
                  padding: ,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // Bouton de téléchargement
         => _downloadReconstructionVideo(),
            icon: const Icon(Icons.info),
            label: const Text('Télécharger la vidéo'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              padding: ,
          ),
        ),
      ],
    );
  }

  /// 📖 Guide d'utilisation
  Widget _buildUsageGuide() {
    return Card(
      child: (1),
                const SizedBox(width: 12),
                ,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildGuideSteps(),
          ],
        ),
      ),
    );
  }

  Widget _buildGuideSteps() {
    final steps = [
      '1. Prenez des photos claires de l\'accident',
      '2. Ajoutez une description vocale ou écrite',
      '3. Lancez l\'analyse IA gratuite',
      '4. Consultez les résultats générés',
      '5. Utilisez les données dans votre constat',
    ];

    return Column(
      children: steps.map((step) => (1),
              ),
              child: ,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(child: )),
          ],
        ),
      )).toList(),
    );
  }

  /// 🎬 Afficher la reconstitution vidéo
  void _showReconstructionVideo() {
    // Démarrer l'animation automatiquement
    _animationController.reset();
    _animationController.repeat();

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          padding: ,
                  const SizedBox(width: 12),
                  Expanded(
                    child: ,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.info),
                  ),
                ],
              ),
              ,

              // Lecteur vidéo simulé avec animation
              Expanded(
                child: _buildVideoPlayer(),
              ),

              const SizedBox(height: 16),

              // Contrôles vidéo
              _buildVideoControls(),

              const SizedBox(height: 16),

              // Informations de l'analyse
              _buildVideoInfo(),
            ],
          ),
        ),
      ),
    );
  }

  /// 📊 Afficher les détails de la vidéo
  void _showVideoDetails() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            ,
            const SizedBox(width: 8),
            const Text('Détails de l\'analyse'),
          ],
        ),
        content: ({(_analysis!.imageAnalysis.confidence * 100).toInt()}%',
                ),
                const SizedBox(height: 16),
                _buildDetailSection('💥 Impact', [
                  'Direction: ${_analysis!.imageAnalysis.impact.direction}',
                  'Angle: ${_analysis!.imageAnalysis.impact.angle}',
                  'Vitesse: ${_analysis!.imageAnalysis.impact.speed}',
                ),
                const SizedBox(height: 16),
                _buildDetailSection('🎬 Reconstitution', [
                  'Confiance: ${(_analysis!.reconstruction.confidence * 100).toInt()}%',
                  'Durée estimée: 30 secondes',
                  'Format: MP4 HD',
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailSection(String title, List<String> details) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        ),
        const SizedBox(height: 8),
        ...details.map((detail) => (1),
        )),
      ],
    );
  }

  /// 🎬 Construire le lecteur vidéo avec animation
  Widget _buildVideoPlayer() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: ,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            // Fond de la vidéo avec route visible
            Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.green[100], // Fond vert pour simuler l'herbe
              child: CustomPaint(
                size: Size.infinite,
                painter: RoadPainter(),
              ),
            ),

            // Animation de véhicules
            _buildVehicleAnimation(),

            // Overlay avec informations
            Positioned(
              top: 16,
              left: 16,
              right: 16,
              child: Container(
                padding: ,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    ,
                    const SizedBox(width: 8),
                    ,
                    ),
                    ,
                    Container(
                      padding: ,
                      ),
                      child: ,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Bouton play/pause au centre
            (1),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: ,
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: ,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 🚗 Animation des véhicules
  Widget _buildVehicleAnimation() {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return CustomPaint(
          size: Size.infinite,
          painter: VehicleAnimationPainter(
            progress: _animationController.value,
            vehicleCount: _analysis?.imageAnalysis.vehicleCount ?? 2,
          ),
        );
      },
    );
  }

  /// 🎮 Contrôles vidéo
  Widget _buildVideoControls() {
    return Container(
      padding: ,
      ),
      child: Column(
        children: [
          // Barre de progression
          Row(
            children: [
              ({(_animationController.value * 15).toInt().toString().padLeft(2, '0')}',
                style: ,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: LinearProgressIndicator(
                  value: _animationController.value,
                  backgroundColor: Colors.grey[300],
                  valueColor: ,
                ),
              ),
              const SizedBox(width: 8),
              ,
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Boutons de contrôle
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(
                onPressed: () {
                  _animationController.reset();
                },
                icon: const Icon(Icons.info),
                color: Colors.purple,
              ),
              const SizedBox(width: 16),
              IconButton(
                onPressed: () {
                  if (_animationController.isAnimating) {
                    _animationController.stop();
                  } else {
                    _animationController.repeat();
                  }
                },
                icon: const Icon(Icons.info),
                color: Colors.purple,
                iconSize: 32,
              ),
              const SizedBox(width: 16),
              IconButton(
                onPressed: () {
                  _animationController.forward();
                },
                icon: const Icon(Icons.info),
                color: Colors.purple,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 📊 Informations de la vidéo
  Widget _buildVideoInfo() {
    return Container(
      padding: ,
        border: Border.all(color: Colors.purple[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              ,
              const SizedBox(width: 8),
              ,
              ),
            ],
          ),
          const SizedBox(height: 12),
          ,
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              _buildInfoChip('Véhicules', '${_analysis?.imageAnalysis.vehicleCount ?? 0}'),
              const SizedBox(width: 8),
              _buildInfoChip('Confiance', '${((_analysis?.reconstruction.confidence ?? 0) * 100).toInt()}%'),
              const SizedBox(width: 8),
              _buildInfoChip('Durée', '15s'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoChip(String label, String value) {
    return Container(
      padding: ,
        border: Border.all(color: Colors.purple[200]!),
      ),
      child: (value',
        style: ,
      ),
    );
  }

  /// 📥 Télécharger la vidéo de reconstitution
  void _downloadReconstructionVideo() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            ,
            const SizedBox(width: 8),
            Expanded(
              child: const Text('🎥 Génération de la vidéo en cours...'),
            ),
          ],
        ),
        backgroundColor: Colors.purple,
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: 'Voir',
          textColor: Colors.white,
          onPressed: () => _showReconstructionVideo(),
        ),
      ),
    );

    // Simulation du téléchargement
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: const Text('✅ Vidéo générée ! Cliquez sur "Voir" pour la visionner'),
            backgroundColor: Colors.green,
          ),
        );
      }
    });
  }
}

/// 🛣️ Painter pour dessiner la route
class RoadPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    // Route principale horizontale
    final roadPaint = Paint()
      ..color = Colors.grey[700]!
      ..style = PaintingStyle.fill;

    canvas.drawRect(
      Rect.fromLTWH(0, size.height * 0.4, size.width, size.height * 0.2),
      roadPaint,
    );

    // Route secondaire verticale
    canvas.drawRect(
      Rect.fromLTWH(size.width * 0.4, 0, size.width * 0.2, size.height),
      roadPaint,
    );

    // Lignes blanches horizontales
    final linePaint = Paint()
      ..color = Colors.white
      ..strokeWidth = 3;

    for (double x = 0; x < size.width; x += 50) {
      canvas.drawLine(
        Offset(x, size.height * 0.5),
        Offset(x + 25, size.height * 0.5),
        linePaint,
      );
    }

    // Lignes blanches verticales
    for (double y = 0; y < size.height; y += 50) {
      canvas.drawLine(
        Offset(size.width * 0.5, y),
        Offset(size.width * 0.5, y + 25),
        linePaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// 🎨 Painter pour l'animation des véhicules
class VehicleAnimationPainter extends CustomPainter {
  final double progress;
  final int vehicleCount;

  VehicleAnimationPainter({
    required this.progress,
    required this.vehicleCount,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill;

    // Véhicule 1 (Berline Noir - venant de la gauche)
    final vehicle1X = (size.width * 0.7 * progress) - 40;
    final vehicle1Y = size.height * 0.42;

    // Ombre du véhicule 1
    paint.color = Colors.black.withValues(alpha: 0.3);
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(vehicle1X + 2, vehicle1Y + 2, 70, 35),
        ,
      paint,
    );

    // Corps du véhicule 1
    paint.color = Colors.black;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(vehicle1X, vehicle1Y, 70, 35),
        ,
      paint,
    );

    // Fenêtres véhicule 1
    paint.color = Colors.blue[200]!;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(vehicle1X + 5, vehicle1Y + 5, 60, 25),
        ,
      paint,
    );

    // Véhicule 2 (Citadine Bleu - venant du haut)
    final vehicle2X = size.width * 0.47;
    final vehicle2Y = (size.height * 0.7 * progress) - 40;

    // Ombre du véhicule 2
    paint.color = Colors.black.withValues(alpha: 0.3);
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(vehicle2X + 2, vehicle2Y + 2, 35, 70),
        ,
      paint,
    );

    // Corps du véhicule 2
    paint.color = Colors.blue[700]!;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(vehicle2X, vehicle2Y, 35, 70),
        ,
      paint,
    );

    // Fenêtres véhicule 2
    paint.color = Colors.blue[200]!;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(vehicle2X + 5, vehicle2Y + 5, 25, 60),
        ,
      paint,
    );

    // Point d
