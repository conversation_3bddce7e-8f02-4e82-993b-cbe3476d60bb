import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/material.dart';

import '../../../../core/theme/modern_theme.dart';
import '../../services/dashboard_data_service.dart';

/// 📊 Provider pour les statistiques générales
final dashboardStatsProvider = FutureProvider<Map<String, int>>((ref) async {
  return await DashboardDataService.getGeneralStats();
});

/// 🕒 Provider pour l'activité récente
final recentActivityProvider = FutureProvider<List<Map<String, dynamic>>>((ref) async {
  return await DashboardDataService.getRecentActivity();
});

/// 📈 Provider pour les statistiques détaillées
final detailedStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  return await DashboardDataService.getDetailedStats();
});

/// 🔄 Provider pour les statistiques en temps réel (stream)
final statsStreamProvider = StreamProvider<Map<String, int>>((ref) {
  return DashboardDataService.getStatsStream();
});

/// 🔄 Provider pour l'activité en temps réel (stream)
final activityStreamProvider = StreamProvider<List<Map<String, dynamic>>>((ref) {
  return DashboardDataService.getActivityStream();
});

/// 📊 Provider pour calculer les tendances
final statsTrendsProvider = Provider<Map<String, String>>((ref) {
  final detailedStats = ref.watch(detailedStatsProvider);
  
  return detailedStats.when(
    data: (stats) {
      final weekly = stats['weekly'] as Map<String, dynamic>? ?? {};
      final monthly = stats['monthly'] as Map<String, dynamic>? ?? {};
      
      // Calculer les tendances (simulation basée sur les données)
      final weeklyRequests = weekly['requests'] as int? ?? 0;
      final monthlyRequests = monthly['requests'] as int? ?? 0;
      final weeklyClaims = weekly['claims'] as int? ?? 0;
      final monthlyClaims = monthly['claims'] as int? ?? 0;
      
      return {
        'users': weeklyRequests > 5 ? '+${weeklyRequests}%' : '+${weeklyRequests}',
        'agencies': monthlyRequests > 10 ? '+3%' : '+1',
        'claims': weeklyClaims > 0 ? '-${weeklyClaims}%' : '0',
        'pending': weeklyRequests > 0 ? '+${weeklyRequests}' : '0',
      };
    },
    loading: () => {
      'users': '...',
      'agencies': '...',
      'claims': '...',
      'pending': '...',
    },
    error: (_, __) => {
      'users': 'N/A',
      'agencies': 'N/A',
      'claims': 'N/A',
      'pending': 'N/A',
    },
  );
});

/// 🎯 Provider pour les actions rapides avec compteurs réels
final quickActionsProvider = Provider<List<Map<String, dynamic>>>((ref) {
  final stats = ref.watch(dashboardStatsProvider);
  
  return stats.when(
    data: (data) => [
      {
        'title': 'Initialiser Collections',
        'icon': Icons.storage_rounded,
        'color': Colors.purple.shade600,
        'count': null,
      },
      {
        'title': 'Test Alternative',
        'icon': Icons.science_rounded,
        'color': Colors.orange.shade600,
        'count': null,
      },
      {
        'title': 'Import CSV Assurance',
        'icon': Icons.upload_file_rounded,
        'color': Colors.teal.shade600,
        'count': null,
      },
      {
        'title': 'CSV Simplifié',
        'icon': Icons.table_chart_rounded,
        'color': Colors.cyan.shade600,
        'count': null,
      },
      {
        'title': 'Diagnostic Firestore',
        'icon': Icons.healing_rounded,
        'color': Colors.red.shade600,
        'count': null,
      },
      {
        'title': 'Contournement',
        'icon': Icons.build_circle_rounded,
        'color': Colors.amber.shade600,
        'count': null,
      },
      {
        'title': 'Gestion Compagnies',
        'icon': Icons.domain_rounded,
        'color': ModernTheme.primaryColor,
        'count': data['total_companies'] ?? 0,
      },
      {
        'title': 'Gestion Agences',
        'icon': Icons.business_rounded,
        'color': Colors.blue.shade600,
        'count': data['total_agencies'] ?? 0,
      },
      {
        'title': 'Valider Demandes',
        'icon': Icons.check_circle_rounded,
        'color': ModernTheme.successColor,
        'count': data['pending_requests'] ?? 0,
      },
      {
        'title': 'Voir Rapports',
        'icon': Icons.assessment_rounded,
        'color': ModernTheme.secondaryColor,
        'count': data['total_claims'] ?? 0,
      },
      {
        'title': 'Nouvelle Agence',
        'icon': Icons.add_business_rounded,
        'color': ModernTheme.accentColor,
        'count': null,
      },
    ],
    loading: () => [
      {
        'title': 'Initialiser Collections',
        'icon': Icons.storage_rounded,
        'color': Colors.purple.shade600,
        'count': null,
      },
      {
        'title': 'Test Alternative',
        'icon': Icons.science_rounded,
        'color': Colors.orange.shade600,
        'count': null,
      },
      {
        'title': 'Import CSV Assurance',
        'icon': Icons.upload_file_rounded,
        'color': Colors.teal.shade600,
        'count': null,
      },
      {
        'title': 'CSV Simplifié',
        'icon': Icons.table_chart_rounded,
        'color': Colors.cyan.shade600,
        'count': null,
      },
      {
        'title': 'Diagnostic Firestore',
        'icon': Icons.healing_rounded,
        'color': Colors.red.shade600,
        'count': null,
      },
      {
        'title': 'Contournement',
        'icon': Icons.build_circle_rounded,
        'color': Colors.amber.shade600,
        'count': null,
      },
      {
        'title': 'Gestion Compagnies',
        'icon': Icons.domain_rounded,
        'color': ModernTheme.primaryColor,
        'count': '...',
      },
      {
        'title': 'Gestion Agences',
        'icon': Icons.business_rounded,
        'color': Colors.blue.shade600,
        'count': '...',
      },
      {
        'title': 'Valider Demandes',
        'icon': Icons.check_circle_rounded,
        'color': ModernTheme.successColor,
        'count': '...',
      },
      {
        'title': 'Voir Rapports',
        'icon': Icons.assessment_rounded,
        'color': ModernTheme.secondaryColor,
        'count': '...',
      },
      {
        'title': 'Nouvelle Agence',
        'icon': Icons.add_business_rounded,
        'color': ModernTheme.accentColor,
        'count': null,
      },
    ],
    error: (_, __) => [
      {
        'title': 'Gestion Compagnies',
        'icon': Icons.domain,
        'color': Colors.blue,
        'count': 'Err',
      },
      {
        'title': 'Gestion Agences',
        'icon': Icons.business,
        'color': Colors.blue.shade600,
        'count': 'Err',
      },
      {
        'title': 'Valider Demandes',
        'icon': Icons.check_circle,
        'color': Colors.green,
        'count': 'Err',
      },
      {
        'title': 'Voir Rapports',
        'icon': Icons.assessment,
        'color': Colors.purple,
        'count': 'Err',
      },
      {
        'title': 'Nouvelle Agence',
        'icon': Icons.add_business,
        'color': Colors.grey,
        'count
