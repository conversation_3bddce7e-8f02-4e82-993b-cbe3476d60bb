import 'package:cloud_firestore/cloud_firestore.dart';

/// 📋 Modèle pour un contrat d'assurance
class ContractAssurance {
  final String id;
  final String numeroContrat; // Numéro unique du contrat
  final String compagnieId; // 🔗 Lien vers la compagnie
  final String agenceId; // 🔗 Lien vers l'agence
  final String conducteurId; // 🔗 Lien vers le conducteur
  final String vehiculeId; // 🔗 Lien vers le véhicule
  final String agentId; // 🔗 Agent qui a créé le contrat
  final DateTime dateDebut;
  final DateTime dateFin;
  final DateTime dateCreation;
  final String typeAssurance; // tous_risques, tiers, vol_incendie
  final double montantPrime;
  final String statutPaiement; // paye, impaye, partiel
  final bool isActive;
  final Map<String, dynamic>? metadata;

  ;

  /// 🔄 Conversion depuis Firestore
  factory ContractAssurance.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ContractAssurance(
      id: doc.id,
      numeroContrat: data['numeroContrat'] ?? 'Contenu',
      compagnieId: data['compagnieId'] ?? 'Contenu',
      agenceId: data['agenceId'] ?? 'Contenu',
      conducteurId: data['conducteurId'] ?? 'Contenu',
      vehiculeId: data['vehiculeId'] ?? 'Contenu',
      agentId: data['agentId'] ?? 'Contenu',
      dateDebut: (data['dateDebut'] as Timestamp?)?.toDate() ?? DateTime.now(),
      dateFin: (data['dateFin'] as Timestamp?)?.toDate() ?? DateTime.now(),
      dateCreation: (data['dateCreation'] as Timestamp?)?.toDate() ?? DateTime.now(),
      typeAssurance: data['typeAssurance'] ?? 'Contenu',
      montantPrime: (data['montantPrime'] ?? 0).toDouble(),
      statutPaiement: data['statutPaiement'] ?? 'impaye',
      isActive: data['isActive'] ?? true,
      metadata: data['metadata'],
    );
  }

  /// 🔄 Conversion vers Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'numeroContrat': numeroContrat,
      'compagnieId': compagnieId,
      'agenceId': agenceId,
      'conducteurId': conducteurId,
      'vehiculeId': vehiculeId,
      'agentId': agentId,
      'dateDebut': Timestamp.fromDate(dateDebut),
      'dateFin': Timestamp.fromDate(dateFin),
      'dateCreation': Timestamp.fromDate(dateCreation),
      'typeAssurance': typeAssurance,
      'montantPrime': montantPrime,
      'statutPaiement': statutPaiement,
      'isActive': isActive,
      'metadata': metadata,
    };
  }

  /// 🎯 Vérifications d'état
  bool get isExpired => DateTime.now().isAfter(dateFin);
  bool get isValid => isActive && !isExpired;
  bool get isPaid => statutPaiement == 'paye';

  /// 📋 Copie avec modifications
  ContractAssurance copyWith({
    String? id,
    String? numeroContrat,
    String? compagnieId,
    String? agenceId,
    String? conducteurId,
    String? vehiculeId,
    String? agentId,
    DateTime? dateDebut,
    DateTime? dateFin,
    DateTime? dateCreation,
    String? typeAssurance,
    double? montantPrime,
    String? statutPaiement,
    bool? isActive,
    Map<String, dynamic>? metadata,
  }) {
    return ContractAssurance(
      id: id ?? this.id,
      numeroContrat: numeroContrat ?? this.numeroContrat,
      compagnieId: compagnieId ?? this.compagnieId,
      agenceId: agenceId ?? this.agenceId,
      conducteurId: conducteurId ?? this.conducteurId,
      vehiculeId: vehiculeId ?? this.vehiculeId,
      agentId: agentId ?? this.agentId,
      dateDebut: dateDebut ?? this.dateDebut,
      dateFin: dateFin ?? this.dateFin,
      dateCreation: dateCreation ?? this.dateCreation,
      typeAssurance: typeAssurance ?? this.typeAssurance,
      montantPrime: montantPrime ?? this.montantPrime,
      statutPaiement: statutPaiement ?? this.statutPaiement,
      isActive: isActive ?? this.isActive,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'ContractAssurance(id: $id, numeroContrat: $numeroContrat, compagnieId: $compagnieId, agenceId: $agenceId)
