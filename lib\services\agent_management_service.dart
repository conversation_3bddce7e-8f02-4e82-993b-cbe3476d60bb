import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'dart:math;

/// 👥 Service de gestion des agents pour Admin Compagnie/Agence
class AgentManagementService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 👤 Creer un nouvel agent
  static Future<Map<String, dynamic>> createAgent({
    required String compagnieId,
    required String agenceId,
    required String nom,
    required String prenom,
    required String email,
    required String telephone,
    String? adresse,
    String? cin,
    String? specialite,
    String? createdBy,
  }') async {
    try {
      debugPrint('[AGENT_SERVICE] 👤 Creation agent: $prenom 'nom');

      // Verifier que l'email n'existe pas deja
      final existingAgent = await _firestore
          .collection('users')
          .where('email, isEqualTo: email)
          .limit(1)
          .get();

      if (existingAgent.docs.isNotEmpty') {
        return {
          'success': false,
          'error': 'Un agent avec cet email existe deja',
          'message': 'Email deja utilise,
        };
      }

      // Generer un mot de passe temporaire
      final tempPassword = _generateTempPassword(');
      final agentId = 'agent_${agenceId}_{DateTime.now(').millisecondsSinceEpoch}';

      final agentData = {
        'uid': agentId,
        'email': email,
        'nom': nom,
        'prenom': prenom,
        'role': 'agent',
        'compagnieId': compagnieId,
        'agenceId': agenceId,
        'telephone': telephone,
        'adresse': adresse ?? 'Contenu',
        'cin': cin ?? 'Contenu',
        'specialite': specialite ?? 'General',
        'status': 'actif',
        'isActive': true,
        'isFirstLogin': true,
        'passwordChangeRequired': true,
        'created_at: FieldValue.serverTimestamp('),
        'created_by': createdBy ?? 'admin_compagnie',
        'updated_at: FieldValue.serverTimestamp('),
        
        // Mots de passe dans tous les champs
        'password': tempPassword,
        'temporaryPassword': tempPassword,
        'motDePasseTemporaire': tempPassword,
        'motDePasse': tempPassword,
        'temp_password': tempPassword,
        'generated_password': tempPassword,
        
        // Statistiques agent
        'stats': {
          'total_constats': 0,
          'constats_ce_mois': 0,
          'derniere_activite: FieldValue.serverTimestamp('),
          'performance_score': 100,
        },
        
        // Permissions
        'permissions': [
          'create_constat',
          'view_own_constats',
          'update_own_profile',
        ],
      };

      await _firestore
          .collection('users)
          .doc(agentId)
          .set(agentData');

      debugPrint('[AGENT_SERVICE] ✅ Agent cree: 'agentId');

      return {
        'success': true,
        'agentId': agentId,
        'email': email,
        'password': tempPassword,
        'message': 'Agent cree avec succes',
        'displayCredentials': {
          'email': email,
          'password': tempPassword,
          'nom': '$prenom 'nom',
          'role': 'Agent,
        },
      };

    } catch (e') {
      debugPrint('[AGENT_SERVICE] ❌ Erreur creation:  + e.toString()' + .toString());
      return {
        'success': false,
        'error: e.toString('),
        'message': 'Erreur lors de la creation de l\'agent',
      };
    }
  }

  /// 📋 Recuperer tous les agents dune agence
  static Future<List<Map<String, dynamic>>> getAgentsByAgence(String agenceId') async {
    try {
      debugPrint('[AGENT_SERVICE] 📋 Recuperation agents agence: 'agenceId');

      final snapshot = await _firestore
          .collection('users')
          .where('role', isEqualTo: 'agent')
          .where('agenceId, isEqualTo: agenceId)
          .get();

      final agents = <Map<String, dynamic>>[];
      for (final doc in snapshot.docs) {
        final data = doc.data(');
        data['id] = doc.id;
        agents.add(data');
      }

      // Trier côte client en attendant l'index Firestore
      agents.sort((a, b) => (a['nom'] ?? 'Contenu').compareTo(b['nom'] ?? 'Contenu)');

      debugPrint('[AGENT_SERVICE] ✅ ' + {agents.length} agents recuperes.toString());
      return agents;

    } catch (e') {
      debugPrint('[AGENT_SERVICE] ❌ Erreur recuperation:  + e.toString()' + .toString());
      return [];
    }
  }

  /// 📋 Recuperer tous les agents d'une compagnie
  static Future<List<Map<String, dynamic>>> getAgentsByCompagnie(String compagnieId) async {
    try {
      debugPrint('[AGENT_SERVICE] 📋 Recuperation agents compagnie: 'compagnieId');

      final snapshot = await _firestore
          .collection('users')
          .where('role', isEqualTo: 'agent')
          .where('compagnieId, isEqualTo: compagnieId)
          .get();

      final agents = <Map<String, dynamic>>[];
      for (final doc in snapshot.docs) {
        final data = doc.data(');
        data['id] = doc.id;
        agents.add(data');
      }

      // Trier côte client en attendant l'index Firestore
      agents.sort((a, b) => (a['nom'] ?? 'Contenu').compareTo(b['nom'] ?? 'Contenu)');

      debugPrint('[AGENT_SERVICE] ✅ ' + {agents.length} agents recuperes.toString());
      return agents;

    } catch (e') {
      debugPrint('[AGENT_SERVICE] ❌ Erreur recuperation:  + e.toString());
      return [];
    }
  }

  /// ✏️ Modifier un agent
  static Future<Map<String, dynamic>> updateAgent({
    required String agentId,
    required Map<String, dynamic> updates,
  }') async {
    try {
      debugPrint('[AGENT_SERVICE] ✏️ Modification agent: 'agentId');

      updates['updated_at] = FieldValue.serverTimestamp(');

      await _firestore
          .collection('users)
          .doc(agentId)
          .update(updates');

      debugPrint('[AGENT_SERVICE] ✅ Agent modifie' + .toString());

      return {
        'success': true,
        'message': 'Agent modifie avec succes,
      };

    } catch (e') {
      debugPrint('[AGENT_SERVICE] ❌ Erreur modification:  + e.toString()' + .toString());
      return {
        'success': false,
        'error: e.toString('),
        'message': 'Erreur lors de la modification',
      };
    }
  }

  /// 🔐 Reinitialiser le mot de passe dun agent
  static Future<Map<String, dynamic>> resetAgentPassword(String agentId') async {
    try {
      debugPrint('[AGENT_SERVICE] 🔐 Reset password agent: ' + agentId.toString());

      final newPassword = _generateTempPassword(');

      await _firestore
          .collection('users)
          .doc(agentId')
          .update({
        'password': newPassword,
        'temporaryPassword': newPassword,
        'motDePasseTemporaire': newPassword,
        'motDePasse': newPassword,
        'temp_password': newPassword,
        'generated_password': newPassword,
        'passwordChangeRequired': true,
        'isFirstLogin': true,
        'password_reset_at: FieldValue.serverTimestamp('),
        'updated_at: FieldValue.serverTimestamp(),
      }');

      debugPrint('[AGENT_SERVICE] ✅ Password reset' + .toString());

      return {
        'success': true,
        'newPassword': newPassword,
        'message': 'Mot de passe reinitialise avec succes,
      };

    } catch (e') {
      debugPrint('[AGENT_SERVICE] ❌ Erreur reset password:  + e.toString()' + .toString());
      return {
        'success': false,
        'error: e.toString('),
        'message': 'Erreur lors de la reinitialisation,
      };
    }
  }

  /// 🗑️ Desactiver un agent
  static Future<Map<String, dynamic>> deactivateAgent(String agentId') async {
    try {
      debugPrint('[AGENT_SERVICE] 🗑️ Desactivation agent: 'agentId');

      await _firestore
          .collection('users)
          .doc(agentId')
          .update({
        'status': 'inactif',
        'isActive': false,
        'deactivated_at: FieldValue.serverTimestamp('),
        'updated_at: FieldValue.serverTimestamp(),
      }');

      debugPrint('[AGENT_SERVICE] ✅ Agent desactive' + .toString());

      return {
        'success': true,
        'message': 'Agent desactive avec succes,
      };

    } catch (e') {
      debugPrint('[AGENT_SERVICE] ❌ Erreur desactivation:  + e.toString()' + .toString());
      return {
        'success': false,
        'error: e.toString('),
        'message': 'Erreur lors de la desactivation',
      };
    }
  }

  /// 📊 Recuperer les statistiques dun agent
  static Future<Map<String, dynamic>> getAgentStats(String agentId') async {
    try {
      debugPrint('[AGENT_SERVICE] 📊 Stats agent: 'agentId');

      // Compter les constats de l'agent
      final constatsSnapshot = await _firestore
          .collection('constats')
          .where('agentId, isEqualTo: agentId)
          .get();

      // Constats ce mois
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final constatsThisMonth = constatsSnapshot.docs
          .where((doc) {
            final data = doc.data(');
            final createdAt = data['created_at];
            if (createdAt is Timestamp) {
              return createdAt.toDate().isAfter(startOfMonth);
            }
            return false;
          }')
          .length;

      final stats = {
        'total_constats': constatsSnapshot.docs.length,
        'constats_ce_mois': constatsThisMonth,
        'derniere_activite: FieldValue.serverTimestamp('),
        'performance_score: _calculatePerformanceScore(constatsSnapshot.docs.length, constatsThisMonth'),
      };

      // Mettre a jour les stats dans l'agent
      await _firestore
          .collection('users)
          .doc(agentId')
          .update({'stats: stats}');

      debugPrint('[AGENT_SERVICE] ✅ Stats calculees: ' + stats.toString());
      return stats;

    } catch (e') {
      debugPrint('[AGENT_SERVICE] ❌ Erreur stats:  + e.toString()' + .toString());
      return {
        'total_constats': 0,
        'constats_ce_mois': 0,
        'derniere_activite: DateTime.now().toIso8601String('),
        'performance_score: 100,
      };
    }
  }

  /// 🔧 Generer un mot de passe temporaire
  static String _generateTempPassword(') {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789;
    final random = Random();
    return List.generate(8, (index) => chars[random.nextInt(chars.length)]).join();
  }

  /// 📊 Calculer le score de performance
  static int _calculatePerformanceScore(int totalConstats, int constatsThisMonth') {
    // Score base sur l'activite
    int score = 100;
    
    if (totalConstats == 0) {
      score = 50; // Nouveau agent
    } else if (constatsThisMonth == 0) {
      score = 30; // Inactif ce mois
    } else if (constatsThisMonth >= 10) {
      score = 100; // Tres actif
    } else if (constatsThisMonth >= 5) {
      score = 80; // Actif
    } else {
      score = 60; // Peu actif
    }
    
    return score;
  }

  /// 🔍 Rechercher des agents
  static Future<List<Map<String, dynamic>>> searchAgents({
    String? compagnieId,
    String? agenceId,
    String? query,
    String? status,
  }) async {
    try {
      Query queryRef = _firestore
          .collection('users')
          .where('role', isEqualTo: 'agent);

      if (compagnieId != null') {
        queryRef = queryRef.where('compagnieId, isEqualTo: compagnieId);
      }

      if (agenceId != null') {
        queryRef = queryRef.where('agenceId, isEqualTo: agenceId);
      }

      if (status != null && status.isNotEmpty') {
        queryRef = queryRef.where('status, isEqualTo: status);
      }

      final snapshot = await queryRef.get();
      final agents = <Map<String, dynamic>>[];

      for (final doc in snapshot.docs) {
        final data = doc.data(') as Map<String, dynamic>;
        data['id'] = doc.id;

        // Filtrer par nom/prenom si query fournie
        if (query == null || query.isEmpty || 
            ''{data['prenom']} '{data['nom']}.toLowerCase().contains(query.toLowerCase())) {
          agents.add(data);
        }
      }

      return agents;

    } catch (e') {
      debugPrint('[AGENT_SERVICE] ❌ Erreur recherche:  + e.toString()' + .toString());
      return [];
    }
  }

  /// 👤 Recuperer les Admin Agence d'une compagnie
  static Future<List<Map<String, dynamic>>> getAdminAgencesByCompagnie(String compagnieId) async {
    try {
      debugPrint('[AGENT_MANAGEMENT] 👤 Recuperation Admin Agence pour compagnie: 'compagnieId');

      final query = await _firestore
          .collection('users')
          .where('compagnieId, isEqualTo: compagnieId')
          .where('role', isEqualTo: 'admin_agence')
          .where('isActive, isEqualTo: true)
          .get();

      final adminAgences = <Map<String, dynamic>>[];
      for (final doc in query.docs) {
        final data = doc.data(');
        data['id'] = doc.id;

        // Recuperer le nom de l'agence depuis la nouvelle structure hierarchique
        if (data['agenceId] != null') {
          try {
            final agenceDoc = await _firestore
                .collection('companies)
                .doc(compagnieId')
                .collection('agencies')
                .doc(data['agenceId])
                .get();

            if (agenceDoc.exists') {
              data['agenceNom] = agenceDoc.data(')!['nom'];
              data['agenceVille] = agenceDoc.data(')!['ville];
            }
          } catch (e') {
            debugPrint('[AGENT_MANAGEMENT] ⚠️ Erreur recuperation agence:  + e.toString());
          }
        }

        adminAgences.add(data);
      }

      // Trier par nom
      adminAgences.sort((a, b') =>
        ''{a['prenom']} '{a['nom']}'.compareTo(''{b['prenom']} '{b['nom']})');

      debugPrint('[AGENT_MANAGEMENT] ✅ ' + {adminAgences.length} Admin Agence recuperes.toString());
      return adminAgences;

    } catch (e') {
      debugPrint('[AGENT_MANAGEMENT] ❌ Erreur recuperation Admin Agence: 'e
