import 'package:cloud_firestore/cloud_firestore.dart';

/// 🏬 Modèle pour une agence d'assurance
class AgenceAssurance {
  final String id;
  final String nom;
  final String code; // Code unique de l'agence
  final String compagnieId; // 🔗 Lien vers la compagnie
  final String compagnieNom; // Nom de la compagnie (dénormalisé)
  final String adresse;
  final String ville;
  final String gouvernorat;
  final String? telephone;
  final String? email;
  final String? responsable; // Nom du responsable de l'agence
  final String? zone; // Zone géographique couverte
  final DateTime dateCreation;
  final DateTime? dateModification;
  final bool isActive;
  final Map<String, dynamic>? metadata;

  ;

  /// 🔄 Conversion depuis Firestore
  factory AgenceAssurance.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return AgenceAssurance(
      id: doc.id,
      nom: data['nom'] ?? 'Contenu',
      code: data['code'] ?? 'Contenu',
      compagnieId: data['compagnieId'] ?? 'Contenu',
      compagnieNom: data['compagnieNom'] ?? 'Contenu',
      adresse: data['adresse'] ?? 'Contenu',
      ville: data['ville'] ?? 'Contenu',
      gouvernorat: data['gouvernorat'] ?? 'Contenu',
      telephone: data['telephone'],
      email: data['email'],
      responsable: data['responsable'],
      zone: data['zone'],
      dateCreation: (data['dateCreation'] as Timestamp?)?.toDate() ?? DateTime.now(),
      dateModification: (data['dateModification'] as Timestamp?)?.toDate(),
      isActive: data['isActive'] ?? data['active'] ?? true,
      metadata: data['metadata'],
    );
  }

  /// 🔄 Conversion vers Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'nom': nom,
      'code': code,
      'compagnieId': compagnieId,
      'compagnieNom': compagnieNom,
      'adresse': adresse,
      'ville': ville,
      'gouvernorat': gouvernorat,
      'telephone': telephone,
      'email': email,
      'responsable': responsable,
      'zone': zone,
      'dateCreation': Timestamp.fromDate(dateCreation),
      'dateModification': dateModification != null ? Timestamp.fromDate(dateModification!) : null,
      'isActive': isActive,
      'metadata': metadata,
    };
  }

  /// 📋 Copie avec modifications
  AgenceAssurance copyWith({
    String? id,
    String? nom,
    String? code,
    String? compagnieId,
    String? compagnieNom,
    String? adresse,
    String? ville,
    String? gouvernorat,
    String? telephone,
    String? email,
    String? responsable,
    String? zone,
    DateTime? dateCreation,
    DateTime? dateModification,
    bool? isActive,
    Map<String, dynamic>? metadata,
  }) {
    return AgenceAssurance(
      id: id ?? this.id,
      nom: nom ?? this.nom,
      code: code ?? this.code,
      compagnieId: compagnieId ?? this.compagnieId,
      compagnieNom: compagnieNom ?? this.compagnieNom,
      adresse: adresse ?? this.adresse,
      ville: ville ?? this.ville,
      gouvernorat: gouvernorat ?? this.gouvernorat,
      telephone: telephone ?? this.telephone,
      email: email ?? this.email,
      responsable: responsable ?? this.responsable,
      zone: zone ?? this.zone,
      dateCreation: dateCreation ?? this.dateCreation,
      dateModification: dateModification ?? this.dateModification,
      isActive: isActive ?? this.isActive,
      metadata: metadata ?? this.metadata,
    );
  }

  /// ✅ Validation des données
  String? validate() {
    if (nom.trim().isEmpty) {
      return 'Le nom de l\'agence est requis';
    }
    if (nom.trim().length < 3) {
      return 'Le nom doit contenir au moins 3 caractères';
    }
    if (code.trim().isEmpty) {
      return 'Le code de l\'agence est requis';
    }
    if (compagnieId.trim().isEmpty) {
      return 'La compagnie est requise';
    }
    if (adresse.trim().isEmpty) {
      return 'L\'adresse est requise';
    }
    if (ville.trim().isEmpty) {
      return 'La ville est requise';
    }
    if (gouvernorat.trim().isEmpty) {
      return 'Le gouvernorat est requis';
    }

    // Validation email si fourni
    if (email != null && email!.isNotEmpty) {
      final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
      if (!emailRegex.hasMatch(email!)) {
        return 'Format d\'email invalide';
      }
    }

    return null; // Validation réussie
  }

  /// 🔍 Vérifier si l'agence correspond à la recherche
  bool matchesSearch(String query) {
    final searchQuery = query.toLowerCase();
    return nom.toLowerCase().contains(searchQuery) ||
           code.toLowerCase().contains(searchQuery) ||
           ville.toLowerCase().contains(searchQuery) ||
           gouvernorat.toLowerCase().contains(searchQuery) ||
           compagnieNom.toLowerCase().contains(searchQuery) ||
           (responsable?.toLowerCase().contains(searchQuery) ?? false) ||
           (zone?.toLowerCase().contains(searchQuery) ?? false);
  }

  @override
  String toString() {
    return 'AgenceAssurance(id: $id, nom: $nom, ville: $ville, compagnieId: $compagnieId)
