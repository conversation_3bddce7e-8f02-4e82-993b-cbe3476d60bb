import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

/// 🌐 Gestionnaire de connexion Firestore avec retry et fallback
class FirestoreConnectionManager {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static bool _isOptimized = false;

  /// 🔧 Optimiser et réparer la connexion Firestore
  static Future<bool> optimizeAndRepairConnection() async {
    if (_isOptimized) return true;

    try {
      debugPrint('[FIRESTORE_MANAGER] 🔧 === OPTIMISATION CONNEXION ===');

      // Étape 1: Vérifier la connectivité Internet
      final hasInternet = await _checkInternetConnectivity();
      if (!hasInternet) {
        debugPrint('[FIRESTORE_MANAGER] ❌ Pas de connexion Internet');
        return false;
      }

      // Étape 2: Redémarrer la connexion Firestore
      await _restartFirestoreConnection();

      // Étape 3: Configurer les paramètres optimaux
      await _configureOptimalSettings();

      // Étape 4: Test de connexion
      final connectionWorks = await _testFirestoreConnection();

      _isOptimized = connectionWorks;
      debugPrint('[FIRESTORE_MANAGER] ${connectionWorks ? '✅' : '❌'} Optimisation ${connectionWorks ? 'réussie' : 'échouée'}');

      return connectionWorks;

    } catch (e) {
      debugPrint('[FIRESTORE_MANAGER] ❌ Erreur optimisation: $e');
      return false;
    }
  }

  /// 🌐 Vérifier la connectivité Internet
  static Future<bool> _checkInternetConnectivity() async {
    try {
      final connectivityResults = await Connectivity().checkConnectivity();
      final hasConnection = !connectivityResults.contains(ConnectivityResult.none) && connectivityResults.isNotEmpty;

      debugPrint('[FIRESTORE_MANAGER] 🌐 Internet: ${hasConnection ? 'OK' : 'KO'} ($connectivityResults)');
      return hasConnection;
    } catch (e) {
      debugPrint('[FIRESTORE_MANAGER] ❌ Erreur vérification Internet: $e');
      return false;
    }
  }

  /// 🔄 Redémarrer la connexion Firestore
  static Future<void> _restartFirestoreConnection() async {
    try {
      debugPrint('[FIRESTORE_MANAGER] 🔄 Redémarrage connexion Firestore...');

      // Désactiver puis réactiver la connexion
      await _firestore.disableNetwork();
      await Future.delayed(const Duration(seconds: 2));
      await _firestore.enableNetwork();
      await Future.delayed(const Duration(seconds: 1));

      debugPrint('[FIRESTORE_MANAGER] ✅ Connexion redémarrée');
    } catch (e) {
      debugPrint('[FIRESTORE_MANAGER] ⚠️ Erreur redémarrage: $e');
    }
  }

  /// ⚙️ Configurer les paramètres optimaux
  static Future<void> _configureOptimalSettings() async {
    try {
      debugPrint('[FIRESTORE_MANAGER] ⚙️ Configuration paramètres optimaux...');

      _firestore.settings = ;

      debugPrint('[FIRESTORE_MANAGER] ✅ Paramètres configurés');
    } catch (e) {
      debugPrint('[FIRESTORE_MANAGER] ⚠️ Erreur configuration: $e');
    }
  }

  /// 🧪 Tester la connexion Firestore
  static Future<bool> _testFirestoreConnection() async {
    try {
      debugPrint('[FIRESTORE_MANAGER] 🧪 Test connexion Firestore...');

      // Test simple avec timeout court
      final testDoc = _firestore.collection('connection_test').doc('test');
      
      await testDoc.set({
        'test': true,
        'timestamp': FieldValue.serverTimestamp(),
      }).timeout(const Duration(seconds: 8));

      // Vérifier la lecture
      final readDoc = await testDoc.get().timeout(const Duration(seconds: 5));
      
      if (readDoc.exists) {
        // Nettoyer
        await testDoc.delete().timeout(const Duration(seconds: 3));
        debugPrint('[FIRESTORE_MANAGER] ✅ Test connexion réussi');
        return true;
      } else {
        debugPrint('[FIRESTORE_MANAGER] ❌ Document test non trouvé');
        return false;
      }

    } catch (e) {
      debugPrint('[FIRESTORE_MANAGER] ❌ Test connexion échoué: $e');
      return false;
    }
  }

  /// 🚀 Créer un document avec retry intelligent
  static Future<bool> createDocumentWithRetry({
    required String collection,
    required String documentId,
    required Map<String, dynamic> data,
    int maxRetries = 3,
  }) async {
    
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        debugPrint('[FIRESTORE_MANAGER] 🚀 Création document $documentId (tentative $attempt/$maxRetries)');

        // Optimiser la connexion avant chaque tentative
        if (attempt > 1) {
          await optimizeAndRepairConnection();
        }

        // Créer le document avec timeout progressif
        final timeoutDuration = Duration(seconds: 5 + (attempt * 3));
        await _firestore
            .collection(collection)
            .doc(documentId)
            .set(data, SetOptions(merge: true))
            .timeout(timeoutDuration);

        debugPrint('[FIRESTORE_MANAGER] ✅ Document $documentId créé (tentative $attempt)');
        return true;

      } catch (e) {
        debugPrint('[FIRESTORE_MANAGER] ❌ Tentative $attempt échouée pour $documentId: $e');

        if (attempt == maxRetries) {
          debugPrint('[FIRESTORE_MANAGER] 💥 Échec définitif pour $documentId');
          return false;
        }

        // Attendre avant la prochaine tentative avec backoff exponentiel
        final waitTime = Duration(seconds: attempt * 2);
        debugPrint('[FIRESTORE_MANAGER] ⏳ Attente ${waitTime.inSeconds}s avant nouvelle tentative...');
        await Future.delayed(waitTime);
      }
    }

    return false;
  }

  /// 📊 Obtenir le statut de la connexion
  static Future<Map<String, dynamic>> getConnectionStatus() async {
    try {
      final hasInternet = await _checkInternetConnectivity();
      final firestoreWorks = await _testFirestoreConnection();

      return {
        'internet': hasInternet,
        'firestore': firestoreWorks,
        'optimized': _isOptimized,
        'overall_status': hasInternet && firestoreWorks ? 'OK' : 'PROBLEM',
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'internet': false,
        'firestore': false,
        'optimized': false,
        'overall_status': 'ERROR',
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// 🔄 Forcer une nouvelle optimisation
  static Future<bool> forceReoptimization() async {
    _isOptimized = false;
    return await optimizeAndRepairConnection();
  }

  /// 📱 Créer les admins avec le gestionnaire de connexion
  static Future<Map<String, dynamic>> createAdminsWithConnectionManager() async {
    debugPrint('[FIRESTORE_MANAGER] 👥 === CRÉATION ADMINS AVEC GESTIONNAIRE ===');

    final result = {
      'success': false,
      'created_admins': <String>[],
      'failed_admins': <String>[],
      'connection_status': <String, dynamic>{},
    };

    try {
      // Optimiser la connexion d'abord
      final connectionOK = await optimizeAndRepairConnection();
      result['connection_status'] = await getConnectionStatus();

      if (!connectionOK) {
        debugPrint('[FIRESTORE_MANAGER] ❌ Connexion non disponible');
        return result;
      }

      // Créer les admins
      final adminsToCreate = [
        {
          'id': 'admin_star_connection',
          'email': '<EMAIL>',
          'compagnieId': 'star-assurance',
          'compagnieNom': 'STAR Assurance',
        },
        {
          'id': 'admin_comar_connection',
          'email': '<EMAIL>',
          'compagnieId': 'comar-assurance',
          'compagnieNom': 'COMAR Assurance',
        },
        {
          'id': 'admin_gat_connection',
          'email': '<EMAIL>',
          'compagnieId': 'gat-assurance',
          'compagnieNom': 'GAT Assurance',
        },
        {
          'id': 'admin_maghrebia_connection',
          'email': '<EMAIL>',
          'compagnieId': 'maghrebia-assurance',
          'compagnieNom': 'Maghrebia Assurance',
        },
      ];

      for (final admin in adminsToCreate) {
        final adminData = {
          'uid': admin['id']!,
          'email': admin['email']!,
          'nom': 'Admin',
          'prenom': admin['compagnieNom']!,
          'role': 'admin_compagnie',
          'status': 'actif',
          'compagnieId': admin['compagnieId']!,
          'compagnieNom': admin['compagnieNom']!,
          'created_at': FieldValue.serverTimestamp(),
          'created_by': 'connection_manager',
          'source': 'optimized_creation',
          'isLegitimate': true,
          'isActive': true,
        };

        final created = await createDocumentWithRetry(
          collection: 'users',
          documentId: admin['id']!,
          data: adminData,
        );

        if (created) {
          (result['created_admins'] as List<String>).add(admin['email']!);
        } else {
          (result['failed_admins'] as List<String>).add(admin['email']!);
        }
      }

      final successCount = (result['created_admins'] as List).length;
      result['success'] = successCount > 0;

      debugPrint('[FIRESTORE_MANAGER] 📊 Résultat: $successCount/${adminsToCreate.length} admins créés');

      return result;

    } catch (e) {
      debugPrint('[FIRESTORE_MANAGER] ❌ Erreur création admins: $e');
      result['error
