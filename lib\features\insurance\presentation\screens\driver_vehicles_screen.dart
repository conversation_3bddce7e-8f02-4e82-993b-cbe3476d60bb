import 'package:flutter/material.dart';

import '../../../auth/models/user_model.dart';
import '../../models/vehicule_assure_model.dart';
import '../../models/contrat_assurance_model.dart';
import '../../services/insurance_contract_service.dart';
import '../widgets/vehicle_contract_card.dart';
import '../widgets/add_vehicle_dialog.dart';

/// 🚗 Écran des véhicules du conducteur avec leurs contrats
class DriverVehiclesScreen extends StatefulWidget {
  final UserModel driver;
  
   ) : super(key: key);

  @override
  State<DriverVehiclesScreen> createState() => _DriverVehiclesScreenState();
}

class _DriverVehiclesScreenState extends State<DriverVehiclesScreen> {
  List<Map<String, dynamic>> _vehiculesAvecContrats = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadVehiclesWithContracts();
  }

  /// Charger les véhicules avec leurs contrats
  Future<void> _loadVehiclesWithContracts() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final vehiculesAvecContrats = await InsuranceContractService
          .getDriverVehiclesWithContracts(widget.driver.id);

      setState(() {
        _vehiculesAvecContrats = vehiculesAvecContrats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Erreur lors du chargement: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0F0F23),
      appBar: _buildModernAppBar(),
      body: _buildBody(),
      floatingActionButton: _buildAddVehicleButton(),
    );
  }

  /// AppBar moderne
  PreferredSizeWidget _buildModernAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: Container(
          padding: ,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
          ),
          child: ,
        ),
      ),
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ,
          ),
          ({_vehiculesAvecContrats.length} véhicule(s)',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
      actions: [
        IconButton(
          onPressed: _loadVehiclesWithContracts,
          icon: Container(
            padding: ,
              borderRadius: BorderRadius.circular(12),
            ),
            child: ,
          ),
        ),
        const SizedBox(width: 16),
      ],
    );
  }

  /// Corps principal
  Widget _buildBody() {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_errorMessage != null) {
      return _buildErrorState();
    }

    if (_vehiculesAvecContrats.isEmpty) {
      return _buildEmptyState();
    }

    return _buildVehiclesList();
  }

  /// État de chargement
  Widget _buildLoadingState() {
    return ),
          ),
          const SizedBox(height: 16),
          ,
          ),
        ],
      ),
    );
  }

  /// État d'erreur
  Widget _buildErrorState() {
    return (1),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
              ),
              child: ,
            ),
            const SizedBox(height: 16),
            ,
            ),
            const SizedBox(height: 8),
            ,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.const Icon(
              onPressed: _loadVehiclesWithContracts,
              icon: const Icon(Icons.info),
              label: const Text('Réessayer'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF667EEA),
                foregroundColor: Colors.white,
                padding: ,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// État vide
  Widget _buildEmptyState() {
    return (1),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF667EEA).withValues(alpha: 0.1),
                    const Color(0xFF764BA2).withValues(alpha: 0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: const Color(0xFF667EEA).withValues(alpha: 0.3),
                ),
              ),
              child: ,
                size: 64,
              ),
            ),
            const SizedBox(height: 24),
            ,
            ),
            const SizedBox(height: 8),
            ,
                fontSize: 14,
                height: 1.5,
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton.const Icon(
              onPressed: _showAddVehicleDialog,
              icon: const Icon(Icons.info),
              label: const Text('Ajouter un véhicule'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF667EEA),
                foregroundColor: Colors.white,
                padding: ,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Liste des véhicules
  Widget _buildVehiclesList() {
    return RefreshIndicator(
      onRefresh: _loadVehiclesWithContracts,
      color: const Color(0xFF667EEA),
      backgroundColor: const Color(0xFF1A1A2E),
      child: ListView.builder(
        padding:  {
          final vehiculeData = _vehiculesAvecContrats[index];
          final vehicule = vehiculeData['vehicule'] as VehiculeAssure;
          final contrat = vehiculeData['contrat'] as ContratAssurance?;
          final hasValidContract = vehiculeData['hasValidContract'] as bool;

          return (1) => _showVehicleDetails(vehicule, contrat),
              onDeclareAccident: hasValidContract 
                  ? () => _declareAccident(vehicule, contrat!)
                  : null,
            ),
          );
        },
      ),
    );
  }

  /// Bouton d'ajout de véhicule
  Widget _buildAddVehicleButton() {
    return FloatingActionButton.extended(
      onPressed: _showAddVehicleDialog,
      backgroundColor: const Color(0xFF667EEA),
      foregroundColor: Colors.white,
      icon: const Icon(Icons.info),
      label: ,
      ),
    );
  }

  /// Afficher le dialogue d'ajout de véhicule
  void _showAddVehicleDialog() {
    showDialog(
      context: context,
      builder: (context) => AddVehicleDialog(
        driverId: widget.driver.id,
        onVehicleAdded: _loadVehiclesWithContracts,
      ),
    );
  }

  /// Afficher les détails du véhicule
  void _showVehicleDetails(VehiculeAssure vehicule, ContratAssurance? contrat) {
    // TODO: Implémenter l'écran de détails du véhicule
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: ({vehicule.modele}'),
        backgroundColor: const Color(0xFF667EEA),
      ),
    );
  }

  /// Déclarer un accident
  void _declareAccident(VehiculeAssure vehicule, ContratAssurance contrat) {
    // TODO: Naviguer vers l'écran de déclaration d'accident
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: ({vehicule.modele}
