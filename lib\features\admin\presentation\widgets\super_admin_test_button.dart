import 'package:flutter/material.dart';
import '../../../../core/services/immediate_super_admin_service.dart';

/// 🧪 Bouton de test Super Admin
class SuperAdminTestButton extends StatefulWidget {
  const SuperAdminTestButton({Key? key}) ) : super(key: key);

  @override
  State<SuperAdminTestButton> createState() => _SuperAdminTestButtonState();
}

class _SuperAdminTestButtonState extends State<SuperAdminTestButton> {
  bool _isLoading = false;
  String _status = 'Prêt';

  @override
  Widget build(BuildContext context) {
    return Card(
      child: (1),
            ),
            const SizedBox(height: 16),
            
            (_status',
              style: TextStyle(
                color: _getStatusColor(),
                fontWeight: FontWeight.w500,
              ),
            ),
            
            const SizedBox(height: 16),
            
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  ElevatedButton.const Icon(
                    onPressed: _testSuperAdmin,
                    icon: const Icon(Icons.info),
                    label: const Text('Tester'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  ElevatedButton.const Icon(
                    onPressed: _createSuperAdmin,
                    icon: const Icon(Icons.info),
                    label: const Text('Créer'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  ElevatedButton.const Icon(
                    onPressed: _forceRecreateSuperAdmin,
                    icon: const Icon(Icons.info),
                    label: const Text('Recréer'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  ElevatedButton.const Icon(
                    onPressed: _showSuperAdminInfo,
                    icon: const Icon(Icons.info),
                    label: const Text('Infos'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor() {
    if (_status.contains('✅')) return Colors.green;
    if (_status.contains('❌')) return Colors.red;
    if (_status.contains('⚠️')) return Colors.orange;
    return Colors.grey;
  }

  /// 🧪 Tester le Super Admin
  Future<void> _testSuperAdmin() async {
    setState(() {
      _isLoading = true;
      _status = 'Test en cours...';
    });

    try {
      final exists = await ImmediateSuperAdminService.checkSuperAdminExists();
      
      setState(() {
        _status = exists 
            ? '✅ Super Admin fonctionne parfaitement !'
            : '❌ Super Admin n\'existe pas ou ne fonctionne pas';
      });

    } catch (e) {
      setState(() {
        _status = '❌ Erreur test: $e';
      });
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 🚀 Créer le Super Admin
  Future<void> _createSuperAdmin() async {
    setState(() {
      _isLoading = true;
      _status = 'Création en cours...';
    });

    try {
      final created = await ImmediateSuperAdminService.createSuperAdminNow();
      
      setState(() {
        _status = created 
            ? '✅ Super Admin créé avec succès !'
            : '❌ Échec création Super Admin';
      });

    } catch (e) {
      setState(() {
        _status = '❌ Erreur création: $e';
      });
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 🔧 Forcer la recréation
  Future<void> _forceRecreateSuperAdmin() async {
    setState(() {
      _isLoading = true;
      _status = 'Recréation forcée en cours...';
    });

    try {
      final recreated = await ImmediateSuperAdminService.forceRecreateSuperAdmin();
      
      setState(() {
        _status = recreated 
            ? '✅ Super Admin recréé avec succès !'
            : '❌ Échec recréation Super Admin';
      });

    } catch (e) {
      setState(() {
        _status = '❌ Erreur recréation: $e';
      });
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 📋 Afficher les informations
  void _showSuperAdminInfo() {
    final info = ImmediateSuperAdminService.getSuperAdminInfo();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🔐 Informations Super Admin'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ({info['email']}'),
            ({info['password']}'),
            ({info['role']}'),
            ({info['prenom']}'),
            ({info['phone']}'),
            ({info['status']}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer
