import 'package:flutter/material.dart';
import 'screens/insurance_demo_screen.dart';
import 'screens/insurance_main_screen.dart';
import 'services/contract_management_service.dart';
import 'services/auto_fill_service.dart';
import 'services/expert_management_service.dart';

/// 🚀 Exemple d'utilisation du système d'assurance
class InsuranceExampleUsage extends StatelessWidget {
  const Text(\;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Système d\'Assurance - Démo'),
        backgroundColor: const Color(0xFF667eea),
      ),
      body: ,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 40),
            
            // Bouton pour accéder à la démonstration
            ElevatedButton.const Icon(
              onPressed: () => _navigateToDemo(context),
              icon: const Icon(Icons.info),
              label: const Text('Lancer la Démonstration'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF667eea),
                foregroundColor: Colors.white,
                padding: ,
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Boutons d'accès direct par rô const Text(
              'Ou accédez directement par rôle :',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 20),
            
            Wrap(
              spacing: 10,
              runSpacing: 10,
              children: [
                _buildRoleButton(
                  context,
                  'Conducteur',
                  Icons.directions_car,
                  const Color(0xFF667eea),
                  () => _navigateToRole(context, 'conducteur', 'demo_conducteur_001'),
                ),
                _buildRoleButton(
                  context,
                  'Agent',
                  Icons.assignment,
                  const Color(0xFF4facfe),
                  () => _navigateToRole(context, 'agent', 'demo_agent_001'),
                ),
                _buildRoleButton(
                  context,
                  'Expert',
                  Icons.engineering,
                  const Color(0xFFfa709a),
                  () => _navigateToRole(context, 'expert', 'demo_expert_001'),
                ),
                _buildRoleButton(
                  context,
                  'Admin',
                  Icons.admin_panel_settings,
                  const Color(0xFFa8edea),
                  () => _navigateToRole(context, 'admin', 'demo_admin_001'),
                ),
              ],
            ),
            
            const SizedBox(height: 40),
            
            // Informations sur les fonctionnalités
            Container(
              margin: ,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(15),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ,
                  ),
                  const SizedBox(height: 10),
                  const Text('• Gestion multi-véhicules par conducteur'),
                  const Text('• Contrats intelligents avec auto-remplissage'),
                  const Text('• Expertise multi-compagnies'),
                  const Text('• Interfaces modernes et élégantes'),
                  const Text('• Base de données Firebase structurée'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRoleButton(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.const Icon(
      onPressed: onPressed,
      icon: const Icon(icon),
      label: const Text(title),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: ,
        ),
      ),
    );
  }

  void _navigateToDemo(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ,
      ),
    );
  }

  void _navigateToRole(BuildContext context, String role, String userId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InsuranceMainScreen(
          userRole: role,
          userId: userId,
        ),
      ),
    );
  }
}

/// 🎯 Exemple d'intégration dans votre app principale
class MainAppIntegration extends StatelessWidget {
  const Text(\;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Constat Tunisie'),
      ),
      body: Column(
        children: [
          // Vos autres widgets...
          
          // Carte d'accès au système d'assurance
          Container(
            margin: ,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF667eea).withValues(alpha: 0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Column(
              children: [
                ,
                const SizedBox(height: 15),
                ,
                ),
                const SizedBox(height: 10),
                ,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () => _openInsuranceSystem(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: const Color(0xFF667eea),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                  ),
                  child: const Text('Accéder au Système'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _openInsuranceSystem(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ,
      ),
    );
  }
}

/// 📋 Exemple d'utilisation des services
class ServicesExample {
  /// Exemple de création de contrat
  static Future<void> createContractExample() async {
    try {
      // Créer un contrat via le service avec les bons paramètres
      final result = await ContractManagementService.createNewContract(
        agentId: 'demo_agent_001',
        compagnieId: 'star_assurances',
        agenceId: 'star_tunis_001',
        clientCinOrEmail: '<EMAIL>',
        vehiculeImmatriculation: '225 TUN 2215',
        typeContrat: 'Tous Risques',
        primeAnnuelle: 1200.0,
        franchise: 500.0,
        garanties: ['Responsabilité Civile', 'Vol et Incendie'],
      );

      if (result['success']) {
        debugPrint('✅ Contrat créé avec succès');
      } else {
        debugPrint('❌ Erreur: ${result['error']}');
      }
    } catch (e) {
      debugPrint('❌ Erreur lors de la création du contrat: $e');
    }
  }

  /// Exemple d'auto-remplissage
  static Future<void> autoFillExample() async {
    try {
      // Obtenir les données du conducteur pour auto-remplissage
      final conducteurData = await AutoFillService.getConducteurDataForAutoFill('conducteur_001');

      if (conducteurData != null) {
        // Créer un rapport pré-rempli avec les données obtenues
        final preFilledData = AutoFillService.createPreFilledAccidentReport(
          autoFillData: conducteurData,
          dateAccident: DateTime.now(),
          lieuAccident: 'Avenue Habib Bourguiba, Tunis',
          description: 'Accident de démonstration',
        );

        debugPrint('📋 Données pré-remplies: ${preFilledData.keys}');
      } else {
        debugPrint('❌ Aucune donnée trouvée pour ce conducteur');
      }
    } catch (e) {
      debugPrint('❌ Erreur lors de l\'auto-remplissage: $e');
    }
  }

  /// Exemple de gestion des experts
  static Future<void> expertManagementExample() async {
    try {
      // Obtenir tous les experts
      final experts = await ExpertManagementService.getAllExperts();
      debugPrint('👨‍🔧 Nombre d\'experts: ${experts.length}');

      // Assigner un expert à un constat
      final result = await ExpertManagementService.assignExpertToConstat(
        constatId: 'constat_001',
        expertId: 'expert_001',
        notes: 'Expertise urgente requise',
      );

      if (result['success']) {
        debugPrint('✅ Expert assigné avec succès');
      }
    } catch (e) {
      debugPrint('❌ Erreur lors de la gestion des experts: $e
