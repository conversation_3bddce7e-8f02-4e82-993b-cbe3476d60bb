import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/auto_fill_service.dart';
import '../models/insurance_system_models.dart';

/// 🚨 Écran moderne de déclaration d'accident avec auto-remplissage
class AccidentDeclarationScreen extends ConsumerStatefulWidget {
  final String conducteurId;
  final String vehiculeId;
  final Map<String, dynamic>? preFilledData;

  ;

  @override
  ConsumerState<AccidentDeclarationScreen> createState() => _AccidentDeclarationScreenState();
}

class _AccidentDeclarationScreenState extends ConsumerState<AccidentDeclarationScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();
  
  // Contrôleurs pour les champs
  final _lieuController = TextEditingController();
  final _dateController = TextEditingController();
  final _heureController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _degatsController = TextEditingController();
  
  // Variables d'état
  bool _isLoading = false;
  int _currentStep = 0;
  List<String> _selectedCirconstances = [];
  
  // Données pré-remplies
  Map<String, dynamic> _conducteurData = {};
  Map<String, dynamic> _vehiculeData = {};
  Map<String, dynamic> _assuranceData = {};

  // Options disponibles
  final List<String> _circonstancesDisponibles = [
    'Collision frontale',
    'Collision arrière',
    'Collision latérale',
    'Sortie de route',
    'Choc avec obstacle fixe',
    'Renversement',
    'Incendie',
    'Vol/Vandalisme',
    'Bris de glace',
    'Dommages météorologiques',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadPreFilledData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _lieuController.dispose();
    _dateController.dispose();
    _heureController.dispose();
    _descriptionController.dispose();
    _degatsController.dispose();
    super.dispose();
  }

  Future<void> _loadPreFilledData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      if (widget.preFilledData != null) {
        // Utiliser les données pré-remplies fournies
        _conducteurData = widget.preFilledData!['conducteur'] ?? {};
        _vehiculeData = widget.preFilledData!['vehicule'] ?? {};
        _assuranceData = widget.preFilledData!['assurance'] ?? {};
      } else {
        // Charger les données via le service d'auto-remplissage
        final autoFillData = await AutoFillService.getConducteurDataForAutoFill(widget.conducteurId ?? 'Contenu');
        if (autoFillData != null) {
          final preFilledData = AutoFillService.createPreFilledAccidentReport(
            autoFillData: autoFillData,
            dateAccident: DateTime.now(),
            lieuAccident: 'Contenu',
          );

          _conducteurData = preFilledData['conducteur'] ?? {};
          _vehiculeData = preFilledData['vehicule'] ?? {};
          _assuranceData = preFilledData['assurance'] ?? {};
        }
      }

      // Pré-remplir la date et l'heure actuelles
      final now = DateTime.now();
      _dateController.text = '${now.day.toString().padLeft(2, '0')}/${now.month.toString().padLeft(2, '0')}/${now.year}';
      _heureController.text = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';

    } catch (e) {
      _showErrorSnackBar('Erreur lors du chargement des données: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0E21),
      appBar: _buildAppBar(),
      body: _isLoading ? _buildLoadingWidget() : _buildContent(),
    );
  }

  /// 🎨 AppBar moderne avec gradient
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: Colors.transparent,
      flexibleSpace: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFFe74c3c), Color(0xFFc0392b)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
      ),
      title: const Text("Titre"),
      ),
      leading: IconButton(
        icon: const Icon(Icons.info),
        onPressed: () => Navigator.pop(context),
      ),
    );
  }

  /// ⏳ Widget de chargement
  Widget _buildLoadingWidget() {
    return ),
          ),
          const SizedBox(height: 20),
          ,
          ),
        ],
      ),
    );
  }

  /// 📋 Contenu principal
  Widget _buildContent() {
    return Column(
      children: [
        _buildProgressIndicator(),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            physics: ,
            children: [
              _buildPreFilledDataStep(),
              _buildAccidentDetailsStep(),
              _buildCircumstancesStep(),
              _buildConfirmationStep(),
            ],
          ),
        ),
      ],
    );
  }

  /// 📊 Indicateur de progression
  Widget _buildProgressIndicator() {
    return Container(
      padding: ,
          _buildStepConnector(0),
          _buildStepIndicator(1, 'Accident', Icons.car_crash),
          _buildStepConnector(1),
          _buildStepIndicator(2, 'Circonstances', Icons.list),
          _buildStepConnector(2),
          _buildStepIndicator(3, 'Confirmation', Icons.check_circle),
        ],
      ),
    );
  }

  Widget _buildStepIndicator(int step, String title, IconData icon) {
    final isActive = step == _currentStep;
    final isCompleted = step < _currentStep;
    
    return Expanded(
      child: Column(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: isActive || isCompleted
                  ? const LinearGradient(
                      colors: [Color(0xFFe74c3c), Color(0xFFc0392b)],
                    )
                  : null,
              color: isActive || isCompleted ? null : Colors.grey[300],
            ),
            child: ,
          ),
          const SizedBox(height: 8),
          const Text(
            title,
            style: TextStyle(
              color: isActive ? const Color(0xFFe74c3c) : Colors.grey[600],
              fontSize: 10,
              fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStepConnector(int step) {
    final isCompleted = step < _currentStep;
    
    return Expanded(
      child: Container(
        height: 2,
        margin: , Color(0xFFc0392b)],
                )
              : null,
          color: isCompleted ? null : Colors.grey[300],
        ),
      ),
    );
  }

  /// 📋 Étape 1: Données pré-remplies
  Widget _buildPreFilledDataStep() {
    return SingleChildScrollView(
      padding: ,
          const SizedBox(height: 20),
          
          _buildPreFilledSection('Conducteur', _conducteurData, Icons.person),
          const SizedBox(height: 20),
          
          _buildPreFilledSection('Véhicule', _vehiculeData, Icons.directions_car),
          const SizedBox(height: 20),
          
          _buildPreFilledSection('Assurance', _assuranceData, Icons.security),
          const SizedBox(height: 40),
          
          _buildNavigationButtons(
            onNext: () {
              setState(() {
                _currentStep = 1;
                _tabController.animateTo(1);
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPreFilledSection(String title, Map<String, dynamic> data, IconData icon) {
    return Container(
      padding: ,
        gradient: LinearGradient(
          colors: [
            Colors.white.withValues(alpha: 0.1),
            Colors.white.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: , Color(0xFFc0392b)],
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ,
              ),
              const SizedBox(width: 12),
              ,
              ),
            ],
          ),
          const SizedBox(height: 15),
          if (data.isEmpty)
            ,
                fontStyle: FontStyle.italic,
              ),
            )
          else
            ...data.entries.map((entry) => (1),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Expanded(
                    child:  ?? 'Non spécifié',
                      style: ,
                    ),
                  ),
                ],
              ),
            )),
        ],
      ),
    );
  }

  /// 🚨 Étape 2: Détails de l'accident
  Widget _buildAccidentDetailsStep() {
    return SingleChildScrollView(
      padding: ,
            const SizedBox(height: 20),
            
            _buildModernTextField(
              controller: _lieuController,
              label: 'Lieu de l\'accident',
              hint: 'Adresse précise ou description du lieu',
              icon: Icons.location_on,
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'Ce champ est obligatoire';
                }
                return null;
              },
            ),
            const SizedBox(height: 20),
            
            Row(
              children: [
                Expanded(
                  child: _buildModernTextField(
                    controller: _dateController,
                    label: 'Date',
                    hint: 'JJ/MM/AAAA',
                    icon: Icons.calendar_today,
                    readOnly: true,
                    onTap: _selectDate,
                  ),
                ),
                const SizedBox(width: 15),
                Expanded(
                  child: _buildModernTextField(
                    controller: _heureController,
                    label: 'Heure',
                    hint: 'HH:MM',
                    icon: Icons.access_time,
                    readOnly: true,
                    onTap: _selectTime,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            
            _buildModernTextField(
              controller: _descriptionController,
              label: 'Description de l\'accident',
              hint: 'Décrivez les circonstances de l\'accident...',
              icon: Icons.description,
              maxLines: 4,
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'Ce champ est obligatoire';
                }
                return null;
              },
            ),
            const SizedBox(height: 20),
            
            _buildModernTextField(
              controller: _degatsController,
              label: 'Description des dégâts',
              hint: 'Décrivez les dommages subis par le véhicule...',
              icon: Icons.build,
              maxLines: 3,
            ),
            const SizedBox(height: 40),
            
            _buildNavigationButtons(
              onPrevious: () {
                setState(() {
                  _currentStep = 0;
                  _tabController.animateTo(0);
                });
              },
              onNext: () {
                if (_formKey.currentState?.validate() ?? false) {
                  setState(() {
                    _currentStep = 2;
                    _tabController.animateTo(2);
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 📋 Étape 3: Circonstances
  Widget _buildCircumstancesStep() {
    return SingleChildScrollView(
      padding: ,
          const SizedBox(height: 20),
          
          ,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 20),
          
          _buildCircumstancesSelector(),
          const SizedBox(height: 40),
          
          _buildNavigationButtons(
            onPrevious: () {
              setState(() {
                _currentStep = 1;
                _tabController.animateTo(1);
              });
            },
            onNext: () {
              setState(() {
                _currentStep = 3;
                _tabController.animateTo(3);
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCircumstancesSelector() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        color: Colors.white.withValues(alpha: 0.1),
      ),
      child: Column(
        children: _circonstancesDisponibles.map((circonstance) {
          return CheckboxListTile(
            title: const Text("Titre"),
            ),
            value: _selectedCirconstances.contains(circonstance),
            onChanged: (value) {
              setState(() {
                if (value == true) {
                  _selectedCirconstances.add(circonstance);
                } else {
                  _selectedCirconstances.remove(circonstance);
                }
              });
            },
            activeColor: const Color(0xFFe74c3c),
          );
        }).toList(),
      ),
    );
  }

  /// ✅ Étape 4: Confirmation
  Widget _buildConfirmationStep() {
    return SingleChildScrollView(
      padding: ,
          const SizedBox(height: 20),
          _buildSummaryCard(),
          
          const SizedBox(height: 40),
          _buildNavigationButtons(
            onPrevious: () {
              setState(() {
                _currentStep = 2;
                _tabController.animateTo(2);
              });
            },
            onNext: _submitDeclaration,
            nextLabel: 'Envoyer la Déclaration',
            isLoading: _isLoading,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard() {
    return Container(
      padding: ,
        gradient: LinearGradient(
          colors: [
            Colors.white.withValues(alpha: 0.1),
            Colors.white.withValues(alpha: 0.05),
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSummaryRow('Lieu:', _lieuController.text),
          _buildSummaryRow('Date:', _dateController.text),
          _buildSummaryRow('Heure:', _heureController.text),
          _buildSummaryRow('Description:', _descriptionController.text),
          _buildSummaryRow('Dégâts:', _degatsController.text),
          _buildSummaryRow('Circonstances:', _selectedCirconstances.join(', ')),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return (1),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: ,
            ),
          ),
        ],
      ),
    );
  }

  /// 🎨 Widgets utilitaires
  Widget _buildSectionTitle(String title, IconData icon) {
    return Row(
      children: [
        Container(
          padding: , Color(0xFFc0392b)],
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: ,
        ),
        const SizedBox(width: 12),
        ,
        ),
      ],
    );
  }

  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    int maxLines = 1,
    bool readOnly = false,
    VoidCallback? onTap,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        gradient: LinearGradient(
          colors: [
            Colors.white.withValues(alpha: 0.1),
            Colors.white.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: TextFormField(
        controller: controller,
        maxLines: maxLines,
        readOnly: readOnly,
        onTap: onTap,
        validator: validator,
        style: ,
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          labelStyle: TextStyle(color: Colors.white.withValues(alpha: 0.8)),
          hintStyle: TextStyle(color: Colors.white.withValues(alpha: 0.5)),
          prefixIcon: ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(15),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.transparent,
        ),
      ),
    );
  }

  Widget _buildNavigationButtons({
    VoidCallback? onPrevious,
    VoidCallback? onNext,
    String? nextLabel,
    bool isLoading = false,
  }) {
    return Row(
      children: [
        if (onPrevious != null) ...[
          Expanded(
            child: OutlinedButton(
              onPressed: onPrevious,
              style: OutlinedButton.styleFrom(
                side: const BorderSide(color: Color(0xFFe74c3c)),
                padding: ,
                ),
              ),
              child: const Text(
                'Précédent',
                style: TextStyle(color: Color(0xFFe74c3c)),
              ),
            ),
          ),
          const SizedBox(width: 15),
        ],
        Expanded(
          child: ElevatedButton(
            onPressed: isLoading ? null : onNext,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFe74c3c),
              foregroundColor: Colors.white,
              padding: ,
              ),
            ),
            child: isLoading
                ? ,
                    ),
                  )
                : const Text(nextLabel ?? 'Suivant'),
          ),
        ),
      ],
    );
  }

  /// 📅 Sélectionner une date
  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 30)),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ,
            ),
          ),
          child: child!,
        );
      },
    );
    
    if (picked != null) {
      setState(() {
        _dateController.text = '${picked.day.toString().padLeft(2, '0')}/${picked.month.toString().padLeft(2, '0')}/${picked.year}';
      });
    }
  }

  /// ⏰ Sélectionner une heure
  Future<void> _selectTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ,
            ),
          ),
          child: child!,
        );
      },
    );
    
    if (picked != null) {
      setState(() {
        _heureController.text = '${picked.hour.toString().padLeft(2, '0')}:${picked.minute.toString().padLeft(2, '0')}';
      });
    }
  }

  /// 📤 Soumettre la déclaration
  Future<void> _submitDeclaration() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: Implémenter l'envoi de la déclaration
      await Future.delayed(const Duration(seconds: 2)); // Simulation
      
      _showSuccessDialog();
    } catch (e) {
      _showErrorDialog('Erreur lors de l\'envoi: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1D1E33),
        title: const Text("Titre"),
        ),
        content: ,
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1D1E33),
        title: const Text("Titre"),
        ),
        content: ,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK
