import 'package:flutter/material.dart';
import 'package:constat_tunisie/features/admin_compagnie/services/permission_service.dart';
import 'package:constat_tunisie/features/admin_compagnie/presentation/widgets/compagnie_info_card.dart';
import 'package:constat_tunisie/features/admin_compagnie/presentation/widgets/compagnie_stats_card.dart';
import 'package:constat_tunisie/features/admin_compagnie/presentation/widgets/agences_overview_card.dart';
import 'package:constat_tunisie/features/admin_compagnie/presentation/widgets/employes_overview_card.dart';
import 'package:constat_tunisie/features/admin_compagnie/presentation/widgets/fake_data_generator_widget.dart';
import 'package:constat_tunisie/features/admin_compagnie/presentation/screens/agences_management_screen.dart';
import 'package:constat_tunisie/features/admin_compagnie/presentation/screens/employes_management_screen.dart';

/// 🏢 Dashboard principal pour Admin Compagnie
/// 
/// ⚠️ IMPORTANT : Cet écran ne s'affiche que pour les comptes de type 'admin_compagnie'
/// créés par un Super Admin avec une compagnie bien associée (compagnieId).
/// 
/// Si un admin compagnie se connecte sans compagnie, il est redirigé vers une erreur.
class CompagnieDashboardScreen extends StatefulWidget {
  const Text(\;

  @override
  State<CompagnieDashboardScreen> createState() => _CompagnieDashboardScreenState();
}

class _CompagnieDashboardScreenState extends State<CompagnieDashboardScreen> {
  Map<String, dynamic>? _adminInfo;
  bool _isLoading = true;
  String? _error;
  int _selectedIndex = 0;
  int _refreshKey = 0; // Pour forcer l'actualisation des widgets

  @override
  void initState() {
    super.initState();
    _loadAdminInfo();
  }

  /// 🔍 Charger les informations de l'admin compagnie
  Future<void> _loadAdminInfo() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final adminInfo = await PermissionService.getCurrentAdminCompagnieInfo();
      
      if (adminInfo == null) {
        setState(() {
          _error = 'Accès non autorisé. Veuillez contacter votre Super Admin.';
          _isLoading = false;
        });
        return;
      }

      setState(() {
        _adminInfo = adminInfo;
        _isLoading = false;
      });

      debugPrint('[COMPAGNIE_DASHBOARD] ✅ Admin connecté: ${adminInfo['userName']} - ${adminInfo['compagnieNom']}');

    } catch (e) {
      setState(() {
        _error = 'Erreur lors du chargement: $e';
        _isLoading = false;
      });
      debugPrint('[COMPAGNIE_DASHBOARD] ❌ Erreur: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingScreen();
    }

    if (_error != null || _adminInfo == null) {
      return _buildErrorScreen();
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: _buildAppBar(),
      drawer: _buildDrawer(),
      body: _buildBody(),
    );
  }

  /// 🔄 Écran de chargement
  Widget _buildLoadingScreen() {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: Container(
        decoration: , Color(0xFFE2E8F0)],
          ),
        ),
        child: ),
                strokeWidth: 3,
              ),
              const SizedBox(height: 24),
              ,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// ❌ Écran d'erreur
  Widget _buildErrorScreen() {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: Container(
        decoration: , Color(0xFFE2E8F0)],
          ),
        ),
        child: (1),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 20,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: .withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ,
                  ),
                ),
                const SizedBox(height: 24),
                ,
                  ),
                ),
                const SizedBox(height: 8),
                ,
                  ),
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pushReplacementNamed(context, '/login'),
                      child: const Text('Se déconnecter'),
                    ),
                    const SizedBox(width: 16),
                    ElevatedButton.const Icon(
                      onPressed: _loadAdminInfo,
                      icon: const Icon(Icons.info),
                      label: const Text('Réessayer'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF667eea),
                        foregroundColor: Colors.white,
                        padding: ,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 📱 AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ,
            ),
          ),
          const Text(
            _adminInfo!['compagnieNom'],
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF64748B),
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
      backgroundColor: Colors.white,
      foregroundColor: const Color(0xFF1E293B),
      elevation: 0,
      centerTitle: false,
      actions: [
        IconButton(
          icon: Container(
            padding: .withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: ,
              size: 20,
            ),
          ),
          onPressed: () {
            // TODO: Afficher les notifications
          },
          tooltip: 'Notifications',
        ),
        const SizedBox(width: 8),
        IconButton(
          icon: Container(
            padding: .withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: ,
              size: 20,
            ),
          ),
          onPressed: _loadAdminInfo,
          tooltip: 'Actualiser',
        ),
        const SizedBox(width: 16),
      ],
    );
  }

  /// 🎛️ Menu drawer
  Widget _buildDrawer() {
    return Drawer(
      child: Column(
        children: [
          // En-tête du drawer
          Container(
            height: 200,
            decoration: , Color(0xFF764ba2)],
              ),
            ),
            child: SafeArea(
              child: (1),
                      ),
                    ),
                    const SizedBox(height: 16),
                    ,
                    ),
                    const SizedBox(height: 4),
                    ({_adminInfo!['compagnieNom']}',
                      style: ,
                    ),
                    const SizedBox(height: 4),
                    ,
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          // Menu items
          Expanded(
            child: ListView(
              padding: ,
                _buildDrawerItem(
                  icon: Icons.location_city_rounded,
                  title: 'Mes Agences',
                  index: 1,
                ),
                _buildDrawerItem(
                  icon: Icons.people_rounded,
                  title: 'Employés',
                  index: 2,
                ),
                _buildDrawerItem(
                  icon: Icons.analytics_rounded,
                  title: 'Statistiques',
                  index: 3,
                ),
                _buildDrawerItem(
                  icon: Icons.person_rounded,
                  title: 'Mon Profil',
                  index: 4,
                ),
                ,
                ListTile(
                  leading: ,
                  title: const Text('Déconnexion'),
                  onTap: () {
                    // TODO: Implémenter la déconnexion
                    Navigator.pushReplacementNamed(context, '/login');
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 📋 Élément du drawer
  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required int index,
  }) {
    final isSelected = _selectedIndex == index;
    
    return ListTile(
      leading: const Icon(
        icon,
        color: isSelected ? const Color(0xFF667eea) : Colors.grey,
      ),
      title:  : Colors.black87,
        ),
      ),
      selected: isSelected,
      selectedTileColor: const Color(0xFF667eea).withValues(alpha: 0.1),
      onTap: () {
        setState(() {
          _selectedIndex = index;
        });
        Navigator.pop(context);
      },
    );
  }

  /// 📱 Corps principal
  Widget _buildBody() {
    switch (_selectedIndex) {
      case 0:
        return _buildDashboardHome();
      case 1:
        return _buildAgencesView();
      case 2:
        return _buildEmployesView();
      case 3:
        return _buildStatisticsView();
      case 4:
        return _buildProfileView();
      default:
        return _buildDashboardHome();
    }
  }

  /// 🏠 Vue d'accueil du dashboard
  Widget _buildDashboardHome() {
    return SingleChildScrollView(
      padding: 
          FakeDataGeneratorWidget(
            compagnieId: _adminInfo!['compagnieId'],
            compagnieNom: _adminInfo!['compagnieNom'],
            onDataGenerated: () {
              // Actualiser les widgets quand des données sont générées
              setState(() {
                _refreshKey++;
              });
            },
          ),

          // Carte d'informations de la compagnie
          CompagnieInfoCard(compagnieData: _adminInfo!['compagnieData'),

          const SizedBox(height: 16),

          // Statistiques rapides
          CompagnieStatsCard(
            key: ValueKey('stats_$_refreshKey'),
            compagnieId: _adminInfo!['compagnieId'],
          ),

          const SizedBox(height: 16),

          // Aperçu des agences
          AgencesOverviewCard(
            key: ValueKey('agences_$_refreshKey'),
            compagnieId: _adminInfo!['compagnieId'],
          ),

          const SizedBox(height: 16),

          // Aperçu des employés
          EmployesOverviewCard(
            key: ValueKey('employes_$_refreshKey'),
            compagnieId: _adminInfo!['compagnieId'],
          ),
        ],
      ),
    );
  }

  /// 🏛️ Vue des agences
  Widget _buildAgencesView() {
    return ;
  }

  /// 👥 Vue des employés
  Widget _buildEmployesView() {
    return ;
  }

  /// 📊 Vue des statistiques
  Widget _buildStatisticsView() {
    return const Center(
      child: const Text('Vue Statistiques - À implémenter'),
    );
  }

  /// 👤 Vue du profil
  Widget _buildProfileView() {
    return const Center(
      child: const Text('Vue Profil - À implémenter
