import 'package:flutter/material.dart';
import '../../services/tunisia_insurance_service.dart';

/// 📋 Onglet de gestion des contrats d'assurance
class ContratsTab extends StatefulWidget {
  final String compagnieId;
  final String agenceId;
  final String agenceNom;

  ;

  @override
  State<ContratsTab> createState() => _ContratsTabState();
}

class _ContratsTabState extends State<ContratsTab> {
  List<Map<String, dynamic>> _contrats = [];
  bool _isLoading = true;
  final _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadContrats();
  }

  /// 📋 Charger les contrats de l'agence
  Future<void> _loadContrats() async {
    setState(() => _isLoading = true);
    
    try {
      final contrats = await TunisiaInsuranceService.rechercherContrats(
        compagnieId: widget.compagnieId,
        agenceId: widget.agenceId,
      );
      
      setState(() {
        _contrats = contrats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: (e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  /// ➕ Créer un nouveau contrat
  Future<void> _createContrat() async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => _CreateContratDialog(
        compagnieId: widget.compagnieId,
        agenceId: widget.agenceId,
        agenceNom: widget.agenceNom,
      ),
    );

    if (result != null && result['success'] == true) {
      _loadContrats();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: const Text('Contrat créé avec succès'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // En-tête
        Container(
          padding: ,
          ),
          child: Column(
            children: [
              Row(
                children: [
                  ,
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ,
                        ),
                        ({_contrats.length} contrat(s) - ${widget.agenceNom}',
                          style: ,
                        ),
                      ],
                    ),
                  ),
                  ElevatedButton.const Icon(
                    onPressed: _createContrat,
                    icon: const Icon(Icons.info),
                    label: const Text('Nouveau Contrat'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: Colors.purple.shade600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              
              // Barre de recherche
              TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Rechercher par numéro, conducteur, immatriculation...',
                  hintStyle: ,
                  prefixIcon: ,
                  filled: true,
                  fillColor: Colors.white.withValues(alpha: ,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                ),
                style: ,
                onChanged: (value) {
                  // TODO: Implémenter la recherche en temps réel
                },
              ),
            ],
          ),
        ),
        
        // Liste des contrats
        Expanded(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _contrats.isEmpty
                  ? _buildEmptyState()
                  : _buildContratsList(),
        ),
      ],
    );
  }

  /// 📋 Liste des contrats
  Widget _buildContratsList() {
    return ListView.builder(
      padding:  {
        final contrat = _contrats[index];
        return _buildContratCard(contrat);
      },
    );
  }

  /// 📄 Carte de contrat
  Widget _buildContratCard(Map<String, dynamic> contrat) {
    return Card(
      margin: ,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ({contrat['numeroContrat'] ?? contrat['id']}',
                        style: ,
                      ),
                      const SizedBox(height: 4),
                      ({contrat['conducteurNom'] ?? 'Non défini'}',
                        style: TextStyle(color: Colors.grey.shade600),
                      ),
                    ],
                  ),
                ),
                _buildStatusChip(contrat['status'] ?? 'actif'),
                PopupMenuButton(
                  itemBuilder: (context) => [
                    ,
                          const SizedBox(width: 8),
                          const Text('Voir détails'),
                        ],
                      ),
                    ),
                    ,
                          const SizedBox(width: 8),
                          const Text('Créer constat'),
                        ],
                      ),
                    ),
                  ],
                  onSelected: (value) => _handleContratAction(value.toString(), contrat),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    'Véhicule',
                    '${contrat['vehiculeMarque'] ?? 'Contenu'} ${contrat['vehiculeModele'] ?? 'Contenu'}',
                    Icons.directions_car,
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    'Prime',
                    '${contrat['prime'] ?? 0} DT',
                    Icons.euro,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    'Début',
                    _formatDate(contrat['dateDebut'),
                    Icons.calendar_today,
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    'Fin',
                    _formatDate(contrat['dateFin'),
                    Icons.event,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 📊 Élément d'information
  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Row(
      children: [
        ,
        const SizedBox(width: 4),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ,
              ),
              ,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 🏷️ Chip de statut
  Widget _buildStatusChip(String status) {
    Color color;
    String label;
    
    switch (status.toLowerCase()) {
      case 'actif':
        color = Colors.green;
        label = 'Actif';
        break;
      case 'expire':
        color = Colors.red;
        label = 'Expiré';
        break;
      case 'suspendu':
        color = Colors.orange;
        label = 'Suspendu';
        break;
      default:
        color = Colors.grey;
        label = status;
    }

    return Container(
      padding: ,
        borderRadius: BorderRadius.circular(12),
      ),
      child: ,
      ),
    );
  }

  /// 📅 Formater une date
  String _formatDate(dynamic date) {
    if (date == null) return 'Non défini';
    
    try {
      if (date is String) {
        final parsedDate = DateTime.parse(date);
        return '${parsedDate.day}/${parsedDate.month}/${parsedDate.year}';
      }
      return date.toString();
    } catch (e) {
      return 'Date invalide';
    }
  }

  /// 🎯 Gérer les actions sur un contrat
  void _handleContratAction(String action, Map<String, dynamic> contrat) {
    switch (action) {
      case 'view':
        // TODO: Afficher les détails du contrat
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: const Text('Détails du contrat bientôt disponibles')),
        );
        break;
      case 'constat':
        _createConstat(contrat);
        break;
    }
  }

  /// 📋 Créer un constat pour ce contrat
  Future<void> _createConstat(Map<String, dynamic> contrat) async {
    // TODO: Implémenter la création de constat
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: const Text('Création de constat bientôt disponible')),
    );
  }

  /// 📭 État vide
  Widget _buildEmptyState() {
    return ,
          const SizedBox(height: 16),
          ,
          ),
          const SizedBox(height: 8),
          ,
          ),
          const SizedBox(height: 16),
          ElevatedButton.const Icon(
            onPressed: _createContrat,
            icon: const Icon(Icons.info),
            label: const Text('Créer un contrat'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}

/// 🏗️ Dialog de création de contrat (placeholder)
class _CreateContratDialog extends StatelessWidget {
  final String compagnieId;
  final String agenceId;
  final String agenceNom;

  ;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: (agenceNom'),
      content: const Text(
        'La création de contrats sera implémentée dans la prochaine version.\n\n'
        'Cette fonctionnalité inclura :\n'
        '• Informations du conducteur\n'
        '• Détails du véhicule\n'
        '• Conditions du contrat\n'
        '• Calcul automatique de la prime',
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Fermer
