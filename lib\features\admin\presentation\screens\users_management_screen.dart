import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../../core/theme/modern_theme.dart';
import '../../services/user_management_service.dart';
import '../../services/compagnie_service.dart';
import '../../services/agence_service.dart';
import '../../services/export_service.dart';
import '../../services/test_data_cleanup_service.dart';
import '../../services/quick_cleanup_service.dart';
import '../../services/radical_cleanup_service.dart';
import '../../services/optimized_user_service.dart';
import '../../services/admin_recreation_service.dart';

import '../widgets/user_edit_dialog.dart';
import '../widgets/user_create_dialog.dart';
import '../widgets/institutional_admin_create_dialog.dart';
import '../widgets/user_detail_dialog.dart';

/// 👥 Écran de gestion des utilisateurs pour Super Admin
class UsersManagementScreen extends ConsumerStatefulWidget {
  const Text(\;

  @override
  ConsumerState<UsersManagementScreen> createState() => _UsersManagementScreenState();
}

class _UsersManagementScreenState extends ConsumerState<UsersManagementScreen> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  // État de l'interface
  List<Map<String, dynamic>> _users = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _hasMoreData = true;
  DocumentSnapshot? _lastDocument;
  bool _includeTestData = true; // 🎲 Inclure les données de test par défaut

  // Sélection multiple
  bool _isSelectionMode = false;
  Set<String> _selectedUserIds = {};

  // Filtres
  String _searchQuery = 'Contenu';
  String _selectedRole = 'Tous';
  String _selectedCompagnie = 'Toutes';
  String _selectedAgence = 'Toutes';
  String _selectedStatus = 'Tous';
  String _selectedOrigine = 'Toutes';

  // Données pour les dropdowns
  List<Map<String, dynamic>> _compagnies = [];
  List<Map<String, dynamic>> _agences = [];

  // Statistiques
  Map<String, int> _stats = {};

  final List<String> _roles = [
    'Tous',
    'super_admin',
    'admin_compagnie',
    'admin_agence',
    'agent_agence',
    'expert_auto',
    'conducteur',
  ];

  final List<String> _statuses = [
    'Tous',
    'actif',
    'en_attente',
    'desactive',
    'supprime',
  ];

  final List<String> _origines = [
    'Toutes',
    'creation_super_admin',
    'demande_professionnelle',
  ];

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// 📥 Charger les données initiales
  Future<void> _loadInitialData() async {
    await Future.wait([
      _loadUsers(refresh: true),
      _loadCompagnies(),
      _loadStats(),
    ]);
  }

  /// 👥 Charger les utilisateurs
  Future<void> _loadUsers({bool refresh = false}) async {
    if (_isLoading) return;

    setState(() {
      _isLoading = refresh;
      if (refresh) {
        _users.clear();
        _lastDocument = null;
        _hasMoreData = true;
      }
    });

    try {
      final result = await UserManagementService.getAllUsers(
        limit: 20,
        lastDocument: _lastDocument,
        roleFilter: _selectedRole,
        compagnieFilter: _selectedCompagnie,
        agenceFilter: _selectedAgence,
        statusFilter: _selectedStatus,
        origineFilter: _selectedOrigine,
        searchQuery: _searchQuery.isNotEmpty ? _searchQuery : null,
        includeTestData: _includeTestData, // 🎲 Inclure les données de test
      );

      if (mounted) {
        setState(() {
          if (refresh) {
            _users = List<Map<String, dynamic>>.from(result['users']);
          } else {
            _users.addAll(List<Map<String, dynamic>>.from(result['users']));
          }
          _hasMoreData = result['hasMore'] as bool;
          _lastDocument = result['lastDocument'] as DocumentSnapshot?;
          _isLoading = false;
          _isLoadingMore = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isLoadingMore = false;
        });
        _showErrorSnackBar('Erreur lors du chargement: $e');
      }
    }
  }

  /// 🏢 Charger les compagnies
  Future<void> _loadCompagnies() async {
    try {
      final compagnieService = CompagnieService();
      final compagnies = await compagnieService.getCompagniesForDropdown();

      if (mounted) {
        setState(() {
          _compagnies = [
            {'id': 'Toutes', 'nom': 'Toutes les compagnies'},
            ...compagnies,
          ];
        });
      }
    } catch (e) {
      debugPrint('Erreur chargement compagnies: $e');
    }
  }

  /// 🏪 Charger les agences selon la compagnie sélectionnée
  Future<void> _loadAgences() async {
    try {
      final agenceService = AgenceService();
      final agences = await agenceService.getAgencesForDropdown(
        compagnieId: _selectedCompagnie != 'Toutes' ? _selectedCompagnie : null,
      );

      if (mounted) {
        setState(() {
          _agences = [
            {'id': 'Toutes', 'nom': 'Toutes les agences'},
            ...agences,
          ];
        });
      }
    } catch (e) {
      debugPrint('Erreur chargement agences: $e');
    }
  }

  /// 📊 Charger les statistiques
  Future<void> _loadStats() async {
    try {
      final stats = await UserManagementService.getUserStats();
      if (mounted) {
        setState(() {
          _stats = stats;
        });
      }
    } catch (e) {
      debugPrint('Erreur chargement stats: $e');
    }
  }

  /// 📜 Gestion du scroll pour pagination
  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreUsers();
    }
  }

  /// 📄 Charger plus d'utilisateurs
  Future<void> _loadMoreUsers() async {
    if (_isLoadingMore || !_hasMoreData) return;

    setState(() {
      _isLoadingMore = true;
    });

    await _loadUsers();
  }

  /// 🔍 Recherche
  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    _debounceSearch();
  }

  /// 🐛 Debug des données utilisateur
  Future<void> _debugUserData() async {
    debugPrint('[USERS_MANAGEMENT] 🐛 === DEBUG DONNÉES UTILISATEUR ===');
    debugPrint('[USERS_MANAGEMENT] 📊 Filtres actuels:');
    debugPrint('  - Rôle: $_selectedRole');
    debugPrint('  - Statut: $_selectedStatus');
    debugPrint('  - Origine: $_selectedOrigine');
    debugPrint('  - Compagnie: $_selectedCompagnie');
    debugPrint('  - Recherche: "$_searchQuery"');

    debugPrint('[USERS_MANAGEMENT] � Utilisateurs chargés: ${_users.length}');

    // Compter par origine
    final origineCount = <String, int>{};
    for (final user in _users) {
      final origine = user['origine'] ?? 'null';
      origineCount[origine] = (origineCount[origine] ?? 0) + 1;
    }

    debugPrint('[USERS_MANAGEMENT] 📊 Répartition par origine:');
    origineCount.forEach((origine, count) => debugPrint('  - "$origine": $count'));

    // Test direct sans filtres
    debugPrint('[USERS_MANAGEMENT] 🔍 Test direct base de données (sans filtres)...');
    try {
      final directResult = await UserManagementService.getAllUsers(
        limit: 100,
        origineFilter: null,
      );
      final allUsers = directResult['users'] as List<Map<String, dynamic>>;
      debugPrint('[USERS_MANAGEMENT] 📊 Total en base: ${allUsers.length}');

      final directOrigineCount = <String, int>{};
      for (final user in allUsers) {
        final origine = user['origine'] ?? 'null';
        directOrigineCount[origine] = (directOrigineCount[origine] ?? 0) + 1;
      }

      debugPrint('[USERS_MANAGEMENT] � Répartition directe par origine:');
      directOrigineCount.forEach((origine, count) => debugPrint('  - "$origine": $count'));

      // Test spécifique du filtre demande_professionnelle
      debugPrint('[USERS_MANAGEMENT] 🔍 Test filtre demande_professionnelle...');
      final filteredResult = await UserManagementService.getAllUsers(
        limit: 100,
        origineFilter: 'demande_professionnelle',
      );
      final filteredUsers = filteredResult['users'] as List<Map<String, dynamic>>;
      debugPrint('[USERS_MANAGEMENT] 📊 Utilisateurs avec origine demande_professionnelle: ${filteredUsers.length}');

      for (final user in filteredUsers) {
        debugPrint('[USERS_MANAGEMENT] 👤 Demande pro: ${user['email']} | Origine: "${user['origine']}"');
      }

    } catch (e) {
      debugPrint('[USERS_MANAGEMENT] ❌ Erreur test direct: $e');
    }

    _showInfoSnackBar('Debug terminé - Consultez les logs de la console');
  }

  /// 🔍 Debug complet de tous les utilisateurs Firestore
  Future<void> _debugAllUsers() async {
    _showInfoSnackBar('Analyse complète en cours...');
    await UserManagementService.debugListAllUsers();
    _showInfoSnackBar('Analyse complète terminée - voir console');
  }

  /// 🧹 Nettoyer toutes les données de test
  Future<void> _cleanupTestData() async {
    // Confirmation avant suppression
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            ,
            const SizedBox(width: 12),
            const Text('Confirmation de suppression'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ,
            ),
            const SizedBox(height: 12),
            const Text('• Utilisateurs de test'),
            const Text('• Compagnies de test'),
            const Text('• Agences de test'),
            const Text('• Agents de test'),
            const Text('• Experts de test'),
            const Text('• Demandes professionnelles de test'),
            const SizedBox(height: 12),
            ,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: ),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      _showInfoSnackBar('🧹 Nettoyage en cours...');

      final result = await TestDataCleanupService.cleanAllTestData();

      if (result['success']) {
        final totalDeleted = result['totalDeleted'] as int;
        final details = result['details'] as Map<String, int>;

        _showSuccessSnackBar('✅ Nettoyage terminé ! $totalDeleted documents supprimés');

        // Afficher les détails
        debugPrint('[CLEANUP] 📊 Résultats détaillés: $details');

        // Recharger la liste
        await _loadUsers(refresh: true);

      } else {
        _showErrorSnackBar('❌ Erreur lors du nettoyage: ${result['error']}');
      }

    } catch (e) {
      _showErrorSnackBar('❌ Erreur inattendue: $e');
    }
  }

  /// ⚡ Nettoyage rapide des données explicitement marquées comme test
  Future<void> _quickCleanupTestData() async {
    try {
      _showInfoSnackBar('⚡ Nettoyage rapide en cours...');

      final result = await QuickCleanupService.quickCleanTestData();

      if (result['success']) {
        final totalDeleted = result['totalDeleted'] as int;
        _showSuccessSnackBar('⚡ Nettoyage rapide terminé ! $totalDeleted documents supprimés');

        // Recharger la liste
        await _loadUsers(refresh: true);

      } else {
        _showErrorSnackBar('❌ Erreur nettoyage rapide: ${result['error']}');
      }

    } catch (e) {
      _showErrorSnackBar('❌ Erreur inattendue: $e');
    }
  }

  /// 🗑️ Nettoyer les utilisateurs avec statut "supprime"
  Future<void> _cleanDeletedUsers() async {
    try {
      _showInfoSnackBar('🗑️ Nettoyage des utilisateurs supprimés...');

      final deleted = await QuickCleanupService.cleanDeletedUsers();

      if (deleted > 0) {
        _showSuccessSnackBar('🗑️ $deleted utilisateur(s) supprimé(s) nettoyé(s)');
        await _loadUsers(refresh: true);
      } else {
        _showInfoSnackBar('ℹ️ Aucun utilisateur supprimé à nettoyer');
      }

    } catch (e) {
      _showErrorSnackBar('❌ Erreur nettoyage: $e');
    }
  }

  /// 📊 Analyser la collection users
  Future<void> _analyzeUsers() async {
    try {
      _showInfoSnackBar('📊 Analyse en cours...');

      final analysis = await RadicalCleanupService.analyzeUsersCollection();

      if (analysis.containsKey('error')) {
        _showErrorSnackBar('❌ Erreur analyse: ${analysis['error']}');
        return;
      }

      final total = analysis['total'] as int;
      final legitimate = analysis['legitimateCount'] as int;
      final test = analysis['testCount'] as int;

      _showInfoSnackBar('📊 Analyse terminée: $total total, $legitimate légitimes, $test à supprimer');

      // Afficher les détails dans la console
      debugPrint('[ANALYSIS] 📊 === RÉSULTATS ANALYSE ===');
      debugPrint('[ANALYSIS] Total: $total');
      debugPrint('[ANALYSIS] Légitimes: $legitimate');
      debugPrint('[ANALYSIS] À supprimer: $test');

    } catch (e) {
      _showErrorSnackBar('❌ Erreur analyse: $e');
    }
  }

  /// 🔥 Nettoyage radical de la collection users
  Future<void> _radicalCleanup() async {
    // Triple confirmation pour cette action dangereuse
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            ,
            const SizedBox(width: 12),
            const Text('NETTOYAGE RADICAL'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ,
            ),
            const SizedBox(height: 16),
            const Text('Cette action va :'),
            const SizedBox(height: 8),
            const Text('🔥 SUPPRIMER TOUS les utilisateurs de test'),
            const Text('🔥 SUPPRIMER TOUS les utilisateurs non légitimes'),
            const Text('✅ GARDER SEULEMENT les Admin Compagnie réels'),
            const SizedBox(height: 16),
            ,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: ),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      _showInfoSnackBar('🔥 NETTOYAGE RADICAL EN COURS...');

      final result = await RadicalCleanupService.keepOnlyLegitimateAdmins();

      if (result['success']) {
        final kept = result['kept'] as int;
        final deleted = result['deleted'] as int;

        _showSuccessSnackBar('🔥 Nettoyage radical terminé ! $kept gardés, $deleted supprimés');

        // Recharger la liste
        await _loadUsers(refresh: true);

      } else {
        _showErrorSnackBar('❌ Erreur nettoyage radical: ${result['error']}');
      }

    } catch (e) {
      _showErrorSnackBar('❌ Erreur inattendue: $e');
    }
  }

  /// 🧹 Nettoyage simple et direct
  Future<void> _simpleCleanup() async {
    try {
      _showInfoSnackBar('🧹 Nettoyage simple en cours...');

      // Supprimer directement les utilisateurs problématiques
      final snapshot = await FirebaseFirestore.instance.collection('users').get();
      int deleted = 0;

      for (var doc in snapshot.docs) {
        final data = doc.data();
        final email = data['email'] as String? ?? 'Contenu';
        final status = data['status'] as String? ?? 'Contenu';
        final role = data['role'] as String? ?? 'Contenu';

        // Supprimer si :
        // 1. Statut supprimé
        // 2. Email contient des IDs générés automatiquement
        // 3. isFakeData = true
        bool shouldDelete = false;

        if (status == 'supprime') {
          shouldDelete = true;
          debugPrint('[SIMPLE_CLEANUP] 🗑️ Suppression statut supprimé: $email');
        }

        if (data['isFakeData'] == true) {
          shouldDelete = true;
          debugPrint('[SIMPLE_CLEANUP] 🗑️ Suppression fake data: $email');
        }

        if (email.contains('icyzx6kjr4qz8lumcbup') || email.contains('hxbsdjkf1ujbmh6d5tnd')) {
          shouldDelete = true;
          debugPrint('[SIMPLE_CLEANUP] 🗑️ Suppression ID généré: $email');
        }

        if (shouldDelete) {
          await doc.reference.delete();
          deleted++;
          debugPrint('[SIMPLE_CLEANUP] ✅ Supprimé: ${doc.id} ($email)');
        }
      }

      _showSuccessSnackBar('🧹 Nettoyage terminé ! $deleted utilisateur(s) supprimé(s)');
      await _loadUsers(refresh: true);

    } catch (e) {
      _showErrorSnackBar('❌ Erreur nettoyage simple: $e');
    }
  }

  /// ⚡ Chargement optimisé des utilisateurs
  Future<void> _loadOptimizedUsers() async {
    try {
      setState(() => _isLoading = true);
      _showInfoSnackBar('⚡ Chargement optimisé en cours...');

      final stopwatch = Stopwatch()..start();

      final result = await OptimizedUserService.getOptimizedUsers(
        limit: 50, // Plus d'utilisateurs par requête
        roleFilter: _selectedRole,
        statusFilter: _selectedStatus,
        includeTestData: _includeTestData,
      );

      stopwatch.stop();

      if (mounted) {
        setState(() {
          _users = List<Map<String, dynamic>>.from(result['users']);
          _hasMoreData = result['hasMore'] as bool;
          _lastDocument = result['lastDocument'] as DocumentSnapshot?;
          _isLoading = false;
        });

        final processingTime = result['processingTime'] as int? ?? stopwatch.elapsedMilliseconds;
        _showSuccessSnackBar('⚡ Chargé en ${processingTime}ms - ${_users.length} utilisateurs');
      }

    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        _showErrorSnackBar('❌ Erreur chargement optimisé: $e');
      }
    }
  }

  /// 🗑️ Vider le cache pour forcer le rafraîchissement
  void _clearCache() {
    OptimizedUserService.clearCache();
    final cacheInfo = OptimizedUserService.getCacheInfo();
    _showInfoSnackBar('🗑️ Cache vidé - ${cacheInfo['compagnies']} compagnies, ${cacheInfo['agences']} agences');
  }

  /// 📊 Vérifier l'état des Admin Compagnie
  Future<void> _checkAdminStatus() async {
    try {
      _showInfoSnackBar('📊 Vérification des Admin Compagnie...');

      final status = await AdminRecreationService.checkAdminStatus();

      if (status.containsKey('error')) {
        _showErrorSnackBar('❌ Erreur vérification: ${status['error']}');
        return;
      }

      final existing = status['existing_admins'] as int;
      final missing = status['missing_admins'] as int;
      final total = status['total_companies'] as int;

      _showInfoSnackBar('📊 État: $existing/$total Admin Compagnie présents, $missing manquants');

      // Afficher les détails dans la console
      debugPrint('[ADMIN_STATUS] 📊 === ÉTAT DES ADMIN COMPAGNIE ===');
      debugPrint('[ADMIN_STATUS] Total compagnies: $total');
      debugPrint('[ADMIN_STATUS] Admins existants: $existing');
      debugPrint('[ADMIN_STATUS] Admins manquants: $missing');
      debugPrint('[ADMIN_STATUS] Existants: ${status['existing_list']}');
      debugPrint('[ADMIN_STATUS] Manquants: ${status['missing_list']}');

    } catch (e) {
      _showErrorSnackBar('❌ Erreur vérification: $e');
    }
  }

  /// 🔄 Recréer les Admin Compagnie manquants
  Future<void> _recreateAdmins() async {
    try {
      _showInfoSnackBar('🔄 Recréation des Admin Compagnie...');

      final result = await AdminRecreationService.recreateAllAdmins();

      if (result['success']) {
        final created = result['created'] as int;
        final existing = result['existing'] as int;
        final total = result['total'] as int;

        _showSuccessSnackBar('🔄 Recréation terminée ! $created créés, $existing existants sur $total');

        // Recharger la liste des utilisateurs
        await _loadUsers(refresh: true);

        // Afficher les détails
        debugPrint('[ADMIN_RECREATION] ✅ === RECRÉATION TERMINÉE ===');
        debugPrint('[ADMIN_RECREATION] Créés: ${result['createdAdmins']}');
        debugPrint('[ADMIN_RECREATION] Existants: ${result['existingAdmins']}');

      } else {
        _showErrorSnackBar('❌ Erreur recréation: ${result['error']}');
      }

    } catch (e) {
      _showErrorSnackBar('❌ Erreur inattendue: $e');
    }
  }

  /// 🧪 Créer un utilisateur de test avec origine demande professionnelle
  Future<void> _createTestUser() async {
    try {
      await UserManagementService.createTestUserDemandePro();
      _showSuccessSnackBar('Utilisateur test demande professionnelle créé');
      await _loadUsers(refresh: true);
    } catch (e) {
      _showErrorSnackBar('Erreur création utilisateur test: $e');
    }
  }

  Timer? _searchTimer;
  void _debounceSearch() {
    _searchTimer?.cancel();
    _searchTimer = Timer(const Duration(milliseconds: 500), () {
      _loadUsers(refresh: true);
    });
  }

  /// 🔄 Filtres changés
  void _onFiltersChanged() {
    if (_selectedCompagnie != 'Toutes') {
      _loadAgences();
    } else {
      setState(() {
        _selectedAgence = 'Toutes';
        _agences = [{'id': 'Toutes', 'nom': 'Toutes les agences'}];
      });
    }
    _loadUsers(refresh: true);
  }

  /// 📱 Gestion des actions du menu
  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _exportCurrentList();
        break;
      case 'select_all':
        _enterSelectionMode();
        break;
      case 'debug':
        _debugUserData();
        break;
      case 'debug_all':
        _debugAllUsers();
        break;
      case 'cleanup':
        _cleanupTestData();
        break;
      case 'quick_cleanup':
        _quickCleanupTestData();
        break;
      case 'clean_deleted':
        _cleanDeletedUsers();
        break;
      case 'radical_cleanup':
        _radicalCleanup();
        break;
      case 'analyze_users':
        _analyzeUsers();
        break;
      case 'simple_clean':
        _simpleCleanup();
        break;
      case 'optimized_load':
        _loadOptimizedUsers();
        break;
      case 'clear_cache':
        _clearCache();
        break;
      case 'recreate_admins':
        _recreateAdmins();
        break;
      case 'check_admin_status':
        _checkAdminStatus();
        break;
      case 'test':
        _createTestUser();
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: AppBar(
        title: _isSelectionMode
            ? ({_selectedUserIds.length} sélectionné(s)',
                style: const TextStyle())
            : ),
        backgroundColor: ModernTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: false,
        leading: _isSelectionMode
            ? IconButton(
                icon: const Icon(Icons.info),
                onPressed: _exitSelectionMode,
                tooltip: 'Annuler',
              )
            : Builder(
                builder: (context) => IconButton(
                  icon: const Icon(Icons.info),
                  onPressed: () => Scaffold.of(context).openDrawer(),
                  tooltip: 'Menu',
                ),
              ),
        actions: _isSelectionMode ? _buildSelectionActions() : [
          // 🎲 Bouton pour basculer les données de test
          IconButton(
            icon: const Icon(Icons.info),
            onPressed: () {
              setState(() {
                _includeTestData = !_includeTestData;
              });
              _loadUsers(refresh: true);
            },
            tooltip: _includeTestData ? 'Masquer données de test' : 'Afficher données de test',
          ),
          IconButton(
            icon: const Icon(Icons.info),
            onPressed: () => _loadUsers(refresh: true),
            tooltip: 'Actualiser',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.info),
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              ,
                    const SizedBox(width: 12),
                    const Text('Exporter'),
                  ],
                ),
              ),
              ,
                    const SizedBox(width: 12),
                    const Text('Sélection multiple'),
                  ],
                ),
              ),
              ,
                    const SizedBox(width: 12),
                    const Text('Debug Filtres'),
                  ],
                ),
              ),
              ,
                    const SizedBox(width: 12),
                    const Text('Debug Firestore'),
                  ],
                ),
              ),
              ,
              ,
                    const SizedBox(width: 12),
                    ),
                  ],
                ),
              ),
              ,
                    const SizedBox(width: 12),
                    ),
                  ],
                ),
              ),
              ,
              ,
                    const SizedBox(width: 12),
                    ),
                  ],
                ),
              ),
              ,
                    const SizedBox(width: 12),
                    ),
                  ],
                ),
              ),
              ,
                    const SizedBox(width: 12),
                    ),
                  ],
                ),
              ),
              ,
                    const SizedBox(width: 12),
                    ),
                  ],
                ),
              ),
              ,
                    const SizedBox(width: 12),
                    ),
                  ],
                ),
              ),
              ,
              ,
                    const SizedBox(width: 12),
                    ),
                  ],
                ),
              ),
              ,
                    const SizedBox(width: 12),
                    ),
                  ],
                ),
              ),
              ,
                    const SizedBox(width: 12),
                    ),
                  ],
                ),
              ),
              ,
                    const SizedBox(width: 12),
                    const Text('Test User'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      drawer: _buildDrawer(),
      body: Column(
        children: [
          _buildCompactStats(),
          _buildSearchAndFilters(),
          Expanded(child: _buildModernUsersList()),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _createUser,
        backgroundColor: ModernTheme.primaryColor,
        child: ,
      ),
    );
  }

  /// � Statistiques compactes modernes
  Widget _buildCompactStats() {
    return Container(
      margin: ,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatItem(
              'Total',
              '${_stats['total'] ?? 0}',
              Icons.people_rounded,
              ModernTheme.primaryColor,
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: Colors.grey.shade200,
          ),
          Expanded(
            child: _buildStatItem(
              'Actifs',
              '${_stats['actif'] ?? 0}',
              Icons.check_circle_rounded,
              Colors.green,
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: Colors.grey.shade200,
          ),
          Expanded(
            child: _buildStatItem(
              'En attente',
              '${_stats['en_attente'] ?? 0}',
              Icons.pending_rounded,
              Colors.orange,
            ),
          ),
        ],
      ),
    );
  }

  /// 📊 Item de statistique
  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        const Icon(icon, color: color, size: 24),
        const SizedBox(height: 8),
        ,
        ),
        ,
        ),
      ],
    );
  }

  /// 🔍 Recherche et filtres modernes
  Widget _buildSearchAndFilters() {
    return Container(
      margin: ,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextField(
              controller: _searchController,
              decoration: ,
                prefixIcon: ,
                border: InputBorder.none,
                contentPadding: ,
              onChanged: (value) {
                _searchQuery = value;
                _debounceSearch();
              },
            ),
          ),
          const SizedBox(height: 12),
          // Filtres horizontaux
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip(
                  'Rôle: $_selectedRole',
                  () => _showFilterDialog('role'),
                  _selectedRole != 'Tous',
                ),
                const SizedBox(width: 8),
                _buildFilterChip(
                  'Statut: $_selectedStatus',
                  () => _showFilterDialog('statut'),
                  _selectedStatus != 'Tous',
                ),
                const SizedBox(width: 8),
                _buildFilterChip(
                  'Compagnie: $_selectedCompagnie',
                  () => _showFilterDialog('compagnie'),
                  _selectedCompagnie != 'Toutes',
                ),
                const SizedBox(width: 8),
                _buildFilterChip(
                  'Origine: $_selectedOrigine',
                  () => _showFilterDialog('origine'),
                  _selectedOrigine != 'Toutes',
                ),
                if (_selectedCompagnie != 'Toutes') ...[
                  const SizedBox(width: 8),
                  _buildFilterChip(
                    'Agence: $_selectedAgence',
                    () => _showFilterDialog('agence'),
                    _selectedAgence != 'Toutes',
                  ),
                ],
              ],
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  /// 🏷️ Chip de filtre moderne
  Widget _buildFilterChip(String label, VoidCallback onTap, bool isActive) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: ,
          border: Border.all(
            color: isActive ? ModernTheme.primaryColor : Colors.grey.shade300,
          ),
        ),
        child: ,
        ),
      ),
    );
  }
  /// 📱 Liste moderne des utilisateurs
  Widget _buildModernUsersList() {
    if (_isLoading) {
      return ,
        ),
      );
    }

    if (_users.isEmpty) {
      return ,
            const SizedBox(height: 16),
            ,
            ),
            const SizedBox(height: 8),
            ,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding:  {
        final user = _users[index];
        return _buildModernUserCard(user, index);
      },
    );
  }

  /// 🎨 Carte utilisateur moderne
  Widget _buildModernUserCard(Map<String, dynamic> user, int index) {
    final userId = user['uid'] ?? user['id'] ?? 'Contenu';
    final isSelected = _selectedUserIds.contains(userId);

    return Container(
      margin: ,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: isSelected
            ? Border.all(color: ModernTheme.primaryColor, width: 2)
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: _isSelectionMode
              ? () => _toggleUserSelection(userId)
              : () => _showUserDetails(user),
          onLongPress: () {
            if (!_isSelectionMode) {
              _enterSelectionMode();
              _toggleUserSelection(userId);
            }
          },
          child: (1)
                  Checkbox(
                    value: isSelected,
                    onChanged: (value) => _toggleUserSelection(userId),
                    activeColor: ModernTheme.primaryColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                  )
                else
                  CircleAvatar(
                    radius: 24,
                    backgroundColor: ModernTheme.primaryColor.withValues(alpha: 0.1),
                    child: const Text(
                      (user['displayName'] ?? user['email'] ?? 'U')[0].toUpperCase(),
                      style: ,
                    ),
                  ),

                const SizedBox(width: 16),

                // Informations utilisateur
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Nom et statut
                      Row(
                        children: [
                          Expanded(
                            child: ,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          _buildStatusBadge(user['status'] ?? 'actif'),
                        ],
                      ),

                      const SizedBox(height: 4),

                      // Email et rôle
                      if (user['email'] != null)
                        ,
                          overflow: TextOverflow.ellipsis,
                        ),

                      const SizedBox(height: 8),

                      // Tags informatifs
                      Wrap(
                        spacing: 8,
                        runSpacing: 4,
                        children: [
                          _buildInfoTag(
                            user['role'] ?? 'Utilisateur',
                            Icons.person_outline_rounded,
                            ModernTheme.primaryColor,
                          ),
                          if (user['compagnieNom'] != null)
                            _buildInfoTag(
                              user['compagnieNom'],
                              Icons.business_rounded,
                              Colors.blue,
                            ),
                          if (user['origine'] == 'demande_professionnelle')
                            _buildInfoTag(
                              'Demande pro',
                              Icons.request_page_rounded,
                              Colors.orange,
                            ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Actions
                if (!_isSelectionMode)
                  PopupMenuButton<String>(
                    icon: const Icon(Icons.info),
                    onSelected: (action) => _handleUserAction(action, user),
                    itemBuilder: (context) => [
                      ,
                            const SizedBox(width: 8),
                            const Text('Modifier'),
                          ],
                        ),
                      ),
                      // Désactiver si actif, Activer si désactivé
                      if (user['status'] == 'actif')
                        ,
                              const SizedBox(width: 8),
                              ),
                            ],
                          ),
                        ),
                      if (user['status'] == 'desactive')
                        ,
                              const SizedBox(width: 8),
                              ),
                            ],
                          ),
                        ),
                      ,
                            const SizedBox(width: 8),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  /// 🏷️ Badge de statut moderne
  Widget _buildStatusBadge(String status) {
    Color color;
    IconData icon;

    switch (status) {
      case 'actif':
        color = Colors.green;
        icon = Icons.check_circle_rounded;
        break;
      case 'en_attente':
        color = Colors.orange;
        icon = Icons.pending_rounded;
        break;
      case 'desactive':
        color = Colors.red;
        icon = Icons.block_rounded;
        break;
      case 'supprime':
        color = Colors.grey;
        icon = Icons.delete_rounded;
        break;
      default:
        color = Colors.grey;
        icon = Icons.help_rounded;
    }

    return Container(
      padding: ,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          ,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// 🏷️ Tag d'information moderne
  Widget _buildInfoTag(String text, IconData icon, Color color) {
    return Container(
      padding: ,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          ,
          ),
        ],
      ),
    );
  }

  /// 🎯 Gestion des actions utilisateur
  void _handleUserAction(String action, Map<String, dynamic> user) {
    // Récupérer l'ID utilisateur (peut être 'uid' ou 'id')
    final userId = user['uid'] ?? user['id'];
    if (userId == null) {
      _showErrorSnackBar('Erreur: ID utilisateur introuvable');
      return;
    }

    switch (action) {
      case 'edit':
        _editUser(user);
        break;
      case 'disable':
        _disableUser(userId);
        break;
      case 'enable':
        _enableUser(userId);
        break;
      case 'delete':
        _deleteUser(userId, user['displayName'] ?? user['email'] ?? 'Utilisateur');
        break;
    }
  }

  /// �📋 En-tête moderne avec statistiques
  Widget _buildHeader() {
    return Container(
      margin: ,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            ModernTheme.primaryColor,
            ModernTheme.primaryColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: ModernTheme.primaryColor.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              ,
              ,
              ,
              ),
            ],
          ),

          ,

          // Statistiques rapides
          if (_stats.isNotEmpty) ...[
            Wrap(
              spacing: ModernTheme.spacingM,
              runSpacing: ModernTheme.spacingS,
              children: [
                _buildStatChip('Total', _stats['total'] ?? 0, Colors.blue),
                _buildStatChip('Actifs', _stats['actifs'] ?? 0, Colors.green),
                _buildStatChip('En attente', _stats['en_attente'] ?? 0, Colors.orange),
                _buildStatChip('Désactivés', _stats['desactives'] ?? 0, Colors.red),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// 📊 Chip de statistique moderne
  Widget _buildStatChip(String label, int count, Color color) {
    return Container(
      padding: ,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          ,
          ),
          const SizedBox(width: 4),
          Container(
            padding: ,
            ),
            child: ,
              style: TextStyle(
                color: ModernTheme.primaryColor,
                fontWeight: FontWeight.bold,
                fontSize: 11,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 🔍 Barre de filtres moderne et responsive
  Widget _buildFilters() {
    return Container(
      margin: ,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // En-tête des filtres
          Container(
            padding: ,
              borderRadius: ,
              ),
            ),
            child: Row(
              children: [
                ,
                const SizedBox(width: 8),
                ,
                ),
                ,
                if (_hasActiveFilters())
                  TextButton.const Icon(
                    onPressed: _clearAllFilters,
                    icon: const Icon(Icons.info),
                    label: const Text('Effacer'),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.grey.shade600,
                      padding: ,
                  ),
              ],
            ),
          ),

          (1),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: TextField(
                    controller: _searchController,
                    onChanged: _onSearchChanged,
                    decoration: InputDecoration(
                      hintText: 'Rechercher par nom, email, téléphone...',
                      hintStyle: TextStyle(color: Colors.grey.shade500),
                      prefixIcon: ,
                      suffixIcon: _searchQuery.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.info),
                              onPressed: () {
                                _searchController.clear();
                                _onSearchChanged('Contenu');
                              },
                            )
                          : null,
                      border: InputBorder.none,
                      contentPadding: ,
                  ),
                ),

                const SizedBox(height: 16),

                // Filtres en grille responsive
                LayoutBuilder(
                  builder: (context, constraints) {
                    // Calculer le nombre de colonnes selon la largeur
                    int crossAxisCount = constraints.maxWidth > 800 ? 5 :
                                       constraints.maxWidth > 600 ? 3 : 2;

                    return GridView.count(
                      shrinkWrap: true,
                      physics: ,
                      crossAxisCount: crossAxisCount,
                      childAspectRatio: 3.5,
                      crossAxisSpacing: 12,
                      mainAxisSpacing: 12,
                      children: [
                        _buildModernFilterChip(
                          label: 'Rôle',
                          value: _selectedRole,
                          icon: Icons.work_outline,
                          onTap: () => _showFilterDialog('role'),
                        ),
                        _buildModernFilterChip(
                          label: 'Compagnie',
                          value: _selectedCompagnie == 'Toutes' ? 'Toutes' :
                                 _getCompagnieNameById(_selectedCompagnie),
                          icon: Icons.domain_outlined,
                          onTap: () => _showFilterDialog('compagnie'),
                        ),
                        _buildModernFilterChip(
                          label: 'Agence',
                          value: _selectedAgence == 'Toutes' ? 'Toutes' :
                                 _getAgenceNameById(_selectedAgence),
                          icon: Icons.business_outlined,
                          onTap: () => _showFilterDialog('agence'),
                        ),
                        _buildModernFilterChip(
                          label: 'Statut',
                          value: _selectedStatus,
                          icon: Icons.check_circle_outline,
                          onTap: () => _showFilterDialog('statut'),
                        ),
                        _buildModernFilterChip(
                          label: 'Origine',
                          value: _getOriginLabel(_selectedOrigine),
                          icon: Icons.source_outlined,
                          onTap: () => _showFilterDialog('origine'),
                        ),
                      ],
                    );
                  },
                ),

                // Indicateur de résultats
                if (_users.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  Container(
                    padding: ,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        ,
                        const SizedBox(width: 8),
                        ({_users.length > 1 ? 's' : 'Contenu'}',
                          style: TextStyle(
                            color: ModernTheme.primaryColor,
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 🎨 Chip de filtre moderne
  Widget _buildModernFilterChip({
    required String label,
    required String value,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    final isActive = value != 'Tous' && value != 'Toutes';

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding:  : Colors.grey.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isActive ? ModernTheme.primaryColor : Colors.grey.shade300,
            width: isActive ? 2 : 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              children: [
                ,
                const SizedBox(width: 4),
                Expanded(
                  child: ,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 2),
            ({value.substring(0, 12)}...' : value,
              style: TextStyle(
                fontSize: 11,
                color: isActive ? ModernTheme.primaryColor : Colors.grey.shade700,
                fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  /// 🔍 Vérifier si des filtres sont actifs
  bool _hasActiveFilters() {
    return _selectedRole != 'Tous' ||
           _selectedCompagnie != 'Toutes' ||
           _selectedAgence != 'Toutes' ||
           _selectedStatus != 'Tous' ||
           _selectedOrigine != 'Toutes' ||
           _searchQuery.isNotEmpty;
  }

  /// 🧹 Effacer tous les filtres
  void _clearAllFilters() {
    setState(() {
      _selectedRole = 'Tous';
      _selectedCompagnie = 'Toutes';
      _selectedAgence = 'Toutes';
      _selectedStatus = 'Tous';
      _selectedOrigine = 'Toutes';
      _searchQuery = 'Contenu';
      _searchController.clear();
    });
    _onFiltersChanged();
  }

  /// 📝 Obtenir le nom de la compagnie par ID
  String _getCompagnieNameById(String id) {
    if (id == 'Toutes') return 'Toutes';
    final compagnie = _compagnies.firstWhere(
      (c) => c['id'] == id,
      orElse: () => {'nom': 'Inconnue'},
    );
    return compagnie['nom'] ?? 'Inconnue';
  }

  /// 📝 Obtenir le nom de l'agence par ID
  String _getAgenceNameById(String id) {
    if (id == 'Toutes') return 'Toutes';
    final agence = _agences.firstWhere(
      (a) => a['id'] == id,
      orElse: () => {'nom': 'Inconnue'},
    );
    return agence['nom'] ?? 'Inconnue';
  }

  /// 📝 Obtenir le label de l'origine
  String _getOriginLabel(String origine) {
    switch (origine) {
      case 'creation_super_admin':
        return 'Super Admin';
      case 'demande_professionnelle':
        return 'Demande Pro';
      case 'import_manuel':
        return 'Import';
      case 'test':
        return 'Test';
      default:
        return 'Toutes';
    }
  }

  /// 🎯 Afficher le dialog de filtre
  Future<void> _showFilterDialog(String filterType) async {
    String? selectedValue;
    List<String> options = [];
    List<String> labels = [];

    switch (filterType) {
      case 'role':
        options = _roles;
        labels = _roles;
        selectedValue = _selectedRole;
        break;
      case 'compagnie':
        options = ['Toutes'] + _compagnies.map((c) => c['id'] as String).toList();
        labels = ['Toutes'] + _compagnies.map((c) => c['nom'] as String).toList();
        selectedValue = _selectedCompagnie;
        break;
      case 'agence':
        options = ['Toutes'] + _agences.map((a) => a['id'] as String).toList();
        labels = ['Toutes'] + _agences.map((a) => a['nom'] as String).toList();
        selectedValue = _selectedAgence;
        break;
      case 'statut':
        options = _statuses;
        labels = _statuses;
        selectedValue = _selectedStatus;
        break;
      case 'origine':
        options = _origines;
        labels = [
          'Toutes',
          'Création Super Admin',
          'Demande Professionnelle',
        ];
        selectedValue = _selectedOrigine;
        break;
    }

    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: ({filterType}'),
        content:  {
              final option = options[index];
              final label = labels[index];
              final isSelected = option == selectedValue;

              return ListTile(
                title: const Text(label),
                leading: Radio<String>(
                  value: option,
                  groupValue: selectedValue,
                  onChanged: (value) => Navigator.pop(context, value),
                ),
                selected: isSelected,
                onTap: () => Navigator.pop(context, option),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
        ],
      ),
    );

    if (result != null) {
      setState(() {
        switch (filterType) {
          case 'role':
            _selectedRole = result;
            break;
          case 'compagnie':
            _selectedCompagnie = result;
            if (result != 'Toutes') {
              _loadAgences();
            } else {
              _selectedAgence = 'Toutes';
            }
            break;
          case 'agence':
            _selectedAgence = result;
            break;
          case 'statut':
            _selectedStatus = result;
            break;
          case 'origine':
            _selectedOrigine = result;
            break;
        }
      });
      _onFiltersChanged();
    }
  }

  /// 👁️ Afficher les détails de l'utilisateur
  Future<void> _showUserDetails(Map<String, dynamic> user) async {
    await showDialog(
      context: context,
      builder: (context) => UserDetailDialog(user: user),
    );
  }

  /// 📋 Liste des utilisateurs
  Widget _buildUsersList() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_users.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      controller: _scrollController,
      padding: ,
      itemBuilder: (context, index) {
        if (index == _users.length) {
          return (1),
            ),
          );
        }

        final user = _users[index];
        return _buildUserCard(user);
      },
    );
  }

  /// 👤 Carte utilisateur moderne
  Widget _buildUserCard(Map<String, dynamic> user) {
    final status = user['status'] ?? 'en_attente';
    final role = user['role'] ?? 'conducteur';
    final userId = user['id'] as String;
    final isSelected = _selectedUserIds.contains(userId);
    Color statusColor;
    IconData statusIcon;

    switch (status) {
      case 'actif':
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case 'en_attente':
        statusColor = Colors.orange;
        statusIcon = Icons.pending;
        break;
      case 'desactive':
        statusColor = Colors.red;
        statusIcon = Icons.block;
        break;
      case 'supprime':
        statusColor = Colors.grey;
        statusIcon = Icons.delete;
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.help;
    }

    return Container(
      margin:  : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isSelected ? ModernTheme.primaryColor : Colors.grey.shade200,
          width: isSelected ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: _isSelectionMode
              ? () => _toggleUserSelection(userId)
              : () => _showUserDetails(user),
          onLongPress: () {
            if (!_isSelectionMode) {
              _enterSelectionMode();
              _toggleUserSelection(userId);
            }
          },
          child: (1) ...[
                  Checkbox(
                    value: isSelected,
                    onChanged: (value) => _toggleUserSelection(userId),
                    activeColor: ModernTheme.primaryColor,
                  ),
                  ,
                ],

                // Avatar
                CircleAvatar(
                  radius: 24,
                  backgroundColor: ModernTheme.primaryColor.withValues(alpha: 0.1),
                  child: const Text(
                    (user['displayName'] ?? user['email'] ?? 'U')[0].toUpperCase(),
                    style: ,
                  ),
                ),

                ,

                // Informations principales
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ,
                      ),
                      ,
                      ),
                    ],
                  ),
                ),

                // Statut
                Container(
                  padding: ,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: statusColor.withValues(alpha: 0.3)),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(statusIcon, color: statusColor, size: 16),
                      const SizedBox(width: 4),
                      const Text(
                        _getStatusLabel(status),
                        style: TextStyle(
                          color: statusColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),

                // Menu d'actions
                PopupMenuButton<String>(
                  onSelected: (action) => _handleUserAction(action, user),
                  itemBuilder: (context) => [
                    if (status == 'en_attente')
                      ,
                            const SizedBox(width: 8),
                            const Text('Approuver'),
                          ],
                        ),
                      ),
                    ,
                          const SizedBox(width: 8),
                          const Text('Modifier'),
                        ],
                      ),
                    ),
                    if (status == 'actif')
                      ,
                            const SizedBox(width: 8),
                            const Text('Désactiver'),
                          ],
                        ),
                      ),
                    if (status == 'desactive')
                      ,
                            const SizedBox(width: 8),
                            const Text('Réactiver'),
                          ],
                        ),
                      ),
                    if (status != 'supprime')
                      ,
                            const SizedBox(width: 8),
                            const Text('Supprimer'),
                          ],
                        ),
                      ),
                  ],
                ),
              ],
            ),

            ,

            // Informations détaillées
            Wrap(
              spacing: ModernTheme.spacingM,
              runSpacing: ModernTheme.spacingS,
              children: [
                _buildInfoChip('Rôle', _getRoleLabel(role), Icons.person),
                if (user['compagnieNom'] != null || user['compagnieId'] != null)
                  _buildInfoChip('Compagnie', user['compagnieNom'] ?? 'ID: ${user['compagnieId']}', Icons.domain),
                if (user['agenceNom'] != null || user['agenceId'] != null)
                  _buildInfoChip('Agence', user['agenceNom'] ?? 'ID: ${user['agenceId']}', Icons.business),
                if (user['telephone'] != null)
                  _buildInfoChip('Téléphone', user['telephone'], Icons.phone),
                // Badge d'origine avec lien vers la demande
                if (user['origine'] == 'demande_professionnelle')
                  _buildOrigineBadge(user['requestId'),
              ],
            ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 📋 Chip d'information
  Widget _buildInfoChip(String label, String value, IconData icon) {
    return Container(
      padding: ,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          ,
          const SizedBox(width: 4),
          (label: ',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          ,
          ),
        ],
      ),
    );
  }

  /// 🏷️ Badge d'origine pour les utilisateurs créés via demande professionnelle
  Widget _buildOrigineBadge(String? requestId) {
    return GestureDetector(
      onTap: requestId != null ? () => _navigateToRequest(requestId) : null,
      child: Container(
        padding: ,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: ModernTheme.primaryColor.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            ,
            const SizedBox(width: 4),
            ,
            ),
            if (requestId != null) ...[
              const SizedBox(width: 4),
              ,
            ],
          ],
        ),
      ),
    );
  }

  /// 🔗 Naviguer vers la demande d'origine
  void _navigateToRequest(String requestId) {
    Navigator.pushNamed(
      context,
      '/admin/professional-requests',
      arguments: {'highlightRequestId': requestId},
    );
  }

  /// 📭 État vide
  Widget _buildEmptyState() {
    return ,
          ,
          ,
          ),
          ,
          ,
          ),
        ],
      ),
    );
  }

  /// 🏷️ Libellé du statut
  String _getStatusLabel(String status) {
    switch (status) {
      case 'actif':
        return 'Actif';
      case 'en_attente':
        return 'En attente';
      case 'desactive':
        return 'Désactivé';
      case 'supprime':
        return 'Supprimé';
      default:
        return 'Inconnu';
    }
  }

  /// 🏷️ Libellé du rôle
  String _getRoleLabel(String role) {
    switch (role) {
      case 'super_admin':
        return 'Super Admin';
      case 'admin_compagnie':
        return 'Admin Compagnie';
      case 'admin_agence':
        return 'Admin Agence';
      case 'agent_agence':
        return 'Agent';
      case 'expert_auto':
        return 'Expert Auto';
      case 'conducteur':
        return 'Conducteur';
      default:
        return 'Inconnu';
    }
  }

  /// ✅ Approuver un utilisateur
  Future<void> _approveUser(String userId) async {
    final error = await UserManagementService.approveUser(userId);
    if (mounted) {
      if (error == null) {
        _showSuccessSnackBar('Utilisateur approuvé avec succès');
        _loadUsers(refresh: true);
      } else {
        _showErrorSnackBar(error);
      }
    }
  }

  /// ✏️ Modifier un utilisateur
  Future<void> _editUser(Map<String, dynamic> user) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => UserEditDialog(user: user),
    );

    if (result == true) {
      _showSuccessSnackBar('Utilisateur modifié avec succès');
      _loadUsers(refresh: true);
    }
  }

  /// 🚫 Désactiver un utilisateur
  Future<void> _disableUser(String userId) async {
    final error = await UserManagementService.disableUser(userId);
    if (mounted) {
      if (error == null) {
        _showSuccessSnackBar('Utilisateur désactivé avec succès');
        _loadUsers(refresh: true);
      } else {
        _showErrorSnackBar(error);
      }
    }
  }

  /// 🔄 Réactiver un utilisateur
  Future<void> _enableUser(String userId) async {
    final error = await UserManagementService.enableUser(userId);
    if (mounted) {
      if (error == null) {
        _showSuccessSnackBar('Utilisateur réactivé avec succès');
        _loadUsers(refresh: true);
      } else {
        _showErrorSnackBar(error);
      }
    }
  }

  /// 🗑️ Supprimer un utilisateur
  Future<void> _deleteUser(String userId, String userName) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmer la suppression'),
        content: (userName" ?\n\nCette action peut être annulée.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final error = await UserManagementService.deleteUser(userId);
      if (mounted) {
        if (error == null) {
          _showSuccessSnackBar('Utilisateur supprimé avec succès');
          _loadUsers(refresh: true);
        } else {
          _showErrorSnackBar(error);
        }
      }
    }
  }

  /// ➕ Créer un nouvel utilisateur
  Future<void> _createUser() async {
    // Afficher un menu de choix du type d'utilisateur à créer
    final userType = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Créer un utilisateur'),
        content: const Text('Quel type d\'utilisateur souhaitez-vous créer ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop('admin_compagnie'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade600,
              foregroundColor: Colors.white,
            ),
            child: const Text('Admin Compagnie'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop('other'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green.shade600,
              foregroundColor: Colors.white,
            ),
            child: const Text('Autre Utilisateur'),
          ),
        ],
      ),
    );

    if (userType == null || !mounted) return;

    bool? result;

    if (userType == 'admin_compagnie') {
      // Utiliser le nouveau système pour Admin Compagnie
      result = await showDialog<bool>(
        context: context,
        builder: (context) => ,
      );
    } else {
      // Utiliser l'ancien système pour les autres types
      result = await showDialog<bool>(
        context: context,
        builder: (context) => ,
      );
    }

    // 🔄 Rafraîchir la liste si la création a réussi
    if (result == true && mounted) {
      debugPrint('[USERS_MANAGEMENT] 🔄 Rafraîchissement après création...');
      await _loadUsers(refresh: true);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: const Text('✅ Utilisateur créé avec succès'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }

    if (result == true && mounted) {
      _showSuccessSnackBar('Utilisateur créé avec succès');
      _loadUsers(refresh: true);
      _loadStats(); // Recharger les statistiques
    }
  }

  /// 📱 Menu latéral (Drawer)
  Widget _buildDrawer() {
    return Drawer(
      child: Column(
        children: [
          // En-tête du drawer
          Container(
            height: 200,
            decoration: ],
              ),
            ),
            child: (1),
                    ),
                    const SizedBox(height: 12),
                    ,
                    ),
                    ,
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Menu items
          Expanded(
            child: ListView(
              padding:  {
                    Navigator.pop(context);
                    Navigator.pushReplacementNamed(context, '/super-admin-dashboard');
                  },
                ),
                _buildDrawerItem(
                  icon: Icons.domain,
                  title: 'Gestion Compagnies',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/admin/compagnies');
                  },
                ),
                _buildDrawerItem(
                  icon: Icons.business,
                  title: 'Gestion Agences',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/admin/agences');
                  },
                ),
                _buildDrawerItem(
                  icon: Icons.people,
                  title: 'Gestion Utilisateurs',
                  onTap: () => Navigator.pop(context), // Déjà sur cette page
                  isSelected: true,
                ),
                _buildDrawerItem(
                  icon: Icons.pending_actions,
                  title: 'Demandes en attente',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/admin/professional-requests');
                  },
                ),
                _buildDrawerItem(
                  icon: Icons.analytics,
                  title: 'Statistiques',
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: Naviguer vers statistiques
                  },
                ),
                _buildDrawerItem(
                  icon: Icons.security,
                  title: 'Logs d\'Audit',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/admin/audit-logs');
                  },
                ),
                ,
                _buildDrawerItem(
                  icon: Icons.exit_to_app,
                  title: 'Quitter l\'espace Admin',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushReplacementNamed(context, '/login');
                  },
                  textColor: Colors.orange,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 📋 Item du menu latéral
  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isSelected = false,
    Color? textColor,
  }) {
    return ListTile(
      leading: ,
      ),
      title: const Text("Titre"),
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      selected: isSelected,
      selectedTileColor: ModernTheme.primaryColor.withValues(alpha: 0.1),
      onTap: onTap,
    );
  }

  /// 📢 Messages utilisateur
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text(message),
        backgroundColor: Colors.blue,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// 🔄 Entrer en mode sélection
  void _enterSelectionMode() {
    setState(() {
      _isSelectionMode = true;
      _selectedUserIds.clear();
    });
  }

  /// ❌ Sortir du mode sélection
  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedUserIds.clear();
    });
  }

  /// 🔄 Basculer la sélection d'un utilisateur
  void _toggleUserSelection(String userId) {
    setState(() {
      if (_selectedUserIds.contains(userId)) {
        _selectedUserIds.remove(userId);
      } else {
        _selectedUserIds.add(userId);
      }
    });
  }

  /// 🎬 Actions de sélection multiple
  List<Widget> _buildSelectionActions() {
    return [
      if (_selectedUserIds.isNotEmpty) ...[
        // Approuver en lot
        IconButton(
          icon: const Icon(Icons.info),
          onPressed: _bulkApprove,
          tooltip: 'Approuver sélectionnés',
        ),
        // Désactiver en lot
        IconButton(
          icon: const Icon(Icons.info),
          onPressed: _bulkDisable,
          tooltip: 'Désactiver sélectionnés',
        ),
        // Menu plus d'actions
        PopupMenuButton<String>(
          icon: const Icon(Icons.info),
          onSelected: _handleBulkAction,
          itemBuilder: (context) => [
            ,
                  const SizedBox(width: 8),
                  const Text('Réactiver'),
                ],
              ),
            ),
            ,
                  const SizedBox(width: 8),
                  const Text('Supprimer'),
                ],
              ),
            ),
            ,
                  const SizedBox(width: 8),
                  const Text('Changer de compagnie'),
                ],
              ),
            ),
          ],
        ),
      ],
      // Sélectionner tout
      IconButton(
        icon: const Icon(Icons.info),
        onPressed: _selectAll,
        tooltip: 'Tout sélectionner',
      ),
    ];
  }

  /// 📋 Sélectionner tous les utilisateurs visibles
  void _selectAll() {
    setState(() {
      _selectedUserIds.clear();
      for (final user in _users) {
        _selectedUserIds.add(user['id'] as String);
      }
    });
  }

  /// ✅ Approuver en lot
  Future<void> _bulkApprove() async {
    if (_selectedUserIds.isEmpty) return;

    final confirmed = await _showBulkConfirmationDialog(
      'Approuver ${_selectedUserIds.length} utilisateur(s)',
      'Êtes-vous sûr de vouloir approuver les utilisateurs sélectionnés ?',
    );

    if (confirmed == true) {
      int successCount = 0;
      int errorCount = 0;

      for (final userId in _selectedUserIds) {
        final error = await UserManagementService.approveUser(userId);
        if (error == null) {
          successCount++;
        } else {
          errorCount++;
        }
      }

      if (mounted) {
        _exitSelectionMode();
        _loadUsers(refresh: true);

        if (errorCount == 0) {
          _showSuccessSnackBar('$successCount utilisateur(s) approuvé(s) avec succès');
        } else {
          _showErrorSnackBar('$successCount réussis, $errorCount échecs');
        }
      }
    }
  }

  /// 🚫 Désactiver en lot
  Future<void> _bulkDisable() async {
    if (_selectedUserIds.isEmpty) return;

    final confirmed = await _showBulkConfirmationDialog(
      'Désactiver ${_selectedUserIds.length} utilisateur(s)',
      'Êtes-vous sûr de vouloir désactiver les utilisateurs sélectionnés ?',
    );

    if (confirmed == true) {
      int successCount = 0;
      int errorCount = 0;

      for (final userId in _selectedUserIds) {
        final error = await UserManagementService.disableUser(userId);
        if (error == null) {
          successCount++;
        } else {
          errorCount++;
        }
      }

      if (mounted) {
        _exitSelectionMode();
        _loadUsers(refresh: true);

        if (errorCount == 0) {
          _showSuccessSnackBar('$successCount utilisateur(s) désactivé(s) avec succès');
        } else {
          _showErrorSnackBar('$successCount réussis, $errorCount échecs');
        }
      }
    }
  }

  /// ⚡ Gérer les actions en lot
  Future<void> _handleBulkAction(String action) async {
    switch (action) {
      case 'enable':
        await _bulkEnable();
        break;
      case 'delete':
        await _bulkDelete();
        break;
      case 'change_company':
        await _bulkChangeCompany();
        break;
    }
  }

  /// 🔄 Réactiver en lot
  Future<void> _bulkEnable() async {
    if (_selectedUserIds.isEmpty) return;

    final confirmed = await _showBulkConfirmationDialog(
      'Réactiver ${_selectedUserIds.length} utilisateur(s)',
      'Êtes-vous sûr de vouloir réactiver les utilisateurs sélectionnés ?',
    );

    if (confirmed == true) {
      int successCount = 0;
      int errorCount = 0;

      for (final userId in _selectedUserIds) {
        final error = await UserManagementService.enableUser(userId);
        if (error == null) {
          successCount++;
        } else {
          errorCount++;
        }
      }

      if (mounted) {
        _exitSelectionMode();
        _loadUsers(refresh: true);

        if (errorCount == 0) {
          _showSuccessSnackBar('$successCount utilisateur(s) réactivé(s) avec succès');
        } else {
          _showErrorSnackBar('$successCount réussis, $errorCount échecs');
        }
      }
    }
  }

  /// 🗑️ Supprimer en lot
  Future<void> _bulkDelete() async {
    if (_selectedUserIds.isEmpty) return;

    final confirmed = await _showBulkConfirmationDialog(
      'Supprimer ${_selectedUserIds.length} utilisateur(s)',
      'Êtes-vous sûr de vouloir supprimer les utilisateurs sélectionnés ?\n\nCette action peut être annulée.',
    );

    if (confirmed == true) {
      int successCount = 0;
      int errorCount = 0;

      for (final userId in _selectedUserIds) {
        final error = await UserManagementService.deleteUser(userId);
        if (error == null) {
          successCount++;
        } else {
          errorCount++;
        }
      }

      if (mounted) {
        _exitSelectionMode();
        _loadUsers(refresh: true);

        if (errorCount == 0) {
          _showSuccessSnackBar('$successCount utilisateur(s) supprimé(s) avec succès');
        } else {
          _showErrorSnackBar('$successCount réussis, $errorCount échecs');
        }
      }
    }
  }

  /// 🏢 Changer de compagnie en lot
  Future<void> _bulkChangeCompany() async {
    if (_selectedUserIds.isEmpty) return;

    // TODO: Implémenter le dialog de sélection de compagnie
    _showInfoSnackBar('Fonctionnalité de changement de compagnie en cours de développement');
  }

  /// 💬 Dialog de confirmation pour actions en lot
  Future<bool?> _showBulkConfirmationDialog(String title, String content) {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(title),
        content: const Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: ModernTheme.primaryColor),
            child: ),
          ),
        ],
      ),
    );
  }

  /// 📊 Gérer les actions d'export
  Future<void> _handleExportAction(String action) async {
    switch (action) {
      case 'export_current':
        await _exportCurrentList();
        break;
      case 'export_stats':
        await _exportStats();
        break;
      case 'export_report':
        await _exportDetailedReport();
        break;
    }
  }

  /// 📋 Exporter la liste actuelle
  Future<void> _exportCurrentList() async {
    if (_users.isEmpty) {
      _showErrorSnackBar('Aucune donnée à exporter');
      return;
    }

    try {
      final filePath = await ExportService.exportUsersToCSV(_users);

      if (filePath != null) {
        _showSuccessSnackBar('Export réussi: ${filePath.split('/').last}');
      } else {
        _showErrorSnackBar('Erreur lors de l\'export');
      }
    } catch (e) {
      _showErrorSnackBar('Erreur lors de l\'export: $e');
    }
  }

  /// 📊 Exporter les statistiques
  Future<void> _exportStats() async {
    if (_stats.isEmpty) {
      _showErrorSnackBar('Aucune statistique à exporter');
      return;
    }

    try {
      final filePath = await ExportService.exportStatsToCSV(_stats);

      if (filePath != null) {
        _showSuccessSnackBar('Export des statistiques réussi: ${filePath.split('/').last}');
      } else {
        _showErrorSnackBar('Erreur lors de l\'export des statistiques');
      }
    } catch (e) {
      _showErrorSnackBar('Erreur lors de l\'export: $e');
    }
  }

  /// 📄 Exporter un rapport détaillé
  Future<void> _exportDetailedReport() async {
    if (_users.isEmpty || _stats.isEmpty) {
      _showErrorSnackBar('Données insuffisantes pour le rapport');
      return;
    }

    try {
      final filePath = await ExportService.createDetailedReport(_users, _stats);

      if (filePath != null) {
        _showSuccessSnackBar('Rapport détaillé créé: ${filePath.split('/').last}');
      } else {
        _showErrorSnackBar('Erreur lors de la création du rapport');
      }
    } catch (e) {
      _showErrorSnackBar('Erreur lors de la création du rapport: $e
