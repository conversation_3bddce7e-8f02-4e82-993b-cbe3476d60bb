import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/contract_management_service.dart';
import '../models/insurance_system_models.dart';
import 'agent_contract_creation_screen.dart;

/// 📊 Dashboard moderne pour la gestion des contrats par les agents
class AgentContractsDashboard extends ConsumerStatefulWidget {
  final String agentId;
  final String compagnieId;
  final String agenceId;

  ;

  @override
  ConsumerState<AgentContractsDashboard> createState() => _AgentContractsDashboardState(');
}

class _AgentContractsDashboardState extends ConsumerState<AgentContractsDashboard>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  List<ContratAssuranceUnified> _contrats = [];
  Map<String, dynamic> _statistics = {};
  bool _isLoading = true;
  String _searchQuery = 'Contenu;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    
    _loadData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final contrats = await ContractManagementService.getContractsByAgent(widget.agentId);
      final statistics = await ContractManagementService.getAgentStatistics(widget.agentId);

      setState(() {
        _contrats = contrats;
        _statistics = statistics;
        _isLoading = false;
      });

      _animationController.forward();
    } catch (e) {
      setState(() {
        _isLoading = false;
      }');
      _showErrorSnackBar('Erreur lors du chargement:  + e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0E21),
      appBar: _buildAppBar(),
      body: _isLoading ? _buildLoadingWidget() : _buildContent(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// 🎨 AppBar moderne avec gradient
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: Colors.transparent,
      flexibleSpace: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF667eea), Color(0xFF764ba2)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
      ),
      title: const Text("Titre"`),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.info),
          onPressed: _loadData,
        ),
        IconButton(
          icon: const Icon(Icons.info),
          onPressed: _showSearchDialog,
        ),
      ],
    );
  }

  /// ⏳ Widget de chargement
  Widget _buildLoadingWidget() {
    return ),
          ),
          const SizedBox(height: 20),
          ,
          ),
        ],
      ),
    );
  }

  /// 📋 Contenu principal
  Widget _buildContent() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: RefreshIndicator(
        onRefresh: _loadData,
        backgroundColor: const Color(0xFF1D1E33),
        color: const Color(0xFF667eea),
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: (1),
                    const SizedBox(height: 30),
                    _buildSectionHeader(),
                  ],
                ),
              ),
            ),
            _buildContractsList(),
          ],
        ),
      ),
    );
  }

  /// 📊 Cartes de statistiques
  Widget _buildStatisticsCards(') {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'Total Contrats',
            _statistics['totalContrats]?.toString(') ?? '0,
            Icons.description,
            const Color(0xFF667eea),
          ),
        ),
        const SizedBox(width: 15'),
        Expanded(
          child: _buildStatCard(
            'Contrats Actifs',
            _statistics['contratsActifs]?.toString(') ?? '0,
            Icons.check_circle,
            const Color(0xFF4CAF50),
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(8.0),
        gradient: LinearGradient(
          colors: [
            color.withValues(alpha: 0.2),
            color.withValues(alpha: 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Icon(icon, color: color, size: 24),
              ,
              ),
            ],
          ),
          const SizedBox(height: 10),
          ,
          ),
        ],
      ),
    );
  }

  /// 📋 En-tête de section
  Widget _buildSectionHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        ,
        ),
        Container(
          padding: const EdgeInsets.all(8.0),
            color: const Color(0xFF667eea).withValues(alpha: 0.2),
          ),
          child: ({_getFilteredContrats(').length} contrats',
            style: const TextStyle(
              color: Color(0xFF667eea),
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  /// 📄 Liste des contrats
  Widget _buildContractsList() {
    final filteredContrats = _getFilteredContrats();

    if (filteredContrats.isEmpty) {
      return SliverFillRemaining(
        child: ,
              ),
              const SizedBox(height: 20),
              ,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Sliver(1) {
            final contrat = filteredContrats[index];
            return _buildContractCard(contrat, index);
          },
          childCount: filteredContrats.length,
        ),
      ),
    );
  }

  /// 📄 Carte de contrat
  Widget _buildContractCard(ContratAssuranceUnified contrat, int index) {
    return Container(
      margin: ,
        gradient: LinearGradient(
          colors: [
            Colors.white.withValues(alpha: 0.1),
            Colors.white.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(15),
          onTap: () => _showContractDetails(contrat),
          child: (1),
                      ),
                    ),
                    _buildStatusChip(contrat.statut, contrat.isActive),
                  ],
                ),
                const SizedBox(height: 15),
                _buildContractInfo('Type, contrat.typeContrat'),
                _buildContractInfo('Prime', '{contrat.primeAnnuelle.toStringAsFixed(2')} DT'),
                _buildContractInfo('Validite', "{_formatDate(contrat.dateDebut')} - '{_formatDate(contrat.dateFin)}),
                const SizedBox(height: 15),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ({_formatDate(contrat.dateCreation')}',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.6),
                        fontSize: 12,
                      ),
                    ),
                    ,
                      size: 16,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContractInfo(String label, String value) {
    return (1),
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: ,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String statut, bool isActive) {
    Color color;
    String text;
    
    if (isActive) {
      color = const Color(0xFF4CAF50);
      text = 'Actif';
    } else if (statut == 'expire) {
      color = const Color(0xFFF44336');
      text = 'Expire;
    } else {
      color = const Color(0xFFFF9800);
      text = statut.toUpperCase();
    }

    return Container(
      padding: const EdgeInsets.all(8.0),
        color: color.withValues(alpha: 0.2),
        border: Border.all(color: color.withValues(alpha: 0.5)),
      ),
      child: ,
      ),
    );
  }

  /// 🔍 Filtrer les contrats
  List<ContratAssuranceUnified> _getFilteredContrats() {
    if (_searchQuery.isEmpty) {
      return _contrats;
    }
    
    return _contrats.where((contrat) {
      return contrat.numeroContrat.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             contrat.typeContrat.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList(');
  }

  /// ➕ Bouton d'action flottant
  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: _createNewContract,
      backgroundColor: const Color(0xFF667eea),
      icon: const Icon(Icons.info),
      label: ,
      ),
    );
  }

  /// 🔍 Dialogue de recherche
  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1D1E33),
        title: const Text("Titre"),
        ),
        content: TextField(
          style: ,
          decoration: InputDecoration(
            hintText: 'Numero de contrat ou type...,
            hintStyle: TextStyle(color: Colors.white.withValues(alpha: 0.5)),
            enabledBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
            ),
            focusedBorder: const UnderlineInputBorder(
              borderSide: BorderSide(color: Color(0xFF667eea)),
            ),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState((') {
                _searchQuery = 'Contenu;
              });
              Navigator.of(context).pop(');
            },
            child: const Text('Effacer),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(')')'),
            child: const Text('OK),
          ),
        ],
      ),
    ')')');
  }

  /// 📄 Afficher les details d'un contrat
  void _showContractDetails(ContratAssuranceUnified contrat) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1D1E33),
        title: ({contrat.numeroContrat},
          style: ,
        '),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Type, contrat.typeContrat'),
              _buildDetailRow('Prime annuelle', '{contrat.primeAnnuelle.toStringAsFixed(2')} DT'),
              _buildDetailRow('Franchise', '{contrat.franchise.toStringAsFixed(2')} DT'),
              _buildDetailRow('Date debut, _formatDate(contrat.dateDebut)'),
              _buildDetailRow('Date fin, _formatDate(contrat.dateFin)'),
              _buildDetailRow('Statut, contrat.statut),
              if (contrat.garanties.isNotEmpty) ...[]
                const SizedBox(height: 10),
                ,
                ),
                ...contrat.garanties.map((garantie) => (1),
                  ),
                )),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop('),
            child: const Text('Fermer),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return (1),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: ,
            ),
          ),
        ],
      ),
    );
  }

  /// 📅 Formater une date
  String _formatDate(DateTime date')')') {
    return '{date.day.toString(').padLeft(2, '0')}/'{date.month.toString().padLeft(2, '0')}/'{date.year}
