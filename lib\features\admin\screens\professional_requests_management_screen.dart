import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/professional_request_model_final.dart';
import '../services/professional_request_management_service.dart';

/// 👨‍💼 Écran de gestion des demandes de comptes professionnels
class ProfessionalRequestsManagementScreen extends StatefulWidget {
  const Text(\;

  @override
  State<ProfessionalRequestsManagementScreen> createState() => _ProfessionalRequestsManagementScreenState();
}

class _ProfessionalRequestsManagementScreenState extends State<ProfessionalRequestsManagementScreen> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  String _selectedFilter = 'all';
  bool _isProcessing = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildFilterTabs(),
          Expanded(
            child: _buildRequestsList(),
          ),
        ],
      ),
    );
  }

  /// 🎯 AppBar moderne
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: Icon<PERSON><PERSON>on(
        icon: ),
        onPressed: () => Navigator.pop(context),
      ),
      title: const Text(
        'Demandes de Comptes Professionnels',
        style: TextStyle(
          color: Color(0xFF1E293B),
          fontSize: 18,
          fontWeight: FontWeight.w700,
        ),
      ),
      centerTitle: true,
      bottom: PreferredSize(
        preferredSize: ,
        ),
      ),
    );
  }

  /// 📊 Onglets de filtrage
  Widget _buildFilterTabs() {
    return Container(
      margin: ,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: ,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          _buildFilterTab('all', 'Toutes', Icons.list_alt),
          _buildFilterTab('en_attente', 'En attente', Icons.hourglass_empty),
          _buildFilterTab('acceptee', 'Acceptées', Icons.check_circle),
          _buildFilterTab('rejetee', 'Rejetées', Icons.cancel),
        ],
      ),
    );
  }

  Widget _buildFilterTab(String value, String label, IconData icon) {
    final isSelected = _selectedFilter == value;
    return Expanded(
      child: GestureDetector(
        onTap: () => setState(() => _selectedFilter = value),
        child: Container(
          padding:  : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              ,
                size: 20,
              ),
              const SizedBox(height: 4),
              ,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 📋 Liste des demandes
  Widget _buildRequestsList() {
    return StreamBuilder<QuerySnapshot>(
      stream: _getFilteredStream(),
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return _buildErrorWidget(snapshot.error.toString());
        }

        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        final requests = snapshot.data?.docs ?? [];

        if (requests.isEmpty) {
          return _buildEmptyWidget();
        }

        return ListView.builder(
          padding:  {
            final doc = requests[index];
            final request = ProfessionalRequestModel.fromFirestore(doc);
            return _buildRequestCard(request);
          },
        );
      },
    );
  }

  /// 🔍 Stream filtré selon la sélection
  Stream<QuerySnapshot> _getFilteredStream() {
    Query query = _firestore.collection('demandes_professionnels');
    
    if (_selectedFilter != 'all') {
      query = query.where('status', isEqualTo: _selectedFilter);
    }
    
    return query.orderBy('envoye_le', descending: true).snapshots();
  }

  /// 🎴 Carte de demande
  Widget _buildRequestCard(ProfessionalRequestModel request) {
    return Container(
      margin: ,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // En-tête de la carte
          Container(
            padding: .withValues(alpha: 0.1),
              borderRadius: ,
              ),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  backgroundColor: _getStatusColor(request.status),
                  child: .toUpperCase(),
                    style: ,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ,
                        ),
                      ),
                      ,
                        ),
                      ),
                    ],
                  ),
                ),
                _buildStatusBadge(request.status),
              ],
            ),
          ),
          
          // Contenu de la carte
          (1)),
                _buildInfoRow('Téléphone', request.tel),
                _buildInfoRow('CIN', request.cin),
                if (request.nomCompagnie != null)
                  _buildInfoRow('Compagnie', request.nomCompagnie!),
                if (request.nomAgence != null)
                  _buildInfoRow('Agence', request.nomAgence!),
                _buildInfoRow('Date', _formatDate(request.envoyeLe)),
                
                if (request.status == 'en_attente') ...[
                  const SizedBox(height: 16),
                  _buildActionButtons(request),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 🏷️ Badge de statut
  Widget _buildStatusBadge(String status) {
    return Container(
      padding: ,
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Text(
        _getStatusLabel(status),
        style: ,
      ),
    );
  }

  /// 📝 Ligne d'information
  Widget _buildInfoRow(String label, String value) {
    return (1),
              ),
            ),
          ),
          Expanded(
            child: const Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF1E293B),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 🎬 Boutons d'action
  Widget _buildActionButtons(ProfessionalRequestModel request) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.const Icon(
            onPressed: _isProcessing ? null : () => _handleApproval(request, false),
            icon: const Icon(Icons.info),
            label: const Text('Refuser'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: ,
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.const Icon(
            onPressed: _isProcessing ? null : () => _handleApproval(request, true),
            icon: const Icon(Icons.info),
            label: const Text('Approuver'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              padding: ,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// ✅ Gérer l'approbation/refus
  Future<void> _handleApproval(ProfessionalRequestModel request, bool approve) async {
    setState(() => _isProcessing = true);

    try {
      final success = await ProfessionalRequestManagementService.processRequest(
        request.id,
        approve,
        'Traité par Super Admin',
      );

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(
                approve
                    ? 'Demande approuvée avec succès!'
                    : 'Demande refusée avec succès!',
              ),
              backgroundColor: approve ? Colors.green : Colors.orange,
            ),
          );
        }
      } else {
        throw Exception('Échec du traitement');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isProcessing = false);
    }
  }

  /// 🎨 Couleur selon le statut
  Color _getStatusColor(String status) {
    switch (status) {
      case 'en_attente':
        return Colors.orange;
      case 'acceptee':
        return Colors.green;
      case 'rejetee':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  /// 🏷️ Label du statut
  String _getStatusLabel(String status) {
    switch (status) {
      case 'en_attente':
        return 'En attente';
      case 'acceptee':
        return 'Acceptée';
      case 'rejetee':
        return 'Rejetée';
      default:
        return 'Inconnu';
    }
  }

  /// 🏷️ Label du rôle
  String _getRoleLabel(String role) {
    switch (role) {
      case 'agent_agence':
        return 'Agent d\'agence';
      case 'expert_auto':
        return 'Expert automobile';
      case 'admin_compagnie':
        return 'Admin compagnie';
      case 'admin_agence':
        return 'Admin agence';
      default:
        return role;
    }
  }

  /// 📅 Formater la date
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} à ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  /// ❌ Widget d'erreur
  Widget _buildErrorWidget(String error) {
    return ,
          const SizedBox(height: 16),
          (error',
            style: ,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => setState(() {},
            child: const Text('Réessayer'),
          ),
        ],
      ),
    );
  }

  /// 📭 Widget vide
  Widget _buildEmptyWidget() {
    String message;
    IconData icon;

    switch (_selectedFilter) {
      case 'en_attente':
        message = 'Aucune demande en attente';
        icon = Icons.hourglass_empty;
        break;
      case 'acceptee':
        message = 'Aucune demande acceptée';
        icon = Icons.check_circle;
        break;
      case 'rejetee':
        message = 'Aucune demande rejetée';
        icon = Icons.cancel;
        break;
      default:
        message = 'Aucune demande trouvée
