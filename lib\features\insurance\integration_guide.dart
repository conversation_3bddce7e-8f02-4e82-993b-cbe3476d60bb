import 'package:flutter/material.dart';
import 'widgets/insurance_navigation.dart';
import 'utils/insurance_styles.dart';

/// 📱 Guide d'intégration simple pour le système d'assurance
class InsuranceIntegrationGuide extends StatelessWidget {
  const InsuranceIntegrationGuide({Key? key}) ) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text("Titre"),
        ),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: ,
            const SizedBox(height: 24),

            // Section d'accès principal
            _buildMainAccessSection(context),
            const SizedBox(height: 24),

            // Section d'information
            _buildInfoSection(),
            const SizedBox(height: 24),

            // Section des fonctionnalités
            _buildFeaturesSection(),
          ],
        ),
      ),
    );
  }

  /// 👋 Section de bienvenue
  Widget _buildWelcomeSection() {
    return Container(
      width: double.infinity,
      padding: ,
          ),
          const SizedBox(height: 8),
          ,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  /// 🎯 Section d'accès principal
  Widget _buildMainAccessSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        const SizedBox(height: 16),
        
        // Carte d'accès à l'assurance
        InsuranceNavigation.buildInsuranceCard(context),
        
        const SizedBox(height: 16),
        
        // Boutons d'accès direct
        Row(
          children: [
            Expanded(
              child: ElevatedButton.const Icon(
                onPressed: () => InsuranceNavigation.navigateToMyVehicles(context),
                icon: const Icon(Icons.info),
                label: const Text('Mes Véhicules'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[600],
                  foregroundColor: Colors.white,
                  padding: ,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.const Icon(
                onPressed: () => _checkUserRole(context),
                icon: const Icon(Icons.info),
                label: const Text('Espace Agent'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green[600],
                  foregroundColor: Colors.white,
                  padding: ,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// ℹ️ Section d'information
  Widget _buildInfoSection() {
    return Container(
      padding: ,
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              ,
              const SizedBox(width: 8),
              ,
              ),
            ],
          ),
          const SizedBox(height: 12),
          ,
          ),
        ],
      ),
    );
  }

  /// 🚀 Section des fonctionnalités
  Widget _buildFeaturesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        const SizedBox(height: 16),
        
        // Fonctionnalités pour agents
        _buildFeatureGroup(
          title: '👨‍💼 Pour les Agents d\'Assurance',
          features: [
            'Tableau de bord avec statistiques',
            'Création de contrats en 3 étapes',
            'Recherche de conducteurs',
            'Gestion complète des contrats',
            'Notifications automatiques',
          ],
          color: Colors.green,
        ),
        
        const SizedBox(height: 16),
        
        // Fonctionnalités pour conducteurs
        _buildFeatureGroup(
          title: '🚗 Pour les Conducteurs',
          features: [
            'Visualisation des véhicules assurés',
            'Détails des contrats d\'assurance',
            'Notifications de nouveaux contrats',
            'Contact direct avec l\'agent',
            'Statut d\'expiration en temps réel',
          ],
          color: Colors.blue,
        ),
      ],
    );
  }

  Widget _buildFeatureGroup({
    required String title,
    required List<String> features,
    required Color color,
  }) {
    return Container(
      padding: ,
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: ,
                  borderRadius: BorderRadius.circular(8),
                ),
                child:  ? Icons.business : Icons.directions_car,
                  color: color,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...features.map((feature) => (1),
                const SizedBox(width: 8),
                Expanded(
                  child: const Text(
                    feature,
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  /// 🔍 Vérifier le rôle de l'utilisateur
  void _checkUserRole(BuildContext context) {
    final user = FirebaseAuth.instance.currentUser;
    
    if (user == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: const Text('Veuillez vous connecter pour accéder à l\'espace agent'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // Logique simple basée sur l'email pour la démo
    final email = user.email ?? 'Contenu';
    if (email.contains('agent') || email.contains('assurance')) {
      InsuranceNavigation.navigateToInsuranceDashboard(context);
    } else {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Accès Restreint'),
          content: const Text(
            'L\'espace agent est réservé aux agents d\'assurance.\n\n'
            'Pour accéder à cette fonctionnalité, votre email doit contenir "agent" ou "assurance".',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Compris'),
            ),
          ],
        ),
      );
    }
  }
}

/// 🔧 Exemple d'intégration dans main.dart
class MainAppExample extends StatelessWidget {
  const MainAppExample({Key? key}) ) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Constat Tunisie'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
      ),
      body: (1),
            
            // Intégration du système d
