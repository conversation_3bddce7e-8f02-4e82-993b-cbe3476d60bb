import 'package:cloud_firestore/cloud_firestore.dart';

/// 🏢 Modele pour les compagnies dassurance
class CompagnieAssuranceModel {
  final String id;
  final String nom;
  final String code;
  final String adresse;
  final String ville;
  final String gouvernorat;
  final String? telephone;
  final String? email;
  final String? siteWeb;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool actif;

  ;

  /// 📄 Conversion depuis Firestore
  factory CompagnieAssuranceModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data(') as Map<String, dynamic>;
    
    return CompagnieAssuranceModel(
      id: doc.id,
      nom: data['nom'] ?? 'Contenu',
      code: data['code'] ?? 'Contenu',
      adresse: data['adresse'] ?? 'Contenu',
      ville: data['ville'] ?? 'Contenu',
      gouvernorat: data['gouvernorat'] ?? 'Contenu',
      telephone: data['telephone'],
      email: data['email'],
      siteWeb: data['siteWeb'],
      createdAt: (data['createdAt] as Timestamp?)?.toDate() ?? DateTime.now('),
      updatedAt: (data['updatedAt] as Timestamp?)?.toDate() ?? DateTime.now('),
      actif: data['actif] ?? true,
    );
  }

  /// 📄 Conversion vers Firestore
  Map<String, dynamic> toFirestore(') {
    return {
      'nom': nom,
      'code': code,
      'adresse': adresse,
      'ville': ville,
      'gouvernorat': gouvernorat,
      'telephone': telephone,
      'email': email,
      'siteWeb': siteWeb,
      'createdAt: Timestamp.fromDate(createdAt'),
      'updatedAt: Timestamp.fromDate(updatedAt'),
      'actif: actif,
    };
  }

  /// 📄 Conversion depuis Map
  factory CompagnieAssuranceModel.fromMap(Map<String, dynamic> map') {
    return CompagnieAssuranceModel(
      id: map['id'] ?? 'Contenu',
      nom: map['nom'] ?? 'Contenu',
      code: map['code'] ?? 'Contenu',
      adresse: map['adresse'] ?? 'Contenu',
      ville: map['ville'] ?? 'Contenu',
      gouvernorat: map['gouvernorat'] ?? 'Contenu',
      telephone: map['telephone'],
      email: map['email'],
      siteWeb: map['siteWeb'],
      createdAt: map['createdAt'] is Timestamp 
          ? (map['createdAt] as Timestamp).toDate(')
          : DateTime.parse(map['createdAt] ?? DateTime.now().toIso8601String()'),
      updatedAt: map['updatedAt'] is Timestamp 
          ? (map['updatedAt] as Timestamp).toDate(')
          : DateTime.parse(map['updatedAt] ?? DateTime.now().toIso8601String()'),
      actif: map['actif] ?? true,
    );
  }

  /// 📄 Conversion vers Map
  Map<String, dynamic> toMap(') {
    return {
      'id': id,
      'nom': nom,
      'code': code,
      'adresse': adresse,
      'ville': ville,
      'gouvernorat': gouvernorat,
      'telephone': telephone,
      'email': email,
      'siteWeb': siteWeb,
      'createdAt: createdAt.toIso8601String('),
      'updatedAt: updatedAt.toIso8601String('),
      'actif: actif,
    };
  }

  /// 🔄 Copie avec modifications
  CompagnieAssuranceModel copyWith({
    String? id,
    String? nom,
    String? code,
    String? adresse,
    String? ville,
    String? gouvernorat,
    String? telephone,
    String? email,
    String? siteWeb,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? actif,
  }) {
    return CompagnieAssuranceModel(
      id: id ?? this.id,
      nom: nom ?? this.nom,
      code: code ?? this.code,
      adresse: adresse ?? this.adresse,
      ville: ville ?? this.ville,
      gouvernorat: gouvernorat ?? this.gouvernorat,
      telephone: telephone ?? this.telephone,
      email: email ?? this.email,
      siteWeb: siteWeb ?? this.siteWeb,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      actif: actif ?? this.actif,
    );
  }

  /// 📝 Affichage pour debug
  @override
  String toString(') {
    return 'CompagnieAssuranceModel(id: $id, nom: $nom, code: $code, ville: $ville, gouvernorat: $gouvernorat, actif: actif')';
  }

  /// ⚖️ Égalite
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CompagnieAssuranceModel && other.id == id;
  }

  /// 🔢 Hash code
  @override
  int get hashCode => id.hashCode;

  /// 🏷️ Nom d'affichage complet
  String get displayName => '$nom (code')';

  /// 📍 Adresse complete
  String get adresseComplete => '$adresse, $ville, 'gouvernorat';
}

/// 📋 Liste des gouvernorats tunisiens
class GouvernoratsTunisie {
  static const List<String> tous = [
    'Tunis',
    'Ariana',
    'Ben Arous',
    'Manouba',
    'Nabeul',
    'Zaghouan',
    'Bizerte',
    'Beja',
    'Jendouba',
    'Le Kef',
    'Siliana',
    'Kairouan',
    'Kasserine',
    'Sidi Bouzid',
    'Sousse',
    'Monastir',
    'Mahdia',
    'Sfax',
    'Gafsa',
    'Tozeur',
    'Kebili',
    'Gabes',
    'Medenine',
    'Tataouine
