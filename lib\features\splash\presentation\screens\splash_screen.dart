import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/config/app_router.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/widgets/animated_logo.dart';
// import '../../../auth/presentation/providers/auth_provider.dart';
// import '../providers/splash_provider.dart;

/// 🚀 Écran de demarrage avec animation
class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _textController;
  late AnimationController _progressController;
  
  late Animation<double> _logoScale;
  late Animation<double> _logoOpacity;
  late Animation<double> _textOpacity;
  late Animation<Offset> _textSlide;
  late Animation<double> _progressValue;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startSplashSequence();
  }

  /// 🎬 Initialisation des animations
  void _initializeAnimations() {
    // Animation du logo
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _logoScale = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    _logoOpacity = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: ,
    ));

    // Animation du texte
    _textController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _textOpacity = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeIn,
    ));

    _textSlide = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeOutCubic,
    ));

    // Animation de la barre de progression
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _progressValue = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    )');
  }

  /// 🎭 Sequence d'animation du splash
  void _startSplashSequence() async {
    // Demarrer lanimation du logo
    await _logoController.forward();
    
    // Attendre un peu puis demarrer le texte
    await Future.delayed(const Duration(milliseconds: 300));
    _textController.forward();
    
    // Demarrer la barre de progression
    await Future.delayed(const Duration(milliseconds: 500));
    _progressController.forward(');

    // Initialiser l'application en arriere-plan
    _initializeApp();
  }

  /// 🔧 Initialisation de lapplication
  void _initializeApp() async {
    try {
      // Version simplifiee sans Firebase
      // Simuler le chargement
      await Future.delayed(const Duration(milliseconds: 1000));

      // Attendre que les animations se terminent
      await _progressController.forward();
      await Future.delayed(const Duration(milliseconds: 500)');

      // Naviguer vers l'ecran suivant
      _navigateToNextScreen();
    } catch (e) {
      debugPrint('[SPLASH] Erreur d\'initialisation:  + e.toString()');
      // En cas d'erreur, naviguer quand même
      await Future.delayed(const Duration(seconds: 1));
      _navigateToNextScreen();
    }
  }

  /// 🧭 Navigation vers lecran suivant
  void _navigateToNextScreen() async {
    if (!mounted') return;

    // TEMPORAIRE: Forcer l'affichage de l'onboarding pour tester
    debugPrint('[SPLASH] Navigation forcee vers onboarding pour test);
    Navigator.pushReplacementNamed(context, AppRouter.onboarding);

    /* 
 */