import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/insurance_system_service.dart';
import '../services/auto_fill_service.dart';
import '../models/insurance_system_models.dart';
import 'accident_declaration_screen.dart';

/// 🚗 Dashboard moderne pour l'espace conducteur multi-véhicules
class ConducteurDashboard extends ConsumerStatefulWidget {
  final String conducteurId;

  ;

  @override
  ConsumerState<ConducteurDashboard> createState() => _ConducteurDashboardState();
}

class _ConducteurDashboardState extends ConsumerState<ConducteurDashboard>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  List<VehiculeUnified> _vehicules = [];
  Map<String, ContratAssuranceUnified> _contrats = {};
  Map<String, CompagnieAssuranceUnified> _compagnies = {};
  bool _isLoading = true;
  String _searchQuery = 'Contenu';

  // Couleurs pour les cartes de véhicules
  final List<List<Color>> _cardGradients = [
    [const Color(0xFF667eea), const Color(0xFF764ba2)],
    [const Color(0xFF4facfe), const Color(0xFF00f2fe)],
    [const Color(0xFFfa709a), const Color(0xFFfee140)],
    [const Color(0xFFa8edea), const Color(0xFFfed6e3)],
    [const Color(0xFFffecd2), const Color(0xFFfcb69f)],
    [const Color(0xFFd299c2), const Color(0xFFfef9d7)],
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic));
    
    _loadData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Charger les véhicules du conducteur
      final vehicules = await InsuranceSystemService.getVehiculesByClient(widget.conducteurId);
      
      // Charger les contrats et compagnies associés
      final Map<String, ContratAssuranceUnified> contrats = {};
      final Map<String, CompagnieAssuranceUnified> compagnies = {};
      
      for (final vehicule in vehicules) {
        if (vehicule.contratId != null) {
          final contrat = await InsuranceSystemService.getContratById(vehicule.contratId!);
          if (contrat != null) {
            contrats[vehicule.contratId!] = contrat;
            
            final compagnie = await InsuranceSystemService.getCompagnieById(contrat.compagnieId);
            if (compagnie != null) {
              compagnies[contrat.compagnieId] = compagnie;
            }
          }
        }
      }

      setState(() {
        _vehicules = vehicules;
        _contrats = contrats;
        _compagnies = compagnies;
        _isLoading = false;
      });

      _animationController.forward();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('Erreur lors du chargement: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0E21),
      appBar: _buildAppBar(),
      body: _isLoading ? _buildLoadingWidget() : _buildContent(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// 🎨 AppBar moderne avec gradient
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: Colors.transparent,
      flexibleSpace: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF667eea), Color(0xFF764ba2)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
      ),
      title: const Text("Titre"),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.info),
          onPressed: _loadData,
        ),
        IconButton(
          icon: const Icon(Icons.info),
          onPressed: _showSearchDialog,
        ),
        IconButton(
          icon: const Icon(Icons.info),
          onPressed: _showProfileDialog,
        ),
      ],
    );
  }

  /// ⏳ Widget de chargement
  Widget _buildLoadingWidget() {
    return ),
          ),
          const SizedBox(height: 20),
          ,
          ),
        ],
      ),
    );
  }

  /// 📋 Contenu principal
  Widget _buildContent() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: RefreshIndicator(
          onRefresh: _loadData,
          backgroundColor: const Color(0xFF1D1E33),
          color: const Color(0xFF667eea),
          child: CustomScrollView(
            slivers: [
              SliverToBoxAdapter(
                child: (1),
                      const SizedBox(height: 30),
                      _buildStatisticsRow(),
                      const SizedBox(height: 30),
                      _buildSectionHeader(),
                    ],
                  ),
                ),
              ),
              _buildVehiculesList(),
            ],
          ),
        ),
      ),
    );
  }

  /// 👋 Carte de bienvenue
  Widget _buildWelcomeCard() {
    return Container(
      padding: ,
        gradient: const LinearGradient(
          colors: [Color(0xFF667eea), Color(0xFF764ba2)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF667eea).withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ,
                ),
                const SizedBox(height: 8),
                ,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: ,
              borderRadius: BorderRadius.circular(15),
            ),
            child: ,
          ),
        ],
      ),
    );
  }

  /// 📊 Ligne de statistiques
  Widget _buildStatisticsRow() {
    final vehiculesActifs = _vehicules.where((v) => 
      _contrats[v.contratId]?.isActive ?? false).length;
    
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'Total Véhicules',
            _vehicules.length.toString(),
            Icons.directions_car,
            const Color(0xFF4CAF50),
          ),
        ),
        const SizedBox(width: 15),
        Expanded(
          child: _buildStatCard(
            'Assurés',
            vehiculesActifs.toString(),
            Icons.security,
            const Color(0xFF2196F3),
          ),
        ),
        const SizedBox(width: 15),
        Expanded(
          child: _buildStatCard(
            'Compagnies',
            _compagnies.length.toString(),
            Icons.business,
            const Color(0xFFFF9800),
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: ,
        color: color.withValues(alpha: 0.1),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          const Icon(icon, color: color, size: 30),
          const SizedBox(height: 10),
          ,
          ),
          const SizedBox(height: 5),
          ,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 📋 En-tête de section
  Widget _buildSectionHeader() {
    final filteredVehicules = _getFilteredVehicules();
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        ,
        ),
        Container(
          padding: ,
            color: const Color(0xFF667eea).withValues(alpha: 0.2),
          ),
          child: ({filteredVehicules.length > 1 ? 's' : 'Contenu'}',
            style: const TextStyle(
              color: Color(0xFF667eea),
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  /// 🚗 Liste des véhicules
  Widget _buildVehiculesList() {
    final filteredVehicules = _getFilteredVehicules();

    if (filteredVehicules.isEmpty) {
      return SliverFillRemaining(
        child: ,
              ),
              const SizedBox(height: 20),
              ,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 20),
              ElevatedButton.const Icon(
                onPressed: _addNewVehicule,
                icon: const Icon(Icons.info),
                label: const Text('Ajouter un véhicule'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF667eea),
                  foregroundColor: Colors.white,
                  padding: ,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Sliver(1) {
            final vehicule = filteredVehicules[index];
            final gradientIndex = index % _cardGradients.length;
            return _buildVehiculeCard(vehicule, gradientIndex);
          },
          childCount: filteredVehicules.length,
        ),
      ),
    );
  }

  /// 🚙 Carte de véhicule
  Widget _buildVehiculeCard(VehiculeUnified vehicule, int gradientIndex) {
    final contrat = _contrats[vehicule.contratId];
    final compagnie = contrat != null ? _compagnies[contrat.compagnieId] : null;
    final gradient = _cardGradients[gradientIndex];

    return Container(
      margin: ,
        gradient: LinearGradient(
          colors: gradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: gradient[0].withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () => _showVehiculeDetails(vehicule),
          child: (1),
                          ),
                          const SizedBox(height: 5),
                          ,
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                    _buildStatusBadge(contrat?.isActive ?? false),
                  ],
                ),
                const SizedBox(height: 20),
                if (compagnie != null) ...[
                  _buildInfoRow(Icons.business, 'Assureur', compagnie.nom),
                  const SizedBox(height: 8),
                ],
                if (contrat != null) ...[
                  _buildInfoRow(Icons.confirmation_number, 'Contrat', contrat.numeroContrat),
                  const SizedBox(height: 8),
                  _buildInfoRow(Icons.calendar_today, 'Validité', 
                    '${_formatDate(contrat.dateDebut)} - ${_formatDate(contrat.dateFin)}'),
                ] else ...[
                  _buildInfoRow(Icons.warning, 'Statut', 'Non assuré'),
                ],
                const SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                      child: _buildActionButton(
                        'Voir Détails',
                        Icons.visibility,
                        () => _showVehiculeDetails(vehicule),
                      ),
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      child: _buildActionButton(
                        'Déclarer Accident',
                        Icons.report_problem,
                        () => _declareAccident(vehicule),
                        isPrimary: true,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        , size: 16),
        const SizedBox(width: 8),
        (label: ',
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.8),
            fontSize: 14,
          ),
        ),
        Expanded(
          child: ,
          ),
        ),
      ],
    );
  }

  Widget _buildStatusBadge(bool isActive) {
    return Container(
      padding: ,
        color: isActive 
            ? Colors.green.withValues(alpha: 0.2)
            : Colors.red.withValues(alpha: 0.2),
        border: Border.all(
          color: isActive 
              ? Colors.green.withValues(alpha: 0.5)
              : Colors.red.withValues(alpha: 0.5),
        ),
      ),
      child: ,
      ),
    );
  }

  Widget _buildActionButton(String label, IconData icon, VoidCallback onPressed, {bool isPrimary = false}) {
    return ElevatedButton.const Icon(
      onPressed: onPressed,
      icon: const Icon(icon, size: 16),
      label: const Text(label, style: const TextStyle(fontSize: 12)),
      style: ElevatedButton.styleFrom(
        backgroundColor: isPrimary 
            ? Colors.white.withValues(alpha: 0.2)
            : Colors.white.withValues(alpha: 0.1),
        foregroundColor: Colors.white,
        elevation: 0,
        padding: ,
          side: BorderSide(
            color: Colors.white.withValues(alpha: 0.3),
          ),
        ),
      ),
    );
  }

  /// 🔍 Filtrer les véhicules
  List<VehiculeUnified> _getFilteredVehicules() {
    if (_searchQuery.isEmpty) {
      return _vehicules;
    }
    
    return _vehicules.where((vehicule) {
      return vehicule.immatriculation.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             vehicule.marque.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             vehicule.modele.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
  }

  /// ➕ Bouton d'action flottant
  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: _addNewVehicule,
      backgroundColor: const Color(0xFF667eea),
      icon: const Icon(Icons.info),
      label: ,
      ),
    );
  }

  /// 🔍 Dialogue de recherche
  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1D1E33),
        title: const Text("Titre"),
        ),
        content: TextField(
          style: ,
          decoration: InputDecoration(
            hintText: 'Immatriculation, marque ou modèle...',
            hintStyle: TextStyle(color: Colors.white.withValues(alpha: 0.5)),
            enabledBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
            ),
            focusedBorder: const UnderlineInputBorder(
              borderSide: BorderSide(color: Color(0xFF667eea)),
            ),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _searchQuery = 'Contenu';
              });
              Navigator.of(context).pop();
            },
            child: const Text('Effacer'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// 👤 Dialogue de profil
  void _showProfileDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1D1E33),
        title: const Text("Titre"),
        ),
        content: ,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Ouvrir l'écran de modification du profil
            },
            child: const Text('Modifier'),
          ),
        ],
      ),
    );
  }

  /// 🚗 Afficher les détails d'un véhicule
  void _showVehiculeDetails(VehiculeUnified vehicule) {
    final contrat = _contrats[vehicule.contratId];
    final compagnie = contrat != null ? _compagnies[contrat.compagnieId] : null;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1D1E33),
        title: ({vehicule.modele}',
          style: ,
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Immatriculation', vehicule.immatriculation),
              _buildDetailRow('Année', vehicule.annee.toString()),
              _buildDetailRow('Couleur', vehicule.couleur),
              _buildDetailRow('Carburant', vehicule.carburant),
              _buildDetailRow('Puissance', '${vehicule.puissance} CV'),
              if (compagnie != null) ...[
                const SizedBox(height: 15),
                ,
                ),
                _buildDetailRow('Compagnie', compagnie.nom),
                if (contrat != null) ...[
                  _buildDetailRow('N° Contrat', contrat.numeroContrat),
                  _buildDetailRow('Type', contrat.typeContrat),
                  _buildDetailRow('Prime', '${contrat.primeAnnuelle.toStringAsFixed(2)} DT'),
                  _buildDetailRow('Validité', '${_formatDate(contrat.dateDebut)} - ${_formatDate(contrat.dateFin)}'),
                ],
              ] else ...[
                const SizedBox(height: 15),
                ,
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
          if (contrat != null)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _declareAccident(vehicule);
              },
              child: const Text('Déclarer Accident'),
            ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return (1),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: ,
            ),
          ),
        ],
      ),
    );
  }

  /// 📅 Formater une date
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// 🚨 Déclarer un accident
  Future<void> _declareAccident(VehiculeUnified vehicule) async {
    try {
      // Utiliser le service d'auto-remplissage
      final autoFillData = await AutoFillService.getConducteurDataForAutoFill(widget.conducteurId);
      if (autoFillData != null) {
        final preFilledData = AutoFillService.createPreFilledAccidentReport(
          autoFillData: autoFillData,
          dateAccident: DateTime.now(),
          lieuAccident: 'Contenu',
        );

        // Vérifier que le widget est toujours monté avant d'utiliser le contexte
        if (!mounted) return;

        // Naviguer vers l'écran de déclaration d'accident avec les données pré-remplies
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AccidentDeclarationScreen(
              conducteurId: widget.conducteurId,
              vehiculeId: vehicule.id,
              preFilledData: preFilledData,
            ),
          ),
        );
      } else {
        // Naviguer sans données pré-remplies
        if (!mounted) return;
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AccidentDeclarationScreen(
              conducteurId: widget.conducteurId,
              vehiculeId: vehicule.id,
            ),
          ),
        );
      }

    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Erreur lors de la préparation du formulaire: $e');
      }
    }
  }

  /// ➕ Ajouter un nouveau véhicule
  void _addNewVehicule() {
    // TODO: Naviguer vers l'écran d'ajout de véhicule
    _showInfoSnackBar('Fonctionnalité d\'ajout de véhicule à implémenter');
  }

  /// 📱 Messages d
