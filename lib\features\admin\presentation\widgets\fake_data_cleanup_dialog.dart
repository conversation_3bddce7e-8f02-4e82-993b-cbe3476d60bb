import 'package:flutter/material.dart';
import '../../services/fake_data_cleanup_service.dart';

/// 🧹 Dialog pour nettoyer les données fake
class FakeDataCleanupDialog extends StatefulWidget {
  const FakeDataCleanupDialog({Key? key}) ) : super(key: key);

  @override
  State<FakeDataCleanupDialog> createState() => _FakeDataCleanupDialogState();
}

class _FakeDataCleanupDialogState extends State<FakeDataCleanupDialog> {
  bool _isLoading = false;
  bool _isChecking = true;
  Map<String, dynamic>? _fakeDataStatus;
  Map<String, dynamic>? _cleanupResult;

  @override
  void initState() {
    super.initState();
    _checkFakeDataStatus();
  }

  Future<void> _checkFakeDataStatus() async {
    setState(() => _isChecking = true);
    
    final status = await FakeDataCleanupService.getFakeDataStatus();
    
    if (mounted) {
      setState(() {
        _fakeDataStatus = status;
        _isChecking = false;
      });
    }
  }

  Future<void> _cleanAllFakeData() async {
    final confirmed = await _showConfirmationDialog();
    if (!confirmed) return;

    setState(() => _isLoading = true);
    
    final result = await FakeDataCleanupService.cleanAllFakeData();
    
    if (mounted) {
      setState(() {
        _cleanupResult = result;
        _isLoading = false;
      });
      
      // Recharger le statut après nettoyage
      await _checkFakeDataStatus();
    }
  }

  Future<bool> _showConfirmationDialog() async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('⚠️ Confirmation'),
        content: const Text(
          'Êtes-vous sûr de vouloir supprimer TOUTES les données fake ?\n\n'
          'Cette action est irréversible et supprimera tous les documents '
          'marqués avec isFakeData: true dans toutes les collections.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade600,
              foregroundColor: Colors.white,
            ),
            child: const Text('Confirmer'),
          ),
        ],
      ),
    ) ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: 600,
        constraints: const BoxConstraints(maxHeight: 700),
        padding: ,
                  decoration: BoxDecoration(
                    color: Colors.orange.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      .textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      .textTheme.bodyMedium?.copyWith(
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.info),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Statut des données fake
                    if (_isChecking) ...[
                      const Center(
                        child: Column(
                          children: [
                            CircularProgressIndicator(),
                            const SizedBox(height: 16),
                            const Text('Vérification des données fake...'),
                          ],
                        ),
                      ),
                    ] else if (_fakeDataStatus != null) ...[
                      _buildStatusSection(),
                    ],
                    
                    const SizedBox(height: 24),
                    
                    // Résultat du nettoyage
                    if (_cleanupResult != null) ...[
                      _buildCleanupResultSection(),
                      const SizedBox(height: 24),
                    ],
                    
                    // Avertissement
                    Container(
                      padding: ,
                        border: Border.all(color: Colors.red.shade200),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              ,
                              const SizedBox(width: 8),
                              ,
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          ,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Boutons d'action
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.const Icon(
                    onPressed: _isLoading ? null : _checkFakeDataStatus,
                    icon: const Icon(Icons.info),
                    label: const Text('Actualiser'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.const Icon(
                    onPressed: _isLoading || _isChecking ? null : _cleanAllFakeData,
                    icon: _isLoading 
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : ,
                    label: const Text(_isLoading ? 'Nettoyage...' : 'Nettoyer Tout'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red.shade600,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSection() {
    final status = _fakeDataStatus!;
    final hasFakeData = status['hasFakeData'] as bool? ?? false;
    final totalFake = status['totalFake'] as int? ?? 0;
    final details = status['details'] as Map<String, int>? ?? {};

    return Container(
      padding: ,
        border: Border.all(
          color: hasFakeData ? Colors.orange.shade200 : Colors.green.shade200,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              ,
              const SizedBox(width: 8),
              ,
              ),
            ],
          ),
          const SizedBox(height: 8),
          (totalFake documents fake trouvés dans la base de données'
                : 'La base de données ne contient aucune donnée fake',
            style: TextStyle(
              color: hasFakeData ? Colors.orange.shade700 : Colors.green.shade700,
            ),
          ),
          
          if (hasFakeData && details.isNotEmpty) ...[
            const SizedBox(height: 12),
            ,
            ),
            const SizedBox(height: 8),
            ...details.entries
                .where((entry) => entry.value > 0)
                .map((entry) => (1),
                      ),
                      Container(
                        padding: ,
                        ),
                        child: ({entry.value}',
                          style: TextStyle(
                            color: Colors.orange.shade700,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ],
      ),
    );
  }

  Widget _buildCleanupResultSection() {
    final result = _cleanupResult!;
    final success = result['success'] as bool? ?? false;
    final totalDeleted = result['totalDeleted'] as int? ?? 0;
    final details = result['details'] as Map<String, int>? ?? {};

    return Container(
      padding: ,
        border: Border.all(
          color: success ? Colors.green.shade200 : Colors.red.shade200,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              ,
              const SizedBox(width: 8),
              ,
              ),
            ],
          ),
          const SizedBox(height: 8),
          (totalDeleted documents fake supprimés avec succès'
                : result['message'] ?? 'Erreur inconnue',
            style: TextStyle(
              color: success ? Colors.green.shade700 : Colors.red.shade700,
            ),
          ),
          
          if (success && details.isNotEmpty) ...[
            const SizedBox(height: 12),
            ,
            ),
            const SizedBox(height: 8),
            ...details.entries
                .where((entry) => entry.value > 0)
                .map((entry) => (1),
                      ),
                      Container(
                        padding: ,
                        ),
                        child: ({entry.value} supprimés
