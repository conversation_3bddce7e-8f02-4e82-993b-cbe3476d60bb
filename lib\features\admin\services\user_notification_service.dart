import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// 📧 Service de notifications pour les actions utilisateur
class UserNotificationService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  /// ✅ Notification d'approbation d'utilisateur
  static Future<void> notifyUserApproval({
    required String userId,
    required String userEmail,
    required String userName,
    String? approvedBy,
  }) async {
    try {
      // Créer une notification dans Firestore
      await _createNotification(
        userId: userId,
        type: 'user_approved',
        title: 'Compte approuvé',
        message: 'Votre compte a été approuvé par l\'administrateur. Vous pouvez maintenant vous connecter.',
        data: {
          'approvedBy': approvedBy,
          'approvedAt': FieldValue.serverTimestamp(),
        },
      );

      // Envoyer un email (si configuré)
      await _sendEmailNotification(
        to: userEmail,
        subject: 'Compte approuvé - Constat Tunisie',
        template: 'user_approved',
        data: {
          'userName': userName,
          'approvedBy': approvedBy ?? 'Administrateur',
        },
      );

      debugPrint('[UserNotificationService] ✅ Notification d\'approbation envoyée à $userEmail');
    } catch (e) {
      debugPrint('[UserNotificationService] ❌ Erreur notification approbation: $e');
    }
  }

  /// 🚫 Notification de désactivation d'utilisateur
  static Future<void> notifyUserDisabled({
    required String userId,
    required String userEmail,
    required String userName,
    String? disabledBy,
    String? reason,
  }) async {
    try {
      await _createNotification(
        userId: userId,
        type: 'user_disabled',
        title: 'Compte désactivé',
        message: reason ?? 'Votre compte a été temporairement désactivé par l\'administrateur.',
        data: {
          'disabledBy': disabledBy,
          'disabledAt': FieldValue.serverTimestamp(),
          'reason': reason,
        },
      );

      await _sendEmailNotification(
        to: userEmail,
        subject: 'Compte désactivé - Constat Tunisie',
        template: 'user_disabled',
        data: {
          'userName': userName,
          'disabledBy': disabledBy ?? 'Administrateur',
          'reason': reason ?? 'Aucune raison spécifiée',
        },
      );

      debugPrint('[UserNotificationService] 🚫 Notification de désactivation envoyée à $userEmail');
    } catch (e) {
      debugPrint('[UserNotificationService] ❌ Erreur notification désactivation: $e');
    }
  }

  /// 🔄 Notification de réactivation d'utilisateur
  static Future<void> notifyUserEnabled({
    required String userId,
    required String userEmail,
    required String userName,
    String? enabledBy,
  }) async {
    try {
      await _createNotification(
        userId: userId,
        type: 'user_enabled',
        title: 'Compte réactivé',
        message: 'Votre compte a été réactivé. Vous pouvez maintenant vous connecter.',
        data: {
          'enabledBy': enabledBy,
          'enabledAt': FieldValue.serverTimestamp(),
        },
      );

      await _sendEmailNotification(
        to: userEmail,
        subject: 'Compte réactivé - Constat Tunisie',
        template: 'user_enabled',
        data: {
          'userName': userName,
          'enabledBy': enabledBy ?? 'Administrateur',
        },
      );

      debugPrint('[UserNotificationService] 🔄 Notification de réactivation envoyée à $userEmail');
    } catch (e) {
      debugPrint('[UserNotificationService] ❌ Erreur notification réactivation: $e');
    }
  }

  /// 🗑️ Notification de suppression d'utilisateur
  static Future<void> notifyUserDeleted({
    required String userId,
    required String userEmail,
    required String userName,
    String? deletedBy,
  }) async {
    try {
      await _createNotification(
        userId: userId,
        type: 'user_deleted',
        title: 'Compte supprimé',
        message: 'Votre compte a été supprimé par l\'administrateur.',
        data: {
          'deletedBy': deletedBy,
          'deletedAt': FieldValue.serverTimestamp(),
        },
      );

      await _sendEmailNotification(
        to: userEmail,
        subject: 'Compte supprimé - Constat Tunisie',
        template: 'user_deleted',
        data: {
          'userName': userName,
          'deletedBy': deletedBy ?? 'Administrateur',
        },
      );

      debugPrint('[UserNotificationService] 🗑️ Notification de suppression envoyée à $userEmail');
    } catch (e) {
      debugPrint('[UserNotificationService] ❌ Erreur notification suppression: $e');
    }
  }

  /// 🏢 Notification de changement de compagnie/agence
  static Future<void> notifyUserAssignmentChanged({
    required String userId,
    required String userEmail,
    required String userName,
    String? newCompagnie,
    String? newAgence,
    String? changedBy,
  }) async {
    try {
      String message = 'Vos affectations ont été mises à jour par l\'administrateur.';
      if (newCompagnie != null) {
        message += '\nNouvelle compagnie: $newCompagnie';
      }
      if (newAgence != null) {
        message += '\nNouvelle agence: $newAgence';
      }

      await _createNotification(
        userId: userId,
        type: 'user_assignment_changed',
        title: 'Affectation mise à jour',
        message: message,
        data: {
          'changedBy': changedBy,
          'changedAt': FieldValue.serverTimestamp(),
          'newCompagnie': newCompagnie,
          'newAgence': newAgence,
        },
      );

      await _sendEmailNotification(
        to: userEmail,
        subject: 'Affectation mise à jour - Constat Tunisie',
        template: 'user_assignment_changed',
        data: {
          'userName': userName,
          'changedBy': changedBy ?? 'Administrateur',
          'newCompagnie': newCompagnie ?? 'Non spécifiée',
          'newAgence': newAgence ?? 'Non spécifiée',
        },
      );

      debugPrint('[UserNotificationService] 🏢 Notification de changement d\'affectation envoyée à $userEmail');
    } catch (e) {
      debugPrint('[UserNotificationService] ❌ Erreur notification changement: $e');
    }
  }

  /// 📝 Créer une notification dans Firestore
  static Future<void> _createNotification({
    required String userId,
    required String type,
    required String title,
    required String message,
    Map<String, dynamic>? data,
  }) async {
    try {
      await _firestore.collection('notifications').add({
        'userId': userId,
        'type': type,
        'title': title,
        'message': message,
        'data': data ?? {},
        'read': false,
        'createdAt': FieldValue.serverTimestamp(),
        'createdBy': FirebaseAuth.instance.currentUser?.uid,
      });
    } catch (e) {
      debugPrint('[UserNotificationService] ❌ Erreur création notification: $e');
    }
  }

  /// 📧 Envoyer un email de notification
  static Future<void> _sendEmailNotification({
    required String to,
    required String subject,
    required String template,
    Map<String, dynamic>? data,
  }) async {
    try {
      // TODO: Implémenter l'envoi d'email avec le service EmailJS ou autre
      // Pour l'instant, on simule l'envoi
      debugPrint('[UserNotificationService] 📧 Email simulé:');
      debugPrint('  - À: $to');
      debugPrint('  - Sujet: $subject');
      debugPrint('  - Template: $template');
      debugPrint('  - Données: $data');
      
      // Ici on pourrait utiliser le service EmailJS existant
      // ou un autre service d'email comme SendGrid, Mailgun, etc.
      
    } catch (e) {
      debugPrint('[UserNotificationService] ❌ Erreur envoi email: $e');
    }
  }

  /// 📊 Notification d'export de données
  static Future<void> notifyDataExport({
    required String adminEmail,
    required String adminName,
    required String exportType,
    required String fileName,
    int? recordCount,
  }) async {
    try {
      await _sendEmailNotification(
        to: adminEmail,
        subject: 'Export de données - Constat Tunisie',
        template: 'data_export',
        data: {
          'adminName': adminName,
          'exportType': exportType,
          'fileName': fileName,
          'recordCount': recordCount ?? 0,
          'exportDate': DateTime.now().toIso8601String(),
        },
      );

      debugPrint('[UserNotificationService] 📊 Notification d\'export envoyée à $adminEmail');
    } catch (e) {
      debugPrint('[UserNotificationService] ❌ Erreur notification export: $e');
    }
  }

  /// 🔔 Obtenir les notifications d'un utilisateur
  static Stream<List<Map<String, dynamic>>> getUserNotifications(String userId) {
    return _firestore
        .collection('notifications')
        .where('userId', isEqualTo: userId)
        .orderBy('createdAt', descending: true)
        .limit(50)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    });
  }

  /// ✅ Marquer une notification comme lue
  static Future<void> markNotificationAsRead(String notificationId) async {
    try {
      await _firestore
          .collection('notifications')
          .doc(notificationId)
          .update({'read': true, 'readAt': FieldValue.serverTimestamp()});
    } catch (e) {
      debugPrint('[UserNotificationService] ❌ Erreur marquage lecture: $e');
    }
  }

  /// 🗑️ Supprimer une notification
  static Future<void> deleteNotification(String notificationId) async {
    try {
      await _firestore.collection('notifications').doc(notificationId).delete();
    } catch (e) {
      debugPrint('[UserNotificationService] ❌ Erreur suppression notification: $e
