import 'package:flutter/material.dart';

/// 🔐 Widget pour vérifier et afficher l'état de connexion Firebase Auth
class AuthStatusWidget extends StatefulWidget {
  const Text(\;

  @override
  State<AuthStatusWidget> createState() => _AuthStatusWidgetState();
}

class _AuthStatusWidgetState extends State<AuthStatusWidget> {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  User? _currentUser;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _checkAuthStatus();
    
    // Écouter les changements d'état d'authentification
    _auth.authStateChanges().listen((User? user) {
      if (mounted) {
        setState(() => _currentUser = user);
      }
    });
  }

  /// 🔍 Vérifier l'état d'authentification
  void _checkAuthStatus() {
    setState(() {
      _currentUser = _auth.currentUser;
    });
  }

  /// 🔐 Se connecter avec le compte Super Admin
  Future<void> _signInAsSuperAdmin() async {
    setState(() => _isLoading = true);

    try {
      debugPrint('[AUTH_STATUS] 🔐 Tentative de connexion Super Admin...');
      
      final userCredential = await _auth.signInWithEmailAndPassword(
        email: '<EMAIL>',
        password: 'Acheya123',
      );

      debugPrint('[AUTH_STATUS] ✅ Connexion réussie: ${userCredential.user?.uid}');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: const Text('✅ Connecté en tant que Super Admin !'),
            backgroundColor: Colors.green,
          ),
        );
      }

    } catch (e) {
      debugPrint('[AUTH_STATUS] ❌ Erreur connexion: $e');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 🚪 Se déconnecter
  Future<void> _signOut() async {
    try {
      await _auth.signOut();
      debugPrint('[AUTH_STATUS] 🚪 Déconnexion réussie');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: const Text('🚪 Déconnecté'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      debugPrint('[AUTH_STATUS] ❌ Erreur déconnexion: $e');
    }
  }

  /// 🎨 Obtenir la couleur du statut
  Color _getStatusColor() {
    if (_currentUser == null) return Colors.red;
    if (_currentUser!.email == '<EMAIL>') return Colors.green;
    return Colors.orange;
  }

  /// 🎨 Obtenir l'icône du statut
  IconData _getStatus {
    if (_currentUser == null) return Icons.error;
    if (_currentUser!.email == '<EMAIL>') return Icons.admin_panel_settings;
    return Icons.warning;
  }

  /// 🎨 Obtenir le texte du statut
  String _getStatus {
    if (_currentUser == null) return 'Non connecté';
    if (_currentUser!.email == '<EMAIL>') return 'Super Admin';
    return 'Compte incorrect';
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: (1),
                  color: _getStatusColor(),
                  size: 24,
                ),
                const SizedBox(width: 8),
                ,
                ),
                ,
                IconButton(
                  onPressed: _checkAuthStatus,
                  icon: const Icon(Icons.info),
                  tooltip: 'Actualiser',
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Statut actuel
            Container(
              padding: .withValues(alpha: ,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _getStatusColor().withValues(alpha: ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      (1),
                        color: _getStatusColor(),
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      (1),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: _getStatusColor(),
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 8),
                  
                  if (_currentUser != null) ...[
                    ({_currentUser!.email}'),
                    ({_currentUser!.uid}'),
                    ({_currentUser!.emailVerified}'),
                  ] else ...[
                    ,
                    ),
                  ],
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Actions
            Row(
              children: [
                if (_currentUser == null) ...[
                  ElevatedButton.const Icon(
                    onPressed: _isLoading ? null : _signInAsSuperAdmin,
                    icon: _isLoading 
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : ,
                    label: const Text('Se connecter Super Admin'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ] else ...[
                  if (_currentUser!.email != '<EMAIL>') ...[
                    ElevatedButton.const Icon(
                      onPressed: _signOut,
                      icon: const Icon(Icons.info),
                      label: const Text('Se déconnecter'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton.const Icon(
                      onPressed: _isLoading ? null : _signInAsSuperAdmin,
                      icon: _isLoading 
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : ,
                      label: const Text('Basculer Super Admin'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ] else ...[
                    ElevatedButton.const Icon(
                      onPressed: _signOut,
                      icon: const Icon(Icons.info),
                      label: const Text('Se déconnecter'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ],
              ],
            ),
            
            // Instructions
            if (_currentUser == null) ...[
              const SizedBox(height: 16),
              Container(
                padding: ,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ,
                    ),
                    const SizedBox(height: 8),
                    const Text('1. Cliquez sur "Se connecter Super Admin"'),
                    const Text('2. Ou connectez-vous manuellement avec:'),
                    ,
                    const Text('   🔑 Mot de passe: Acheya123
