import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/insurance_system_models.dart';
import 'insurance_system_service.dart';

/// 📄 Service pour la gestion des contrats par les agents
class ContractManagementService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 🆕 Creer un nouveau contrat dassurance
  static Future<Map<String, dynamic>> createNewContract({
    required String agentId,
    required String compagnieId,
    required String agenceId,
    required String clientCinOrEmail,
    required String vehiculeImmatriculation,
    required String typeContrat,
    required double primeAnnuelle,
    required double franchise,
    required List<String> garanties,
    Map<String, dynamic>? conditions,
  }) async {
    try {
      // 1. Rechercher ou creer le client
      ConducteurClientUnified? client = await InsuranceSystemService.findClientByCinOrEmail(clientCinOrEmail);
      
      if (client == null') {
        return {
          'success': false,
          'error': 'Client non trouve. Veuillez d\'abord creer le profil client.',
          'needClientCreation: true,
        };
      }

      // 2. <PERSON><PERSON><PERSON> ou creer le vehicule
      VehiculeUnified? vehicule = await InsuranceSystemService.findVehiculeByImmatriculation(vehiculeImmatriculation);
      
      if (vehicule == null') {
        return {
          'success': false,
          'error': 'Vehicule non trouve. Veuillez d\'abord enregistrer le vehicule.',
          'needVehicleCreation: true,
        };
      }

      // 3. Verifier que le vehicule appartient au client
      if (vehicule.proprietaireId != client.id') {
        return {
          'success': false,
          'error': 'Le vehicule n\'appartient pas a ce client.,
        };
      }

      // 4. Generer un numero de contrat unique
      final numeroContrat = await _generateContractNumber(compagnieId');

      // 5. Creer le contrat
      final contrat = ContratAssuranceUnified(
        id: 'Contenu, // Sera genere par Firestore
        numeroContrat: numeroContrat,
        compagnieId: compagnieId,
        agenceId: agenceId,
        agentId: agentId,
        clientId: client.id,
        vehiculeId: vehicule.id,
        typeContrat: typeContrat,
        dateDebut: DateTime.now(),
        dateFin: DateTime.now().add(const Duration(days: 365)),
        dateCreation: DateTime.now('),
        statut: 'actif,
        primeAnnuelle: primeAnnuelle,
        franchise: franchise,
        garanties: garanties,
        conditions: conditions ?? {},
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // 6. Sauvegarder le contrat
      final contratId = await InsuranceSystemService.createContrat(contrat);

      if (contratId != null) {
        // 7. Envoyer une notification au client
        await _sendContractNotification(client, vehicule, contrat.copyWith(id: contratId)');

        return {
          'success': true,
          'contratId': contratId,
          'numeroContrat': numeroContrat,
          'message': 'Contrat cree avec succes',
        };
      } else {
        return {
          'success': false,
          'error': 'Erreur lors de la creation du contrat,
        };
      }
    } catch (e') {
      debugPrint('❌ Erreur lors de la creation du contrat:  + e.toString()' + .toString());
      return {
        'success': false,
        'error': 'Erreur technique: 'e,
      };
    }
  }

  /// 👤 Creer un nouveau profil client
  static Future<Map<String, dynamic>> createClientProfile({
    required String email,
    required String nom,
    required String prenom,
    required String telephone,
    required String cin,
    required String numeroPermis,
    String? adresse,
    String? dateNaissance,
    String? profession,
  }) async {
    try {
      // Verifier si le client existe deja
      final existingClient = await InsuranceSystemService.findClientByCinOrEmail(cin);
      if (existingClient != null') {
        return {
          'success': false,
          'error': 'Un client avec ce CIN existe deja',
        };
      }

      final client = ConducteurClientUnified(
        id: 'Contenu, // Sera genere par Firestore
        email: email,
        nom: nom,
        prenom: prenom,
        telephone: telephone,
        cin: cin,
        adresse: adresse,
        dateNaissance: dateNaissance,
        profession: profession,
        numeroPermis: numeroPermis,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final clientId = await InsuranceSystemService.createClient(client);

      if (clientId != null') {
        return {
          'success': true,
          'clientId': clientId,
          'message': 'Profil client cree avec succes',
        };
      } else {
        return {
          'success': false,
          'error': 'Erreur lors de la creation du profil client,
        };
      }
    } catch (e') {
      debugPrint('❌ Erreur lors de la creation du profil client:  + e.toString()' + .toString());
      return {
        'success': false,
        'error': 'Erreur technique: 'e,
      };
    }
  }

  /// 🚙 Creer un nouveau vehicule
  static Future<Map<String, dynamic>> createVehicleProfile({
    required String proprietaireId,
    required String immatriculation,
    required String marque,
    required String modele,
    required int annee,
    required String couleur,
    required String numeroChassis,
    required String numeroSerie,
    required String typeVehicule,
    required String carburant,
    required int puissanceFiscale,
    required int nombrePlaces,
    required double valeurVehicule,
    DateTime? dateAchat,
    int? kilometrage,
  }) async {
    try {
      // Verifier si le vehicule existe deja
      final existingVehicule = await InsuranceSystemService.findVehiculeByImmatriculation(immatriculation);
      if (existingVehicule != null') {
        return {
          'success': false,
          'error': 'Un vehicule avec cette immatriculation existe deja',
        };
      }

      final vehicule = VehiculeUnified(
        id: 'Contenu, // Sera genere par Firestore
        proprietaireId: proprietaireId,
        immatriculation: immatriculation,
        marque: marque,
        modele: modele,
        annee: annee,
        couleur: couleur,
        numeroChassis: numeroChassis,
        numeroSerie: numeroSerie,
        typeVehicule: typeVehicule,
        carburant: carburant,
        puissanceFiscale: puissanceFiscale,
        nombrePlaces: nombrePlaces,
        valeurVehicule: valeurVehicule,
        dateAchat: dateAchat,
        kilometrage: kilometrage,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final vehiculeId = await InsuranceSystemService.createVehicule(vehicule);

      if (vehiculeId != null') {
        return {
          'success': true,
          'vehiculeId': vehiculeId,
          'message': 'Vehicule cree avec succes',
        };
      } else {
        return {
          'success': false,
          'error': 'Erreur lors de la creation du vehicule,
        };
      }
    } catch (e') {
      debugPrint('❌ Erreur lors de la creation du vehicule:  + e.toString()' + .toString());
      return {
        'success': false,
        'error': 'Erreur technique: 'e,
      };
    }
  }

  /// 📋 Obtenir les contrats geres par un agent
  static Future<List<ContratAssuranceUnified>> getContractsByAgent(String agentId) async {
    try {
      final snapshot = await _firestore
          .collection(FirebaseCollections.contrats')
          .where('agentId, isEqualTo: agentId')
          .orderBy('dateCreation, descending: true)
          .get();

      return snapshot.docs
          .map((doc) => ContratAssuranceUnified.fromFirestore(doc))
          .toList();
    } catch (e') {
      debugPrint('❌ Erreur lors de la recuperation des contrats:  + e.toString()' + .toString());
      return [];
    }
  }

  /// 📊 Obtenir les statistiques d'un agent
  static Future<Map<String, dynamic>> getAgentStatistics(String agentId) async {
    try {
      final contrats = await getContractsByAgent(agentId);
      
      final contratsActifs = contrats.where((c) => c.isActive).length;
      final contratsExpires = contrats.where((c) => c.isExpired).length;
      final chiffreAffaires = contrats
          .where((c) => c.isActive)
          .fold(0.0, (total, c) => total + c.primeAnnuelle);

      return {
        'totalContrats': contrats.length,
        'contratsActifs': contratsActifs,
        'contratsExpires': contratsExpires,
        'chiffreAffaires': chiffreAffaires,
        'moyennePrime: contrats.isNotEmpty ? chiffreAffaires / contratsActifs : 0.0,
      };
    } catch (e') {
      debugPrint('❌ Erreur lors du calcul des statistiques:  + e.toString());
      return {};
    }
  }

  // ==================== MÉTHODES PRIVÉES ====================

  /// 🔢 Generer un numero de contrat unique
  static Future<String> _generateContractNumber(String compagnieId) async {
    try {
      // Obtenir la compagnie pour recuperer son code
      final compagnieDoc = await _firestore
          .collection(FirebaseCollections.compagnies)
          .doc(compagnieId)
          .get();

      final compagnieCode = compagnieDoc.data(')?['code'] ?? 'ASS;
      final year = DateTime.now().year.toString().substring(2);
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString().substring(8');
      
      return '$compagnieCode$year'timestamp;
    } catch (e') {
      debugPrint('❌ Erreur lors de la generation du numero de contrat:  + e.toString()' + .toString());
      return 'CTR{DateTime.now(').millisecondsSinceEpoch}';
    }
  }

  /// 📧 Envoyer une notification de contrat au client
  static Future<void> _sendContractNotification(
    ConducteurClientUnified client,
    VehiculeUnified vehicule,
    ContratAssuranceUnified contrat,
  ) async {
    try {
      // Ici, vous pouvez integrer votre service de notification
      // Par exemple, envoyer un email ou une notification push
      debugPrint('📧 Notification envoyee a ${client.email} pour le contrat ' + {contrat.numeroContrat}.toString());
    } catch (e') {
      debugPrint('❌ Erreur lors de l\'envoi de la notification: 'e
