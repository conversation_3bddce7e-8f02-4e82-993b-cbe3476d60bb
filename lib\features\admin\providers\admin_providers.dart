import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/admin_models.dart';
import '../models/agent_admin_model.dart';
import '../services/admin_firestore_service.dart';
import '../services/agent_admin_service.dart';
import '../services/compagnie_service.dart';

/// 🏢 PROVIDERS POUR LES COMPAGNIES

/// Provider pour le service des compagnies
final compagnieServiceProvider = Provider<CompagnieService>((ref) {
  return CompagnieService();
});

/// Provider pour toutes les compagnies (Super Admin)
final compagniesProvider = StreamProvider<List<CompagnieAssurance>>((ref) {
  return AdminFirestoreService.getAllCompagnies();
});

/// Provider pour une compagnie spécifique
final compagnieProvider = FutureProvider.family<CompagnieAssurance?, String>((ref, compagnieId) {
  return AdminFirestoreService.getCompagnieById(compagnieId);
});

/// Provider pour les compagnies en format dropdown (mise à jour en temps réel)
final compagniesDropdownProvider = StreamProvider<List<Map<String, dynamic>>>((ref) {
  return ref.read(compagnieServiceProvider).getCompagniesForDropdownStream();
});

/// 🏬 PROVIDERS POUR LES AGENCES

/// Provider pour toutes les agences (Super Admin)
final agencesProvider = StreamProvider<List<AgenceAssurance>>((ref) {
  return AdminFirestoreService.getAllAgences();
});

/// Provider pour les agences d'une compagnie (Admin Compagnie)
final agencesByCompagnieProvider = StreamProvider.family<List<AgenceAssurance>, String>((ref, compagnieId) {
  return AdminFirestoreService.getAgencesByCompagnie(compagnieId);
});

/// Provider pour une agence spécifique
final agenceProvider = FutureProvider.family<AgenceAssurance?, String>((ref, agenceId) {
  return AdminFirestoreService.getAgenceById(agenceId);
});

/// 👤 PROVIDERS POUR LES UTILISATEURS

/// Provider pour tous les utilisateurs (Super Admin)
final usersProvider = StreamProvider<List<UserProfile>>((ref) {
  return AdminFirestoreService.getAllUsers();
});

/// Provider pour les utilisateurs d'une compagnie (Admin Compagnie)
final usersByCompagnieProvider = StreamProvider.family<List<UserProfile>, String>((ref, compagnieId) {
  return AdminFirestoreService.getUsersByCompagnie(compagnieId);
});

/// Provider pour les utilisateurs d'une agence (Admin Agence)
final usersByAgenceProvider = StreamProvider.family<List<UserProfile>, String>((ref, agenceId) {
  return AdminFirestoreService.getUsersByAgence(agenceId);
});

/// Provider pour un utilisateur spécifique
final userProvider = FutureProvider.family<UserProfile?, String>((ref, userId) {
  return AdminFirestoreService.getUserById(userId);
});

/// 📋 PROVIDERS POUR LES CONTRATS

/// Provider pour tous les contrats (Super Admin)
final contractsProvider = StreamProvider<List<ContractAssurance>>((ref) {
  return AdminFirestoreService.getAllContracts();
});

/// Provider pour les contrats d'une compagnie (Admin Compagnie)
final contractsByCompagnieProvider = StreamProvider.family<List<ContractAssurance>, String>((ref, compagnieId) {
  return AdminFirestoreService.getContractsByCompagnie(compagnieId);
});

/// Provider pour les contrats d'une agence (Admin Agence)
final contractsByAgenceProvider = StreamProvider.family<List<ContractAssurance>, String>((ref, agenceId) {
  return AdminFirestoreService.getContractsByAgence(agenceId);
});

/// 🚧 PROVIDERS POUR LES SINISTRES

/// Provider pour tous les sinistres (Super Admin)
final sinistresProvider = StreamProvider<List<SinistreAssurance>>((ref) {
  return AdminFirestoreService.getAllSinistres();
});

/// Provider pour les sinistres d'une compagnie (Admin Compagnie)
final sinistresByCompagnieProvider = StreamProvider.family<List<SinistreAssurance>, String>((ref, compagnieId) {
  return AdminFirestoreService.getSinistresByCompagnie(compagnieId);
});

/// Provider pour les sinistres d'une agence (Admin Agence)
final sinistresByAgenceProvider = StreamProvider.family<List<SinistreAssurance>, String>((ref, agenceId) {
  return AdminFirestoreService.getSinistresByAgence(agenceId);
});

/// 📊 PROVIDERS POUR LES STATISTIQUES

/// Provider pour les statistiques globales (Super Admin)
final globalStatsProvider = FutureProvider<Map<String, int>>((ref) async {
  final compagniesCount = await AdminFirestoreService.countDocuments('compagnies', filters: {'isActive': true});
  final agencesCount = await AdminFirestoreService.countDocuments('agences', filters: {'isActive': true});
  final usersCount = await AdminFirestoreService.countDocuments('users', filters: {'isActive': true});
  final contractsCount = await AdminFirestoreService.countDocuments('contracts', filters: {'isActive': true});
  final sinistresCount = await AdminFirestoreService.countDocuments('sinistres');
  
  return {
    'compagnies': compagniesCount,
    'agences': agencesCount,
    'users': usersCount,
    'contracts': contractsCount,
    'sinistres': sinistresCount,
  };
});

/// Provider pour les statistiques d'une compagnie (Admin Compagnie)
final compagnieStatsProvider = FutureProvider.family<Map<String, int>, String>((ref, compagnieId) async {
  final agencesCount = await AdminFirestoreService.countDocuments('agences', filters: {
    'compagnieId': compagnieId,
    'isActive': true,
  });
  final usersCount = await AdminFirestoreService.countDocuments('users', filters: {
    'compagnieId': compagnieId,
    'isActive': true,
  });
  final contractsCount = await AdminFirestoreService.countDocuments('contracts', filters: {
    'compagnieId': compagnieId,
    'isActive': true,
  });
  final sinistresCount = await AdminFirestoreService.countDocuments('sinistres', filters: {
    'compagnieAId': compagnieId,
  });
  
  return {
    'agences': agencesCount,
    'users': usersCount,
    'contracts': contractsCount,
    'sinistres': sinistresCount,
  };
});

/// Provider pour les statistiques d'une agence (Admin Agence)
final agenceStatsProvider = FutureProvider.family<Map<String, int>, String>((ref, agenceId) async {
  final usersCount = await AdminFirestoreService.countDocuments('users', filters: {
    'agenceId': agenceId,
    'isActive': true,
  });
  final contractsCount = await AdminFirestoreService.countDocuments('contracts', filters: {
    'agenceId': agenceId,
    'isActive': true,
  });
  final sinistresCount = await AdminFirestoreService.countDocuments('sinistres', filters: {
    'agenceAId': agenceId,
  });
  
  return {
    'users': usersCount,
    'contracts': contractsCount,
    'sinistres': sinistresCount,
  };
});

/// 🔐 PROVIDER POUR L'UTILISATEUR CONNECTÉ
final currentUserProvider = StateProvider<UserProfile?>((ref) => null);

/// 🎯 PROVIDER POUR FILTRER LES DONNÉES SELON LE RÔLE
final filteredDataProvider = Provider<Map<String, dynamic>>((ref) {
  final currentUser = ref.watch(currentUserProvider);
  
  if (currentUser == null) {
    return {};
  }
  
  // Retourner les providers appropriés selon le rôle
  switch (currentUser.role) {
    case 'super_admin':
      return {
        'compagnies': ref.watch(compagniesProvider),
        'agences': ref.watch(agencesProvider),
        'users': ref.watch(usersProvider),
        'contracts': ref.watch(contractsProvider),
        'sinistres': ref.watch(sinistresProvider),
        'stats': ref.watch(globalStatsProvider),
      };
      
    case 'admin_compagnie':
      if (currentUser.compagnieId != null) {
        return {
          'agences': ref.watch(agencesByCompagnieProvider(currentUser.compagnieId!)),
          'users': ref.watch(usersByCompagnieProvider(currentUser.compagnieId!)),
          'contracts': ref.watch(contractsByCompagnieProvider(currentUser.compagnieId!)),
          'sinistres': ref.watch(sinistresByCompagnieProvider(currentUser.compagnieId!)),
          'stats': ref.watch(compagnieStatsProvider(currentUser.compagnieId!)),
        };
      }
      break;
      
    case 'admin_agence':
      if (currentUser.agenceId != null) {
        return {
          'users': ref.watch(usersByAgenceProvider(currentUser.agenceId!)),
          'contracts': ref.watch(contractsByAgenceProvider(currentUser.agenceId!)),
          'sinistres': ref.watch(sinistresByAgenceProvider(currentUser.agenceId!)),
          'stats': ref.watch(agenceStatsProvider(currentUser.agenceId!)),
        };
      }
      break;
  }
  
  return {};
});

/// 👨‍💼 PROVIDERS POUR LES AGENTS

/// Provider pour tous les agents (Super Admin)
final agentsProvider = StreamProvider<List<AgentAdmin>>((ref) {
  return AgentAdminService.getAgents();
});

/// Provider pour les agents d'une agence (Admin Agence)
final agentsByAgenceProvider = StreamProvider.family<List<AgentAdmin>, String>((ref, agenceId) {
  return AgentAdminService.getAgentsByAgence(agenceId);
});

/// Provider pour les agents d'une compagnie (Admin Compagnie)
final agentsByCompagnieProvider = StreamProvider.family<List<AgentAdmin>, String>((ref, compagnieId) {
  return AgentAdminService.getAgents(compagnieId: compagnieId);
});

/// Provider pour un agent spécifique
final agentProvider = FutureProvider.family<AgentAdmin?, String>((ref, agentId) {
  return AgentAdminService.getAgentById(agentId);
});

/// Provider pour les statistiques des agents d'une agence
final agenceAgentsStatsProvider = FutureProvider.family<Map<String, dynamic>, String>((ref, agenceId) {
  return AgentAdminService.getAgenceAgentsStats(agenceId);
});

/// Provider pour générer un matricule unique
final generateMatriculeProvider = FutureProvider.family<String, Map<String, String>>((ref, params) {
  final compagnieId = params['compagnieId']!;
  final agenceId = params['agenceId']!;
  return AgentAdminService.generateMatricule(compagnieId, agenceId);
});

/// 🎯 PROVIDER POUR LA GESTION D'ÉTAT DES AGENTS

/// Provider pour l'état de création d'agent
final agentCreationStateProvider = StateProvider<AsyncValue<String?>>((ref) {
  return ;

/// Provider pour l'état de modification d'agent
final agentUpdateStateProvider = StateProvider<AsyncValue<String?>>((ref) {
  return ;

/// Provider pour l'état de suppression d'agent
final agentDeletionStateProvider = StateProvider<AsyncValue<String?>>((ref) {
  return ;

/// Provider pour l'agent sélectionné dans l'interface
final selectedAgentProvider = StateProvider<AgentAdmin?>((ref) => null);

/// Provider pour les filtres de recherche d'agents
final agentFiltersProvider = StateProvider<Map<String, String?>>((ref) {
  return {
    'searchQuery': null,
    'statut': null,
    'poste': null,
    'specialite
