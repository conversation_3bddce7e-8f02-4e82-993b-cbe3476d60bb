import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/agent_admin_model.dart';
import '../../providers/admin_providers.dart';
import '../widgets/agent_card.dart';
import '../widgets/agent_form_dialog.dart';
import '../widgets/admin_search_bar.dart';
import '../widgets/admin_filter_chips.dart';
import '../widgets/admin_stats_card.dart';

/// 👨‍💼 Écran de gestion des agents pour Admin Agence
class AgentsManagementScreen extends ConsumerStatefulWidget {
  final String agenceId;
  final String? compagnieId;

  ;

  @override
  ConsumerState<AgentsManagementScreen> createState() => _AgentsManagementScreenState();
}

class _AgentsManagementScreenState extends ConsumerState<AgentsManagementScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedStatut = 'Tous';
  String _selectedPoste = 'Tous';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final agentsAsync = ref.watch(agentsByAgenceProvider(widget.agenceId));
    final statsAsync = ref.watch(agenceAgentsStatsProvider(widget.agenceId));

    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: AppBar(
        title: const Text("Titre"),
        ),
        backgroundColor: const Color(0xFF7C3AED),
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.info),
            onPressed: () => _showAddAgentDialog(),
            tooltip: 'Ajouter un agent',
          ),
        ],
      ),
      body: Column(
        children: [
          // En-tête avec statistiques
          Container(
            decoration: , Color(0xFF8B5CF6)],
              ),
            ),
            child: Column(
              children: [
                // Statistiques
                (1) => _buildStatsRow(stats),
                    loading: () => _buildStatsLoading(),
                    error: (error, stack) => _buildStatsError(),
                  ),
                ),
                
                // Barre de recherche et filtres
                Container(
                  margin: ,
                      const SizedBox(height: 12),
                      _buildFilterChips(),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // Liste des agents
          Expanded(
            child: agentsAsync.when(
              data: (agents) => _buildAgentsList(agents),
              loading: () => _buildLoadingList(),
              error: (error, stack) => _buildErrorList(error),
            ),
          ),
        ],
      ),
    );
  }

  /// 📊 Construire la ligne de statistiques
  Widget _buildStatsRow(Map<String, dynamic> stats) {
    return Row(
      children: [
        Expanded(
          child: AdminStatsCard(
            title: 'Total',
            value: stats['totalAgents']?.toString() ?? '0',
            icon: Icons.people,
            color: const Color(0xFF7C3AED),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: AdminStatsCard(
            title: 'Actifs',
            value: stats['agentsActifs']?.toString() ?? '0',
            icon: Icons.check_circle,
            color: const Color(0xFF10B981),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: AdminStatsCard(
            title: 'Contrats',
            value: stats['agentsAvecContrats']?.toString() ?? '0',
            icon: Icons.description,
            color: const Color(0xFF3B82F6),
          ),
        ),
      ],
    );
  }

  /// 🔄 Construire le loading des statistiques
  Widget _buildStatsLoading() {
    return Row(
      children: List.generate(3, (index) => 
        Expanded(
          child: Container(
            margin: ,
              borderRadius: BorderRadius.circular(12),
            ),
            child: ,
            ),
          ),
        ),
      ),
    );
  }

  /// ❌ Construire l'erreur des statistiques
  Widget _buildStatsError() {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: ,
        ),
      ),
    );
  }

  /// 🔍 Construire les filtres
  Widget _buildFilterChips() {
    return Row(
      children: [
        Expanded(
          child: AdminFilterChips(
            label: 'Statut',
            options: const ['Tous', 'actif', 'suspendu', 'inactif', 'en_formation'],
            selectedValue: _selectedStatut,
            onChanged: (value) {
              setState(() => _selectedStatut = value);
              _applyFilters();
            },
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: AdminFilterChips(
            label: 'Poste',
            options: const ['Tous', ...AgentConstants.postes],
            selectedValue: _selectedPoste,
            onChanged: (value) {
              setState(() => _selectedPoste = value);
              _applyFilters();
            },
          ),
        ),
      ],
    );
  }

  /// 📋 Construire la liste des agents
  Widget _buildAgentsList(List<AgentAdmin> agents) {
    final filteredAgents = _filterAgents(agents);
    
    if (filteredAgents.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding:  {
        final agent = filteredAgents[index];
        return (1) => _showAgentDetails(agent),
            onEdit: () => _showEditAgentDialog(agent),
            onDelete: () => _showDeleteConfirmation(agent),
          ),
        );
      },
    );
  }

  /// 🔄 Construire le loading de la liste
  Widget _buildLoadingList() {
    return ListView.builder(
      padding:  => Container(
        margin: ,
        ),
        child: const Center(
          child: CircularProgressIndicator(
            color: Color(0xFF7C3AED),
          ),
        ),
      ),
    );
  }

  /// ❌ Construire l'erreur de la liste
  Widget _buildErrorList(Object error) {
    return ,
          ),
          const SizedBox(height: 16),
          .textTheme.headlineSmall?.copyWith(
              color: const Color(0xFF1E293B),
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          ,
            textAlign: TextAlign.center,
            style: const TextStyle(color: Color(0xFF64748B)),
          ),
          const SizedBox(height: 24),
          ElevatedButton.const Icon(
            onPressed: () => ref.refresh(agentsByAgenceProvider(widget.agenceId)),
            icon: const Icon(Icons.info),
            label: const Text('Réessayer'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF7C3AED),
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// 📭 Construire l'état vide
  Widget _buildEmptyState() {
    return ,
          ),
          const SizedBox(height: 16),
          .textTheme.headlineSmall?.copyWith(
              color: const Color(0xFF1E293B),
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Commencez par ajouter votre premier agent',
            style: TextStyle(color: Color(0xFF64748B)),
          ),
          const SizedBox(height: 24),
          ElevatedButton.const Icon(
            onPressed: _showAddAgentDialog,
            icon: const Icon(Icons.info),
            label: const Text('Ajouter un agent'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF7C3AED),
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  // ==================== MÉTHODES D'ACTION ====================

  /// 🔍 Filtrer les agents selon les critères
  List<AgentAdmin> _filterAgents(List<AgentAdmin> agents) {
    return agents.where((agent) {
      // Filtre par recherche
      final searchQuery = _searchController.text.toLowerCase();
      if (searchQuery.isNotEmpty) {
        final matchesSearch = agent.nomComplet.toLowerCase().contains(searchQuery) ||
                             agent.email.toLowerCase().contains(searchQuery) ||
                             agent.matricule.toLowerCase().contains(searchQuery);
        if (!matchesSearch) return false;
      }

      // Filtre par statut
      if (_selectedStatut != 'Tous' && agent.statut != _selectedStatut) {
        return false;
      }

      // Filtre par poste
      if (_selectedPoste != 'Tous' && agent.poste != _selectedPoste) {
        return false;
      }

      return true;
    }).toList();
  }

  /// 🔍 Gérer le changement de recherche
  void _onSearchChanged(String value) {
    setState(() {}); // Déclencher le rebuild pour filtrer
  }

  /// 🔧 Appliquer les filtres
  void _applyFilters() {
    setState(() {}); // Déclencher le rebuild pour filtrer
  }

  /// ➕ Afficher le dialogue d'ajout d'agent
  void _showAddAgentDialog() {
    showDialog(
      context: context,
      builder: (context) => AgentFormDialog(
        agenceId: widget.agenceId,
        compagnieId: widget.compagnieId,
        onSuccess: () {
          ref.refresh(agentsByAgenceProvider(widget.agenceId));
          ref.refresh(agenceAgentsStatsProvider(widget.agenceId));
        },
      ),
    );
  }

  /// ✏️ Afficher le dialogue de modification d'agent
  void _showEditAgentDialog(AgentAdmin agent) {
    showDialog(
      context: context,
      builder: (context) => AgentFormDialog(
        agent: agent,
        agenceId: widget.agenceId,
        compagnieId: widget.compagnieId,
        onSuccess: () {
          ref.refresh(agentsByAgenceProvider(widget.agenceId));
          ref.refresh(agenceAgentsStatsProvider(widget.agenceId));
        },
      ),
    );
  }

  /// 👁️ Afficher les détails d'un agent
  void _showAgentDetails(AgentAdmin agent) {
    // TODO: Implémenter l'écran de détails d'agent
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: ({agent.nomComplet}'),
        backgroundColor: const Color(0xFF7C3AED),
      ),
    );
  }

  /// 🗑️ Afficher la confirmation de suppression
  void _showDeleteConfirmation(AgentAdmin agent) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmer la suppression'),
        content: ({agent.nomComplet} ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteAgent(agent);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFEF4444),
              foregroundColor: Colors.white,
            ),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }

  /// 🗑️ Supprimer un agent
  void _deleteAgent(AgentAdmin agent) async {
    // TODO: Implémenter la suppression d'agent
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: ({agent.nomComplet} supprimé
