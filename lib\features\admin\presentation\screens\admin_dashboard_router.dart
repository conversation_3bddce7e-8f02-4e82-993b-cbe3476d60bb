import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/admin_providers.dart';
import '../../models/admin_models.dart';
import 'super_admin_dashboard.dart';
import 'admin_company_dashboard.dart';
import 'admin_agency_dashboard.dart';

/// 🎯 Router pour les dashboards admin selon le rôle
class AdminDashboardRouter extends ConsumerWidget {
  final String userId;
  
  ;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userAsync = ref.watch(userProvider(userId));
    
    return userAsync.when(
      data: (user) {
        if (user == null) {
          return _buildErrorScreen(context, 'Utilisateur non trouvé');
        }

        return _buildDashboardForRole(context, user);
      },
      loading: () => _buildLoadingScreen(),
      error: (error, stack) => _buildErrorScreen(context, 'Erreur: $error'),
    );
  }

  /// 🎯 Construit le dashboard approprié selon le rôle
  Widget _buildDashboardForRole(BuildContext context, UserProfile user) {
    switch (user.role) {
      case 'super_admin':
        return ;

      case 'admin_compagnie':
        if (user.compagnieId == null) {
          return _buildErrorScreen(context, 'Admin Compagnie sans compagnie assignée');
        }
        return AdminCompanyDashboard(compagnieId: user.compagnieId!);

      case 'admin_agence':
        if (user.agenceId == null) {
          return _buildErrorScreen(context, 'Admin Agence sans agence assignée');
        }
        return AdminAgencyDashboard(agenceId: user.agenceId!);

      default:
        return _buildErrorScreen(context, 'Rôle non autorisé: ${user.role}');
    }
  }

  /// ⏳ Écran de chargement
  Widget _buildLoadingScreen() {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: (1),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF3B82F6)),
                  ),
                  const SizedBox(height: 16),
                  ,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// ❌ Écran d'erreur
  Widget _buildErrorScreen(BuildContext context, String message) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: (1),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: .withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(50),
                ),
                child: ,
                ),
              ),
              const SizedBox(height: 24),
              ,
                ),
              ),
              const SizedBox(height: 16),
              ,
              ),
              const SizedBox(height: 32),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ElevatedButton.const Icon(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.info),
                    label: const Text('Retour'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF64748B),
                      foregroundColor: Colors.white,
                      padding: ,
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton.const Icon(
                    onPressed: () {
                      // TODO: Implémenter la déconnexion
                    },
                    icon: const Icon(Icons.info),
                    label: const Text('Se Déconnecter'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFEF4444),
                      foregroundColor: Colors.white,
                      padding: ,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 🎯 Widget pour sélectionner le type d'admin au login
class AdminTypeSelector extends StatelessWidget {
  final Function(String) onAdminTypeSelected;
  
  ;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: (1),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ,
              ),
              const SizedBox(height: 24),
              ,
                ),
              ),
              const SizedBox(height: 32),
              
              _buildAdminTypeCard(
                'Super Admin',
                'Gestion complète du système',
                Icons.security,
                const Color(0xFF3B82F6),
                () => onAdminTypeSelected('super_admin'),
              ),
              const SizedBox(height: 16),
              
              _buildAdminTypeCard(
                'Admin Compagnie',
                'Gestion des agences et agents',
                Icons.business,
                const Color(0xFF059669),
                () => onAdminTypeSelected('admin_compagnie'),
              ),
              const SizedBox(height: 16),
              
              _buildAdminTypeCard(
                'Admin Agence',
                'Gestion des agents et contrats',
                Icons.store,
                const Color(0xFF7C3AED),
                () => onAdminTypeSelected('admin_agence
