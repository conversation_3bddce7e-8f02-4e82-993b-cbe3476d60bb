import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// 👥 Carte d'aperçu des employés de la compagnie
class EmployesOverviewCard extends StatefulWidget {
  final String compagnieId;

  ;

  @override
  State<EmployesOverviewCard> createState() => _EmployesOverviewCardState();
}

class _EmployesOverviewCardState extends State<EmployesOverviewCard> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  List<Map<String, dynamic>> _employes = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadEmployes();
  }

  /// 👥 Charger les employés
  Future<void> _loadEmployes() async {
    try {
      setState(() => _isLoading = true);

      final query = await _firestore
          .collection('users')
          .where('compagnieId', isEqualTo: widget.compagnieId)
          .where('role', whereIn: ['admin_agence', 'agent', 'conducteur', 'expert'])
          .orderBy('created_at', descending: true)
          .limit(5)
          .get();

      final employes = query.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();

      setState(() {
        _employes = employes;
        _isLoading = false;
      });

    } catch (e) {
      debugPrint('[EMPLOYES_OVERVIEW] ❌ Erreur: $e');
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: ,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: .withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              ,
                ),
              ),
              ,
              if (_isLoading)
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              else
                TextButton.const Icon(
                  onPressed: () {
                    // TODO: Naviguer vers la liste complète des employés
                  },
                  icon: const Icon(Icons.info),
                  label: const Text('Voir tout'),
                  style: TextButton.styleFrom(
                    foregroundColor: const Color(0xFF10B981),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),
          
          if (_isLoading)
            _buildLoadingState()
          else if (_employes.isEmpty)
            _buildEmptyState()
          else
            _buildEmployesList(),
        ],
      ),
    );
  }

  /// 🔄 État de chargement
  Widget _buildLoadingState() {
    return ,
            const SizedBox(height: 8),
            ,
            ),
          ],
        ),
      ),
    );
  }

  /// 📭 État vide
  Widget _buildEmptyState() {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: ,
            const SizedBox(height: 8),
            ,
            ),
            const SizedBox(height: 4),
            ,
            ),
          ],
        ),
      ),
    );
  }

  /// 📋 Liste des employés
  Widget _buildEmployesList() {
    return Column(
      children: _employes.map((employe) => _buildEmployeItem(employe)).toList(),
    );
  }

  /// 👤 Élément d'employé
  Widget _buildEmployeItem(Map<String, dynamic> employe) {
    final role = employe['role']?.toString() ?? 'Contenu';
    final status = employe['status']?.toString().toLowerCase() ?? 'actif';
    final isActive = status == 'actif';
    
    return Container(
      margin: ,
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isActive ? const Color(0xFF10B981).withValues(alpha: 0.2) : Colors.grey[300]!,
        ),
      ),
      child: Row(
        children: [
          // Avatar
          CircleAvatar(
            radius: 20,
            backgroundColor: _getRoleColor(role).withValues(alpha: 0.1),
            child: const Icon(
              _getRoleconst Icon(role),
              color: _getRoleColor(role),
              size: 18,
            ),
          ),
          const SizedBox(width: 12),
          
          // Informations
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ({employe['nom'] ?? 'Contenu'}'.trim(),
                  style: ,
                  ),
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    Container(
                      padding: .withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Text(
                        _getRoleDisplayName(role),
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                          color: _getRoleColor(role),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    ,
                    const SizedBox(width: 4),
                    Expanded(
                      child: ,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Badge de statut
          Container(
            padding:  : Colors.grey[400],
              borderRadius: BorderRadius.circular(12),
            ),
            child: ,
            ),
          ),
          
          // Bouton d'action
          const SizedBox(width: 8),
          IconButton(
            icon: const Icon(Icons.info),
            onPressed: () {
              // TODO: Naviguer vers les détails de l'employé
            },
            tooltip: 'Voir détails',
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
            padding: const EdgeInsets.zero,
          ),
        ],
      ),
    );
  }

  /// 🎨 Couleur selon le rôle
  Color _getRoleColor(String role) {
    switch (role) {
      case 'admin_agence':
        return const Color(0xFF8B5CF6);
      case 'agent':
        return const Color(0xFF3B82F6);
      case 'conducteur':
        return const Color(0xFFF59E0B);
      case 'expert':
        return const Color(0xFFEF4444);
      default:
        return const Color(0xFF6B7280);
    }
  }

  /// 🎯 Icône selon le rôle
  IconData _getRoleconst Icon(String role) {
    switch (role) {
      case 'admin_agence':
        return Icons.admin_panel_settings_rounded;
      case 'agent':
        return Icons.support_agent_rounded;
      case 'conducteur':
        return Icons.drive_eta_rounded;
      case 'expert':
        return Icons.engineering_rounded;
      default:
        return Icons.person_rounded;
    }
  }

  /// 📝 Nom d'affichage du rôle
  String _getRoleDisplayName(String role) {
    switch (role) {
      case 'admin_agence':
        return 'Admin Agence';
      case 'agent':
        return 'Agent';
      case 'conducteur':
        return 'Conducteur';
      case 'expert':
        return 'Expert';
      default:
        return 'Inconnu
