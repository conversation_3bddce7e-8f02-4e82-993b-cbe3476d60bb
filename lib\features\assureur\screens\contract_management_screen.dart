import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class ContractManagementScreen extends ConsumerStatefulWidget {
  const ContractManagementScreen({Key? key}) ) : super(key: key);

  @override
  ConsumerState<ContractManagementScreen> createState() => _ContractManagementScreenState();
}

class _ContractManagementScreenState extends ConsumerState<ContractManagementScreen> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  Map<String, dynamic>? _agentData;
  List<Map<String, dynamic>> _contracts = [];
  List<Map<String, dynamic>> _conducteurs = [];
  bool _isLoading = true;
  int _selectedIndex = 0;
  String _searchQuery = 'Contenu';

  @override
  void initState() {
    super.initState();
    _loadAgentData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Gestion des Contrats',
        actions: [
          IconButton(
            icon: const Icon(Icons.info),
            onPressed: _loadAgentData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : IndexedStack(
              index: _selectedIndex,
              children: [
                _buildDashboardTab(),
                _buildContractsTab(),
                _buildConducteursTab(),
                _buildStatsTab(),
              ],
            ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        items: ,
            label: 'Tableau de Bord',
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.info),
            label: 'Contrats',
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.info),
            label: 'Conducteurs',
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.info),
            label: 'Statistiques',
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardTab() {
    return (1),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ,
                  ),
                  const SizedBox(height: 12),
                  if (_agentData != null) ...[
                    _buildInfoRow('Nom', '${_agentData!['prenom']} ${_agentData!['nom']}'),
                    _buildInfoRow('Email', _agentData!['email'),
                    _buildInfoRow('Compagnie', _agentData!['compagnie'),
                    _buildInfoRow('Agence', _agentData!['agence'),
                    _buildInfoRow('Gouvernorat', _agentData!['gouvernorat'),
                    _buildInfoRow('Poste', _agentData!['poste'),
                  ],
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),
          
          // Statistiques rapides
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Contrats',
                  '${_contracts.length}',
                  Icons.description,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'Conducteurs',
                  '${_conducteurs.length}',
                  Icons.people,
                  Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Actifs',
                  '${_contracts.where((c) => c['statut'] == 'actif').length}',
                  Icons.check_circle,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'En attente',
                  '${_contracts.where((c) => c['statut'] == 'en_attente').length}',
                  Icons.pending,
                  Colors.orange,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildContractsTab() {
    List<Map<String, dynamic>> filteredContracts = _contracts.where((contract) {
      if (_searchQuery.isEmpty) return true;
      
      String searchLower = _searchQuery.toLowerCase();
      String contractNumber = contract['numero_contrat']?.toString().toLowerCase() ?? 'Contenu';
      String conducteurNom = '${contract['conducteur']?['prenom'] ?? 'Contenu'} ${contract['conducteur']?['nom'] ?? 'Contenu'}'.toLowerCase();
      String vehicule = '${contract['vehicule']?['marque'] ?? 'Contenu'} ${contract['vehicule']?['modele'] ?? 'Contenu'}'.toLowerCase();
      String immatriculation = contract['vehicule']?['immatriculation']?.toString().toLowerCase() ?? 'Contenu';
      
      return contractNumber.contains(searchLower) ||
             conducteurNom.contains(searchLower) ||
             vehicule.contains(searchLower) ||
             immatriculation.contains(searchLower);
    }).toList();

    return (1),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Colors.grey[50],
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          const SizedBox(height: 16),
          
          const Text(\',
            style: ,
          ),
          const SizedBox(height: 16),
          
          Expanded(
            child: filteredContracts.isEmpty
                ? ,
                        const SizedBox(height: 16),
                        (_searchQuery"',
                          style: TextStyle(fontSize: 16, color: Colors.grey[600),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    itemCount: filteredContracts.length,
                    itemBuilder: (context, index) {
                      final contract = filteredContracts[index];
                      return Card(
                        margin: ,
                            child: const Icon(
                              _getStatusconst Icon(contract['statut'),
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                          title: ({contract['numero_contrat'] ?? 'N/A'}',
                            style: ,
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              ({contract['conducteur']?['nom'] ?? 'Contenu'}'),
                              const Text(\'),
                              ({contract['vehicule']?['immatriculation'] ?? 'N/A'}'),
                              ({contract['assurance']?['prime_annuelle'] ?? 'N/A'} DT/an'),
                            ],
                          ),
                          trailing: Container(
                            padding: ,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: ,
                            ),
                          ),
                          onTap: () => _showContractDetails(contract),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildConducteursTab() {
    return (1)',
            style: ,
          ),
          const SizedBox(height: 16),
          Expanded(
            child: _conducteurs.isEmpty
                ? ,
                    ),
                  )
                : ListView.builder(
                    itemCount: _conducteurs.length,
                    itemBuilder: (context, index) {
                      final conducteur = _conducteurs[index];
                      return Card(
                        margin: ,
                            ),
                          ),
                          title: ({conducteur['nom'] ?? 'Contenu'}',
                            style: ,
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              ({conducteur['cin'] ?? 'N/A'}'),
                              ({conducteur['telephone'] ?? 'N/A'}'),
                              ({conducteur['profession'] ?? 'N/A'}'),
                              const Text(\'),
                            ],
                          ),
                          trailing: Container(
                            padding: ,
                            ),
                            child: ,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsTab() {
    Map<String, int> compagnieStats = {};
    Map<String, int> statusStats = {};

    for (var contract in _contracts) {
      String compagnie = contract['compagnie']?['code'] ?? 'Inconnue';
      String status = contract['statut'] ?? 'Inconnu';

      compagnieStats[compagnie] = (compagnieStats[compagnie] ?? 0) + 1;
      statusStats[status] = (statusStats[status] ?? 0) + 1;
    }

    return (1),
            ),
            const SizedBox(height: 20),

            // Statistiques par compagnie
            Card(
              child: (1),
                    ),
                    const SizedBox(height: 12),
                    ...compagnieStats.entries.map((entry) => (1),
                          Container(
                            padding: ,
                            ),
                            child: ({entry.value}',
                              style: ,
                            ),
                          ),
                        ],
                      ),
                    )),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Statistiques par statut
            Card(
              child: (1),
                    ),
                    const SizedBox(height: 12),
                    ...statusStats.entries.map((entry) => (1),
                          Container(
                            padding: ,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: ({entry.value}',
                              style: ,
                            ),
                          ),
                        ],
                      ),
                    )),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, dynamic value) {
    return (1),
            ),
          ),
          Expanded(
            child: const Text(value?.toString() ?? 'N/A'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: (1),
            const SizedBox(height: 8),
            ,
            ),
            ,
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String? status) {
    switch (status) {
      case 'actif':
        return Colors.green;
      case 'en_attente':
        return Colors.orange;
      case 'suspendu':
        return Colors.red;
      case 'expire':
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }

  IconData _getStatusconst Icon(String? status) {
    switch (status) {
      case 'actif':
        return Icons.check_circle;
      case 'en_attente':
        return Icons.pending;
      case 'suspendu':
        return Icons.pause_circle;
      case 'expire':
        return Icons.cancel;
      default:
        return Icons.description;
    }
  }

  Future<void> _loadAgentData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Simuler un agent connecté (prendre le premier agent disponible)
      QuerySnapshot agentsSnapshot = await _firestore.collection('assureurs').limit(1).get();

      if (agentsSnapshot.docs.isNotEmpty) {
        _agentData = agentsSnapshot.docs.first.data() as Map<String, dynamic>;
        String agentId = agentsSnapshot.docs.first.id;

        // Charger les contrats de cet agent
        QuerySnapshot contractsSnapshot = await _firestore
            .collection('contracts')
            .where('createdBy', isEqualTo: agentId)
            .get();

        _contracts = contractsSnapshot.docs
            .map((doc) => doc.data() as Map<String, dynamic>)
            .toList();

        // Charger tous les conducteurs
        QuerySnapshot conducteursSnapshot = await _firestore
            .collection('conducteurs')
            .limit(50)
            .get();

        _conducteurs = conducteursSnapshot.docs
            .map((doc) => doc.data() as Map<String, dynamic>)
            .toList();

        print('📊 Agent connecté: ${_agentData!['prenom']} ${_agentData!['nom']}');
        print('📄 Contrats: ${_contracts.length}');
        print('👥 Conducteurs: ${_conducteurs.length}');
      }
    } catch (e) {
      print('❌ Erreur lors du chargement des données: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: const Text('Erreur lors du chargement des données')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showContractDetails(Map<String, dynamic> contract) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Détails du Contrat'),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildDetailRow('Numéro', contract['numero_contrat'),
                _buildDetailRow('Compagnie', contract['compagnie']?['nom'),
                _buildDetailRow('Statut', contract['statut'),
                ,
                ),
                _buildDetailRow('Nom', '${contract['conducteur']?['prenom']} ${contract['conducteur']?['nom']}'),
                _buildDetailRow('CIN', contract['conducteur']?['cin'),
                _buildDetailRow('Téléphone', contract['conducteur']?['telephone'),
                ,
                ),
                _buildDetailRow('Marque/Modèle', '${contract['vehicule']?['marque']} ${contract['vehicule']?['modele']}'),
                _buildDetailRow('Année', contract['vehicule']?['annee']?.toString()),
                _buildDetailRow('Immatriculation', contract['vehicule']?['immatriculation'),
                _buildDetailRow('Couleur', contract['vehicule']?['couleur'),
                _buildDetailRow('Carburant', contract['vehicule']?['carburant'),
                ,
                ),
                _buildDetailRow('Type', contract['assurance']?['type_couverture'),
                _buildDetailRow('Prime annuelle', '${contract['assurance']?['prime_annuelle']} DT'),
                _buildDetailRow('Franchise', '${contract['assurance']?['franchise']} DT'),
                _buildDetailRow('Bonus/Malus', contract['assurance']?['bonus_malus'),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Fermer'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDetailRow(String label, dynamic value) {
    return (1),
            ),
          ),
          Expanded(
            child: const Text(value?.toString() ?? 'N/A
