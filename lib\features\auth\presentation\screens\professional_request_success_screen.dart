import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/config/app_router.dart';
import '../../models/professional_account_request.dart';

/// 🎉 Écran de confirmation après soumission d'une demande de compte professionnel
class ProfessionalRequestSuccessScreen extends StatefulWidget {
  final ProfessionalAccountRequest request;

   ) : super(key: key);

  @override
  State<ProfessionalRequestSuccessScreen> createState() => _ProfessionalRequestSuccessScreenState();
}

class _ProfessionalRequestSuccessScreenState extends State<ProfessionalRequestSuccessScreen>
    with TickerProviderStateMixin {
  late AnimationController _iconController;
  late AnimationController _contentController;
  late Animation<double> _iconScale;
  late Animation<double> _iconRotation;
  late Animation<double> _contentOpacity;
  late Animation<Offset> _contentSlide;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }

  void _initializeAnimations() {
    // Animation de l'icône
    _iconController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _iconScale = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _iconController,
      curve: Curves.elasticOut,
    ));

    _iconRotation = Tween<double>(
      begin: -0.2,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _iconController,
      curve: Curves.easeOut,
    ));

    // Animation du contenu
    _contentController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _contentOpacity = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _contentController,
      curve: Curves.easeIn,
    ));

    _contentSlide = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _contentController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _startAnimations() async {
    await Future.delayed(const Duration(milliseconds: 300));
    _iconController.forward();
    
    await Future.delayed(const Duration(milliseconds: 600));
    _contentController.forward();
  }

  @override
  void dispose() {
    _iconController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  String _getRoleDisplayName(String role) {
    switch (role) {
      case 'agent_agence':
        return 'Agent d\'agence';
      case 'expert_auto':
        return 'Expert automobile';
      case 'admin_compagnie':
        return 'Administrateur de compagnie';
      case 'admin_agence':
        return 'Administrateur d\'agence';
      default:
        return role;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme.primaryColor,
              AppTheme.primaryColor.withValues(alpha: 0.8),
              Colors.indigo.shade900,
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: ,

                // Icône de succès animée (compacte)
                _buildAnimatedSuccess,

                const SizedBox(height: 20),

                // Contenu principal
                _buildAnimatedContent(),

                const SizedBox(height: 20),

                // Boutons d'action
                _buildActionButtons(),

                // Espace du bas minimal
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 🎯 Icône de succès animée (compacte)
  Widget _buildAnimatedSuccess {
    return AnimatedBuilder(
      animation: _iconController,
      builder: (context, child) {
        return Transform.scale(
          scale: _iconScale.value,
          child: Transform.rotate(
            angle: _iconRotation.value,
            child: Container(
              width: 90, // Réduit de 120 à 90
              height: 90, // Réduit de 120 à 90
              decoration: BoxDecoration(
                color: Colors.green.shade400,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.green.withValues(alpha: 0.3),
                    blurRadius: 15, // Réduit de 20 à 15
                    spreadRadius: 3, // Réduit de 5 à 3
                  ),
                ],
              ),
              child: ,
            ),
          ),
        );
      },
    );
  }

  /// 📄 Contenu principal animé
  Widget _buildAnimatedContent() {
    return AnimatedBuilder(
      animation: _contentController,
      builder: (context, child) {
        return SlideTransition(
          position: _contentSlide,
          child: Opacity(
            opacity: _contentOpacity.value,
            child: Column(
              children: [
                // Titre principal (plus compact)
                .textTheme.headlineSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 22, // Réduit de 26 à 22
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 12), // Réduit de 16 à 12

                // Message explicatif (plus compact)
                .textTheme.bodyMedium?.copyWith( // bodyLarge -> bodyMedium
                    color: Colors.white.withValues(alpha: 0.9),
                    height: 1.4, // Réduit de 1.5 à 1.4
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 20), // Réduit de 32 à 20

                // Détails de la demande
                _buildRequestDetails(),

                const SizedBox(height: 16), // Réduit de 24 à 16

                // Délai estimé
                _buildEstimatedDelay(),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 📋 Détails de la demande (compacts)
  Widget _buildRequestDetails() {
    return Container(
      padding: ,
        borderRadius: BorderRadius.circular(12), // Réduit de 16 à 12
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          .textTheme.titleSmall?.copyWith( // titleMedium -> titleSmall
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12), // Réduit de 16 à 12
          _buildDetailRow('🧑', 'Nom complet', widget.request.fullName),
          _buildDetailRow('✉️', 'Email', widget.request.email),
          _buildDetailRow('🏢', 'Rôle demandé', _getRoleDisplayName(widget.request.role)),
          if (widget.request.companyName.isNotEmpty)
            _buildDetailRow('🏢', 'Compagnie', widget.request.companyName),
          if (widget.request.agencyName.isNotEmpty)
            _buildDetailRow('📍', 'Agence', widget.request.agencyName),
          _buildDetailRow(
            '📅',
            'Date de soumission',
            DateFormat('dd/MM/yyyy à HH:mm').format(widget.request.submittedAt),
          ),
        ],
      ),
    );
  }

  /// 📝 Ligne de détail (compacte)
  Widget _buildDetailRow(String icon, String label, String value) {
    return (1), // Réduit de 16 à 14
          ),
          const SizedBox(width: 6), // Réduit de 8 à 6
          Expanded(
            flex: 2,
            child: (label :',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontWeight: FontWeight.w500,
                fontSize: 13, // Ajout d'une taille plus petite
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: ,
            ),
          ),
        ],
      ),
    );
  }

  /// ⏳ Délai estimé (compact)
  Widget _buildEstimatedDelay() {
    return Container(
      padding: ,
        borderRadius: BorderRadius.circular(10), // Réduit de 12 à 10
        border: Border.all(
          color: Colors.orange.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          ,
          const SizedBox(width: 10), // Réduit de 12 à 10
          Expanded(
            child: ,
            ),
          ),
        ],
      ),
    );
  }

  /// 🎯 Boutons d'action (compacts)
  Widget _buildActionButtons() {
    return Column(
      children: [
        // Bouton principal - Retour à la connexion
         {
              Navigator.pushNamedAndRemoveUntil(
                context,
                AppRouter.userTypeSelection,
                (route) => false,
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: AppTheme.primaryColor,
              padding: , // Réduit de 12 à 10
              ),
              elevation: 3, // Réduit de 4 à 3
            ),
            icon: const Icon(Icons.info), // Taille réduite
            label: ,
            ),
          ),
        ),

        const SizedBox(height: 8), // Réduit de 12 à 8

        // Bouton secondaire - Contact d'urgence
        TextButton.const Icon(
          onPressed: () {
            // TODO: Implémenter le contact d'urgence
            ScaffoldMessenger.of(context).showSnackBar(
              ,
                backgroundColor: Colors.green,
              ),
            );
          },
          style: TextButton.styleFrom(
            foregroundColor: Colors.white.withValues(alpha: 0.8),
            padding: ,
          icon: const Icon(Icons.info), // Taille réduite
          label: const Text(
            'Contact d\'urgence
