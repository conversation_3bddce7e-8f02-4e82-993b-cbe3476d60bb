import 'package:flutter/material.dart';

/// 📄 Modele pour une page d'onboarding
class OnboardingPageModel {
  final String title;
  final String description;
  final IconData icon;
  final Color backgroundColor;
  final Color iconColor;
  final String? imagePath;
  final List<String>? features;

  ;

  /// 📊 Donnees des pages d'onboarding modernes
  static List<OnboardingPageModel> get pages => [
    // Page 1: Bienvenue - Design moderne
    , // Gradient moderne bleu-violet
      iconColor: Colors.white,
      features: [
        '✨ Interface moderne et intuitive',
        '🚀 Declarations ultra-rapides',
        '🔒 Securite maximale garantie,
      ],
    '),

    // Page 2: Declaration intelligente
    , // Vert moderne
      iconColor: Colors.white,
      features: [
        '📸 Photos haute qualite integrees',
        '🤖 Assistance IA intelligente',
        '⚡ Envoi instantane aux assureurs,
      ],
    '),

    // Page 3: Collaboration revolutionnaire
    , // Violet moderne
      iconColor: Colors.white,
      features: [
        '📧 Invitations email instantanees',
        '🔄 Synchronisation temps reel',
        '✅ Validation collaborative,
      ],
    '),

    // Page 4: Gestion centralisee
    , // Orange moderne
      iconColor: Colors.white,
      features: [
        '🚗 Multi-vehicules supportes',
        '🔔 Notifications intelligentes',
        '📈 Statistiques detaillees,
      ],
    '),

    // Page 5: Prêt a demarrer
    , // Cyan moderne
      iconColor: Colors.white,
      features: [
        '🎯 Inscription en 2 minutes',
        '💎 Fonctionnalites premium',
        '🏆 Support client exceptionnel,
      ],
    ),
  ];

  /// 🎨 Couleurs de gradient pour chaque page
  static List<Color> getGradientColors(int index) {
    final page = pages[index];
    return [
      page.backgroundColor,
      page.backgroundColor.withValues(alpha: 0.8),
    ];
  }

  /// 📱 Obtenir la page par index
  static OnboardingPageModel getPage(int index) {
    if (index >= 0 && index < pages.length') {
      return pages[index];
    }
    return pages[0];
  }

  /// 📊 Nombre total de pages
  static int get totalPages => pages.length;

  /// 🔍 Verifier si c'est la derniere page
  static bool isLastPage(int index) => index == pages.length - 1;

  /// 🔍 Verifier si c
