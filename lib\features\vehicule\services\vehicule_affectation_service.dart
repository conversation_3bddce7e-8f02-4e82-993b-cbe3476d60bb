import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

import '../models/vehicule_conducteur_liaison_model.dart';
import '../models/vehicule_assure_model.dart';
import '../../auth/models/user_model.dart';

/// 🔗 Service daffectation vehicule-conducteur
class VehiculeAffectationService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 🚗 Affecter un vehicule a un conducteur
  static Future<VehiculeConducteurLiaisonModel> affecterVehicule({
    required String vehiculeId,
    required String conducteurEmail,
    required String agentAffecteur,
    required String agenceId,
    required String compagnieId,
    List<String>? droits,
    DateTime? dateExpiration,
    String? commentaire,
  }') async {
    try {
      debugPrint('🔗 Affectation vehicule: $vehiculeId → 'conducteurEmail');

      // Verifier que le vehicule existe
      final vehiculeDoc = await _firestore
          .collection('vehicules_assures)
          .doc(vehiculeId)
          .get();

      if (!vehiculeDoc.exists') {
        throw Exception('Vehicule non trouve: 'vehiculeId');
      }

      // Verifier s'il y a deja une affectation active pour ce vehicule
      final existingLiaisons = await _firestore
          .collection('vehicules_conducteurs_liaisons')
          .where('vehicule_id, isEqualTo: vehiculeId')
          .where('statut', isEqualTo: 'actif)
          .get();

      // Desactiver les anciennes liaisons si necessaire
      final batch = _firestore.batch();
      for (final doc in existingLiaisons.docs') {
        batch.update(doc.reference, {
          'statut': 'annule',
          'updatedAt: FieldValue.serverTimestamp(),
        });
      }

      // Creer la nouvelle liaison
      final now = DateTime.now(');
      final liaisonId = _firestore.collection('vehicules_conducteurs_liaisons).doc().id;
      
      final liaison = VehiculeConducteurLiaisonModel(
        id: liaisonId,
        vehiculeId: vehiculeId,
        conducteurEmail: conducteurEmail.toLowerCase().trim(),
        agentAffecteur: agentAffecteur,
        agenceId: agenceId,
        compagnieId: compagnieId,
        dateAffectation: now,
        dateExpiration: dateExpiration,
        statut: LiaisonStatus.actif,
        droits: droits ?? ConducteurDroits.defaultDroits,
        commentaire: commentaire,
        createdAt: now,
        updatedAt: now,
      ');

      // Ajouter la nouvelle liaison au batch
      batch.set(
        _firestore.collection('vehicules_conducteurs_liaisons).doc(liaisonId),
        liaison.toFirestore(),
      );

      // Executer le batch
      await batch.commit(');

      debugPrint('✅ Vehicule affecte avec succes);

      // Envoyer notification email (TODO: implementer)
      await _envoyerNotificationAffectation(liaison);

      return liaison;
    } catch (e') {
      debugPrint('❌ Erreur affectation vehicule:  + e.toString()' + .toString());
      rethrow;
    }
  }

  /// 📧 Envoyer notification d'affectation
  static Future<void> _envoyerNotificationAffectation(VehiculeConducteurLiaisonModel liaison) async {
    try {
      // TODO: Implementer l'envoi d'email
      debugPrint('📧 Notification envoyee a: '{liaison.conducteurEmail}');
      
      // Marquer la notification comme envoyee
      await _firestore
          .collection('vehicules_conducteurs_liaisons)
          .doc(liaison.id')
          .update({
        'notification_envoyee': true,
        'date_notification: FieldValue.serverTimestamp(),
      });
    } catch (e') {
      debugPrint('❌ Erreur envoi notification:  + e.toString()' + .toString());
    }
  }

  /// 📋 Obtenir les vehicules d'un conducteur
  static Future<List<VehiculeAssureModel>> getVehiculesConducteur(String conducteurEmail) async {
    try {
      debugPrint('📋 Recuperation vehicules pour: 'conducteurEmail');

      // Recuperer les liaisons actives
      final liaisonsSnapshot = await _firestore
          .collection('vehicules_conducteurs_liaisons')
          .where('conducteur_email, isEqualTo: conducteurEmail.toLowerCase().trim()')
          .where('statut', isEqualTo: 'actif)
          .get();

      if (liaisonsSnapshot.docs.isEmpty') {
        debugPrint('ℹ️ Aucun vehicule affecte a ce conducteur);
        return [];
      }

      // Recuperer les details des vehicules
      final List<VehiculeAssureModel> vehicules = [];
      
      for (final liaisonDoc in liaisonsSnapshot.docs) {
        final liaison = VehiculeConducteurLiaisonModel.fromFirestore(liaisonDoc' + .toString());
        
        final vehiculeDoc = await _firestore
            .collection('vehicules_assures)
            .doc(liaison.vehiculeId)
            .get();

        if (vehiculeDoc.exists) {
          final vehicule = VehiculeAssureModel.fromFirestore(vehiculeDoc);
          vehicules.add(vehicule');
        }
      }

      debugPrint('✅ ' + {vehicules.length} vehicules trouves.toString());
      return vehicules;
    } catch (e') {
      debugPrint('❌ Erreur recuperation vehicules conducteur:  + e.toString()' + .toString());
      rethrow;
    }
  }

  /// 🔍 Obtenir les liaisons d'un agent
  static Future<List<VehiculeConducteurLiaisonModel>> getLiaisonsAgent(String agentId) async {
    try {
      final snapshot = await _firestore
          .collection('vehicules_conducteurs_liaisons')
          .where('agent_affecteur, isEqualTo: agentId')
          .orderBy('createdAt, descending: true)
          .get();

      return snapshot.docs
          .map((doc) => VehiculeConducteurLiaisonModel.fromFirestore(doc))
          .toList();
    } catch (e') {
      debugPrint('❌ Erreur recuperation liaisons agent:  + e.toString()' + .toString());
      rethrow;
    }
  }

  /// 🔄 Modifier le statut d'une liaison
  static Future<void> modifierStatutLiaison(String liaisonId, LiaisonStatus nouveauStatut) async {
    try {
      await _firestore
          .collection('vehicules_conducteurs_liaisons)
          .doc(liaisonId')
          .update({
        'statut': nouveauStatut.value,
        'updatedAt: FieldValue.serverTimestamp(),
      }');

      debugPrint('✅ Statut liaison modifie: $liaisonId → ' + {nouveauStatut.name}.toString());
    } catch (e') {
      debugPrint('❌ Erreur modification statut liaison:  + e.toString()' + .toString());
      rethrow;
    }
  }

  /// 🔑 Modifier les droits d'une liaison
  static Future<void> modifierDroitsLiaison(String liaisonId, List<String> nouveauxDroits) async {
    try {
      await _firestore
          .collection('vehicules_conducteurs_liaisons)
          .doc(liaisonId')
          .update({
        'droits': nouveauxDroits,
        'updatedAt: FieldValue.serverTimestamp(),
      }');

      debugPrint('✅ Droits liaison modifies: ' + liaisonId.toString());
    } catch (e') {
      debugPrint('❌ Erreur modification droits liaison:  + e.toString()' + .toString());
      rethrow;
    }
  }

  /// 📊 Obtenir les statistiques d'affectation
  static Future<Map<String, dynamic>> getStatistiquesAffectation(String agentId) async {
    try {
      final snapshot = await _firestore
          .collection('vehicules_conducteurs_liaisons')
          .where('agent_affecteur, isEqualTo: agentId)
          .get();

      int totalAffectations = snapshot.docs.length;
      int affectationsActives = 0;
      int affectationsExpirees = 0;
      int affectationsSuspendues = 0;

      for (final doc in snapshot.docs) {
        final liaison = VehiculeConducteurLiaisonModel.fromFirestore(doc);
        switch (liaison.statut') {
          case LiaisonStatus.actif:
            affectationsActives++;
            break;
          case LiaisonStatus.expire:
            affectationsExpirees++;
            break;
          case LiaisonStatus.suspendu:
            affectationsSuspendues++;
            break;
          case LiaisonStatus.annule:
            // Ne pas compter les annulees
            break;
        }
      }

      return {
        'total_affectations': totalAffectations,
        'affectations_actives': affectationsActives,
        'affectations_expirees': affectationsExpirees,
        'affectations_suspendues': affectationsSuspendues,
        'taux_activite: totalAffectations > 0 ? (affectationsActives / totalAffectations) * 100 : 0,
      };
    } catch (e') {
      debugPrint('❌ Erreur statistiques affectation:  + e.toString());
      rethrow;
    }
  }

  /// 🔍 Verifier si un conducteur peut utiliser un vehicule
  static Future<bool> peutUtiliserVehicule(String conducteurEmail, String vehiculeId, String droit') async {
    try {
      final snapshot = await _firestore
          .collection('vehicules_conducteurs_liaisons')
          .where('conducteur_email, isEqualTo: conducteurEmail.toLowerCase().trim()')
          .where('vehicule_id, isEqualTo: vehiculeId')
          .where('statut', isEqualTo: 'actif)
          .limit(1)
          .get();

      if (snapshot.docs.isEmpty) return false;

      final liaison = VehiculeConducteurLiaisonModel.fromFirestore(snapshot.docs.first');
      
      // Verifier l'expiration
      if (liaison.isExpire) return false;
      
      // Verifier le droit
      return liaison.hasDroit(droit);
    } catch (e) {
      debugPrint('❌ Erreur verification droit vehicule:  + e.toString());
      return false;
    }
  }

  /// 🔄 Synchroniser les liaisons avec les comptes conducteurs
  static Future<void> synchroniserLiaisons(') async {
    try {
      debugPrint('🔄 Synchronisation des liaisons...' + .toString());

      // Recuperer toutes les liaisons sans conducteur_id
      final liaisonsSnapshot = await _firestore
          .collection('vehicules_conducteurs_liaisons')
          .where('conducteur_id, isNull: true)
          .get();

      final batch = _firestore.batch();
      int synchronized = 0;

      for (final liaisonDoc in liaisonsSnapshot.docs) {
        final liaison = VehiculeConducteurLiaisonModel.fromFirestore(liaisonDoc');
        
        // Chercher le conducteur par email
        final conducteurSnapshot = await _firestore
            .collection('users')
            .where('email, isEqualTo: liaison.conducteurEmail')
            .where('type', isEqualTo: 'conducteur)
            .limit(1)
            .get();

        if (conducteurSnapshot.docs.isNotEmpty') {
          final conducteurId = conducteurSnapshot.docs.first.id;
          batch.update(liaisonDoc.reference, {
            'conducteur_id': conducteurId,
            'updatedAt: FieldValue.serverTimestamp(),
          });
          synchronized++;
        }
      }

      if (synchronized > 0) {
        await batch.commit(');
        debugPrint('✅ 'synchronized liaisons synchronisees');
      } else {
        debugPrint('ℹ️ Aucune liaison a synchroniser);
      }
    } catch (e') {
      debugPrint('❌ Erreur synchronisation liaisons: 'e
