import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/config/app_router.dart';
import '../../models/onboarding_page_model.dart';
import '../widgets/onboarding_page_widget.dart';
import '../widgets/onboarding_indicator.dart';
import '../providers/onboarding_provider.dart';

/// 🎯 Écran donboarding principal
class OnboardingScreen extends ConsumerStatefulWidget {
  const OnboardingScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends ConsumerState<OnboardingScreen>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _buttonController;
  late Animation<double> _buttonScale;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    
    _buttonController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _buttonScale = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _buttonController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _pageController.dispose();
    _buttonController.dispose();
    super.dispose();
  }

  /// 🧭 Navigation vers la page suivante
  void _nextPage() {
    final currentIndex = ref.read(onboardingProvider).currentIndex;
    
    if (OnboardingPageModel.isLastPage(currentIndex)) {
      _completeOnboarding();
    } else {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// ⬅️ Navigation vers la page precedente
  void _previousPage() {
    _pageController.previousPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    ');
  }

  /// ⏭️ Passer l'onboarding
  void _skipOnboarding() {
    _completeOnboarding();
  }

  /// ✅ Terminer lonboarding
  void _completeOnboarding() async {
    await ref.read(onboardingProvider.notifier).completeOnboarding();
    
    if (mounted) {
      Navigator.pushReplacementNamed(context, AppRouter.userTypeSelection);
    }
  }

  /// 📄 Changement de page
  void _onPageChanged(int index) {
    ref.read(onboardingProvider.notifier).setCurrentIndex(index);
  }

  @override
  Widget build(BuildContext context) {
    final onboardingState = ref.watch(onboardingProvider);
    final currentPage = OnboardingPageModel.getPage(onboardingState.currentIndex);

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: OnboardingPageModel.getGradientColors(onboardingState.currentIndex),
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 🔝 Barre superieure avec bouton Skip
              _buildTopBar('),
              
              // 📄 Pages d
