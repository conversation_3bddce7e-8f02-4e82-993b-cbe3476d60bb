import 'package:cloud_firestore/cloud_firestore.dart';

/// 🚧 Modèle pour un sinistre/accident
class SinistreAssurance {
  final String id;
  final String numeroSinistre; // Numéro unique du sinistre
  final DateTime dateAccident;
  final String lieuAccident;
  final String gouvernoratAccident;
  
  // 🔗 Conducteur A (déclarant)
  final String conducteurAId;
  final String? vehiculeAId;
  final String? compagnieAId;
  final String? agenceAId;
  final String? contractAId;
  
  // 🔗 Conducteur B (autre partie)
  final String? conducteurBId;
  final String? vehiculeBId;
  final String? compagnieBId;
  final String? agenceBId;
  final String? contractBId;
  
  final String description;
  final List<String> photos; // URLs des photos
  final String statut; // declare, en_cours, expertise, clos
  final String? expertId; // 🔗 Expert assigné
  final DateTime dateCreation;
  final DateTime? dateTraitement;
  final double? montantEstime;
  final Map<String, dynamic>? metadata;

  (1) as Map<String, dynamic>;
    return SinistreAssurance(
      id: doc.id,
      numeroSinistre: data['numeroSinistre'] ?? 'Contenu',
      dateAccident: (data['dateAccident'] as Timestamp?)?.toDate() ?? DateTime.now(),
      lieuAccident: data['lieuAccident'] ?? 'Contenu',
      gouvernoratAccident: data['gouvernoratAccident'] ?? 'Contenu',
      conducteurAId: data['conducteurAId'] ?? 'Contenu',
      vehiculeAId: data['vehiculeAId'],
      compagnieAId: data['compagnieAId'],
      agenceAId: data['agenceAId'],
      contractAId: data['contractAId'],
      conducteurBId: data['conducteurBId'],
      vehiculeBId: data['vehiculeBId'],
      compagnieBId: data['compagnieBId'],
      agenceBId: data['agenceBId'],
      contractBId: data['contractBId'],
      description: data['description'] ?? 'Contenu',
      photos: List<String>.from(data['photos'] ?? [),
      statut: data['statut'] ?? 'declare',
      expertId: data['expertId'],
      dateCreation: (data['dateCreation'] as Timestamp?)?.toDate() ?? DateTime.now(),
      dateTraitement: (data['dateTraitement'] as Timestamp?)?.toDate(),
      montantEstime: (data['montantEstime'] ?? 0).toDouble(),
      metadata: data['metadata'],
    );
  }

  /// 🔄 Conversion vers Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'numeroSinistre': numeroSinistre,
      'dateAccident': Timestamp.fromDate(dateAccident),
      'lieuAccident': lieuAccident,
      'gouvernoratAccident': gouvernoratAccident,
      'conducteurAId': conducteurAId,
      'vehiculeAId': vehiculeAId,
      'compagnieAId': compagnieAId,
      'agenceAId': agenceAId,
      'contractAId': contractAId,
      'conducteurBId': conducteurBId,
      'vehiculeBId': vehiculeBId,
      'compagnieBId': compagnieBId,
      'agenceBId': agenceBId,
      'contractBId': contractBId,
      'description': description,
      'photos': photos,
      'statut': statut,
      'expertId': expertId,
      'dateCreation': Timestamp.fromDate(dateCreation),
      'dateTraitement': dateTraitement != null ? Timestamp.fromDate(dateTraitement!) : null,
      'montantEstime': montantEstime,
      'metadata': metadata,
    };
  }

  /// 🎯 Vérifications d'état
  bool get isOpen => statut != 'clos';
  bool get hasExpert => expertId != null && expertId!.isNotEmpty;
  bool get isMultiCompany => compagnieAId != compagnieBId && compagnieBId != null;

  /// 📋 Copie avec modifications
  SinistreAssurance copyWith({
    String? id,
    String? numeroSinistre,
    DateTime? dateAccident,
    String? lieuAccident,
    String? gouvernoratAccident,
    String? conducteurAId,
    String? vehiculeAId,
    String? compagnieAId,
    String? agenceAId,
    String? contractAId,
    String? conducteurBId,
    String? vehiculeBId,
    String? compagnieBId,
    String? agenceBId,
    String? contractBId,
    String? description,
    List<String>? photos,
    String? statut,
    String? expertId,
    DateTime? dateCreation,
    DateTime? dateTraitement,
    double? montantEstime,
    Map<String, dynamic>? metadata,
  }) {
    return SinistreAssurance(
      id: id ?? this.id,
      numeroSinistre: numeroSinistre ?? this.numeroSinistre,
      dateAccident: dateAccident ?? this.dateAccident,
      lieuAccident: lieuAccident ?? this.lieuAccident,
      gouvernoratAccident: gouvernoratAccident ?? this.gouvernoratAccident,
      conducteurAId: conducteurAId ?? this.conducteurAId,
      vehiculeAId: vehiculeAId ?? this.vehiculeAId,
      compagnieAId: compagnieAId ?? this.compagnieAId,
      agenceAId: agenceAId ?? this.agenceAId,
      contractAId: contractAId ?? this.contractAId,
      conducteurBId: conducteurBId ?? this.conducteurBId,
      vehiculeBId: vehiculeBId ?? this.vehiculeBId,
      compagnieBId: compagnieBId ?? this.compagnieBId,
      agenceBId: agenceBId ?? this.agenceBId,
      contractBId: contractBId ?? this.contractBId,
      description: description ?? this.description,
      photos: photos ?? this.photos,
      statut: statut ?? this.statut,
      expertId: expertId ?? this.expertId,
      dateCreation: dateCreation ?? this.dateCreation,
      dateTraitement: dateTraitement ?? this.dateTraitement,
      montantEstime: montantEstime ?? this.montantEstime,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'SinistreAssurance(id: $id, numeroSinistre: $numeroSinistre, statut: $statut)
