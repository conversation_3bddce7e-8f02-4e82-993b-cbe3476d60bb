import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../models/vehicule_assure_model.dart';
import '../../models/contrat_assurance_model.dart';

/// 🚗 Carte moderne pour afficher un véhicule avec son contrat
class VehicleContractCard extends StatelessWidget {
  final VehiculeAssure vehicule;
  final ContratAssurance? contrat;
  final bool hasValidContract;
  final VoidCallback? onTap;
  final VoidCallback? onDeclareAccident;

   ) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: hasValidContract
              ? [
                  const Color(0xFF667EEA).withValues(alpha: 0.1),
                  const Color(0xFF764BA2).withValues(alpha: 0.1),
                ]
              : [
                  Colors.orange.withValues(alpha: 0.1),
                  Colors.red.withValues(alpha: 0.1),
                ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: hasValidContract
              ? const Color(0xFF667EEA).withValues(alpha: 0.3)
              : Colors.orange.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
          BoxShadow(
            color: hasValidContract
                ? const Color(0xFF667EEA).withValues(alpha: 0.1)
                : Colors.orange.withValues(alpha: 0.1),
            blurRadius: 40,
            offset: const Offset(0, 16),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          child: (1),
                
                const SizedBox(height: 16),
                
                // Informations du véhicule
                _buildVehicleInfo(),
                
                const SizedBox(height: 16),
                
                // Informations du contrat
                _buildContractInfo(),
                
                const SizedBox(height: 20),
                
                // Actions
                _buildActions(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// En-tête avec icône et statut
  Widget _buildHeader() {
    return Row(
      children: [
        // Icône du véhicule
        Container(
          padding: , const Color(0xFF764BA2)]
                  : [Colors.orange, Colors.red],
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: hasValidContract
                    ? const Color(0xFF667EEA).withValues(alpha: 0.3)
                    : Colors.orange.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ,
        ),
        
        const SizedBox(width: 16),
        
        // Nom du véhicule et statut
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ({vehicule.modele}',
                style: ,
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: hasValidContract ? Colors.green : Colors.orange,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  ,
                  ),
                ],
              ),
            ],
          ),
        ),
        
        // Badge d'année
        Container(
          padding: ,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.2),
            ),
          ),
          child: ,
            style: ,
          ),
        ),
      ],
    );
  }

  /// Informations du véhicule
  Widget _buildVehicleInfo() {
    return Container(
      padding: ,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        children: [
          _buildInfoRow('Immatriculation', vehicule.immatriculation, Icons.confirmation_number),
          const SizedBox(height: 8),
          _buildInfoRow('Couleur', vehicule.couleur, Icons.palette),
          const SizedBox(height: 8),
          _buildInfoRow('Carburant', vehicule.typeCarburant, Icons.local_gas_station),
        ],
      ),
    );
  }

  /// Informations du contrat
  Widget _buildContractInfo() {
    if (contrat == null) {
      return Container(
        padding: ,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.orange.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          children: [
            ,
            const SizedBox(width: 12),
            Expanded(
              child: ,
              ),
            ),
          ],
        ),
      );
    }

    final dateFormat = DateFormat('dd/MM/yyyy');
    final joursRestants = contrat!.joursRestants;
    final isExpiringSoon = joursRestants <= 30;

    return Container(
      padding: 
            : Colors.green.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isExpiringSoon 
              ? Colors.orange.withValues(alpha: 0.3)
              : Colors.green.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          _buildInfoRow(
            'N° Contrat', 
            contrat!.numeroContrat, 
            Icons.description,
            color: isExpiringSoon ? Colors.orange : Colors.green,
          ),
          const SizedBox(height: 8),
          _buildInfoRow(
            'Type', 
            _getTypeAssuranceLabel(contrat!.typeAssurance), 
            Icons.security,
            color: isExpiringSoon ? Colors.orange : Colors.green,
          ),
          const SizedBox(height: 8),
          _buildInfoRow(
            'Expire le', 
            dateFormat.format(contrat!.dateFin), 
            Icons.event,
            color: isExpiringSoon ? Colors.orange : Colors.green,
          ),
          if (isExpiringSoon) ...[
            const SizedBox(height: 12),
            Container(
              padding: ,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ,
                  const SizedBox(width: 8),
                  (joursRestants jour(s)',
                    style: ,
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Actions disponibles
  Widget _buildActions() {
    return Row(
      children: [
        // Bouton Voir détails
        Expanded(
          child: OutlinedButton.const Icon(
            onPressed: onTap,
            icon: const Icon(Icons.info),
            label: const Text('Détails'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.white,
              side: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
              padding: ,
              ),
            ),
          ),
        ),
        
        const SizedBox(width: 12),
        
        // Bouton Déclarer accident (si assuré)
        if (hasValidContract && onDeclareAccident != null)
          Expanded(
            child: ElevatedButton.const Icon(
              onPressed: onDeclareAccident,
              icon: const Icon(Icons.info),
              label: const Text('Accident'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF667EEA),
                foregroundColor: Colors.white,
                padding: ,
                ),
              ),
            ),
          ),
      ],
    );
  }

  /// Ligne d'information
  Widget _buildInfoRow(String label, String value, IconData icon, {Color? color}) {
    final textColor = color ?? Colors.white.withValues(alpha: 0.8);
    
    return Row(
      children: [
        const Icon(icon, color: textColor, size: 16),
        const SizedBox(width: 12),
        (label: ',
          style: TextStyle(
            color: textColor,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        Expanded(
          child: ,
          ),
        ),
      ],
    );
  }

  /// Obtenir le label du type d'assurance
  String _getTypeAssuranceLabel(String type) {
    switch (type) {
      case 'tiers':
        return 'Responsabilité Civile';
      case 'tiers_vol_incendie':
        return 'Tiers + Vol/Incendie';
      case 'tous_risques':
        return 'Tous Risques
