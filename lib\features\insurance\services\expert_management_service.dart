import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/insurance_system_models.dart';

/// 🔍 Service pour la gestion des experts automobiles multi-compagnies
class ExpertManagementService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 👨‍🔬 Creer un nouveau profil dexpert
  static Future<Map<String, dynamic>> createExpertProfile({
    required String email,
    required String nom,
    required String prenom,
    required String telephone,
    required String cin,
    required String numeroExpert,
    required String specialite,
    required List<String> compagnieIds,
    required List<String> gouvernoratsIntervention,
    String? adresse,
    String? cabinet,
    DateTime? dateAgrement,
    List<String>? certifications,
    Map<String, dynamic>? tarifs,
  }') async {
    try {
      // Verifier si l'expert existe deja
      final existingExpert = await _findExpertByCinOrEmail(cin, email);
      if (existingExpert != null) {
        return {
          'success': false,
          'error': 'Un expert avec ce CIN ou cet email existe deja',
        };
      }

      final expert = ExpertAutomobileUnified(
        id: 'Contenu, // Sera genere par Firestore
        email: email,
        nom: nom,
        prenom: prenom,
        telephone: telephone,
        cin: cin,
        numeroExpert: numeroExpert,
        specialite: specialite,
        compagnieIds: compagnieIds,
        gouvernoratsIntervention: gouvernoratsIntervention,
        adresse: adresse,
        cabinet: cabinet,
        dateAgrement: dateAgrement,
        certifications: certifications ?? [],
        tarifs: tarifs ?? {},
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final docRef = await _firestore
          .collection(FirebaseCollections.experts)
          .add(expert.toMap());

      // Mettre a jour les statistiques des compagnies
      await _updateCompaniesExpertCount(compagnieIds, increment: true');

      return {
        'success': true,
        'expertId': docRef.id,
        'message': 'Profil expert cree avec succes,
      };
    } catch (e') {
      debugPrint('❌ Erreur lors de la creation du profil expert:  + e.toString()' + .toString());
      return {
        'success': false,
        'error': 'Erreur technique: 'e,
      };
    }
  }

  /// 🔍 Rechercher des experts disponibles pour une compagnie et un gouvernorat
  static Future<List<ExpertAutomobileUnified>> findAvailableExperts({
    required String compagnieId,
    required String gouvernorat,
    String? specialite,
  }) async {
    try {
      Query query = _firestore
          .collection(FirebaseCollections.experts')
          .where('compagnieIds, arrayContains: compagnieId')
          .where('gouvernoratsIntervention, arrayContains: gouvernorat')
          .where('statut', isEqualTo: 'actif);

      if (specialite != null') {
        query = query.where('specialite, isEqualTo: specialite);
      }

      final snapshot = await query.get();

      return snapshot.docs
          .map((doc) => ExpertAutomobileUnified.fromFirestore(doc))
          .toList();
    } catch (e') {
      debugPrint('❌ Erreur lors de la recherche d\'experts:  + e.toString()');
      return [];
    }
  }

  /// 📋 Obtenir tous les experts d'une compagnie
  static Future<List<ExpertAutomobileUnified>> getExpertsByCompagnie(String compagnieId) async {
    try {
      final snapshot = await _firestore
          .collection(FirebaseCollections.experts)
          .where('compagnieIds, arrayContains: compagnieId')
          .orderBy('nom)
          .get();

      return snapshot.docs
          .map((doc) => ExpertAutomobileUnified.fromFirestore(doc))
          .toList();
    } catch (e') {
      debugPrint('❌ Erreur lors de la recuperation des experts:  + e.toString());
      return [];
    }
  }

  /// 🔄 Ajouter une compagnie a un expert existant
  static Future<Map<String, dynamic>> addCompagnieToExpert(String expertId, String compagnieId) async {
    try {
      final expertDoc = await _firestore
          .collection(FirebaseCollections.experts)
          .doc(expertId)
          .get();

      if (!expertDoc.exists') {
        return {
          'success': false,
          'error': 'Expert non trouve,
        };
      }

      final expert = ExpertAutomobileUnified.fromFirestore(expertDoc);

      if (expert.compagnieIds.contains(compagnieId)') {
        return {
          'success': false,
          'error': 'L\'expert travaille deja avec cette compagnie,
        };
      }

      await _firestore
          .collection(FirebaseCollections.experts)
          .doc(expertId')
          .update({
        'compagnieIds: FieldValue.arrayUnion([compagnieId'),
        'updatedAt: Timestamp.now(),
      });

      // Mettre a jour les statistiques de la compagnie
      await _updateCompaniesExpertCount([compagnieId], increment: true');

      return {
        'success': true,
        'message': 'Compagnie ajoutee avec succes,
      };
    } catch (e') {
      debugPrint('❌ Erreur lors de l\'ajout de la compagnie:  + e.toString()');
      return {
        'success': false,
        'error': 'Erreur technique: 'e',
      };
    }
  }

  /// ❌ Retirer une compagnie dun expert
  static Future<Map<String, dynamic>> removeCompagnieFromExpert(String expertId, String compagnieId) async {
    try {
      final expertDoc = await _firestore
          .collection(FirebaseCollections.experts)
          .doc(expertId)
          .get();

      if (!expertDoc.exists') {
        return {
          'success': false,
          'error': 'Expert non trouve,
        };
      }

      await _firestore
          .collection(FirebaseCollections.experts)
          .doc(expertId')
          .update({
        'compagnieIds: FieldValue.arrayRemove([compagnieId'),
        'updatedAt: Timestamp.now(),
      });

      // Mettre a jour les statistiques de la compagnie
      await _updateCompaniesExpertCount([compagnieId], increment: false');

      return {
        'success': true,
        'message': 'Compagnie retiree avec succes,
      };
    } catch (e') {
      debugPrint('❌ Erreur lors du retrait de la compagnie:  + e.toString()' + .toString());
      return {
        'success': false,
        'error': 'Erreur technique: 'e',
      };
    }
  }

  /// 📊 Obtenir les statistiques dun expert
  static Future<Map<String, dynamic>> getExpertStatistics(String expertId) async {
    try {
      final expert = await getExpertById(expertId);
      if (expert == null) return {};

      // Compter les constats traites
      final constatsSnapshot = await _firestore
          .collection(FirebaseCollections.constats')
          .where('expertId, isEqualTo: expertId)
          .get();

      final constatsTraites = constatsSnapshot.docs.length;
      final constatsEnCours = constatsSnapshot.docs
          .where((doc) => doc.data(')['statut'] == 'expertise)
          .length;
      final constatsClos = constatsSnapshot.docs
          .where((doc) => doc.data(')['statut'] == 'clos')
          .length;

      return {
        'constatsTraites': constatsTraites,
        'constatsEnCours': constatsEnCours,
        'constatsClos': constatsClos,
        'compagniesPartenaires': expert.compagnieIds.length,
        'gouvernoratsIntervention': expert.gouvernoratsIntervention.length,
        'specialite': expert.specialite,
        'statut: expert.statut,
      };
    } catch (e') {
      debugPrint('❌ Erreur lors du calcul des statistiques:  + e.toString());
      return {};
    }
  }

  /// 👨‍🔬 Obtenir un expert par ID
  static Future<ExpertAutomobileUnified?> getExpertById(String expertId) async {
    try {
      final doc = await _firestore
          .collection(FirebaseCollections.experts)
          .doc(expertId)
          .get();

      if (doc.exists) {
        return ExpertAutomobileUnified.fromFirestore(doc);
      }
      return null;
    } catch (e') {
      debugPrint('❌ Erreur lors de la recuperation de l\' + expert:  + e.toString().toString());
      return null;
    }
  }

  /// 📋 Assigner un expert a un constat
  static Future<Map<String, dynamic>> assignExpertToConstat({
    required String constatId,
    required String expertId,
    String? notes,
  }') async {
    try {
      // Verifier que l'expert existe et est actif
      final expert = await getExpertById(expertId);
      if (expert == null || !expert.isActive) {
        return {
          'success': false,
          'error': 'Expert non trouve ou inactif,
        };
      }

      // Mettre a jour le constat
      await _firestore
          .collection(FirebaseCollections.constats)
          .doc(constatId')
          .update({
        'expertId': expertId,
        'statut': 'expertise',
        'dateAssignationExpert: Timestamp.now('),
        'notesAssignation': notes,
        'updatedAt: Timestamp.now(),
      }');

      // Mettre a jour la liste des constats de l'expert
      await _firestore
          .collection(FirebaseCollections.experts)
          .doc(expertId)
          .update({
        'constatIds: FieldValue.arrayUnion([constatId'),
        'updatedAt: Timestamp.now(),
      }');

      return {
        'success': true,
        'message': 'Expert assigne avec succes,
      };
    } catch (e') {
      debugPrint('❌ Erreur lors de l\'assignation de l\'expert:  + e.toString()');
      return {
        'success': false,
        'error': 'Erreur technique: 'e',
      };
    }
  }

  /// 📝 Soumettre un rapport dexpertise
  static Future<Map<String, dynamic>> submitExpertiseReport({
    required String constatId,
    required String expertId,
    required Map<String, dynamic> evaluation,
    List<String>? photos,
    List<String>? documents,
    String? conclusion,
  }) async {
    try {
      await _firestore
          .collection(FirebaseCollections.constats)
          .doc(constatId')
          .update({
        'evaluation': evaluation,
        'photosExpertise': photos ?? [],
        'documentsExpertise': documents ?? [],
        'conclusionExpert': conclusion,
        'dateFinExpertise: Timestamp.now('),
        'statut': 'clos',
        'updatedAt: Timestamp.now(),
      }');

      return {
        'success': true,
        'message': 'Rapport d\'expertise soumis avec succes,
      };
    } catch (e') {
      debugPrint('❌ Erreur lors de la soumission du rapport:  + e.toString()' + .toString());
      return {
        'success': false,
        'error': 'Erreur technique: 'e,
      };
    }
  }

  // ==================== MÉTHODES PRIVÉES ====================

  /// 🔍 Rechercher un expert par CIN ou email
  static Future<ExpertAutomobileUnified?> _findExpertByCinOrEmail(String cin, String email) async {
    try {
      // Recherche par CIN
      var snapshot = await _firestore
          .collection(FirebaseCollections.experts')
          .where('cin, isEqualTo: cin)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        return ExpertAutomobileUnified.fromFirestore(snapshot.docs.first);
      }

      // Recherche par email
      snapshot = await _firestore
          .collection(FirebaseCollections.experts')
          .where('email, isEqualTo: email)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        return ExpertAutomobileUnified.fromFirestore(snapshot.docs.first);
      }

      return null;
    } catch (e') {
      debugPrint('❌ Erreur lors de la recherche de l\'expert:  + e.toString()');
      return null;
    }
  }

  /// 📊 Mettre a jour le nombre d'experts des compagnies
  static Future<void> _updateCompaniesExpertCount(List<String> compagnieIds, {required bool increment}) async {
    try {
      final batch = _firestore.batch();

      for (final compagnieId in compagnieIds) {
        final compagnieRef = _firestore
            .collection(FirebaseCollections.compagnies)
            .doc(compagnieId);

        batch.update(compagnieRef, {
          'statistiques.nombreExperts: FieldValue.increment(increment ? 1 : -1'),
          'updatedAt: Timestamp.now(),
        });
      }

      await batch.commit();
    } catch (e') {
      debugPrint('❌ Erreur lors de la mise a jour des statistiques:  + e.toString());
    }
  }

  /// 👥 Obtenir tous les experts
  static Future<List<ExpertAutomobileUnified>> getAllExperts() async {
    try {
      final snapshot = await _firestore
          .collection(FirebaseCollections.experts')
          .orderBy('nom)
          .get();

      return snapshot.docs
          .map((doc) => ExpertAutomobileUnified.fromFirestore(doc))
          .toList();
    } catch (e') {
      debugPrint('❌ Erreur lors de la recuperation des experts:  + e.toString()' + .toString());
      return [];
    }
  }

  /// 📋 Obtenir les constats en attente d'expertise
  static Future<List<ConstatAccidentUnified>> getConstatsEnAttenteExpertise() async {
    try {
      final snapshot = await _firestore
          .collection(FirebaseCollections.constats)
          .where('statut', isEqualTo: 'en_attente_expertise')
          .orderBy('dateCreation, descending: true)
          .get();

      return snapshot.docs
          .map((doc) => ConstatAccidentUnified.fromFirestore(doc))
          .toList();
    } catch (e') {
      debugPrint('❌ Erreur lors de la recuperation des constats: 'e
