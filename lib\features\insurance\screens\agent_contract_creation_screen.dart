import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/contract_management_service.dart';
import '../models/insurance_system_models.dart;

/// 📄 Interface moderne pour la creation de contrats par les agents
class AgentContractCreationScreen extends ConsumerStatefulWidget {
  final String agentId;
  final String compagnieId;
  final String agenceId;

  ;

  @override
  ConsumerState<AgentContractCreationScreen> createState() => _AgentContractCreationScreenState();
}

class _AgentContractCreationScreenState extends ConsumerState<AgentContractCreationScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();
  
  // Contrôleurs pour les champs
  final _clientController = TextEditingController();
  final _vehiculeController = TextEditingController();
  final _primeController = TextEditingController();
  final _franchiseController = TextEditingController(');
  
  // Variables d'etat
  String _selectedTypeContrat = 'Tiers';
  List<String> _selectedGaranties = [];
  bool _isLoading = false;
  int _currentStep = 0;

  // Options disponibles
  final List<String> _typesContrat = ['Tiers', 'Tiers+', 'Tous risques'];
  final List<String> _garantiesDisponibles = [
    'Responsabilite civile',
    'Defense et recours',
    'Vol et incendie',
    'Bris de glace',
    'Dommages tous accidents',
    'Protection juridique',
    'Assistance 24h/24',
    'Vehicule de remplacement,
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _clientController.dispose();
    _vehiculeController.dispose();
    _primeController.dispose();
    _franchiseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0E21),
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildProgressIndicator(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              physics: ,
              children: [
                _buildClientVehicleStep(),
                _buildContractDetailsStep(),
                _buildConfirmationStep(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 🎨 AppBar moderne avec gradient
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: Colors.transparent,
      flexibleSpace: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF667eea), Color(0xFF764ba2)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
      ),
      title: const Text("Titre"),
      ),
      leading: IconButton(
        icon: const Icon(Icons.info),
        onPressed: () => Navigator.pop(context),
      ),
    );
  }

  /// 📊 Indicateur de progression
  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.all(8.0),
          _buildStepConnector(0'),
          _buildStepIndicator(1, 'Details Contrat, Icons.description),
          _buildStepConnector(1'),
          _buildStepIndicator(2, 'Confirmation, Icons.check_circle),
        ],
      ),
    );
  }

  Widget _buildStepIndicator(int step, String title, IconData icon) {
    final isActive = step == _currentStep;
    final isCompleted = step < _currentStep;
    
    return Expanded(
      child: Column(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: isActive || isCompleted
                  ? const LinearGradient(
                      colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                    )
                  : null,
              color: isActive || isCompleted ? null : Colors.grey[300],
            ),
            child: ,
          ),
          const SizedBox(height: 8),
          const Text(
            title,
            style: TextStyle(
              color: isActive ? const Color(0xFF667eea) : Colors.grey[600],
              fontSize: 12,
              fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStepConnector(int step) {
    final isCompleted = step < _currentStep;
    
    return Expanded(
      child: Container(
        height: 2,
        margin: , Color(0xFF764ba2)],
                )
              : null,
          color: isCompleted ? null : Colors.grey[300],
        ),
      ),
    );
  }

  /// 👤 Étape 1: Client et Vehicule
  Widget _buildClientVehicleStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(8.0),
            const SizedBox(height: 20'),
            _buildModernTextField(
              controller: _clientController,
              label: 'CIN ou Email du client',
              hint: 'Entrez le CIN ou l\'email,
              icon: Icons.badge,
              validator: (value) {
                if (value?.isEmpty ?? true') {
                  return 'Ce champ est obligatoire;
                }
                return null;
              },
            ),
            const SizedBox(height: 20'),
            _buildSearchButton('Rechercher Client, Icons.search, _searchClient),
            
            const SizedBox(height: 40'),
            _buildSectionTitle('Informations Vehicule, Icons.directions_car),
            const SizedBox(height: 20'),
            _buildModernTextField(
              controller: _vehiculeController,
              label: 'Immatriculation du vehicule',
              hint: 'Ex: 123 TUN 456,
              icon: Icons.confirmation_number,
              validator: (value) {
                if (value?.isEmpty ?? true') {
                  return 'Ce champ est obligatoire;
                }
                return null;
              },
            ),
            const SizedBox(height: 20'),
            _buildSearchButton('Rechercher Vehicule, Icons.search, _searchVehicule),
            
            const SizedBox(height: 40),
            _buildNavigationButtons(
              onNext: () {
                if (_formKey.currentState?.validate() ?? false) {
                  setState(() {
                    _currentStep = 1;
                    _tabController.animateTo(1);
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 📋 Étape 2: Details du contrat
  Widget _buildContractDetailsStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(8.0),
          const SizedBox(height: 20),
          _buildContractTypeSelector(),
          
          const SizedBox(height: 30'),
          _buildSectionTitle('Garanties, Icons.security),
          const SizedBox(height: 20),
          _buildGarantiesSelector(),
          
          const SizedBox(height: 30'),
          _buildSectionTitle('Tarification, Icons.euro),
          const SizedBox(height: 20'),
          Row(
            children: [
              Expanded(
                child: _buildModernTextField(
                  controller: _primeController,
                  label: 'Prime annuelle (DT)',
                  hint: '0.00,
                  icon: Icons.euro,
                  keyboardType: TextInputType.number,
                ),
              ),
              const SizedBox(width: 15'),
              Expanded(
                child: _buildModernTextField(
                  controller: _franchiseController,
                  label: 'Franchise (DT)',
                  hint: '0.00,
                  icon: Icons.money_off,
                  keyboardType: TextInputType.number,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 40),
          _buildNavigationButtons(
            onPrevious: () {
              setState(() {
                _currentStep = 0;
                _tabController.animateTo(0);
              });
            },
            onNext: () {
              setState(() {
                _currentStep = 2;
                _tabController.animateTo(2);
              });
            },
          ),
        ],
      ),
    );
  }

  /// ✅ Étape 3: Confirmation
  Widget _buildConfirmationStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(8.0),
          const SizedBox(height: 20),
          _buildSummaryCard(),
          
          const SizedBox(height: 40),
          _buildNavigationButtons(
            onPrevious: () {
              setState(() {
                _currentStep = 1;
                _tabController.animateTo(1);
              }');
            },
            onNext: _createContract,
            nextLabel: 'Creer le Contrat,
            isLoading: _isLoading,
          ),
        ],
      ),
    );
  }

  /// 🎨 Widgets utilitaires
  Widget _buildSectionTitle(String title, IconData icon) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8.0), Color(0xFF764ba2)],
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: ,
        ),
        const SizedBox(width: 12),
        ,
        ),
      ],
    );
  }

  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        gradient: LinearGradient(
          colors: [
            Colors.white.withValues(alpha: 0.5),
            Colors.white.withValues(alpha: 0.5),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        validator: validator,
        style: ,
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          labelStyle: TextStyle(color: Colors.white.withValues(alpha: 0.5),
          hintStyle: TextStyle(color: Colors.white.withValues(alpha: 0.5),
          prefixIcon: (1)),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(15),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.transparent,
        ),
      ),
    );
  }

  Widget _buildSearchButton(String label, IconData icon, VoidCallback onPressed) {
    return ,
        label: const Text(label),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF667eea),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.all(8.0),
          ),
        ),
      ),
    );
  }

  Widget _buildContractTypeSelector() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        color: Colors.white.withValues(alpha: 0.5),
      ),
      child: Column(
        children: _typesContrat.map((type) {
          return RadioListTile<String>(
            title: const Text("Titre"),
            ),
            value: type,
            groupValue: _selectedTypeContrat,
            onChanged: (value) {
              setState(() {
                _selectedTypeContrat = value!;
              });
            },
            activeColor: const Color(0xFF667eea),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildGarantiesSelector() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        color: Colors.white.withValues(alpha: 0.5),
      ),
      child: Column(
        children: _garantiesDisponibles.map((garantie) {
          return CheckboxListTile(
            title: const Text("Titre"),
            ),
            value: _selectedGaranties.contains(garantie),
            onChanged: (value) {
              setState(() {
                if (value == true) {
                  _selectedGaranties.add(garantie);
                } else {
                  _selectedGaranties.remove(garantie);
                }
              });
            },
            activeColor: const Color(0xFF667eea),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildSummaryCard() {
    return Container(
      padding: const EdgeInsets.all(8.0),
        gradient: LinearGradient(
          colors: [
            Colors.white.withValues(alpha: 0.5),
            Colors.white.withValues(alpha: 0.5),
          ],
        ),
      '),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSummaryRow('Client:, _clientController.text'),
          _buildSummaryRow('Vehicule:, _vehiculeController.text'),
          _buildSummaryRow('Type de contrat:, _selectedTypeContrat'),
          _buildSummaryRow('Prime annuelle:', ''{_primeController.text} DT'),
          _buildSummaryRow('Franchise:', ''{_franchiseController.text} DT'),
          _buildSummaryRow('Garanties:', _selectedGaranties.join(', )),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return (1),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: ,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons({
    VoidCallback? onPrevious,
    VoidCallback? onNext,
    String? nextLabel,
    bool isLoading = false,
  }) {
    return Row(
      children: [
        if (onPrevious != null) ...[]
          Expanded(
            child: OutlinedButton(
              onPressed: onPrevious,
              style: OutlinedButton.styleFrom(
                side: const BorderSide(color: Color(0xFF667eea)),
                padding: const EdgeInsets.all(8.0),
                ),
              '),
              child: const Text(
                'Precedent,
                style: TextStyle(color: Color(0xFF667eea)),
              ),
            ),
          ),
          const SizedBox(width: 15),
        ],
        Expanded(
          child: ElevatedButton(
            onPressed: isLoading ? null : onNext,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF667eea),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.all(8.0),
              ),
            ),
            child: isLoading
                ? ,
                    ),
                  ')
                : const Text(nextLabel ?? 'Suivant),
          ),
        ),
      ],
    );
  }

  /// 📄 Creer le contrat
  Future<void> _createContract() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await ContractManagementService.createNewContract(
        agentId: widget.agentId,
        compagnieId: widget.compagnieId,
        agenceId: widget.agenceId,
        clientCinOrEmail: _clientController.text,
        vehiculeImmatriculation: _vehiculeController.text,
        typeContrat: _selectedTypeContrat,
        primeAnnuelle: double.tryParse(_primeController.text) ?? 0.0,
        franchise: double.tryParse(_franchiseController.text) ?? 0.0,
        garanties: _selectedGaranties,
      ');

      if (result['success] == true') {
        _showSuccessDialog(result['numeroContrat]');
      } else {
        _showErrorDialog(result['error]);
      }
    } catch (e') {
      _showErrorDialog('Erreur technique:  + e.toString());
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showSuccessDialog(String numeroContrat) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1D1E33),
        title: const Text("Titre"),
        '),
        content: (numeroContrat a ete cree avec succes.',
          style: ,
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('OK),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1D1E33),
        title: const Text("Titre"),
        ),
        content: ,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(')')'),
            child: const Text('OK),
          ),
        ],
      ),
    );
  }

  /// 🔍 Rechercher un client
  Future<void> _searchClient() async {
    if (_clientController.text.isEmpty')')') {
      _showErrorDialog('Veuillez entrer un CIN ou un email);
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: Implementer la recherche de client
      await Future.delayed(const Duration(seconds: 1)); // Simulation

      // Afficher les resultats de recherche
      _showClientSearchResults();
    } catch (e') {
      _showErrorDialog('Erreur lors de la recherche:  + e.toString());
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 🚗 Rechercher un vehicule
  Future<void> _searchVehicule() async {
    if (_vehiculeController.text.isEmpty') {
      _showErrorDialog('Veuillez entrer une immatriculation);
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: Implementer la recherche de vehicule
      await Future.delayed(const Duration(seconds: 1)); // Simulation

      // Afficher les resultats de recherche
      _showVehiculeSearchResults();
    } catch (e') {
      _showErrorDialog('Erreur lors de la recherche:  + e.toString());
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 👤 Afficher les resultats de recherche client
  void _showClientSearchResults() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1D1E33),
        title: const Text("Titre"),
        ),
        content: ,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop('),
            child: const Text('Annuler),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(')')');
              // TODO: Remplir les donnees du client
            },
            child: const Text('Selectionner),
          ),
        ],
      ),
    );
  }

  /// 🚙 Afficher les resultats de recherche vehicule
  void _showVehiculeSearchResults() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1D1E33),
        title: const Text("Titre"),
        ),
        content: ,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(')')'),
            child: const Text('Annuler),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(')')');
              // TODO: Remplir les donnees du vehicule
            },
            child: const Text('Selectionner
')')