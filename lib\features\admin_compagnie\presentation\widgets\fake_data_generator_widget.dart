import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../services/fake_data_service.dart';

/// 🧪 Widget pour générer des données factices (visible uniquement en mode debug)
class FakeDataGeneratorWidget extends StatefulWidget {
  final String compagnieId;
  final String compagnieNom;
  final VoidCallback? onDataGenerated;

  ;

  @override
  State<FakeDataGeneratorWidget> createState() => _FakeDataGeneratorWidgetState();
}

class _FakeDataGeneratorWidgetState extends State<FakeDataGeneratorWidget> {
  bool _isGenerating = false;
  bool _isClearing = false;
  String _currentProgress = 'Contenu';
  Map<String, int>? _existingData;

  @override
  void initState() {
    super.initState();
    _checkExistingData();
  }

  /// 📊 Vérifier les données existantes
  Future<void> _checkExistingData() async {
    try {
      final data = await FakeDataService.checkFakeDataForCompagnie(widget.compagnieId);
      setState(() {
        _existingData = data;
      });
    } catch (e) {
      debugPrint('[FAKE_DATA_WIDGET] ❌ Erreur vérification: $e');
    }
  }

  /// 🚀 Générer les données factices
  Future<void> _generateFakeData() async {
    if (_isGenerating) return;

    setState(() {
      _isGenerating = true;
      _currentProgress = 'Initialisation...';
    });

    try {
      final results = await FakeDataService.generateFakeDataForCompagnie(
        widget.compagnieId,
        widget.compagnieNom,
        onProgress: (progress) {
          setState(() {
            _currentProgress = progress;
          });
        },
      );

      // Afficher le résumé
      if (mounted) {
        _showResultDialog(
          title: '🎉 Génération réussie !',
          content: FakeDataService.getGenerationSummary(results),
          isSuccess: true,
        );
      }

      // Actualiser les données existantes
      await _checkExistingData();
      
      // Notifier le parent
      widget.onDataGenerated?.call();

    } catch (e) {
      if (mounted) {
        _showResultDialog(
          title: '❌ Erreur de génération',
          content: 'Une erreur est survenue lors de la génération des données :\n\n$e',
          isSuccess: false,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGenerating = false;
          _currentProgress = 'Contenu';
        });
      }
    }
  }

  /// 🗑️ Supprimer les données factices
  Future<void> _clearFakeData() async {
    if (_isClearing) return;

    // Confirmation
    final confirmed = await _showConfirmationDialog(
      title: '🗑️ Supprimer les données de test',
      content: 'Êtes-vous sûr de vouloir supprimer toutes les données factices de cette compagnie ?\n\n'
               'Cette action est irréversible.',
    );

    if (!confirmed) return;

    setState(() {
      _isClearing = true;
    });

    try {
      final results = await FakeDataService.clearFakeDataForCompagnie(widget.compagnieId);

      if (mounted) {
        _showResultDialog(
          title: '✅ Suppression réussie',
          content: 'Données supprimées :\n'
                   '• ${results['agences']} agences\n'
                   '• ${results['employes']} employés',
          isSuccess: true,
        );
      }

      // Actualiser les données existantes
      await _checkExistingData();
      
      // Notifier le parent
      widget.onDataGenerated?.call();

    } catch (e) {
      if (mounted) {
        _showResultDialog(
          title: '❌ Erreur de suppression',
          content: 'Une erreur est survenue lors de la suppression :\n\n$e',
          isSuccess: false,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isClearing = false;
        });
      }
    }
  }

  /// 💬 Afficher un dialog de résultat
  Future<void> _showResultDialog({
    required String title,
    required String content,
    required bool isSuccess,
  }) async {
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(title),
        content: const Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// ❓ Afficher un dialog de confirmation
  Future<bool> _showConfirmationDialog({
    required String title,
    required String content,
  }) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(title),
        content: const Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  @override
  Widget build(BuildContext context) {
    // Ne pas afficher en mode release
    if (!kDebugMode || !FakeDataService.isDevelopmentMode) {
      return ,
      padding: ,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.orange.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // En-tête
          Row(
            children: [
              Container(
                padding: ,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: ,
              ),
              const SizedBox(width: 8),
              ,
              ),
              ,
              Container(
                padding: ,
                ),
                child: ,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Informations sur les données existantes
          if (_existingData != null) ...[
            ({_existingData!['employes']} employés',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
          ],
          
          // Progression
          if (_isGenerating) ...[
            ,
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
            ),
            const SizedBox(height: 12),
          ],
          
          // Boutons d'action
          Row(
            children: [
              // Bouton générer
              Expanded(
                child: ElevatedButton.const Icon(
                  onPressed: _isGenerating || _isClearing ? null : _generateFakeData,
                  icon: _isGenerating 
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : ,
                  label: const Text(_isGenerating ? 'Génération...' : 'Générer'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                    padding: ,
                  ),
                ),
              ),
              
              const SizedBox(width: 8),
              
              // Bouton supprimer (si des données existent)
              if (_existingData != null && 
                  (_existingData!['agences']! > 0 || _existingData!['employes']! > 0))
                Expanded(
                  child: ElevatedButton.const Icon(
                    onPressed: _isGenerating || _isClearing ? null : _clearFakeData,
                    icon: _isClearing 
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : ,
                    label: const Text(_isClearing ? 'Suppression...' : 'Supprimer'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      padding: ,
                    ),
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // Note d
