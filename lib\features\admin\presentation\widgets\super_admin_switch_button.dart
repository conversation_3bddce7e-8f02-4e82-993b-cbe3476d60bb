import 'package:flutter/material.dart';
import '../../services/super_admin_diagnostic_service.dart';

/// 🔄 Bouton pour basculer vers le compte Super Admin
class SuperAdminSwitchButton extends StatefulWidget {
  final VoidCallback? onSwitchSuccess;
  
  ;

  @override
  State<SuperAdminSwitchButton> createState() => _SuperAdminSwitchButtonState();
}

class _SuperAdminSwitchButtonState extends State<SuperAdminSwitchButton> {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  bool _isSwitching = false;
  bool _isSuperAdmin = false;

  @override
  void initState() {
    super.initState();
    _checkCurrentUserRole();
  }

  /// 🔍 Vérifier le rôle de l'utilisateur actuel
  Future<void> _checkCurrentUserRole() async {
    final hasPermissions = await SuperAdminDiagnosticService.quickPermissionTest();
    setState(() => _isSuperAdmin = hasPermissions);
  }

  /// 🔄 Basculer vers le compte Super Admin
  Future<void> _switchToSuperAdmin() async {
    setState(() => _isSwitching = true);

    try {
      debugPrint('[SUPER_ADMIN_SWITCH] 🔄 Basculement vers Super Admin...');

      // Se déconnecter du compte actuel
      await _auth.signOut();
      
      // Attendre un peu pour que la déconnexion soit effective
      await Future.delayed(const Duration(milliseconds: 500));

      // Se connecter avec le compte Super Admin
      final userCredential = await _auth.signInWithEmailAndPassword(
        email: '<EMAIL>',
        password: 'Acheya123',
      );

      debugPrint('[SUPER_ADMIN_SWITCH] ✅ Connexion Super Admin réussie: ${userCredential.user?.uid}');

      // Vérifier que le document Firestore existe
      final hasPermissions = await SuperAdminDiagnosticService.quickPermissionTest();
      
      if (!hasPermissions) {
        debugPrint('[SUPER_ADMIN_SWITCH] 🔧 Création document Firestore...');
        await SuperAdminDiagnosticService.createSuperAdminFirestoreDocument();
        
        // Vérifier à nouveau
        await Future.delayed(const Duration(milliseconds: 1000));
        final finalCheck = await SuperAdminDiagnosticService.quickPermissionTest();
        
        if (!finalCheck) {
          throw Exception('Impossible de créer les permissions Super Admin');
        }
      }

      setState(() => _isSuperAdmin = true);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: const Text('✅ Connecté en tant que Super Admin !'),
            backgroundColor: Colors.green,
          ),
        );

        // Appeler le callback si fourni
        widget.onSwitchSuccess?.call();
      }

    } catch (e) {
      debugPrint('[SUPER_ADMIN_SWITCH] ❌ Erreur basculement: $e');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isSwitching = false);
    }
  }

  /// 📊 Afficher les informations du compte actuel
  void _showCurrentAccountInfo() {
    final currentUser = _auth.currentUser;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            ,
            const SizedBox(width: 8),
            const Text('👤 Compte Actuel'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ({currentUser?.email ?? 'Non connecté'}'),
            ({currentUser?.uid ?? 'N/A'}'),
            const SizedBox(height: 12),
            Container(
              padding: ,
              ),
              child: Row(
                children: [
                  ,
                  const SizedBox(width: 8),
                  Expanded(
                    child: const Text(
                      _isSuperAdmin 
                          ? '✅ Super Admin (peut créer des utilisateurs)'
                          : '⚠️ Pas Super Admin (ne peut pas créer d\'utilisateurs)',
                      style: TextStyle(
                        color: _isSuperAdmin ? Colors.green.shade700 : Colors.orange.shade700,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Indicateur de statut
        Container(
          padding: ,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              ,
              const SizedBox(width: 4),
              ,
              ),
            ],
          ),
        ),
        
        const SizedBox(width: 8),
        
        // Bouton d'information
        IconButton(
          onPressed: _showCurrentAccountInfo,
          icon: const Icon(Icons.info),
          tooltip: 'Informations du compte',
        ),
        
        // Bouton de basculement (seulement si pas déjà Super Admin)
        if (!_isSuperAdmin) ...[
          const SizedBox(width: 8),
          ElevatedButton.const Icon(
            onPressed: _isSwitching ? null : _switchToSuperAdmin,
            icon: _isSwitching 
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : ,
            label: const Text('Basculer Super Admin
