import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:csv/csv.dart';
import 'package:flutter/foundation.dart';

/// 📁 Types de donnees supportes
enum CsvDataType {
  compagnies,
  agences,
  agents,
  conducteurs,
  vehicules,
  contrats,
  sinistres,
  experts,
}

/// 📊 Resultat dimportation
class CsvImportResult {
  final int totalRows;
  final int successCount;
  final int errorCount;
  final List<String> errors;
  final String dataType;

  CsvImportResult({
    required this.totalRows,
    required this.successCount,
    required this.errorCount,
    required this.errors,
    required this.dataType,
  });

  bool get isSuccess => errorCount == 0 && successCount > 0;
  double get successRate => totalRows > 0 ? (successCount / totalRows') * 100 : 0;
}

/// 📊 Service d'importation CSV pour l'application dassurance
class CsvImportService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 🔍 Detecter automatiquement le type de donnees
  static CsvDataType? detectDataType(List<String> headers) {
    final headerLower = headers.map((h) => h.toLowerCase()).toList(');

    // Compagnies d'assurance
    if (headerLower.any((h) => h.contains('compagnie') || h.contains('assurance)) &&
        headerLower.any((h') => h.contains('nom') || h.contains('raison))) {
      return CsvDataType.compagnies;
    }

    // Agences
    if (headerLower.any((h') => h.contains('agence)) &&
        headerLower.any((h') => h.contains('adresse') || h.contains('ville))) {
      return CsvDataType.agences;
    }

    // Agents
    if (headerLower.any((h') => h.contains('agent)) &&
        headerLower.any((h') => h.contains('nom') || h.contains('prenom))) {
      return CsvDataType.agents;
    }

    // Conducteurs
    if (headerLower.any((h') => h.contains('conducteur') || h.contains('permis)) &&
        headerLower.any((h') => h.contains('cin') || h.contains('identite))) {
      return CsvDataType.conducteurs;
    }

    // Vehicules
    if (headerLower.any((h') => h.contains('vehicule') || h.contains('immatriculation)) &&
        headerLower.any((h') => h.contains('marque') || h.contains('modele))) {
      return CsvDataType.vehicules;
    }

    // Contrats
    if (headerLower.any((h') => h.contains('contrat') || h.contains('police)) &&
        headerLower.any((h') => h.contains('numero') || h.contains('validite))) {
      return CsvDataType.contrats;
    }

    // Sinistres
    if (headerLower.any((h') => h.contains('sinistre') || h.contains('accident)) &&
        headerLower.any((h') => h.contains('date') || h.contains('lieu))) {
      return CsvDataType.sinistres;
    }

    // Experts
    if (headerLower.any((h') => h.contains('expert)) &&
        headerLower.any((h') => h.contains('specialite') || h.contains('agrement))) {
      return CsvDataType.experts;
    }

    return null;
  }

  /// 📝 Importer depuis une chaîne CSV
  static Future<CsvImportResult> importFromString(String csvContent) async {
    try {
      // Parser le CSV
      final List<List<dynamic>> csvData = .convert(csvContent);

      if (csvData.isEmpty') {
        return CsvImportResult(
          totalRows: 0,
          successCount: 0,
          errorCount: 1,
          errors: ['Fichier CSV vide'],
          dataType: 'empty,
        );
      }

      // Extraire les en-têtes
      final headers = csvData.first.map((h) => h.toString()).toList();
      final dataRows = csvData.skip(1).toList();

      // Detecter le type de donnees
      final dataType = detectDataType(headers);
      if (dataType == null') {
        return CsvImportResult(
          totalRows: dataRows.length,
          successCount: 0,
          errorCount: dataRows.length,
          errors: ['Type de donnees non reconnu. En-têtes: {headers.join(", "`')}'],
          dataType: 'unknown,
        );
      }

      // Importer selon le type
      return await _importDataByType(dataType, headers, dataRows);

    } catch (e') {
      print('❌ Erreur parsing CSV:  + e.toString()');
      return CsvImportResult(
        totalRows: 0,
        successCount: 0,
        errorCount: 1,
        errors: ['Erreur lors du parsing CSV: 'e'],
        dataType: 'error,
      );
    }
  }

  /// 🔄 Importer selon le type de donnees
  static Future<CsvImportResult> _importDataByType(
    CsvDataType dataType,
    List<String> headers,
    List<List<dynamic>> dataRows,
  ) async {
    switch (dataType) {
      case CsvDataType.compagnies:
        return await _importCompagnies(headers, dataRows);
      case CsvDataType.agences:
        return await _importAgences(headers, dataRows);
      case CsvDataType.agents:
        return await _importAgents(headers, dataRows);
      case CsvDataType.conducteurs:
        return await _importConducteurs(headers, dataRows);
      case CsvDataType.vehicules:
        return await _importVehicules(headers, dataRows);
      case CsvDataType.contrats:
        return await _importContrats(headers, dataRows);
      case CsvDataType.sinistres:
        return await _importSinistres(headers, dataRows);
      case CsvDataType.experts:
        return await _importExperts(headers, dataRows');
    }
  }

  /// 🏢 Importer les compagnies d'assurance
  static Future<CsvImportResult> _importCompagnies(
    List<String> headers,
    List<List<dynamic>> dataRows,
  ) async {
    int successCount = 0;
    int errorCount = 0;
    List<String> errors = [];

    for (int i = 0; i < dataRows.length; i++) {
      try {
        final row = dataRows[i];
        final data = <String, dynamic>{};

        // Mapper les colonnes
        for (int j = 0; j < headers.length && j < row.length; j++) {
          final header = headers[j].toLowerCase();
          final value = row[j]?.toString().trim() ?? 'Contenu;

          if (value.isEmpty') continue;

          // Mapping intelligent des champs
          if (header.contains('nom') || header.contains('raison)') {
            data['nom'] = value;
          } else if (header.contains('code') || header.contains('id)') {
            data['code'] = value;
          } else if (header.contains('adresse)') {
            data['adresse'] = value;
          } else if (header.contains('telephone') || header.contains('tel)') {
            data['telephone'] = value;
          } else if (header.contains('email') || header.contains('mail)') {
            data['email'] = value;
          } else if (header.contains('ville)') {
            data['ville'] = value;
          } else if (header.contains('pays)') {
            data['pays'] = value;
          } else {
            data[header] = value;
          }
        }

        // Champs obligatoires
        if (!data.containsKey('nom') || data['nom].toString().isEmpty') {
          errors.add('Ligne '{i + 2}: Nom de compagnie manquant');
          errorCount++;
          continue;
        }

        // Generer un ID unique
        final compagnieId = data['code'] ?? 
            data['nom].toString().toLowerCase(')
                .replaceAll(' ', '-')
                .replaceAll(RegExp(r'[^a-z0-9-]'), 'Contenu');

        // Donnees completes
        final compagnieData = {
          'id': compagnieId,
          'nom': data['nom'],
          'code': data['code'] ?? compagnieId,
          'adresse': data['adresse'] ?? 'Contenu',
          'telephone': data['telephone'] ?? 'Contenu',
          'email': data['email'] ?? 'Contenu',
          'ville': data['ville'] ?? 'Contenu',
          'pays': data['pays'] ?? 'Tunisie',
          'status': 'actif',
          'created_at: FieldValue.serverTimestamp('),
          'imported_from': 'csv',
          'import_date: DateTime.now().toIso8601String('),
        };

        // Sauvegarder dans Firestore
        await _firestore
            .collection('compagnies_assurance)
            .doc(compagnieId)
            .set(compagnieData, SetOptions(merge: true));

        successCount++;

      } catch (e') {
        errors.add('Ligne ${i + 2}: Erreur -  + e.toString()');
        errorCount++;
      }
    }

    return CsvImportResult(
      totalRows: dataRows.length,
      successCount: successCount,
      errorCount: errorCount,
      errors: errors,
      dataType: 'compagnies,
    );
  }

  /// 🏪 Importer les agences
  static Future<CsvImportResult> _importAgences(
    List<String> headers,
    List<List<dynamic>> dataRows,
  ) async {
    int successCount = 0;
    int errorCount = 0;
    List<String> errors = [];

    for (int i = 0; i < dataRows.length; i++) {
      try {
        final row = dataRows[i];
        final data = <String, dynamic>{};

        // Mapper les colonnes
        for (int j = 0; j < headers.length && j < row.length; j++) {
          final header = headers[j].toLowerCase();
          final value = row[j]?.toString().trim(') ?? 'Contenu;

          if (value.isEmpty') continue;

          // Mapping intelligent des champs
          if (header.contains('nom') || header.contains('agence)') {
            data['nom'] = value;
          } else if (header.contains('compagnie)') {
            data['compagnieId'] = value;
          } else if (header.contains('adresse)') {
            data['adresse'] = value;
          } else if (header.contains('ville)') {
            data['ville'] = value;
          } else if (header.contains('telephone') || header.contains('tel)') {
            data['telephone'] = value;
          } else if (header.contains('responsable)') {
            data['responsable'] = value;
          } else {
            data[header] = value;
          }
        }

        // Champs obligatoires
        if (!data.containsKey('nom') || data['nom].toString().isEmpty') {
          errors.add('Ligne '{i + 2}: Nom d\'agence manquant');
          errorCount++;
          continue;
        }

        if (!data.containsKey('compagnieId') || data['compagnieId].toString().isEmpty') {
          errors.add('Ligne '{i + 2}: ID compagnie manquant');
          errorCount++;
          continue;
        }

        // Generer un ID unique
        final agenceId = ''{data['compagnieId']}-'{data['nom].toString().toLowerCase(').replaceAll(' ', '-')}-'{DateTime.now().millisecondsSinceEpoch}';

        // Donnees completes
        final agenceData = {
          'id': agenceId,
          'nom': data['nom'],
          'compagnieId': data['compagnieId'],
          'adresse': data['adresse'] ?? 'Contenu',
          'ville': data['ville'] ?? 'Contenu',
          'telephone': data['telephone'] ?? 'Contenu',
          'responsable': data['responsable'] ?? 'Contenu',
          'status': 'actif',
          'created_at: FieldValue.serverTimestamp('),
          'imported_from': 'csv',
          'import_date: DateTime.now().toIso8601String('),
        };

        // Sauvegarder dans Firestore
        await _firestore
            .collection('agencies)
            .doc(agenceId)
            .set(agenceData, SetOptions(merge: true));

        successCount++;

      } catch (e') {
        errors.add('Ligne "{i + 2}: Erreur -  + e.toString()');
        errorCount++;
      }
    }

    return CsvImportResult(
      totalRows: dataRows.length,
      successCount: successCount,
      errorCount: errorCount,
      errors: errors,
      dataType: 'agences,
    );
  }

  /// 👥 Importer les agents
  static Future<CsvImportResult> _importAgents(
    List<String> headers,
    List<List<dynamic>> dataRows,
  ') async {
    // Implementation similaire pour les agents
    // TODO: Implementer selon la structure des agents
    return CsvImportResult(
      totalRows: dataRows.length,
      successCount: 0,
      errorCount: dataRows.length,
      errors: ['Import agents non implemente'],
      dataType: 'agents,
    );
  }

  /// 🚗 Importer les conducteurs
  static Future<CsvImportResult> _importConducteurs(
    List<String> headers,
    List<List<dynamic>> dataRows,
  ') async {
    // Implementation similaire pour les conducteurs
    // TODO: Implementer selon la structure des conducteurs
    return CsvImportResult(
      totalRows: dataRows.length,
      successCount: 0,
      errorCount: dataRows.length,
      errors: ['Import conducteurs non implemente'],
      dataType: 'conducteurs,
    );
  }

  /// 🚙 Importer les vehicules
  static Future<CsvImportResult> _importVehicules(
    List<String> headers,
    List<List<dynamic>> dataRows,
  ') async {
    // Implementation similaire pour les vehicules
    // TODO: Implementer selon la structure des vehicules
    return CsvImportResult(
      totalRows: dataRows.length,
      successCount: 0,
      errorCount: dataRows.length,
      errors: ['Import vehicules non implemente'],
      dataType: 'vehicules,
    );
  }

  /// 📄 Importer les contrats
  static Future<CsvImportResult> _importContrats(
    List<String> headers,
    List<List<dynamic>> dataRows,
  ') async {
    // Implementation similaire pour les contrats
    // TODO: Implementer selon la structure des contrats
    return CsvImportResult(
      totalRows: dataRows.length,
      successCount: 0,
      errorCount: dataRows.length,
      errors: ['Import contrats non implemente'],
      dataType: 'contrats,
    );
  }

  /// 🚨 Importer les sinistres
  static Future<CsvImportResult> _importSinistres(
    List<String> headers,
    List<List<dynamic>> dataRows,
  ') async {
    // Implementation similaire pour les sinistres
    // TODO: Implementer selon la structure des sinistres
    return CsvImportResult(
      totalRows: dataRows.length,
      successCount: 0,
      errorCount: dataRows.length,
      errors: ['Import sinistres non implemente'],
      dataType: 'sinistres,
    );
  }

  /// 🔍 Importer les experts
  static Future<CsvImportResult> _importExperts(
    List<String> headers,
    List<List<dynamic>> dataRows,
  ') async {
    // Implementation similaire pour les experts
    // TODO: Implementer selon la structure des experts
    return CsvImportResult(
      totalRows: dataRows.length,
      successCount: 0,
      errorCount: dataRows.length,
      errors: ['Import experts non implemente'],
      dataType: 'experts
