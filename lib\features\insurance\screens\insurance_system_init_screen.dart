import 'package:flutter/material.dart';
import '../services/insurance_company_service.dart';
import '../models/compagnie_model.dart';

/// 🏢 Écran d'initialisation du système d'assurance
class InsuranceSystemInitScreen extends StatefulWidget {
  const Text(\;

  @override
  State<InsuranceSystemInitScreen> createState() => _InsuranceSystemInitScreenState();
}

class _InsuranceSystemInitScreenState extends State<InsuranceSystemInitScreen> {
  bool _isLoading = false;
  Map<String, dynamic>? _initResults;
  List<CompagnieModel> _companies = [];

  @override
  void initState() {
    super.initState();
    _loadCompanies();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text("Titre"),
        ),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: ,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.blue[700]!, Colors.blue[900]!],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.blue.withValues(alpha: ,
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ,
                  ),
                  const SizedBox(height: 8),
                  ,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Boutons d'action
            Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    icon: Icons.rocket_launch,
                    label: 'Initialiser Système',
                    color: Colors.green,
                    onPressed: _isLoading ? null : _initializeSystem,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildActionButton(
                    icon: Icons.refresh,
                    label: 'Recharger',
                    color: Colors.orange,
                    onPressed: _isLoading ? null : _loadCompanies,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Résultats d'initialisation
            if (_initResults != null) _buildInitResults(),

            // Liste des compagnies
            if (_companies.isNotEmpty) _buildCompaniesList(),

            // Loading
            if (_isLoading)
              (1),
                      const SizedBox(height: 16),
                      const Text(
                        'Initialisation en cours...',
                        style: TextStyle(fontSize: 16),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback? onPressed,
  }) {
    return ElevatedButton.const Icon(
      onPressed: onPressed,
      icon: const Icon(icon),
      label: const Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: ,
        ),
        elevation: 2,
      ),
    );
  }

  Widget _buildInitResults() {
    final compagniesCreated = _initResults!['compagnies_created'] as int;
    final agencesCreated = _initResults!['agences_created'] as int;
    final errors = _initResults!['errors'] as List<String>;

    return Container(
      width: double.infinity,
      padding: ,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: ,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ,
          ),
          const SizedBox(height: 12),
          
          // Statistiques
          Row(
            children: [
              _buildStatChip('Compagnies', compagniesCreated.toString(), Colors.blue),
              const SizedBox(width: 8),
              _buildStatChip('Agences', agencesCreated.toString(), Colors.green),
              const SizedBox(width: 8),
              _buildStatChip('Erreurs', errors.length.toString(), Colors.red),
            ],
          ),
          
          if (errors.isNotEmpty) ...[
            const SizedBox(height: 16),
            ,
            ),
            const SizedBox(height: 8),
            ...errors.map((error) => (1),
              ),
            )),
          ],
        ],
      ),
    );
  }

  Widget _buildCompaniesList() {
    return Container(
      width: double.infinity,
      padding: ,
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: ,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(\',
            style: ,
          ),
          const SizedBox(height: 16),
          
          ..._companies.map((company) => _buildCompanyCard(company)),
        ],
      ),
    );
  }

  Widget _buildCompanyCard(CompagnieModel company) {
    return Container(
      margin: ,
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: Colors.blue[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: ,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ,
                ),
                const SizedBox(height: 4),
                ,
                ),
                const SizedBox(height: 4),
                ({company.typesAssurance.length} types d\'assurance',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: ,
            ),
            child: ,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatChip(String label, String value, Color color) {
    return Container(
      padding: ,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withValues(alpha: ),
      ),
      child: (value',
        style: TextStyle(
          color: color,
          fontWeight: FontWeight.w600,
          fontSize: 12,
        ),
      ),
    );
  }

  Future<void> _initializeSystem() async {
    setState(() {
      _isLoading = true;
      _initResults = null;
    });

    try {
      final results = await InsuranceCompanyService.initializeInsuranceSystem();
      setState(() {
        _initResults = results;
      });
      
      // Recharger les compagnies
      await _loadCompanies();
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: const Text('🎉 Système d\'assurance initialisé avec succès'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: (e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadCompanies() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final companies = await InsuranceCompanyService.getAllCompanies();
      setState(() {
        _companies = companies;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: (e
