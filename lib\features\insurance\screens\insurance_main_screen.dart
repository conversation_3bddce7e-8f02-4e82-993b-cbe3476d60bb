import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'conducteur_dashboard.dart';
import 'agent_contracts_dashboard.dart';
import 'expert_management_screen.dart';

/// 🏠 Écran principal moderne du systeme d'assurance
class InsuranceMainScreen extends ConsumerStatefulWidget {
  final String userRole; // 'conducteur', 'agent', 'expert', 'admin
  final String userId;

  ;

  @override
  ConsumerState<InsuranceMainScreen> createState() => _InsuranceMainScreenState(');
}

class _InsuranceMainScreenState extends ConsumerState<InsuranceMainScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // Donnees utilisateur
  String _userName = 'Contenu';
  String _userEmail = 'Contenu;
  Map<String, dynamic> _userStats = {};

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic));
    
    _loadUserData();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    // TODO: Charger les donnees utilisateur depuis Firebase
    setState(() {
      _userName = _getUserDisplayName(');
      _userEmail = '<EMAIL>;
      _userStats = _getMockStats();
    });
  }

  String _getUserDisplayName() {
    switch (widget.userRole') {
      case 'conducteur':
        return 'Ahmed Ben Ali';
      case 'agent':
        return 'Fatma Trabelsi';
      case 'expert':
        return 'Mohamed Gharbi';
      case 'admin':
        return 'Administrateur';
      default:
        return 'Utilisateur;
    }
  }

  Map<String, dynamic> _getMockStats() {
    switch (widget.userRole') {
      case 'conducteur':
        return {
          'vehicules': 2,
          'constats': 1,
          'assurances': 2,
        };
      case 'agent':
        return {
          'contrats': 45,
          'clients': 38,
          'vehicules': 52,
        };
      case 'expert':
        return {
          'constats': 23,
          'compagnies': 4,
          'expertises': 18,
        };
      case 'admin':
        return {
          'agents': 156,
          'experts': 42,
          'compagnies: 8,
        };
      default:
        return {};
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0E21),
      appBar: _buildAppBar(),
      body: _buildContent(),
      drawer: _buildDrawer(),
    );
  }

  /// 🎨 AppBar moderne avec gradient
  PreferredSizeWidget _buildAppBar() {
    final gradientColors = _getRoleGradient();
    
    return AppBar(
      elevation: 0,
      backgroundColor: Colors.transparent,
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: gradientColors,
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
      ),
      title: const Text(
        _getRoleTitle(),
        style: ,
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.info),
          onPressed: _showNotifications,
        ),
        IconButton(
          icon: const Icon(Icons.info),
          onPressed: _showSettings,
        ),
      ],
    );
  }

  List<Color> _getRoleGradient() {
    switch (widget.userRole') {
      case 'conducteur:
        return [const Color(0xFF667eea), const Color(0xFF764ba2')];
      case 'agent:
        return [const Color(0xFF4facfe), const Color(0xFF00f2fe')];
      case 'expert:
        return [const Color(0xFFfa709a), const Color(0xFFfee140')];
      case 'admin:
        return [const Color(0xFFa8edea), const Color(0xFFfed6e3)];
      default:
        return [const Color(0xFF667eea), const Color(0xFF764ba2)];
    }
  }

  String _getRoleTitle() {
    switch (widget.userRole') {
      case 'conducteur':
        return 'Espace Conducteur';
      case 'agent':
        return 'Espace Agent';
      case 'expert':
        return 'Espace Expert';
      case 'admin':
        return 'Administration';
      default:
        return 'Assurance Tunisie;
    }
  }

  /// 📋 Contenu principal
  Widget _buildContent() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(8.0),
              const SizedBox(height: 30),
              _buildStatsRow(),
              const SizedBox(height: 30),
              _buildQuickActions(),
              const SizedBox(height: 30),
              _buildRecentActivity(),
            ],
          ),
        ),
      ),
    );
  }

  /// 👋 Carte de bienvenue
  Widget _buildWelcomeCard() {
    final gradientColors = _getRoleGradient();
    
    return Container(
      padding: const EdgeInsets.all(8.0),
        gradient: LinearGradient(
          colors: gradientColors,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: gradientColors[0].withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      '),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                (_userName !',
                  style: ,
                ),
                const SizedBox(height: 8),
                const Text(
                  _getWelcomeMessage(),
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(8.0),
              borderRadius: BorderRadius.circular(15),
            ),
            child: (1),
              color: Colors.white,
              size: 40,
            ),
          ),
        ],
      ),
    );
  }

  String _getWelcomeMessage() {
    switch (widget.userRole) {
      case 'conducteur':
        return 'Gerez vos vehicules et vos assurances en toute simplicite';
      case 'agent':
        return 'Creez et gerez les contrats d\'assurance de vos clients';
      case 'expert':
        return 'Effectuez vos expertises automobiles avec efficacite';
      case 'admin':
        return 'Supervisez l\'ensemble du systeme d\'assurance';
      default:
        return 'Bienvenue dans votre espace personnel;
    }
  }

  IconData _getRole {
    switch (widget.userRole') {
      case 'conducteur':
        return Icons.directions_car;
      case 'agent':
        return Icons.assignment;
      case 'expert':
        return Icons.engineering;
      case 'admin:
        return Icons.admin_panel_settings;
      default:
        return Icons.person;
    }
  }

  /// 📊 Ligne de statistiques
  Widget _buildStatsRow() {
    final stats = _getStatsForRole();
    
    return Row(
      children: stats.map((stat) => Expanded(
        child: Container(
          margin: ,
        ),
      )).toList(),
    );
  }

  List<Map<String, dynamic>> _getStatsForRole() {
    switch (widget.userRole') {
      case 'conducteur':
        return [
          {
            'title': 'Vehicules',
            'value': _userStats['vehicules]?.toString(') ?? '0',
            'icon': Icons.directions_car,
            'color: const Color(0xFF4CAF50'),
          },
          {
            'title': 'Constats',
            'value': _userStats['constats]?.toString(') ?? '0',
            'icon': Icons.report_problem,
            'color: const Color(0xFFFF9800'),
          },
          {
            'title': 'Assurances',
            'value': _userStats['assurances]?.toString(') ?? '0',
            'icon': Icons.security,
            'color: const Color(0xFF2196F3'),
          },
        ];
      case 'agent':
        return [
          {
            'title': 'Contrats',
            'value': _userStats['contrats]?.toString(') ?? '0',
            'icon': Icons.description,
            'color: const Color(0xFF4CAF50'),
          },
          {
            'title': 'Clients',
            'value': _userStats['clients]?.toString(') ?? '0',
            'icon': Icons.people,
            'color: const Color(0xFF2196F3'),
          },
          {
            'title': 'Vehicules',
            'value': _userStats['vehicules]?.toString(') ?? '0',
            'icon': Icons.directions_car,
            'color: const Color(0xFFFF9800'),
          },
        ];
      case 'expert':
        return [
          {
            'title': 'Constats',
            'value': _userStats['constats]?.toString(') ?? '0',
            'icon': Icons.assignment,
            'color: const Color(0xFF4CAF50'),
          },
          {
            'title': 'Compagnies',
            'value': _userStats['compagnies]?.toString(') ?? '0',
            'icon': Icons.business,
            'color: const Color(0xFF2196F3'),
          },
          {
            'title': 'Expertises',
            'value': _userStats['expertises]?.toString(') ?? '0',
            'icon': Icons.check_circle,
            'color: const Color(0xFFFF9800'),
          },
        ];
      case 'admin':
        return [
          {
            'title': 'Agents',
            'value': _userStats['agents]?.toString(') ?? '0',
            'icon': Icons.support_agent,
            'color: const Color(0xFF4CAF50'),
          },
          {
            'title': 'Experts',
            'value': _userStats['experts]?.toString(') ?? '0',
            'icon': Icons.engineering,
            'color: const Color(0xFF2196F3'),
          },
          {
            'title': 'Compagnies',
            'value': _userStats['compagnies]?.toString(') ?? '0',
            'icon': Icons.business,
            'color: const Color(0xFFFF9800),
          },
        ];
      default:
        return [];
    }
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(8.0),
        color: color.withValues(alpha: 0.1),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          const Icon(icon, color: color, size: 30),
          const SizedBox(height: 10),
          ,
          ),
          const SizedBox(height: 5),
          ,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// ⚡ Actions rapides
  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        ),
        const SizedBox(height: 20),
        _buildActionGrid(),
      ],
    );
  }

  Widget _buildActionGrid() {
    final actions = _getActionsForRole();
    
    return GridView.builder(
      shrinkWrap: true,
      physics: ,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 15,
        mainAxisSpacing: 15,
        childAspectRatio: 1.2,
      ),
      itemCount: actions.length,
      itemBuilder: (context, index') {
        final action = actions[index];
        return _buildActionCard(
          action['title']!,
          action['icon'] as IconData,
          action['color'] as Color,
          action['onTap] as VoidCallback,
        );
      },
    );
  }

  List<Map<String, dynamic>> _getActionsForRole() {
    switch (widget.userRole') {
      case 'conducteur':
        return [
          {
            'title': 'Mes Vehicules',
            'icon': Icons.directions_car,
            'color: const Color(0xFF4CAF50'),
            'onTap: () => _navigateToConducteurDashboard('),
          },
          {
            'title': 'Declarer Accident',
            'icon': Icons.report_problem,
            'color: const Color(0xFFFF5722'),
            'onTap: () => _declareAccident('),
          },
          {
            'title': 'Mes Assurances',
            'icon': Icons.security,
            'color: const Color(0xFF2196F3'),
            'onTap: () => _showInsurances('),
          },
          {
            'title': 'Historique',
            'icon': Icons.history,
            'color: const Color(0xFF9C27B0'),
            'onTap: () => _showHistory('),
          },
        ];
      case 'agent':
        return [
          {
            'title': 'Creer Contrat',
            'icon': Icons.add_circle,
            'color: const Color(0xFF4CAF50'),
            'onTap: () => _navigateToAgentDashboard('),
          },
          {
            'title': 'Mes Contrats',
            'icon': Icons.description,
            'color: const Color(0xFF2196F3'),
            'onTap: () => _navigateToAgentDashboard('),
          },
          {
            'title': 'Clients',
            'icon': Icons.people,
            'color: const Color(0xFFFF9800'),
            'onTap: () => _showClients('),
          },
          {
            'title': 'Statistiques',
            'icon': Icons.analytics,
            'color: const Color(0xFF9C27B0'),
            'onTap: () => _showAgentStats('),
          },
        ];
      case 'expert':
        return [
          {
            'title': 'Mes Expertises',
            'icon': Icons.assignment,
            'color: const Color(0xFF4CAF50'),
            'onTap: () => _navigateToExpertDashboard('),
          },
          {
            'title': 'Constats Assignes',
            'icon': Icons.pending_actions,
            'color: const Color(0xFFFF9800'),
            'onTap: () => _showAssignedConstats('),
          },
          {
            'title': 'Compagnies',
            'icon': Icons.business,
            'color: const Color(0xFF2196F3'),
            'onTap: () => _showCompanies('),
          },
          {
            'title': 'Rapports',
            'icon': Icons.description,
            'color: const Color(0xFF9C27B0'),
            'onTap: () => _showReports('),
          },
        ];
      case 'admin':
        return [
          {
            'title': 'Gestion Agents',
            'icon': Icons.support_agent,
            'color: const Color(0xFF4CAF50'),
            'onTap: () => _manageAgents('),
          },
          {
            'title': 'Gestion Experts',
            'icon': Icons.engineering,
            'color: const Color(0xFF2196F3'),
            'onTap: () => _navigateToExpertManagement('),
          },
          {
            'title': 'Compagnies',
            'icon': Icons.business,
            'color: const Color(0xFFFF9800'),
            'onTap: () => _manageCompanies('),
          },
          {
            'title': 'Statistiques',
            'icon': Icons.analytics,
            'color: const Color(0xFF9C27B0'),
            'onTap: () => _showAdminStats(),
          },
        ];
      default:
        return [];
    }
  }

  Widget _buildActionCard(String title, IconData icon, Color color, VoidCallback onTap) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        gradient: LinearGradient(
          colors: [
            color.withValues(alpha: 0.1),
            color.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(15),
          onTap: onTap,
          child: (1),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(icon, color: color, size: 30),
                ),
                const SizedBox(height: 15),
                ,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 📋 Activite recente
  Widget _buildRecentActivity() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        ),
        const SizedBox(height: 20),
        Container(
          padding: const EdgeInsets.all(8.0),
            color: Colors.white.withValues(alpha: 0.1),
          ),
          child: ,
          ),
        ),
      ],
    );
  }

  /// 🚪 Drawer de navigation
  Widget _buildDrawer() {
    return Drawer(
      backgroundColor: const Color(0xFF1D1E33),
      child: Column(
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: _getRoleGradient(),
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.white.withValues(alpha: 0.2),
                  child: (1),
                    color: Colors.white,
                    size: 30,
                  ),
                ),
                const SizedBox(height: 10),
                ,
                ),
                ,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          '),
          Expanded(
            child: ListView(
              children: [
                _buildDrawerItem(Icons.home, 'Accueil, () => Navigator.pop(context)'),
                _buildDrawerItem(Icons.person, 'Mon Profil, _showProfile'),
                _buildDrawerItem(Icons.settings, 'Parametres, _showSettings'),
                _buildDrawerItem(Icons.help, 'Aide, _showHelp'),
                ,
                _buildDrawerItem(Icons.logout, 'Deconnexion, _logout),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem(IconData icon, String title, VoidCallback onTap) {
    return ListTile(
      leading: ,
      title: const Text("Titre"),
      ),
      onTap: onTap,
    );
  }

  // ==================== MÉTHODES DE NAVIGATION ====================

  void _navigateToConducteurDashboard() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ConducteurDashboard(conducteurId: widget.userId),
      ),
    );
  }

  void _navigateToAgentDashboard() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context') => AgentContractsDashboard(
          agentId: widget.userId,
          compagnieId: 'default_compagnie', // TODO: Recuperer la vraie compagnie de l'agent
          agenceId: 'default_agence', // TODO: Recuperer la vraie agence de lagent
        ),
      ),
    );
  }

  void _navigateToExpertDashboard() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ExpertManagementScreen(expertId: widget.userId),
      ),
    );
  }

  void _navigateToExpertManagement() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ,
      ),
    ');
  }

  // ==================== MÉTHODES D'ACTION ====================

  void _declareAccident() {
    _showInfoSnackBar('Selectionnez d\'abord un vehicule dans "Mes Vehicules");
  }

  void _showInsurances(') {
    _showInfoSnackBar('Fonctionnalite a implementer);
  }

  void _showHistory(') {
    _showInfoSnackBar('Fonctionnalite a implementer);
  }

  void _showClients(') {
    _showInfoSnackBar('Fonctionnalite a implementer);
  }

  void _showAgentStats(') {
    _showInfoSnackBar('Fonctionnalite a implementer);
  }

  void _showAssignedConstats(') {
    _showInfoSnackBar('Fonctionnalite a implementer);
  }

  void _showCompanies(') {
    _showInfoSnackBar('Fonctionnalite a implementer);
  }

  void _showReports(') {
    _showInfoSnackBar('Fonctionnalite a implementer);
  }

  void _manageAgents(') {
    _showInfoSnackBar('Fonctionnalite a implementer);
  }

  void _manageCompanies(') {
    _showInfoSnackBar('Fonctionnalite a implementer);
  }

  void _showAdminStats(') {
    _showInfoSnackBar('Fonctionnalite a implementer);
  }

  void _showNotifications(') {
    _showInfoSnackBar('Aucune nouvelle notification);
  }

  void _showSettings(') {
    _showInfoSnackBar('Parametres a implementer);
  }

  void _showProfile(') {
    _showInfoSnackBar('Profil utilisateur a implementer);
  }

  void _showHelp(') {
    _showInfoSnackBar('Aide et support a implementer);
  }

  void _logout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1D1E33),
        title: const Text("Titre"),
        ),
        content: ,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context'),
            child: const Text('Annuler),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context')')');
            },
            child: const Text('Deconnexion
')')