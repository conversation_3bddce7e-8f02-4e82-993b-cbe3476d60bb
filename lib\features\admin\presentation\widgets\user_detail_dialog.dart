import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../core/theme/modern_theme.dart';
import '../../services/password_management_service.dart';
import '../../services/user_management_service.dart';

/// 👤 Dialog détaillé d'un utilisateur avec toutes les informations et actions
class UserDetailDialog extends StatefulWidget {
  final Map<String, dynamic> user;

   ) : super(key: key);

  @override
  State<UserDetailDialog> createState() => _UserDetailDialogState();
}

class _UserDetailDialogState extends State<UserDetailDialog>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;
  List<Map<String, dynamic>> _passwordHistory = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadPasswordHistory();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// 📊 Charger l'historique des mots de passe
  Future<void> _loadPasswordHistory() async {
    final history = await PasswordManagementService.getPasswordHistory(
      userId: widget.user['uid'] ?? widget.user['id'],
    );
    if (mounted) {
      setState(() {
        _passwordHistory = history;
      });
    }
  }

  /// 🔄 Réinitialiser le mot de passe
  Future<void> _resetPassword() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Réinitialiser le mot de passe'),
        content: ({widget.user['displayName'] ?? widget.user['email']} ?\n\n'
          'L\'utilisateur devra changer ce mot de passe à sa prochaine connexion.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: ModernTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Réinitialiser'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() => _isLoading = true);

      final result = await PasswordManagementService.resetUserPassword(
        userId: widget.user['uid'] ?? widget.user['id'],
        userEmail: widget.user['email'],
        adminId: 'super_admin', // TODO: Récupérer l'ID de l'admin connecté
      );

      setState(() => _isLoading = false);

      if (mounted) {
        if (result['success']) {
          // Afficher le mot de passe temporaire
          _showTemporaryPassword(result['temporaryPassword']);
          _loadPasswordHistory(); // Recharger l'historique
        } else {
          _showErrorSnackBar(result['message']);
        }
      }
    }
  }

  /// 🔑 Afficher le mot de passe temporaire
  void _showTemporaryPassword(String password) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            ,
            const SizedBox(width: 8),
            const Text('Mot de passe temporaire'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ,
            ),
            const SizedBox(height: 16),
            Container(
              padding: ,
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Selectable.selectable(,
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      Clipboard.setData(ClipboardData(text: password));
                      _showSuccessSnackBar('Mot de passe copié !');
                    },
                    icon: const Icon(Icons.info),
                    tooltip: 'Copier',
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            ,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
          ElevatedButton(
            onPressed: () {
              Clipboard.setData(ClipboardData(text: password));
              _showSuccessSnackBar('Mot de passe copié !');
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: ModernTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Copier et fermer'),
          ),
        ],
      ),
    );
  }

  /// ✅ Afficher message de succès
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// ❌ Afficher message d'erreur
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        constraints: const BoxConstraints(maxWidth: 800, maxHeight: 700),
        child: Column(
          children: [
            _buildHeader(),
            _buildTabBar(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildProfileTab(),
                  _buildSecurityTab(),
                  _buildHistoryTab(),
                ],
              ),
            ),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  /// 📋 En-tête avec informations principales
  Widget _buildHeader() {
    return Container(
      padding: ,
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 30,
            backgroundColor: Colors.white.withValues(alpha: 0.2),
            child: [0].toUpperCase(),
              style: ,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ,
                ),
                const SizedBox(height: 4),
                ,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Flexible(child: _buildStatusChip()),
                    const SizedBox(width: 8),
                    Flexible(child: _buildRoleChip()),
                  ],
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.info),
          ),
        ],
      ),
    );
  }

  /// 🏷️ Chip de statut
  Widget _buildStatusChip() {
    final status = widget.user['status'] ?? 'inconnu';
    Color color;

    switch (status) {
      case 'actif':
        color = Colors.green;
        break;
      case 'en_attente':
        color = Colors.orange;
        break;
      case 'desactive':
        color = Colors.red;
        break;
      default:
        color = Colors.grey;
    }

    return Container(
      padding: ,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.5)),
      ),
      child: ,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// 🏷️ Chip de rôle
  Widget _buildRoleChip() {
    final role = widget.user['role'] ?? 'inconnu';

    return Container(
      padding: ,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.5)),
      ),
      child: ,
        style: ,
      ),
    );
  }

  /// 📑 Barre d'onglets
  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: ModernTheme.primaryColor,
        unselectedLabelColor: Colors.grey,
        indicatorColor: ModernTheme.primaryColor,
        tabs: const [
            text: 'Profil',
          ),
          Tab(
            icon: const Icon(Icons.info),
            text: 'Sécurité',
          ),
          Tab(
            icon: const Icon(Icons.info),
            text: 'Historique',
          ),
        ],
      ),
    );
  }

  /// 👤 Onglet Profil
  Widget _buildProfileTab() {
    return SingleChildScrollView(
      padding: ,
            _buildInfoRow('Email', widget.user['email'] ?? 'Non défini'),
            _buildInfoRow('Téléphone', widget.user['telephone'] ?? 'Non défini'),
            _buildInfoRow('CIN', widget.user['cin'] ?? 'Non défini'),
            _buildInfoRow('Rôle', widget.user['role'] ?? 'Non défini'),
            _buildInfoRow('Statut', widget.user['status'] ?? 'Non défini'),
          ),

          const SizedBox(height: 24),

          _buildInfoSection('Organisation', [
            _buildInfoRow('Compagnie', widget.user['compagnieNom'] ?? 'Non assignée'),
            _buildInfoRow('Agence', widget.user['agenceNom'] ?? 'Non assignée'),
            _buildInfoRow('ID Compagnie', widget.user['compagnieId'] ?? 'N/A'),
            _buildInfoRow('ID Agence', widget.user['agenceId'] ?? 'N/A'),
          ),

          const SizedBox(height: 24),

          _buildInfoSection('Traçabilité', [
            _buildInfoRow('Origine', _getOriginLabel(widget.user['origine'] ?? 'inconnu')),
            _buildInfoRow('Créé par', widget.user['createdBy'] ?? 'Système'),
            _buildInfoRow('Date de création', _formatDate(widget.user['createdAt'])),
            _buildInfoRow('Dernière modification', _formatDate(widget.user['updatedAt'])),
            if (widget.user['requestId'] != null)
              _buildInfoRow('ID Demande', widget.user['requestId'),
          ),

          if (widget.user['experienceAnnees'] != null) ...[
            const SizedBox(height: 24),
            _buildInfoSection('Informations professionnelles', [
              _buildInfoRow('Années d\'expérience', '${widget.user['experienceAnnees']} ans'),
            ),
          ],
        ],
      ),
    );
  }

  /// 🔐 Onglet Sécurité
  Widget _buildSecurityTab() {
    return SingleChildScrollView(
      padding: ,
            _buildInfoRow('Compte actif', widget.user['isActive'] == true ? 'Oui' : 'Non'),
            _buildInfoRow('Compte bloqué', widget.user['isLocked'] == true ? 'Oui' : 'Non'),
            if (widget.user['isLocked'] == true) ...[
              _buildInfoRow('Raison du blocage', widget.user['lockReason'] ?? 'Non spécifiée'),
              _buildInfoRow('Bloqué le', _formatDate(widget.user['lockedAt'])),
              _buildInfoRow('Bloqué par', widget.user['lockedBy'] ?? 'Inconnu'),
            ],
          ),

          const SizedBox(height: 24),

          _buildInfoSection('Mot de passe', [
            _buildInfoRow('Doit changer le mot de passe',
              widget.user['doitChangerMotDePasse'] == true ? 'Oui' : 'Non'),
            if (widget.user['motDePasseReinitialisePar'] != null) ...[
              _buildInfoRow('Réinitialisé par', widget.user['motDePasseReinitialisePar'),
              _buildInfoRow('Réinitialisé le', _formatDate(widget.user['motDePasseReinitialiseLE'])),
            ],
          ),

          const SizedBox(height: 24),

          // Actions de sécurité
          _buildSecurityActions(),
        ],
      ),
    );
  }

  /// 📊 Onglet Historique
  Widget _buildHistoryTab() {
    return SingleChildScrollView(
      padding: ,
              ),
              ,
              IconButton(
                onPressed: _loadPasswordHistory,
                icon: const Icon(Icons.info),
                tooltip: 'Actualiser',
              ),
            ],
          ),

          const SizedBox(height: 16),

          if (_passwordHistory.isEmpty)
            (1),
                ),
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: ,
              itemCount: _passwordHistory.length,
              itemBuilder: (context, index) {
                final entry = _passwordHistory[index];
                return _buildHistoryEntry(entry);
              },
            ),
        ],
      ),
    );
  }

  /// 📝 Section d'informations
  Widget _buildInfoSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: ,
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  /// 📄 Ligne d'information
  Widget _buildInfoRow(String label, String value) {
    return (1),
            ),
          ),
          Expanded(
            child: Selectable.selectable(,
            ),
          ),
        ],
      ),
    );
  }

  /// 🔐 Actions de sécurité
  Widget _buildSecurityActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: [
            ElevatedButton.const Icon(
              onPressed: _isLoading ? null : _resetPassword,
              icon: const Icon(Icons.info),
              label: const Text('Réinitialiser mot de passe'),
              style: ElevatedButton.styleFrom(
                backgroundColor: ModernTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
            if (widget.user['isLocked'] == true)
              ElevatedButton.const Icon(
                onPressed: _isLoading ? null : _unlockAccount,
                icon: const Icon(Icons.info),
                label: const Text('Débloquer compte'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              )
            else
              ElevatedButton.const Icon(
                onPressed: _isLoading ? null : _lockAccount,
                icon: const Icon(Icons.info),
                label: const Text('Bloquer compte'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
              ),
          ],
        ),
      ],
    );
  }

  /// 📊 Entrée d'historique
  Widget _buildHistoryEntry(Map<String, dynamic> entry) {
    final action = entry['action'] ?? 'Contenu';
    final timestamp = entry['timestamp'] as Timestamp?;
    final details = entry['details'] as Map<String, dynamic>? ?? {};

    IconData icon;
    Color color;
    String actionText;

    switch (action) {
      case 'reset_password':
        icon = Icons.key;
        color = Colors.blue;
        actionText = 'Mot de passe réinitialisé';
        break;
      case 'lock_account':
        icon = Icons.lock;
        color = Colors.red;
        actionText = 'Compte bloqué';
        break;
      case 'unlock_account':
        icon = Icons.lock_open;
        color = Colors.green;
        actionText = 'Compte débloqué';
        break;
      default:
        icon = Icons.info;
        color = Colors.grey;
        actionText = action;
    }

    return Container(
      margin: ,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Container(
            padding: ,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ,
                ),
                if (timestamp != null)
                  const Text(
                    _formatDate(timestamp),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                if (details.isNotEmpty)
                  ,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 🔓 Débloquer le compte
  Future<void> _unlockAccount() async {
    setState(() => _isLoading = true);

    final result = await PasswordManagementService.unlockUserAccount(
      userId: widget.user['uid'] ?? widget.user['id'],
      userEmail: widget.user['email'],
      adminId: 'super_admin', // TODO: Récupérer l'ID de l'admin connecté
    );

    setState(() => _isLoading = false);

    if (mounted) {
      if (result['success']) {
        _showSuccessSnackBar(result['message']);
        _loadPasswordHistory(); // Recharger l'historique
      } else {
        _showErrorSnackBar(result['message']);
      }
    }
  }

  /// 🔒 Bloquer le compte
  Future<void> _lockAccount() async {
    final reason = await _showLockReasonDialog();
    if (reason == null) return;

    setState(() => _isLoading = true);

    final result = await PasswordManagementService.lockUserAccount(
      userId: widget.user['uid'] ?? widget.user['id'],
      userEmail: widget.user['email'],
      adminId: 'super_admin', // TODO: Récupérer l'ID de l'admin connecté
      reason: reason,
    );

    setState(() => _isLoading = false);

    if (mounted) {
      if (result['success']) {
        _showSuccessSnackBar(result['message']);
        _loadPasswordHistory(); // Recharger l'historique
      } else {
        _showErrorSnackBar(result['message']);
      }
    }
  }

  /// 📝 Dialog pour saisir la raison du blocage
  Future<String?> _showLockReasonDialog() async {
    final controller = TextEditingController();

    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Bloquer le compte'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ({widget.user['displayName'] ?? widget.user['email']} ?',
            ),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              decoration: const InputDecoration(
                labelText: 'Raison du blocage',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              if (controller.text.trim().isNotEmpty) {
                Navigator.pop(context, controller.text.trim());
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Bloquer'),
          ),
        ],
      ),
    );
  }

  /// 🔄 Actions du dialog
  Widget _buildActions() {
    return Container(
      padding: ,
      ),
      child: Row(
        children: [
          if (_isLoading)
            CircularProgressIndicator()
          else
            ,
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
          const SizedBox(width: 12),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Ouvrir le dialog d'édition
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: ModernTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Modifier'),
          ),
        ],
      ),
    );
  }

  /// 📅 Formater une date
  String _formatDate(dynamic date) {
    if (date == null) return 'Non défini';

    DateTime dateTime;
    if (date is Timestamp) {
      dateTime = date.toDate();
    } else if (date is DateTime) {
      dateTime = date;
    } else {
      return 'Format invalide';
    }

    return '${dateTime.day.toString().padLeft(2, '0')}/'
           '${dateTime.month.toString().padLeft(2, '0')}/'
           '${dateTime.year} à '
           '${dateTime.hour.toString().padLeft(2, '0')}:'
           '${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 🏷️ Obtenir le label de l'origine
  String _getOriginLabel(String origine) {
    switch (origine) {
      case 'creation_super_admin':
        return 'Création Super Admin';
      case 'demande_professionnelle':
        return 'Demande Professionnelle';
      case 'import_manuel':
        return 'Import Manuel';
      case 'test':
        return 'Test
