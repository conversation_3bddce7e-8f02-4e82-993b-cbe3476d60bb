import 'package:flutter/material.dart';
import '../../services/agent_management_service.dart';
import '../../common/widgets/simple_credentials_dialog.dart';

/// 👥 Onglet de gestion des agents pour Admin Agence
class AgenceAgentsTab extends StatefulWidget {
  final String agenceId;
  final String agenceNom;
  final String compagnieId;

   ) : super(key: key);

  @override
  State<AgenceAgentsTab> createState() => _AgenceAgentsTabState();
}

class _AgenceAgentsTabState extends State<AgenceAgentsTab> {
  List<Map<String, dynamic>> _agents = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAgents();
  }

  /// 📋 Charger les agents de l'agence
  Future<void> _loadAgents() async {
    setState(() => _isLoading = true);
    
    try {
      final agents = await AgentManagementService.getAgentsByAgence(widget.agenceId);
      setState(() {
        _agents = agents;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: (e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  /// ➕ Créer un nouvel agent
  Future<void> _createAgent() async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => _CreateAgentDialog(
        compagnieId: widget.compagnieId,
        agenceId: widget.agenceId,
        agenceNom: widget.agenceNom,
      ),
    );

    if (result != null && result['success'] == true) {
      _loadAgents();
      if (mounted) {
        // Afficher les identifiants avec le dialog moderne
        showSimpleCredentialsDialog(
          context: context,
          title: '🎉 Agent créé avec succès',
          primaryColor: Colors.purple,
          credentials: {
            'nom': result['displayCredentials']['nom'],
            'email': result['displayCredentials']['email'],
            'password': result['displayCredentials']['password'],
            'agence': result['displayCredentials']['agence'],
            'role': 'Agent',
          },
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // En-tête
          Container(
            padding: ,
            ),
            child: Row(
              children: [
                ,
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ,
                      ),
                      ({_agents.length} agent(s) - ${widget.agenceNom}',
                        style: ,
                      ),
                    ],
                  ),
                ),
                ElevatedButton.const Icon(
                  onPressed: _createAgent,
                  icon: const Icon(Icons.info),
                  label: const Text('Nouvel Agent'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: Colors.green.shade600,
                  ),
                ),
              ],
            ),
          ),

          // Liste des agents
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _agents.isEmpty
                    ? _buildEmptyState()
                    : _buildAgentsList(),
          ),
        ],
      ),
    );
  }

  /// 📋 Liste des agents
  Widget _buildAgentsList() {
    return RefreshIndicator(
      onRefresh: _loadAgents,
      child: ListView.builder(
        padding:  {
          final agent = _agents[index];
          return _buildAgentCard(agent);
        },
      ),
    );
  }

  /// 👤 Carte d'agent
  Widget _buildAgentCard(Map<String, dynamic> agent) {
    final stats = agent['stats'] as Map<String, dynamic>? ?? {};
    
    return Card(
      margin: ),
      child: (1),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ({agent['nom']}',
                        style: ,
                      ),
                      ,
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: ,
                  ),
                  child: ,
                  ),
                ),
                PopupMenuButton(
                  itemBuilder: (context) => [
                    ,
                          const SizedBox(width: 8),
                          const Text('Modifier'),
                        ],
                      ),
                    ),
                    ,
                          const SizedBox(width: 8),
                          const Text('Reset mot de passe'),
                        ],
                      ),
                    ),
                    ,
                          const SizedBox(width: 8),
                          ),
                        ],
                      ),
                    ),
                  ],
                  onSelected: (value) => _handleAgentAction(value.toString(), agent),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // Informations de contact
            Row(
              children: [
                ,
                const SizedBox(width: 4),
                Expanded(child: const Text(agent['email'] ?? 'N/A')),
                const SizedBox(width: 16),
                ,
                const SizedBox(width: 4),
                const Text(agent['telephone'] ?? 'N/A'),
              ],
            ),
            const SizedBox(height: 12),
            
            // Statistiques
            Row(
              children: [
                _buildStatChip('Constats', stats['total_constats']?.toString() ?? '0', Colors.blue),
                const SizedBox(width: 8),
                _buildStatChip('Ce mois', stats['constats_ce_mois']?.toString() ?? '0', Colors.green),
                const SizedBox(width: 8),
                _buildStatChip('Score', stats['performance_score']?.toString() ?? '100', Colors.orange),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 📊 Chip de statistique
  Widget _buildStatChip(String label, String value, Color color) {
    return Container(
      padding: ,
        borderRadius: BorderRadius.circular(12),
      ),
      child: (value',
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 📭 État vide
  Widget _buildEmptyState() {
    return ,
          const SizedBox(height: 16),
          ,
          ),
          const SizedBox(height: 8),
          ,
          ),
          const SizedBox(height: 16),
          ElevatedButton.const Icon(
            onPressed: _createAgent,
            icon: const Icon(Icons.info),
            label: const Text('Créer un agent'),
          ),
        ],
      ),
    );
  }

  /// 🎯 Gérer les actions sur un agent
  void _handleAgentAction(String action, Map<String, dynamic> agent) {
    switch (action) {
      case 'edit':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: const Text('Modification bientôt disponible')),
        );
        break;
      case 'reset_password':
        _resetAgentPassword(agent);
        break;
      case 'deactivate':
        _confirmDeactivateAgent(agent);
        break;
    }
  }

  /// 🔐 Reset mot de passe
  void _resetAgentPassword(Map<String, dynamic> agent) async {
    final result = await AgentManagementService.resetAgentPassword(agent['id']);
    
    if (result['success'] && mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Mot de passe réinitialisé'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ({agent['nom']}'),
              ({agent['email']}'),
              ({result['newPassword']}'),
              const SizedBox(height: 8),
              ,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        ),
      );
    }
  }

  /// ⚠️ Confirmer la désactivation
  void _confirmDeactivateAgent(Map<String, dynamic> agent) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Désactiver l\'agent'),
        content: ({agent['nom']}" ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final result = await AgentManagementService.deactivateAgent(agent['id']);
              if (result['success']) {
                _loadAgents();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: const Text('Agent désactivé'), backgroundColor: Colors.orange),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: ),
          ),
        ],
      ),
    );
  }
}

/// 🏗️ Dialog de création d'agent pour Admin Agence
class _CreateAgentDialog extends StatefulWidget {
  final String compagnieId;
  final String agenceId;
  final String agenceNom;

  ;

  @override
  State<_CreateAgentDialog> createState() => _CreateAgentDialogState();
}

class _CreateAgentDialogState extends State<_CreateAgentDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nomController = TextEditingController();
  final _prenomController = TextEditingController();
  final _emailController = TextEditingController();
  final _telephoneController = TextEditingController();
  final _adresseController = TextEditingController();
  final _cinController = TextEditingController();
  final _specialiteController = TextEditingController();
  bool _isCreating = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: ({widget.agenceNom}'),
      content: ,
                        ),
                        validator: (value) => value?.isEmpty == true ? 'Prénom requis' : null,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _nomController,
                        decoration: ,
                        ),
                        validator: (value) => value?.isEmpty == true ? 'Nom requis' : null,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                
                TextFormField(
                  controller: _emailController,
                  decoration: ,
                  ),
                  validator: (value) {
                    if (value?.isEmpty == true) return 'Email requis';
                    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value!)) {
                      return 'Format email invalide';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                
                TextFormField(
                  controller: _telephoneController,
                  decoration: ,
                  ),
                  validator: (value) => value?.isEmpty == true ? 'Téléphone requis' : null,
                ),
                const SizedBox(height: 16),
                
                TextFormField(
                  controller: _specialiteController,
                  decoration: ,
                    hintText: 'Ex: Automobile, Habitation...',
                  ),
                ),
                const SizedBox(height: 16),
                
                TextFormField(
                  controller: _adresseController,
                  decoration: ,
                  ),
                ),
                const SizedBox(height: 16),
                
                TextFormField(
                  controller: _cinController,
                  decoration: ,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isCreating ? null : () => Navigator.of(context).pop(),
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: _isCreating ? null : _createAgent,
          child: _isCreating
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Créer'),
        ),
      ],
    );
  }

  Future<void> _createAgent() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isCreating = true);

    try {
      final result = await AgentManagementService.createAgent(
        compagnieId: widget.compagnieId,
        agenceId: widget.agenceId,
        nom: _nomController.text.trim(),
        prenom: _prenomController.text.trim(),
        email: _emailController.text.trim(),
        telephone: _telephoneController.text.trim(),
        adresse: _adresseController.text.trim(),
        cin: _cinController.text.trim(),
        specialite: _specialiteController.text.trim().isEmpty 
            ? 'Général' 
            : _specialiteController.text.trim(),
        createdBy: 'admin_agence',
      );

      if (mounted) {
        Navigator.of(context).pop(result);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: (e
