import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../core/theme/modern_theme.dart';
import '../../services/audit_logger_service.dart';

/// 🔍 Écran de consultation des logs d'audit
class AuditLogsScreen extends StatefulWidget {
  const Text(\;

  @override
  State<AuditLogsScreen> createState() => _AuditLogsScreenState();
}

class _AuditLogsScreenState extends State<AuditLogsScreen> {
  List<Map<String, dynamic>> _logs = [];
  bool _isLoading = true;
  String _selectedAction = 'Toutes';
  String _selectedLevel = 'Tous';
  DateTime? _startDate;
  DateTime? _endDate;
  DocumentSnapshot? _lastDocument;
  bool _hasMoreData = true;

  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _loadLogs();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// 📋 Charger les logs d'audit
  Future<void> _loadLogs({bool loadMore = false}) async {
    if (!loadMore) {
      setState(() {
        _isLoading = true;
        _logs.clear();
        _lastDocument = null;
        _hasMoreData = true;
      });
    }

    try {
      final logs = await AuditLoggerService.getLogs(
        action: _selectedAction == 'Toutes' ? null : _selectedAction,
        level: _selectedLevel == 'Tous' ? null : _selectedLevel,
        startDate: _startDate,
        endDate: _endDate,
        lastDocument: _lastDocument,
        limit: 20,
      );

      if (mounted) {
        setState(() {
          if (loadMore) {
            _logs.addAll(logs);
          } else {
            _logs = logs;
          }
          
          if (logs.isNotEmpty) {
            _lastDocument = logs.last['document'] as DocumentSnapshot?;
          }
          
          _hasMoreData = logs.length == 20;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        _showErrorSnackBar('Erreur lors du chargement des logs: $e');
      }
    }
  }

  /// 📜 Pagination automatique
  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoading && _hasMoreData) {
        _loadLogs(loadMore: true);
      }
    }
  }

  /// 🔄 Actualiser les logs
  Future<void> _refreshLogs() async {
    await _loadLogs();
  }

  /// ❌ Afficher message d'erreur
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text("Titre"),
        ),
        backgroundColor: ModernTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _refreshLogs,
            icon: const Icon(Icons.info),
            tooltip: 'Actualiser',
          ),
          IconButton(
            onPressed: _showFilters,
            icon: const Icon(Icons.info),
            tooltip: 'Filtres',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFiltersBar(),
          Expanded(
            child: _isLoading && _logs.isEmpty
                ? const Center(child: CircularProgressIndicator())
                : _logs.isEmpty
                    ? _buildEmptyState()
                    : _buildLogsList(),
          ),
        ],
      ),
    );
  }

  /// 🔍 Barre de filtres
  Widget _buildFiltersBar() {
    return Container(
      padding: ,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: ({_logs.length} logs trouvés',
              style: ,
            ),
          ),
          if (_selectedAction != 'Toutes' || _selectedLevel != 'Tous' || _startDate != null)
            TextButton.const Icon(
              onPressed: _clearFilters,
              icon: const Icon(Icons.info),
              label: const Text('Effacer filtres'),
              style: TextButton.styleFrom(
                foregroundColor: Colors.orange,
              ),
            ),
        ],
      ),
    );
  }

  /// 📋 Liste des logs
  Widget _buildLogsList() {
    return RefreshIndicator(
      onRefresh: _refreshLogs,
      child: ListView.builder(
        controller: _scrollController,
        padding: ,
        itemBuilder: (context, index) {
          if (index == _logs.length) {
            return (1),
              ),
            );
          }

          final log = _logs[index];
          return _buildLogCard(log);
        },
      ),
    );
  }

  /// 📄 Carte de log
  Widget _buildLogCard(Map<String, dynamic> log) {
    final action = log['action'] ?? 'Contenu';
    final level = log['level'] ?? 'Contenu';
    final timestamp = log['timestamp'] as Timestamp?;
    final performedBy = log['performedBy'] ?? 'Contenu';
    final targetUserEmail = log['targetUserEmail'] ?? 'Contenu';
    final details = log['details'] as Map<String, dynamic>? ?? {};

    return Container(
      margin: ,
        border: Border.all(
          color: _getLevelColor(level).withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: (1),
                  decoration: BoxDecoration(
                    color: _getLevelColor(level).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ,
                    style: TextStyle(
                      color: _getLevelColor(level),
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: const Text(
                    _getActionLabel(action),
                    style: ,
                  ),
                ),
                if (timestamp != null)
                  const Text(
                    _formatTimestamp(timestamp),
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                    ),
                  ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            Row(
              children: [
                ,
                const SizedBox(width: 4),
                (performedBy',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12,
                  ),
                ),
                if (targetUserEmail.isNotEmpty) ...[
                  const SizedBox(width: 16),
                  ,
                  const SizedBox(width: 4),
                  (targetUserEmail',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                    ),
                  ),
                ],
              ],
            ),
            
            if (details.isNotEmpty) ...[
              const SizedBox(height: 8),
              Container(
                padding: ,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ,
                    ),
                    const SizedBox(height: 4),
                    ...details.entries.map((entry) => (1),
                      ),
                    )),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 📭 État vide
  Widget _buildEmptyState() {
    return ,
          const SizedBox(height: 16),
          ,
          ),
          const SizedBox(height: 8),
          ,
          ),
        ],
      ),
    );
  }

  /// 🎨 Couleur selon le niveau
  Color _getLevelColor(String level) {
    switch (level.toLowerCase()) {
      case 'critical':
        return Colors.red;
      case 'high':
        return Colors.orange;
      case 'medium':
        return Colors.blue;
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  /// 🏷️ Label de l'action
  String _getActionLabel(String action) {
    switch (action) {
      case 'user_created':
        return 'Utilisateur créé';
      case 'user_updated':
        return 'Utilisateur modifié';
      case 'user_deleted':
        return 'Utilisateur supprimé';
      case 'password_reset':
        return 'Mot de passe réinitialisé';
      case 'account_locked':
        return 'Compte bloqué';
      case 'account_unlocked':
        return 'Compte débloqué';
      case 'login_success':
        return 'Connexion réussie';
      case 'login_failed':
        return 'Échec de connexion';
      case 'bulk_operation':
        return 'Opération en lot';
      default:
        return action.replaceAll('_', ' ').toUpperCase();
    }
  }

  /// 📅 Formater le timestamp
  String _formatTimestamp(Timestamp timestamp) {
    final date = timestamp.toDate();
    return '${date.day.toString().padLeft(2, '0')}/'
           '${date.month.toString().padLeft(2, '0')}/'
           '${date.year} '
           '${date.hour.toString().padLeft(2, '0')}:'
           '${date.minute.toString().padLeft(2, '0')}';
  }

  /// 🔍 Afficher les filtres
  void _showFilters() {
    // TODO: Implémenter le dialog de filtres
    _showErrorSnackBar('Dialog de filtres en cours de développement');
  }

  /// 🧹 Effacer les filtres
  void _clearFilters() {
    setState(() {
      _selectedAction = 'Toutes';
      _selectedLevel = 'Tous
