{"dart.analysisExcludedFolders": [], "dart.showIgnoreQuickFixes": false, "dart.showLintNames": false, "dart.showTodos": false, "dart.analysisServerFolding": false, "dart.previewFlutterUiGuides": false, "dart.previewFlutterUiGuidesCustomTracking": false, "problems.showCurrentInStatus": false, "problems.decorations.enabled": false, "editor.showUnused": false, "editor.showDeprecated": false, "dart.lineLength": 120, "dart.analysisServerArgs": ["--disable-server-feature-completion", "--disable-server-feature-search"], "dart.flutterSdkPath": null, "dart.checkForSdkUpdates": false, "dart.warnWhenEditingFilesOutsideWorkspace": false, "dart.warnWhenEditingFilesInPubCache": false, "editor.codeActionsOnSave": {"source.fixAll": "never", "source.organizeImports": "never"}, "dart.enableSdkFormatter": false, "dart.enableCompletionCommitCharacters": false, "dart.includeDependenciesInWorkspaceSymbols": false, "dart.showMainCodeLens": false, "dart.showTestCodeLens": false, "dart.showDependencyTreeCodeLens": false, "dart.showSkippedTests": false, "dart.promptToGetPackages": false, "dart.promptToRunIfErrors": false, "dart.allowAnalytics": false, "dart.reportAnalyzerErrors": false, "dart.showInspectorNotificationsForWidgetErrors": false, "dart.maxLogLineLength": 1000, "dart.extensionLogFile": null, "dart.analyzerLogFile": null, "dart.flutterLogFile": null, "dart.observatoryLogFile": null, "dart.debugSdkLibraries": false, "dart.debugExternalLibraries": false, "dart.evaluateGettersInDebugViews": false, "dart.evaluateToStringInDebugViews": false, "dart.showDartDeveloperLogs": false, "dart.showWebDeveloperLogs": false, "dart.flutterCreateAndroidLanguage": "kotlin", "dart.flutterCreateIOSLanguage": "swift", "dart.flutterCreateOrganization": "com.example", "dart.insertArgumentPlaceholders": false, "dart.enableSnippets": false, "dart.completeFunctionCalls": false, "dart.runPubGetOnPubspecChanges": false, "dart.automaticCommentSlashes": false, "dart.closingLabels": false, "dart.renameFilesWithClasses": false, "dart.updateImportsOnRename": false, "dart.normalizeWindowsDriveLetters": false, "dart.allowTestsOutsideTestFolder": false, "dart.showDartPadSampleCodeLens": false, "dart.triggerSignatureHelpAutomatically": false, "dart.documentation": "none", "dart.showFixAllQuickFixes": false, "dart.showAssistQuickFixes": false, "dart.showRefactorQuickFixes": false, "dart.showSourceActionQuickFixes": false, "dart.showOtherQuickFixes": false}