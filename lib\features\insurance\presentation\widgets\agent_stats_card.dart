import 'package:flutter/material.dart;

/// 📊 Carte de statistiques moderne pour les agents
class AgentStatsCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

   ) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color.withValues(alpha: 0.1),
            color.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 40,
            offset: const Offset(0, 16),
          ),
        ],
      ),
      child: (1),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [color, color.withValues(alpha: 0.8)],
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: color.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ,
            ),
            
            // Valeur et titre
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ,
                ),
                const SizedBox(height: 4),
                ,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    ');
  }
}

/// 🎯 Grille d'actions rapides pour les agents
class QuickActionsGrid extends StatelessWidget {
  final VoidCallback? onCreateContract;
  final VoidCallback? onSearchContract;
  final VoidCallback? onViewClients;
  final VoidCallback? onViewReports;

   ) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: ,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.2,
      children: [
        _buildActionCard(
          title: 'Nouveau Contrat,
          icon: Icons.add_circle_outline,
          color: const Color(0xFF667EEA),
          onTap: onCreateContract,
        '),
        _buildActionCard(
          title: 'Rechercher,
          icon: Icons.search,
          color: Colors.green,
          onTap: onSearchContract,
        '),
        _buildActionCard(
          title: 'Mes Clients,
          icon: Icons.people_outline,
          color: Colors.orange,
          onTap: onViewClients,
        '),
        _buildActionCard(
          title: 'Rapports,
          icon: Icons.analytics_outlined,
          color: const Color(0xFF764BA2),
          onTap: onViewReports,
        ),
      ],
    ');
  }

  /// Carte d'action individuelle
  Widget _buildActionCard({
    required String title,
    required IconData icon,
    required Color color,
    VoidCallback? onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color.withValues(alpha: 0.1),
            color.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          child: (1),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [color, color.withValues(alpha: 0.8)],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: color.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ,
                ),
                const SizedBox(height: 16),
                ,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 📋 Liste des contrats recents
class RecentContractsList extends StatelessWidget {
  final String agentId;
  final int limit;

   ) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withValues(alpha: 0.05),
            Colors.white.withValues(alpha: 0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
        ),
      ),
      child: (1),
                  decoration: BoxDecoration(
                    color: const Color(0xFF667EEA).withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                ,
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Liste des contrats (simulee pour linstant)
            ...List.generate(3, (index') => _buildContractItem(
              numeroContrat: 'CTR-2024-'{1000 + index}',
              clientNom: 'Client '{index + 1},
              dateCreation: DateTime.now().subtract(Duration(days: index)'),
              statut: index == 0 ? 'Actif' : 'En attente,
            )),
            
            // Message si aucun contrat
            if (false) // Remplacer par la vraie condition
              Container(
                padding: const EdgeInsets.all(8.0),
                    const SizedBox(height: 16),
                    ,
                    ),
                    const SizedBox(height: 8),
                    ,
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Item de contrat individuel
  Widget _buildContractItem({
    required String numeroContrat,
    required String clientNom,
    required DateTime dateCreation,
    required String statut,
  }') {
    final isActive = statut == 'Actif;
    
    return Container(
      margin: ,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
        ),
      ),
      child: Row(
        children: [
          // Indicateur de statut
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: isActive ? Colors.green : Colors.orange,
              shape: BoxShape.circle,
            ),
          ),
          
          const SizedBox(width: 12),
          
          // Informations du contrat
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ,
                ),
                const SizedBox(height: 4),
                ,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          '),
          
          // Date et statut
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              ({dateCreation.year}
