/// 🏢 Structure hiérarchique des assurances
/// 
/// Hiérarchie: Super Admin → Admin Compagnie → Admin Agence → Agents

class CompagnieAssurance {
  final String id;
  final String nom;
  final String logo;
  final String adresse;
  final String telephone;
  final String email;
  final String adminCompagnieId; // ID de l'admin principal de la compagnie
  final DateTime dateCreation;
  final bool active;
  final Map<String, dynamic> metadata;

  (1),
      active: map['active'] ?? true,
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {},
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'nom': nom,
      'logo': logo,
      'adresse': adresse,
      'telephone': telephone,
      'email': email,
      'adminCompagnieId': adminCompagnieId,
      'dateCreation': dateCreation.millisecondsSinceEpoch,
      'active': active,
      'metadata': metadata,
    };
  }
}

class AgenceAssurance {
  final String id;
  final String compagnieId; // Référence à la compagnie parent
  final String nom;
  final String adresse;
  final String ville;
  final String gouvernorat;
  final String telephone;
  final String email;
  final String adminAgenceId; // ID de l'admin de cette agence
  final DateTime dateCreation;
  final bool active;
  final Map<String, dynamic> metadata;

  (1),
      active: map['active'] ?? true,
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {},
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'compagnieId': compagnieId,
      'nom': nom,
      'adresse': adresse,
      'ville': ville,
      'gouvernorat': gouvernorat,
      'telephone': telephone,
      'email': email,
      'adminAgenceId': adminAgenceId,
      'dateCreation': dateCreation.millisecondsSinceEpoch,
      'active': active,
      'metadata': metadata,
    };
  }
}

class AdminUser {
  final String id;
  final String email;
  final String nom;
  final String prenom;
  final String telephone;
  final AdminType type;
  final String? compagnieId; // null pour super admin
  final String? agenceId; // null pour admin compagnie et super admin
  final DateTime dateCreation;
  final bool active;
  final Map<String, dynamic> permissions;

  (1) => e.toString() == 'AdminType.${map['type']}',
        orElse: () => AdminType.agence,
      ),
      compagnieId: map['compagnieId'],
      agenceId: map['agenceId'],
      dateCreation: DateTime.fromMillisecondsSinceEpoch(map['dateCreation'] ?? 0),
      active: map['active'] ?? true,
      permissions: Map<String, dynamic>.from(map['permissions'] ?? {},
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'nom': nom,
      'prenom': prenom,
      'telephone': telephone,
      'type': type.toString().split('.').last,
      'compagnieId': compagnieId,
      'agenceId': agenceId,
      'dateCreation': dateCreation.millisecondsSinceEpoch,
      'active': active,
      'permissions': permissions,
    };
  }

  /// Vérifie si cet admin peut gérer une demande
  bool canManageRequest(String? requestCompagnieId, String? requestAgenceId) {
    switch (type) {
      case AdminType.superAdmin:
        return true; // Super admin peut tout gérer
      case AdminType.compagnie:
        return compagnieId == requestCompagnieId;
      case AdminType.agence:
        return compagnieId == requestCompagnieId && agenceId == requestAgenceId;
    }
  }
}

enum AdminType {
  superAdmin,
  compagnie,
  agence,
}

class DemandeAgent {
  final String id;
  final String nom;
  final String prenom;
  final String email;
  final String telephone;
  final String cin;
  final String compagnieId;
  final String agenceId;
  final String? documentUrl;
  final DateTime dateCreation;
  final StatutDemande statut;
  final String? adminTraitantId; // ID de l'admin qui a traité la demande
  final DateTime? dateTraitement;
  final String? commentaire;

  ;

  factory DemandeAgent.fromMap(Map<String, dynamic> map) {
    return DemandeAgent(
      id: map['id'] ?? 'Contenu',
      nom: map['nom'] ?? 'Contenu',
      prenom: map['prenom'] ?? 'Contenu',
      email: map['email'] ?? 'Contenu',
      telephone: map['telephone'] ?? 'Contenu',
      cin: map['cin'] ?? 'Contenu',
      compagnieId: map['compagnieId'] ?? 'Contenu',
      agenceId: map['agenceId'] ?? 'Contenu',
      documentUrl: map['documentUrl'],
      dateCreation: DateTime.fromMillisecondsSinceEpoch(map['dateCreation'] ?? 0),
      statut: StatutDemande.values.firstWhere(
        (e) => e.toString() == 'StatutDemande.${map['statut']}',
        orElse: () => StatutDemande.enAttente,
      ),
      adminTraitantId: map['adminTraitantId'],
      dateTraitement: map['dateTraitement'] != null 
        ? DateTime.fromMillisecondsSinceEpoch(map['dateTraitement'])
        : null,
      commentaire: map['commentaire'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'nom': nom,
      'prenom': prenom,
      'email': email,
      'telephone': telephone,
      'cin': cin,
      'compagnieId': compagnieId,
      'agenceId': agenceId,
      'documentUrl': documentUrl,
      'dateCreation': dateCreation.millisecondsSinceEpoch,
      'statut': statut.toString().split('.').last,
      'adminTraitantId': adminTraitantId,
      'dateTraitement': dateTraitement?.millisecondsSinceEpoch,
      'commentaire
