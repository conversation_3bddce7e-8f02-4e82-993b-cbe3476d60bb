import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';

import '../../../../core/services/compagnie_agence_service.dart';
import '../../../../shared/models/compagnie_assurance_model.dart';
import '../../../../shared/models/agence_assurance_model.dart';

/// 🏢 État pour les compagnies et agences
class CompagnieAgenceState {
  final List<CompagnieAssuranceModel> compagnies;
  final List<AgenceAssuranceModel> agences;
  final bool isLoadingCompagnies;
  final bool isLoadingAgences;
  final String? error;
  final CompagnieAssuranceModel? selectedCompagnie;
  final AgenceAssuranceModel? selectedAgence;

  ;

  CompagnieAgenceState copyWith({
    List<CompagnieAssuranceModel>? compagnies,
    List<AgenceAssuranceModel>? agences,
    bool? isLoadingCompagnies,
    bool? isLoadingAgences,
    String? error,
    CompagnieAssuranceModel? selectedCompagnie,
    AgenceAssuranceModel? selectedAgence,
  }) {
    return CompagnieAgenceState(
      compagnies: compagnies ?? this.compagnies,
      agences: agences ?? this.agences,
      isLoadingCompagnies: isLoadingCompagnies ?? this.isLoadingCompagnies,
      isLoadingAgences: isLoadingAgences ?? this.isLoadingAgences,
      error: error,
      selectedCompagnie: selectedCompagnie ?? this.selectedCompagnie,
      selectedAgence: selectedAgence ?? this.selectedAgence,
    );
  }
}

/// 🏢 Notifier pour gérer les compagnies et agences
class CompagnieAgenceNotifier extends StateNotifier<CompagnieAgenceState> {
  CompagnieAgenceNotifier() : super(const CompagnieAgenceState());

  /// 📋 Charger toutes les compagnies
  Future<void> loadCompagnies() async {
    state = state.copyWith(isLoadingCompagnies: true, error: null);
    
    try {
      final compagnies = await CompagnieAgenceService.getCompagniesActives();
      state = state.copyWith(
        compagnies: compagnies,
        isLoadingCompagnies: false,
      );
    } catch (e) {
      debugPrint('Erreur lors du chargement des compagnies: $e');
      state = state.copyWith(
        isLoadingCompagnies: false,
        error: 'Erreur lors du chargement des compagnies',
      );
    }
  }

  /// 🏬 Charger les agences d'une compagnie
  Future<void> loadAgencesByCompagnie(String compagnieId) async {
    state = state.copyWith(isLoadingAgences: true, error: null);
    
    try {
      final agences = await CompagnieAgenceService.getAgencesByCompagnie(compagnieId);
      state = state.copyWith(
        agences: agences,
        isLoadingAgences: false,
      );
    } catch (e) {
      debugPrint('Erreur lors du chargement des agences: $e');
      state = state.copyWith(
        isLoadingAgences: false,
        error: 'Erreur lors du chargement des agences',
      );
    }
  }

  /// 🏢 Sélectionner une compagnie
  void selectCompagnie(CompagnieAssuranceModel? compagnie) {
    state = state.copyWith(
      selectedCompagnie: compagnie,
      selectedAgence: null, // Reset agence when compagnie changes
      agences: [], // Clear agences list
    );
    
    // Charger les agences de la nouvelle compagnie
    if (compagnie != null) {
      loadAgencesByCompagnie(compagnie.id);
    }
  }

  /// 🏬 Sélectionner une agence
  void selectAgence(AgenceAssuranceModel? agence) {
    state = state.copyWith(selectedAgence: agence);
  }

  /// 🔄 Reset des sélections
  void resetSelections() {
    state = state.copyWith(
      selectedCompagnie: null,
      selectedAgence: null,
      agences: [],
      error: null,
    );
  }

  /// 🔍 Rechercher une compagnie par ID
  CompagnieAssuranceModel? getCompagnieById(String id) {
    try {
      return state.compagnies.firstWhere((c) => c.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 🔍 Rechercher une agence par ID
  AgenceAssuranceModel? getAgenceById(String id) {
    try {
      return state.agences.firstWhere((a) => a.id == id);
    } catch (e) {
      return null;
    }
  }
}

/// 🏢 Provider pour les compagnies et agences
final compagnieAgenceProvider = StateNotifierProvider<CompagnieAgenceNotifier, CompagnieAgenceState>(
  (ref) => CompagnieAgenceNotifier(),
);

/// 📋 Provider pour charger les compagnies au démarrage
final compagniesProvider = FutureProvider<List<CompagnieAssuranceModel>>((ref) async {
  return await CompagnieAgenceService.getCompagniesActives();
});

/// 🏬 Provider pour charger les agences d'une compagnie
final agencesProvider = FutureProvider.family<List<AgenceAssuranceModel>, String>((ref, compagnieId) async {
  return await CompagnieAgenceService.getAgencesByCompagnie(compagnieId);
});

/// 🔍 Provider pour obtenir une compagnie par ID
final compagnieByIdProvider = FutureProvider.family<CompagnieAssuranceModel?, String>((ref, compagnieId) async {
  return await CompagnieAgenceService.getCompagnieById(compagnieId);
});

/// 🔍 Provider pour obtenir une agence par ID
final agenceByIdProvider = FutureProvider.family<AgenceAssuranceModel?, String>((ref, agenceId) async {
  return await CompagnieAgenceService.getAgenceById(agenceId);
});

/// 📊 Provider pour les statistiques d
