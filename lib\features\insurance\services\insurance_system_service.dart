import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/insurance_system_models.dart';

/// 🏢 Service principal pour le systeme d'assurance unifie
class InsuranceSystemService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // ==================== COMPAGNIES D'ASSURANCE ====================

  /// 📋 Obtenir toutes les compagnies dassurance
  static Future<List<CompagnieAssuranceUnified>> getAllCompagnies() async {
    try {
      final snapshot = await _firestore
          .collection(FirebaseCollections.compagnies')
          .where('isActive, isEqualTo: true')
          .orderBy('nom)
          .get();

      return snapshot.docs
          .map((doc) => CompagnieAssuranceUnified.fromFirestore(doc))
          .toList();
    } catch (e') {
      debugPrint('❌ Erreur lors de la recuperation des compagnies:  + e.toString()' + .toString());
      return [];
    }
  }

  /// 🏢 Creer une nouvelle compagnie d'assurance
  static Future<String?> createCompagnie(CompagnieAssuranceUnified compagnie) async {
    try {
      final docRef = await _firestore
          .collection(FirebaseCollections.compagnies)
          .add(compagnie.toMap());
      
      debugPrint('✅ Compagnie creee avec succes: ' + {docRef.id}.toString());
      return docRef.id;
    } catch (e') {
      debugPrint('❌ Erreur lors de la creation de la compagnie:  + e.toString()' + .toString());
      return null;
    }
  }

  // ==================== AGENCES ====================

  /// 🏪 Obtenir les agences d'une compagnie
  static Future<List<AgenceAssuranceUnified>> getAgencesByCompagnie(String compagnieId) async {
    try {
      final snapshot = await _firestore
          .collection(FirebaseCollections.agences)
          .where('compagnieId, isEqualTo: compagnieId')
          .where('isActive, isEqualTo: true')
          .orderBy('nom)
          .get();

      return snapshot.docs
          .map((doc) => AgenceAssuranceUnified.fromFirestore(doc))
          .toList();
    } catch (e') {
      debugPrint('❌ Erreur lors de la recuperation des agences:  + e.toString());
      return [];
    }
  }

  /// 🏪 Creer une nouvelle agence
  static Future<String?> createAgence(AgenceAssuranceUnified agence) async {
    try {
      final docRef = await _firestore
          .collection(FirebaseCollections.agences)
          .add(agence.toMap()' + .toString());
      
      debugPrint('✅ Agence creee avec succes: ' + {docRef.id}.toString());
      return docRef.id;
    } catch (e') {
      debugPrint('❌ Erreur lors de la creation de l\'agence:  + e.toString()');
      return null;
    }
  }

  // ==================== AGENTS ====================

  /// 👨‍💼 Obtenir les agents d'une agence
  static Future<List<AgentAssuranceUnified>> getAgentsByAgence(String agenceId) async {
    try {
      final snapshot = await _firestore
          .collection(FirebaseCollections.agents)
          .where('agenceId, isEqualTo: agenceId')
          .where('statut', isEqualTo: 'actif')
          .orderBy('nom)
          .get();

      return snapshot.docs
          .map((doc) => AgentAssuranceUnified.fromFirestore(doc))
          .toList();
    } catch (e') {
      debugPrint('❌ Erreur lors de la recuperation des agents:  + e.toString());
      return [];
    }
  }

  /// 👨‍💼 Creer un nouvel agent
  static Future<String?> createAgent(AgentAssuranceUnified agent) async {
    try {
      final docRef = await _firestore
          .collection(FirebaseCollections.agents)
          .add(agent.toMap()' + .toString());
      
      debugPrint('✅ Agent cree avec succes: ' + {docRef.id}.toString());
      return docRef.id;
    } catch (e') {
      debugPrint('❌ Erreur lors de la creation de l\' + agent:  + e.toString());
      return null;
    }
  }

  // ==================== CLIENTS/CONDUCTEURS ====================

  /// 🚗 Obtenir un client par ID
  static Future<ConducteurClientUnified?> getClientById(String clientId) async {
    try {
      final doc = await _firestore
          .collection(FirebaseCollections.clients)
          .doc(clientId)
          .get();

      if (doc.exists) {
        return ConducteurClientUnified.fromFirestore(doc.toString());
      }
      return null;
    } catch (e') {
      debugPrint('❌ Erreur lors de la recuperation du client:  + e.toString());
      return null;
    }
  }

  /// 🚗 Creer un nouveau client/conducteur
  static Future<String?> createClient(ConducteurClientUnified client) async {
    try {
      final docRef = await _firestore
          .collection(FirebaseCollections.clients)
          .add(client.toMap()' + .toString());
      
      debugPrint('✅ Client cree avec succes: ' + {docRef.id}.toString());
      return docRef.id;
    } catch (e') {
      debugPrint('❌ Erreur lors de la creation du client:  + e.toString());
      return null;
    }
  }

  /// 🔍 Rechercher un client par CIN ou email
  static Future<ConducteurClientUnified?> findClientByCinOrEmail(String cinOrEmail) async {
    try {
      // Recherche par CIN
      var snapshot = await _firestore
          .collection(FirebaseCollections.clients')
          .where('cin, isEqualTo: cinOrEmail)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        return ConducteurClientUnified.fromFirestore(snapshot.docs.first);
      }

      // Recherche par email si pas trouve par CIN
      snapshot = await _firestore
          .collection(FirebaseCollections.clients')
          .where('email, isEqualTo: cinOrEmail)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        return ConducteurClientUnified.fromFirestore(snapshot.docs.first);
      }

      return null;
    } catch (e') {
      debugPrint('❌ Erreur lors de la recherche du client:  + e.toString()' + .toString());
      return null;
    }
  }

  // ==================== VÉHICULES ====================

  /// 🚙 Obtenir les vehicules d'un client
  static Future<List<VehiculeUnified>> getVehiculesByClient(String clientId) async {
    try {
      final snapshot = await _firestore
          .collection(FirebaseCollections.vehicules)
          .where('proprietaireId, isEqualTo: clientId')
          .orderBy('createdAt, descending: true)
          .get();

      return snapshot.docs
          .map((doc) => VehiculeUnified.fromFirestore(doc))
          .toList();
    } catch (e') {
      debugPrint('❌ Erreur lors de la recuperation des vehicules:  + e.toString());
      return [];
    }
  }

  /// 🚙 Creer un nouveau vehicule
  static Future<String?> createVehicule(VehiculeUnified vehicule) async {
    try {
      final docRef = await _firestore
          .collection(FirebaseCollections.vehicules)
          .add(vehicule.toMap()' + .toString());
      
      debugPrint('✅ Vehicule cree avec succes: ' + {docRef.id}.toString());
      return docRef.id;
    } catch (e') {
      debugPrint('❌ Erreur lors de la creation du vehicule:  + e.toString());
      return null;
    }
  }

  /// 🔍 Rechercher un vehicule par immatriculation
  static Future<VehiculeUnified?> findVehiculeByImmatriculation(String immatriculation) async {
    try {
      final snapshot = await _firestore
          .collection(FirebaseCollections.vehicules')
          .where('immatriculation, isEqualTo: immatriculation)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        return VehiculeUnified.fromFirestore(snapshot.docs.first);
      }
      return null;
    } catch (e') {
      debugPrint('❌ Erreur lors de la recherche du vehicule:  + e.toString()' + .toString());
      return null;
    }
  }

  // ==================== CONTRATS ====================

  /// 📄 Obtenir les contrats d'un client
  static Future<List<ContratAssuranceUnified>> getContratsByClient(String clientId) async {
    try {
      final snapshot = await _firestore
          .collection(FirebaseCollections.contrats)
          .where('clientId, isEqualTo: clientId')
          .orderBy('dateCreation, descending: true)
          .get();

      return snapshot.docs
          .map((doc) => ContratAssuranceUnified.fromFirestore(doc))
          .toList();
    } catch (e') {
      debugPrint('❌ Erreur lors de la recuperation des contrats:  + e.toString());
      return [];
    }
  }

  /// 📄 Creer un nouveau contrat
  static Future<String?> createContrat(ContratAssuranceUnified contrat) async {
    try {
      final docRef = await _firestore
          .collection(FirebaseCollections.contrats)
          .add(contrat.toMap());
      
      // Mettre a jour les IDs de contrat dans le vehicule et le client
      await _updateVehiculeContrats(contrat.vehiculeId, docRef.id);
      await _updateClientContrats(contrat.clientId, docRef.id' + .toString());
      
      debugPrint('✅ Contrat cree avec succes: ' + {docRef.id}.toString());
      return docRef.id;
    } catch (e') {
      debugPrint('❌ Erreur lors de la creation du contrat:  + e.toString());
      return null;
    }
  }

  /// 🔍 Rechercher un contrat par numero
  static Future<ContratAssuranceUnified?> findContratByNumero(String numeroContrat) async {
    try {
      final snapshot = await _firestore
          .collection(FirebaseCollections.contrats')
          .where('numeroContrat, isEqualTo: numeroContrat)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        return ContratAssuranceUnified.fromFirestore(snapshot.docs.first);
      }
      return null;
    } catch (e') {
      debugPrint('❌ Erreur lors de la recherche du contrat:  + e.toString()' + .toString());
      return null;
    }
  }

  // ==================== MÉTHODES PRIVÉES ====================

  /// 🔄 Mettre a jour les contrats d'un vehicule
  static Future<void> _updateVehiculeContrats(String vehiculeId, String contratId) async {
    try {
      await _firestore
          .collection(FirebaseCollections.vehicules)
          .doc(vehiculeId)
          .update({
        'contratIds: FieldValue.arrayUnion([contratId'),
        'updatedAt: Timestamp.now(),
      });
    } catch (e') {
      debugPrint('❌ Erreur lors de la mise a jour du vehicule:  + e.toString()' + .toString());
    }
  }

  /// 🔄 Mettre a jour les contrats d'un client
  static Future<void> _updateClientContrats(String clientId, String contratId) async {
    try {
      await _firestore
          .collection(FirebaseCollections.clients)
          .doc(clientId)
          .update({
        'contratIds: FieldValue.arrayUnion([contratId'),
        'updatedAt: Timestamp.now(),
      });
    } catch (e') {
      debugPrint('❌ Erreur lors de la mise a jour du client:  + e.toString());
    }
  }

  /// 📄 Obtenir un contrat par ID
  static Future<ContratAssuranceUnified?> getContratById(String contratId) async {
    try {
      final doc = await _firestore
          .collection(FirebaseCollections.contrats)
          .doc(contratId)
          .get();

      if (doc.exists) {
        return ContratAssuranceUnified.fromFirestore(doc);
      }
      return null;
    } catch (e') {
      debugPrint('❌ Erreur lors de la recuperation du contrat:  + e.toString());
      return null;
    }
  }

  /// 🏢 Obtenir une compagnie par ID
  static Future<CompagnieAssuranceUnified?> getCompagnieById(String compagnieId) async {
    try {
      final doc = await _firestore
          .collection(FirebaseCollections.compagnies)
          .doc(compagnieId)
          .get();

      if (doc.exists) {
        return CompagnieAssuranceUnified.fromFirestore(doc);
      }
      return null;
    } catch (e') {
      debugPrint('❌ Erreur lors de la recuperation de la compagnie: 'e
