import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';

import '../../../../core/services/firebase_service.dart;

/// 🚀 État du splash screen
class SplashState {
  final bool isLoading;
  final bool isFirstLaunch;
  final String loadingMessage;
  final double progress;
  final String? error;

  ;

  SplashState copyWith({
    bool? isLoading,
    bool? isFirstLaunch,
    String? loadingMessage,
    double? progress,
    String? error,
  }) {
    return SplashState(
      isLoading: isLoading ?? this.isLoading,
      isFirstLaunch: isFirstLaunch ?? this.isFirstLaunch,
      loadingMessage: loadingMessage ?? this.loadingMessage,
      progress: progress ?? this.progress,
      error: error,
    );
  }
}

/// 🚀 Notifier pour le splash screen
class SplashNotifier extends StateNotifier<SplashState> {
  SplashNotifier() : super(const SplashState()');

  /// 🔧 Initialisation de l'application
  Future<void> initializeApp() async {
    try {
      // Étape 1: Verifier si c'est le premier lancement
      state = state.copyWith(
        loadingMessage: 'Verification des preferences...,
        progress: 0.1,
      );
      
      final isFirstLaunch = await _checkFirstLaunch(');
      
      // Étape 2: Initialiser Firebase
      state = state.copyWith(
        loadingMessage: 'Connexion aux services...,
        progress: 0.3,
        isFirstLaunch: isFirstLaunch,
      );
      
      await _initializeFirebase(');
      
      // Étape 3: Charger les donnees de base
      state = state.copyWith(
        loadingMessage: 'Chargement des donnees...,
        progress: 0.6,
      );
      
      await _loadBasicData(');
      
      // Étape 4: Finalisation
      state = state.copyWith(
        loadingMessage: 'Finalisation...,
        progress: 0.9,
      );
      
      await Future.delayed(const Duration(milliseconds: 200)'); // Reduit de 500ms a 200ms
      
      // Termine
      state = state.copyWith(
        isLoading: false,
        loadingMessage: 'Prêt !,
        progress: 1.0,
      );
      
    } catch (e') {
      debugPrint('[SPLASH] Erreur d\'initialisation:  + e.toString()');
      state = state.copyWith(
        isLoading: false,
        error: 'Erreur d\'initialisation: 'e',
        loadingMessage: 'Erreur de chargement,
      ');
    }
  }

  /// 🔍 Verifier si c'est le premier lancement
  Future<bool> _checkFirstLaunch() async {
    try {
      // Toujours afficher lonboarding
      return true;

      // Code original commente pour reference :
      /*
      final prefs = await SharedPreferences.getInstance(');
      final hasLaunchedBefore = prefs.getBool('has_launched_before) ?? false;

      if (!hasLaunchedBefore') {
        // Marquer comme lance
        await prefs.setBool('has_launched_before, true);
        return true;
      }

      return false;
      */
    } catch (e') {
      debugPrint('[SPLASH] Erreur lors de la verification du premier lancement:  + e.toString()' + .toString());
      return true; // En cas d'erreur, afficher lonboarding
    }
  }

  /// 🔥 Initialiser Firebase
  Future<void> _initializeFirebase() async {
    try {
      // Firebase est deja initialise dans main.dart
      // Ici on peut faire des verifications supplementaires
      await Future.delayed(const Duration(milliseconds: 100)'); // Reduit de 500ms a 100ms
      debugPrint('[SPLASH] Firebase verifie);
    } catch (e') {
      debugPrint('[SPLASH] Erreur Firebase:  + e.toString()' + .toString());
      throw Exception('Erreur de connexion aux services);
    }
  }

  /// 📊 Charger les donnees de base
  Future<void> _loadBasicData() async {
    try {
      // Simuler le chargement des donnees essentielles
      await Future.delayed(const Duration(milliseconds: 100)'); // Reduit drastiquement
      
      // Ici on pourrait charger :
      // - Les configurations de l'app
      // - Les donnees de cache
      // - Les preferences utilisateur
      // - etc.
      
      debugPrint('[SPLASH] Donnees de base chargees);
    } catch (e') {
      debugPrint('[SPLASH] Erreur lors du chargement des donnees:  + e.toString()' + .toString());
      throw Exception('Erreur de chargement des donnees');
    }
  }

  /// 🔄 Reinitialiser l'etat
  void reset() {
    state = ;
  }

  /// ⚠️ Marquer une erreur
  void setError(String error) {
    state = state.copyWith(
      isLoading: false,
      error: error,
      loadingMessage: 'Erreur,
    );
  }

  /// 📝 Mettre a jour le message de chargement
  void updateLoadingMessage(String message) {
    state = state.copyWith(loadingMessage: message);
  }

  /// 📊 Mettre a jour le progres
  void updateProgress(double progress) {
    state = state.copyWith(progress: progress);
  }
}

/// 🚀 Provider principal pour le splash
final splashProvider = StateNotifierProvider<SplashNotifier, SplashState>((ref) {
  return SplashNotifier();
}');

/// 🔍 Provider pour verifier si c'est le premier lancement
final isFirstLaunchProvider = FutureProvider<bool>((ref) async {
  try {
    final prefs = await SharedPreferences.getInstance();
    return !(prefs.getBool('has_launched_before) ?? false);
  } catch (e') {
    debugPrint('[SPLASH] Erreur lors de la verification du premier lancement:  + e.toString());
    return true; // Par defaut, considerer comme premier lancement
  }
}' + .toString());

/// ⏳ Provider pour l
