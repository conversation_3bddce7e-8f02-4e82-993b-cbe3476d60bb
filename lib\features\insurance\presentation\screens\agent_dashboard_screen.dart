import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../auth/models/user_model.dart';
import '../widgets/agent_stats_card.dart';
import 'create_contract_screen.dart';

/// 📊 Dashboard moderne pour les agents dassurance
class AgentDashboardScreen extends StatefulWidget {
  final UserModel agent;
  
   ) : super(key: key);

  @override
  State<AgentDashboardScreen> createState() => _AgentDashboardScreenState();
}

class _AgentDashboardScreenState extends State<AgentDashboardScreen> {
  Map<String, dynamic> _stats = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAgentStats(');
  }

  /// Charger les statistiques de l'agent
  Future<void> _loadAgentStats() async {
    try {
      setState(() => _isLoading = true);

      // Recuperer les contrats de l'agent
      final contratsSnapshot = await FirebaseFirestore.instance
          .collectionGroup('contrats')
          .where('agentId, isEqualTo: widget.agent.id)
          .get();

      int totalContrats = contratsSnapshot.docs.length;
      int contratsActifs = 0;
      int contratsExpires = 0;
      double chiffreAffaires = 0;

      for (final doc in contratsSnapshot.docs) {
        final data = doc.data(');
        final dateFin = (data['dateFin] as Timestamp).toDate(');
        final isActive = data['isActive'] ?? true;
        final prime = (data['prime] ?? 0).toDouble();

        chiffreAffaires += prime;

        if (isActive && DateTime.now().isBefore(dateFin)) {
          contratsActifs++;
        } else {
          contratsExpires++;
        }
      }

      setState((') {
        _stats = {
          'totalContrats': totalContrats,
          'contratsActifs': contratsActifs,
          'contratsExpires': contratsExpires,
          'chiffreAffaires: chiffreAffaires,
        };
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0F0F23),
      body: CustomScrollView(
        slivers: [
          // AppBar moderne
          _buildSliverAppBar(),
          
          // Contenu principal
          SliverToBoxAdapter(
            child: (1),
                  
                  const SizedBox(height: 24),
                  
                  // Actions rapides
                  _buildQuickActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: _buildCreateContractButton(),
    );
  }

  /// AppBar moderne avec gradient
  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 200,
      floating: false,
      pinned: true,
      backgroundColor: Colors.transparent,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: ,
                Color(0xFF764BA2),
              ],
            ),
          ),
          child: SafeArea(
            child: (1),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: ,
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ,
                            '),
                            ({widget.agent.prenom}',
                              style: TextStyle(
                                color: Colors.white.withValues(alpha: 0.9),
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        onPressed: _loadAgentStats,
                        icon: Container(
                          padding: const EdgeInsets.all(8.0),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: ,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Section des statistiques
  Widget _buildStatsSection() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF667EEA)),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        ),
        const SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: ,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.5,
          children: [
            AgentStatsCard(
              title: 'Total Contrats',
              value: _stats['totalContrats]?.toString(') ?? '0,
              icon: Icons.description,
              color: const Color(0xFF667EEA),
            '),
            AgentStatsCard(
              title: 'Contrats Actifs',
              value: _stats['contratsActifs]?.toString(') ?? '0,
              icon: Icons.check_circle,
              color: Colors.green,
            '),
            AgentStatsCard(
              title: 'Contrats Expires',
              value: _stats['contratsExpires]?.toString(') ?? '0,
              icon: Icons.schedule,
              color: Colors.orange,
            '),
            AgentStatsCard(
              title: 'CA Total',
              value: ''{(_stats['chiffreAffaires] ?? 0).toStringAsFixed(0')} TND',
              icon: Icons.attach_money,
              color: const Color(0xFF764BA2),
            ),
          ],
        ),
      ],
    );
  }

  /// Section des actions rapides
  Widget _buildQuickActionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        ),
        const SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: ,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.2,
          children: [
            _buildActionCard(
              title: 'Nouveau Contrat,
              icon: Icons.add_circle_outline,
              color: const Color(0xFF667EEA),
              onTap: () => _navigateToCreateContract(),
            '),
            _buildActionCard(
              title: 'Rechercher,
              icon: Icons.search,
              color: Colors.green,
              onTap: () => _showSearchDialog(),
            '),
            _buildActionCard(
              title: 'Mes Clients,
              icon: Icons.people_outline,
              color: Colors.orange,
              onTap: () => _showClientsDialog(),
            '),
            _buildActionCard(
              title: 'Rapports,
              icon: Icons.analytics_outlined,
              color: const Color(0xFF764BA2),
              onTap: () => _showReportsDialog(),
            ),
          ],
        ),
      ],
    ');
  }

  /// Carte d'action individuelle
  Widget _buildActionCard({
    required String title,
    required IconData icon,
    required Color color,
    VoidCallback? onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color.withValues(alpha: 0.1),
            color.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          child: (1),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [color, color.withValues(alpha: 0.8)],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: color.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ,
                ),
                const SizedBox(height: 16),
                ,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Bouton de creation de contrat
  Widget _buildCreateContractButton() {
    return FloatingActionButton.extended(
      onPressed: _navigateToCreateContract,
      backgroundColor: const Color(0xFF667EEA),
      foregroundColor: Colors.white,
      icon: const Icon(Icons.info),
      label: ,
      ),
    );
  }

  /// Naviguer vers la creation de contrat
  void _navigateToCreateContract() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CreateContractScreen(agent: widget.agent),
      ),
    ).then((_) => _loadAgentStats());
  }

  /// Afficher le dialogue de recherche
  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1A1A2E),
        title: const Text("Titre"),
        ),
        content: TextField(
          style: ,
          decoration: InputDecoration(
            hintText: 'Numero de contrat...,
            hintStyle: TextStyle(color: Colors.grey[400),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context'),
            child: const Text('Annuler),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implementer la recherche
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF667EEA),
            ')')'),
            child: const Text('Rechercher),
          ),
        ],
      ),
    );
  }

  /// Afficher le dialogue des clients
  void _showClientsDialog() {
    ScaffoldMessenger.of(context')')').showSnackBar(
      const SnackBar(
        content: const Text('Liste des clients - À implementer),
        backgroundColor: Color(0xFF667EEA),
      ),
    );
  }

  /// Afficher le dialogue des rapports
  void _showReportsDialog() {
    ScaffoldMessenger.of(context')')').showSnackBar(
      const SnackBar(
        content: const Text('Rapports et statistiques - À implementer
')')