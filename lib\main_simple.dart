import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';

/// 🚀 Version simplifiée pour tester
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    print('✅ Firebase initialisé');
  } catch (e) {
    print('⚠️ Erreur Firebase: $e');
  }
  
  runApp();
}

class SimpleApp extends StatelessWidget {
  const SimpleApp({Key? key}) ) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Constat Tunisie - Simple',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: ,
      debugShowCheckedModeBanner: false,
    );
  }
}

class SimpleHomePage extends StatelessWidget {
  const SimpleHomePage({Key? key}) ) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Constat Tunisie'),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.check_circle,
              size: 100,
              color: Colors.green.shade600,
            ),
            const SizedBox(height: 20),
            ,
            ),
            const SizedBox(height: 10),
            const Text(
              'L\'application se lance maintenant sans erreur',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 40),
            ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => ,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade600,
                foregroundColor: Colors.white,
                padding: 
              ),
              child: const Text('Tester Dialog Moderne'),
            ),
          ],
        ),
      ),
    );
  }
}

class TestCredentialsDialog extends StatelessWidget {
  const TestCredentialsDialog({Key? key}) ) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Dialog'),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: ElevatedButton(
          onPressed: () => _showTestDialog(context),
          child: const Text('Afficher Dialog Moderne'),
        ),
      ),
    );
  }

  void _showTestDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🎉 Test Réussi !'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Le dialog moderne fonctionne !'),
            const SizedBox(height: 16),
            const Text('Identifiants de test :'),
            const SizedBox(height: 8),
            const Text('Email: <EMAIL>'),
            const Text('Mot de passe: Test123!'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer
