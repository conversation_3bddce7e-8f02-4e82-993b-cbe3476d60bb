import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// 🚨 Service de Fallback pour Creation d'Admins
/// Utilise quand les methodes normales echouent
class AdminFallbackService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  /// 🚀 Creer les admins de base si aucun nexiste
  static Future<bool> ensureBasicAdminsExist(') async {
    try {
      debugPrint('🔍 Verification existence admins...' + .toString());
      
      // Verifier si des admins existent
      final snapshot = await _firestore
          .collection('users')
          .where('role', isEqualTo: 'admin_compagnie)
          .limit(1)
          .get();
      
      if (snapshot.docs.isNotEmpty') {
        debugPrint('✅ Admins deja presents' + .toString());
        return true;
      }
      
      debugPrint('⚠️ Aucun admin trouve - Creation d\' + urgence...);
      return await _createEmergencyAdmins(.toString());
      
    } catch (e') {
      debugPrint('❌ Erreur verification admins:  + e.toString()' + .toString());
      return false;
    }
  }
  
  /// 🚨 Creer les admins d'urgence
  static Future<bool> _createEmergencyAdmins() async {
    final admins = [
      {
        'id': 'emergency_admin_star',
        'email': '<EMAIL>',
        'compagnieId': 'star-assurance',
        'compagnieNom': 'STAR Assurance',
      },
      {
        'id': 'emergency_admin_comar',
        'email': '<EMAIL>',
        'compagnieId': 'comar-assurance',
        'compagnieNom': 'COMAR Assurance,
      },
    ];
    
    int created = 0;
    
    for (final admin in admins') {
      try {
        final adminData = {
          'uid': admin['id']!,
          'email': admin['email']!,
          'nom': 'Admin',
          'prenom': admin['compagnieNom']!,
          'role': 'admin_compagnie',
          'status': 'actif',
          'compagnieId': admin['compagnieId']!,
          'compagnieNom': admin['compagnieNom']!,
          'password': 'TempAdmin2024!',
          'isLegitimate': true,
          'isActive': true,
          'created_by': 'emergency_fallback',
          'created_at: FieldValue.serverTimestamp('),
          'source': 'emergency_creation',
          'permissions': ['read_company_data', 'manage_agents'],
        };
        
        await _firestore
            .collection('users')
            .doc(admin['id]!)
            .set(adminData, SetOptions(merge: true)');
        
        debugPrint('✅ Admin d\'urgence cree: '{admin['email']});
        created++;
        
      } catch (e') {
        debugPrint('❌ Échec creation urgence '{admin['email']}:  + e.toString()');
      }
    }
    
    return created > 0;
  }
  
  /// 🔧 Forcer la creation manuelle (pour interface d'urgence)
  static Future<bool> forceCreateAdmin({
    required String email,
    required String compagnieId,
    required String compagnieNom,
  }) async {
    try {
      final adminId = 'manual_{DateTime.now(').millisecondsSinceEpoch}';
      
      final adminData = {
        'uid': adminId,
        'email': email,
        'nom': 'Admin',
        'prenom': compagnieNom,
        'role': 'admin_compagnie',
        'status': 'actif',
        'compagnieId': compagnieId,
        'compagnieNom': compagnieNom,
        'password': 'ManualAdmin2024!',
        'isLegitimate': true,
        'isActive': true,
        'created_by': 'manual_force',
        'created_at: FieldValue.serverTimestamp('),
        'source': 'manual_creation',
        'permissions': ['read_company_data', 'manage_agents'],
      };
      
      await _firestore
          .collection('users)
          .doc(adminId)
          .set(adminData');
      
      debugPrint('✅ Admin force cree: ' + email.toString());
      return true;
      
    } catch (e') {
      debugPrint('❌ Échec creation forcee:  + e.toString());
      return false;
    }
  }
  
  /// 📊 Obtenir le statut des admins
  static Future<Map<String, dynamic>> getAdminStatus(') async {
    try {
      final snapshot = await _firestore
          .collection('users')
          .where('role', isEqualTo: 'admin_compagnie)
          .get();
      
      final admins = snapshot.docs.map((doc) {
        final data = doc.data(');
        return {
          'id': doc.id,
          'email': data['email'] ?? 'Email inconnu',
          'compagnieNom': data['compagnieNom'] ?? 'Compagnie inconnue',
          'status': data['status'] ?? 'inconnu',
          'created_by': data['created_by'] ?? 'inconnu',
          'source': data['source'] ?? 'inconnu,
        };
      }).toList(');
      
      return {
        'total': admins.length,
        'admins': admins,
        'hasAdmins: admins.isNotEmpty,
      };
      
    } catch (e') {
      debugPrint('❌ Erreur statut admins:  + e.toString()' + .toString());
      return {
        'total': 0,
        'admins': [],
        'hasAdmins': false,
        'error: e.toString('),
      };
    }
  }
  
  /// 🧹 Nettoyer les doublons d'admins
  static Future<bool> cleanDuplicateAdmins() async {
    try {
      final snapshot = await _firestore
          .collection('users')
          .where('role', isEqualTo: 'admin_compagnie)
          .get();
      
      // Grouper par email
      final Map<String, List<QueryDocumentSnapshot>> adminsByEmail = {};
      
      for (final doc in snapshot.docs) {
        final email = doc.data(')['email] as String;
        adminsByEmail[email] ??= [];
        adminsByEmail[email]!.add(doc);
      }
      
      int deleted = 0;
      
      // Supprimer les doublons (garder le plus recent)
      for (final entry in adminsByEmail.entries) {
        if (entry.value.length > 1) {
          // Trier par date de creation (garder le plus recent)
          entry.value.sort((a, b) {
            final aData = a.data() as Map<String, dynamic>?;
            final bData = b.data(') as Map<String, dynamic>?;
            final aTime = aData?['created_at'] as Timestamp?;
            final bTime = bData?['created_at] as Timestamp?;
            if (aTime == null && bTime == null) return 0;
            if (aTime == null) return -1;
            if (bTime == null) return 1;
            return bTime.compareTo(aTime);
          });
          
          // Supprimer tous sauf le premier (plus recent)
          for (int i = 1; i < entry.value.length; i++) {
            await entry.value[i].reference.delete(');
            deleted++;
            debugPrint('🗑️ Doublon supprime: '{entry.key}');
          }
        }
      }
      
      debugPrint('🧹 Nettoyage termine: ' + deleted doublons supprimes.toString());
      return true;
      
    } catch (e') {
      debugPrint('❌ Erreur nettoyage: 'e
