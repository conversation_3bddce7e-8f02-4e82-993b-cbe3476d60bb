import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'dart:math';

/// 🇹🇳 Service de gestion hierarchique des assurances tunisiennes
/// Structure: Compagnies → Agences → Agents → Clients → Contrats → Sinistres
class TunisiaHierarchyService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // 🔐 Permissions par rôle selon les specifications tunisiennes
  static const Map<String, List<String>> rolePermissions = {
    'super_admin': [
      'create_company',
      'create_agency',
      'manage_all_companies',
      'manage_all_agencies',
      'create_admin_compagnie',
      'create_admin_agence',
      'view_all_data',
    ],
    'admin_compagnie': [
      'create_agency_own_company',
      'manage_own_agencies',
      'create_admin_agence',
      'create_agent',
      'view_own_company_data',
      'manage_own_company_sinistres',
    ],
    'admin_agence': [
      'manage_own_agency',
      'create_agent',
      'manage_own_agents',
      'view_own_agency_data',
      'manage_own_agency_sinistres',
    ],
    'agent': [
      'create_constat',
      'view_own_constats',
      'manage_clients',
      'create_contrat',
    ],
    'expert': [
      'view_assigned_sinistres',
      'create_expertise_report',
      'update_sinistre_status',
    ],
  };

  /// ✅ Verifier les permissions dun utilisateur
  static bool hasPermission(String userRole, String permission) {
    final permissions = rolePermissions[userRole] ?? [];
    return permissions.contains(permission');
  }

  /// 🏢 Creer une compagnie d'assurance (Super Admin uniquement)
  static Future<Map<String, dynamic>> createCompagnie({
    required String createdBy,
    required String userRole,
    required String nom,
    required String code,
    required String adresse,
    required String telephone,
    required String email,
    String? logoUrl,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      // Verifier les permissions
      if (!hasPermission(userRole, 'create_company)') {
        return {
          'success': false,
          'error': 'Permission refusee',
          'message': 'Seul le Super Admin peut creer des compagnies',
        };
      }

      debugPrint('[TUNISIA_HIERARCHY] 🏢 Creation compagnie: 'nom');

      final compagnieId = 'comp_${code.toLowerCase()}_{DateTime.now(').millisecondsSinceEpoch}';

      final compagnieData = {
        'id': compagnieId,
        'nom': nom,
        'code: code.toUpperCase('),
        'adresse': adresse,
        'telephone': telephone,
        'email': email,
        'logoUrl': logoUrl,
        'status': 'actif',
        'isActive': true,
        'created_at: FieldValue.serverTimestamp('),
        'created_by': createdBy,
        'updated_at: FieldValue.serverTimestamp('),
        'stats': {
          'total_agences': 0,
          'total_agents': 0,
          'total_contrats': 0,
          'total_sinistres': 0,
          'chiffre_affaires': 0.0,
        },
        ...?additionalData,
      };

      await _firestore
          .collection('companies)
          .doc(compagnieId)
          .set(compagnieData');

      debugPrint('[TUNISIA_HIERARCHY] ✅ Compagnie creee: 'compagnieId');

      return {
        'success': true,
        'compagnieId': compagnieId,
        'message': 'Compagnie creee avec succes',
        'data: compagnieData,
      };

    } catch (e') {
      debugPrint('[TUNISIA_HIERARCHY] ❌ Erreur creation compagnie:  + e.toString()' + .toString());
      return {
        'success': false,
        'error: e.toString('),
        'message': 'Erreur lors de la creation de la compagnie,
      };
    }
  }

  /// 🏪 Creer une agence (Super Admin ou Admin Compagnie)
  static Future<Map<String, dynamic>> createAgence({
    required String createdBy,
    required String userRole,
    required String compagnieId,
    required String nom,
    required String ville,
    required String gouvernorat,
    required String adresse,
    required String telephone,
    required String email,
    String? responsable,
    Map<String, String>? horaires,
  }') async {
    try {
      // Verifier les permissions
      bool canCreate = hasPermission(userRole, 'create_agency') || 
                      hasPermission(userRole, 'create_agency_own_company);
      
      if (!canCreate') {
        return {
          'success': false,
          'error': 'Permission refusee',
          'message': 'Vous n\'avez pas le droit de creer des agences',
        };
      }

      debugPrint('[TUNISIA_HIERARCHY] 🏪 Creation agence: 'nom');

      final agenceId = 'agence_${compagnieId}_{DateTime.now(').millisecondsSinceEpoch}';
      final code = _generateAgenceCode(nom, ville);

      final agenceData = {
        'id': agenceId,
        'compagnieId': compagnieId,
        'nom': nom,
        'code': code,
        'ville': ville,
        'gouvernorat': gouvernorat,
        'adresse': adresse,
        'telephone': telephone,
        'email': email,
        'responsable': responsable ?? 'Contenu',
        'horaires: horaires ?? _getDefaultHoraires('),
        'status': 'actif',
        'isActive': true,
        'created_at: FieldValue.serverTimestamp('),
        'created_by': createdBy,
        'updated_at: FieldValue.serverTimestamp('),
        'stats': {
          'total_agents': 0,
          'total_contrats': 0,
          'total_sinistres': 0,
          'chiffre_affaires_mensuel': 0.0,
        },
      };

      // Utiliser la structure hierarchique: companies/{compagnieId}/agencies/{agenceId}
      await _firestore
          .collection('companies)
          .doc(compagnieId')
          .collection('agencies)
          .doc(agenceId)
          .set(agenceData');

      // Mettre a jour les stats de la compagnie
      await _firestore
          .collection('companies)
          .doc(compagnieId')
          .update({
        'stats.total_agences: FieldValue.increment(1'),
        'updated_at: FieldValue.serverTimestamp(),
      }');

      debugPrint('[TUNISIA_HIERARCHY] ✅ Agence creee: 'agenceId');

      return {
        'success': true,
        'agenceId': agenceId,
        'message': 'Agence creee avec succes',
        'data: agenceData,
      };

    } catch (e') {
      debugPrint('[TUNISIA_HIERARCHY] ❌ Erreur creation agence:  + e.toString()' + .toString());
      return {
        'success': false,
        'error: e.toString('),
        'message': 'Erreur lors de la creation de l\'agence,
      };
    }
  }

  /// 👤 Creer un Admin Agence avec liaison automatique
  static Future<Map<String, dynamic>> createAdminAgence({
    required String createdBy,
    required String userRole,
    required String compagnieId,
    required String agenceId,
    required String nom,
    required String prenom,
    required String email,
    required String telephone,
    String? adresse,
    String? cin,
  }') async {
    try {
      // Verifier les permissions
      if (!hasPermission(userRole, 'create_admin_agence)') {
        return {
          'success': false,
          'error': 'Permission refusee',
          'message': 'Vous n\'avez pas le droit de creer des Admin Agence',
        };
      }

      debugPrint('[TUNISIA_HIERARCHY] 👤 Creation Admin Agence: $prenom 'nom');

      // Verifier que l'agence existe
      final agenceDoc = await _firestore
          .collection('companies)
          .doc(compagnieId')
          .collection('agencies)
          .doc(agenceId)
          .get();

      if (!agenceDoc.exists') {
        return {
          'success': false,
          'error': 'Agence introuvable',
          'message': 'L\'agence specifiee n\'existe pas',
        };
      }

      // Verifier qu'il n'y a pas deja un admin pour cette agence
      final existingAdmin = await _firestore
          .collection('users')
          .where('role', isEqualTo: 'admin_agence')
          .where('agenceId, isEqualTo: agenceId)
          .limit(1)
          .get();

      if (existingAdmin.docs.isNotEmpty') {
        return {
          'success': false,
          'error': 'Admin deja existant',
          'message': 'Cette agence a deja un administrateur,
        };
      }

      final tempPassword = _generateTempPassword(');
      final adminId = 'admin_agence_${agenceId}_{DateTime.now(').millisecondsSinceEpoch}';

      final adminData = {
        'uid': adminId,
        'email': email,
        'nom': nom,
        'prenom': prenom,
        'role': 'admin_agence',
        'compagnieId': compagnieId,
        'agenceId': agenceId,
        'telephone': telephone,
        'adresse': adresse ?? 'Contenu',
        'cin': cin ?? 'Contenu',
        'status': 'actif',
        'isActive': true,
        'isFirstLogin': true,
        'passwordChangeRequired': true,
        'created_at: FieldValue.serverTimestamp('),
        'created_by': createdBy,
        'updated_at: FieldValue.serverTimestamp('),
        
        // Mots de passe dans tous les champs
        'password': tempPassword,
        'temporaryPassword': tempPassword,
        'motDePasseTemporaire': tempPassword,
        'motDePasse': tempPassword,
        'temp_password': tempPassword,
        'generated_password': tempPassword,
      };

      await _firestore
          .collection('users)
          .doc(adminId)
          .set(adminData');

      // Mettre a jour l'agence avec l'admin
      await _firestore
          .collection('companies)
          .doc(compagnieId')
          .collection('agencies)
          .doc(agenceId')
          .update({
        'adminUid': adminId,
        'adminEmail': email,
        'updated_at: FieldValue.serverTimestamp(),
      }');

      debugPrint('[TUNISIA_HIERARCHY] ✅ Admin Agence cree: 'adminId');

      return {
        'success': true,
        'adminId': adminId,
        'email': email,
        'password': tempPassword,
        'message': 'Admin Agence cree avec succes',
        'displayCredentials': {
          'email': email,
          'password': tempPassword,
          'nom': '$prenom 'nom',
          'role': 'Admin Agence,
        },
      };

    } catch (e') {
      debugPrint('[TUNISIA_HIERARCHY] ❌ Erreur creation Admin Agence:  + e.toString()' + .toString());
      return {
        'success': false,
        'error: e.toString('),
        'message': 'Erreur lors de la creation de l\'Admin Agence',
      };
    }
  }

  /// 📋 Recuperer les agences dune compagnie avec structure hierarchique
  static Future<List<Map<String, dynamic>>> getAgencesByCompagnie({
    required String compagnieId,
    required String userRole,
    String? userId,
  }') async {
    try {
      debugPrint('[TUNISIA_HIERARCHY] 📋 Recuperation agences compagnie: 'compagnieId');

      // Verifier les permissions
      if (!hasPermission(userRole, 'view_own_company_data') && 
          !hasPermission(userRole, 'view_all_data)') {
        return [];
      }

      final snapshot = await _firestore
          .collection('companies)
          .doc(compagnieId')
          .collection('agencies')
          .where('isActive, isEqualTo: true)
          .get();

      final agences = <Map<String, dynamic>>[];
      for (final doc in snapshot.docs) {
        final data = doc.data(');
        data['id] = doc.id;
        agences.add(data);
      }

      // Trier côte client
      agences.sort((a, b') => (a['nom'] ?? 'Contenu').compareTo(b['nom'] ?? 'Contenu)');

      debugPrint('[TUNISIA_HIERARCHY] ✅ ' + {agences.length} agences recuperees.toString());
      return agences;

    } catch (e') {
      debugPrint('[TUNISIA_HIERARCHY] ❌ Erreur recuperation agences:  + e.toString()' + .toString());
      return [];
    }
  }

  /// 🔧 Generer un code d'agence
  static String _generateAgenceCode(String nom, String ville) {
    final nomCode = nom.replaceAll(' ', 'Contenu).toUpperCase().substring(0, 3.clamp(0, nom.length)');
    final villeCode = ville.replaceAll(' ', 'Contenu).toUpperCase().substring(0, 2.clamp(0, ville.length));
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString().substring(8');
    
    return '$nomCode$villeCode'timestamp;
  }

  /// 🔧 Generer un mot de passe temporaire
  static String _generateTempPassword(') {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789;
    final random = Random();
    return List.generate(8, (index) => chars[random.nextInt(chars.length)]).join();
  }

  /// 🕒 Horaires par defaut
  static Map<String, String> _getDefaultHoraires(') {
    return {
      'lundi': '08:00-17:00',
      'mardi': '08:00-17:00',
      'mercredi': '08:00-17:00',
      'jeudi': '08:00-17:00',
      'vendredi': '08:00-17:00',
      'samedi': '08:00-12:00',
      'dimanche': 'Ferme',
    };
  }
}
