import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';

import '../../../utils/connectivity_utils.dart';
import '../models/vehicule_model.dart';
import '../providers/vehicule_provider.dart';
import '../../auth/providers/auth_provider.dart';

class VehiculeFormScreen extends ConsumerStatefulWidget {
  final VehiculeModel? vehicule;

   ) : super(key: key);

  @override
  ConsumerState<VehiculeFormScreen> createState() => _VehiculeFormScreenState();
}

class _VehiculeFormScreenState extends ConsumerState<VehiculeFormScreen> with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _immatriculationController = TextEditingController();
  final _marqueController = TextEditingController();
  final _modeleController = TextEditingController();
  final _compagnieAssuranceController = TextEditingController();
  final _numeroContratController = TextEditingController();
  final _quittanceController = TextEditingController();
  final _agenceController = TextEditingController();

  DateTime? _dateDebutValidite;
  DateTime? _dateFinValidite;

  File? _photoRectoFile;
  File? _photoVersoFile;
  bool _photoRectoChanged = false;
  bool _photoVersoChanged = false;

  bool _isLoading = false;
  String? _errorMessage;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // Couleurs pastel modernes
  static const Color _primaryPastel = Color(0xFF6B73FF);
  static const Color _secondaryPastel = Color(0xFF9B59B6);
  static const Color _accentPastel = Color(0xFF3498DB);
  static const Color _successPastel = Color(0xFF2ECC71);

  static const Color _backgroundPastel = Color(0xFFF8F9FA);
  static const Color _cardPastel = Color(0xFFFFFFFF);

  // Listes d'exemples
  final List<String> _marquesExemples = [
    'Renault', 'Peugeot', 'Citroën', 'Volkswagen', 'BMW', 'Mercedes', 
    'Audi', 'Toyota', 'Hyundai', 'Kia', 'Nissan', 'Ford', 'Opel',
    'Fiat', 'Seat', 'Skoda', 'Dacia', 'Suzuki', 'Mazda', 'Honda'
  ];

  final List<String> _assurancesExemples = [
    'STAR', 'GAT', 'COMAR', 'MAGHREBIA', 'LLOYD TUNISIEN', 
    'ASTREE', 'CTAMA', 'ZITOUNA TAKAFUL', 'SALIM', 'CARTE'
  ];

  @override
  void initState() {
    super.initState();
    _initializeForm();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  void _initializeForm() {
    if (widget.vehicule != null) {
      _immatriculationController.text = widget.vehicule!.immatriculation;
      _marqueController.text = widget.vehicule!.marque;
      _modeleController.text = widget.vehicule!.modele;
      _compagnieAssuranceController.text = widget.vehicule!.compagnieAssurance;
      _numeroContratController.text = widget.vehicule!.numeroContrat;
      _quittanceController.text = widget.vehicule!.quittance;
      _agenceController.text = widget.vehicule!.agence;
      _dateDebutValidite = widget.vehicule!.dateDebutValidite;
      _dateFinValidite = widget.vehicule!.dateFinValidite;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _immatriculationController.dispose();
    _marqueController.dispose();
    _modeleController.dispose();
    _compagnieAssuranceController.dispose();
    _numeroContratController.dispose();
    _quittanceController.dispose();
    _agenceController.dispose();
    super.dispose();
  }

  Future<void> _pickImage(ImageSource source, bool isRecto) async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: source,
        imageQuality: 50,
        maxWidth: 800,
        maxHeight: 800,
      );

      if (pickedFile != null && mounted) {
        setState(() {
          if (isRecto) {
            _photoRectoFile = File(pickedFile.path);
            _photoRectoChanged = true;
          } else {
            _photoVersoFile = File(pickedFile.path);
            _photoVersoChanged = true;
          }
        });
      }
    } catch (e) {
      debugPrint('[VehiculeFormScreen] Erreur lors de la sélection de l\'image: $e');
      if (mounted) {
        _showSnackBar('Erreur lors de la sélection de l\'image', isError: true);
      }
    }
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate
          ? _dateDebutValidite ?? DateTime.now()
          : _dateFinValidite ?? (DateTime.now().add(const Duration(days: 365))),
      firstDate: isStartDate ? DateTime(2000) : (_dateDebutValidite ?? DateTime.now()),
      lastDate: DateTime(2100),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ,
          child: child!,
        );
      },
    );

    if (picked != null && mounted) {
      setState(() {
        if (isStartDate) {
          _dateDebutValidite = picked;
          if (_dateFinValidite != null && _dateFinValidite!.isBefore(_dateDebutValidite!)) {
            _dateFinValidite = _dateDebutValidite!.add(const Duration(days: 365));
          }
        } else {
          _dateFinValidite = picked;
        }
      });
    }
  }

  void _showImageSourceDialog(bool isRecto) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          title: Row(
            children: [
              Container(
                padding: ,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: ,
              ),
              const SizedBox(width: 12),
              const Text('Sélectionner une source'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildSourceOption(
                icon: Icons.photo_library,
                title: 'Galerie',
                subtitle: 'Choisir depuis la galerie',
                onTap: () {
                  Navigator.of(context).pop();
                  _pickImage(ImageSource.gallery, isRecto);
                },
              ),
              const SizedBox(height: 12),
              _buildSourceOption(
                icon: Icons.photo_camera,
                title: 'Appareil photo',
                subtitle: 'Prendre une nouvelle photo',
                onTap: () {
                  Navigator.of(context).pop();
                  _pickImage(ImageSource.camera, isRecto);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSourceOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: ,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              padding: ,
                borderRadius: BorderRadius.circular(10),
              ),
              child: const Icon(icon, color: _accentPastel),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ,
                  ),
                  ,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            ,
            const SizedBox(width: 8),
            Expanded(child: const Text(message)),
          ],
        ),
        backgroundColor: isError ? Colors.red.shade400 : _successPastel,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_dateDebutValidite == null || _dateFinValidite == null) {
      _showSnackBar('Veuillez sélectionner les dates de validité', isError: true);
      return;
    }

    // Réinitialiser l'état du provider avant de commencer
    final vehiculeProviderInstance = ref.read(vehiculeProvider);
    vehiculeProviderInstance.resetForNewOperation();

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    
    try {
      final connectivityUtils = ConnectivityUtils();
      bool isConnected = await connectivityUtils.checkConnection();
      if (!isConnected) {
        throw Exception('Pas de connexion internet. Veuillez vérifier votre connexion et réessayer.');
      }
      
      final authProviderInstance = ref.read(authProvider);
      if (!mounted) return;

      if (authProviderInstance.currentUser == null) {
        throw Exception('Utilisateur non authentifié');
      }

      final vehiculeProviderInstance = ref.read(vehiculeProvider);
      
      final vehicule = VehiculeModel(
        proprietaireId: authProviderInstance.currentUser!.id,
        immatriculation: _immatriculationController.text.trim(),
        marque: _marqueController.text.trim(),
        modele: _modeleController.text.trim(),
        compagnieAssurance: _compagnieAssuranceController.text.trim(),
        numeroContrat: _numeroContratController.text.trim(),
        quittance: _quittanceController.text.trim(),
        agence: _agenceController.text.trim(),
        dateDebutValidite: _dateDebutValidite,
        dateFinValidite: _dateFinValidite,
        photoCarteGriseRecto: widget.vehicule?.photoCarteGriseRecto,
        photoCarteGriseVerso: widget.vehicule?.photoCarteGriseVerso,
      );
      
      if (widget.vehicule == null) {
        await vehiculeProviderInstance.addVehicule(
          vehicule: vehicule,
          photoRecto: _photoRectoFile,
          photoVerso: _photoVersoFile,
        );
      } else {
        await vehiculeProviderInstance.updateVehicule(
          vehicule: vehicule.copyWith(id: widget.vehicule!.id),
          photoRecto: _photoRectoChanged ? _photoRectoFile : null,
          photoVerso: _photoVersoChanged ? _photoVersoFile : null,
        );
      }
      
      if (mounted) {
        _showSnackBar(widget.vehicule == null 
            ? 'Véhicule ajouté avec succès' 
            : 'Véhicule mis à jour avec succès');
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = e.toString();
        });

        // Personnaliser le message d'erreur selon le type d'erreur
        String userFriendlyMessage;
        if (e.toString().contains('TimeoutException') || e.toString().contains('timeout')) {
          userFriendlyMessage = 'Le téléchargement prend trop de temps.\n\nConseils :\n• Vérifiez votre connexion internet\n• Utilisez des images plus petites\n• Réessayez dans quelques instants';
        } else if (e.toString().contains('network') || e.toString().contains('connection')) {
          userFriendlyMessage = 'Problème de connexion internet.\nVeuillez vérifier votre connexion et réessayer.';
        } else if (e.toString().contains('permission') || e.toString().contains('denied')) {
          userFriendlyMessage = 'Vous n\'avez pas les autorisations nécessaires.\nVeuillez contacter l\'administrateur.';
        } else {
          userFriendlyMessage = 'Une erreur est survenue.\nVeuillez réessayer.';
        }

        _showSnackBar(userFriendlyMessage, isError: true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.vehicule != null;
    final dateFormat = DateFormat('dd/MM/yyyy');

    return Scaffold(
      backgroundColor: _backgroundPastel,
      appBar: AppBar(
        title: const Text(isEditing ? 'Modifier le véhicule' : 'Ajouter un véhicule'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: ,
          ),
        ),
      ),
      body: Consumer(
        builder: (context, ref, child) {
          final vehiculeProviderInstance = ref.watch(vehiculeProvider);

          if (_isLoading || vehiculeProviderInstance.isLoading) {
            return _buildLoadingState(vehiculeProviderInstance);
          }

          return FadeTransition(
            opacity: _fadeAnimation,
            child: SingleChildScrollView(
              padding:  {
                            if (value == null || value.isEmpty) {
                              return 'Veuillez entrer l\'immatriculation';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 20),
                        _buildAutocompleteField(
                          controller: _marqueController,
                          label: 'Marque',
                          hint: 'Ex: Renault, Peugeot, BMW...',
                          icon: Icons.branding_watermark,
                          suggestions: _marquesExemples,
                        ),
                        const SizedBox(height: 20),
                        _buildModernTextField(
                          controller: _modeleController,
                          label: 'Modèle',
                          hint: 'Ex: 208, Clio, Golf...',
                          icon: Icons.model_training,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Veuillez entrer le modèle';
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),
                    _buildSectionCard(
                      title: 'Photos de la carte grise',
                      icon: Icons.photo_camera,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: _buildPhotoCard(
                                title: 'Recto',
                                photoFile: _photoRectoFile,
                                networkUrl: widget.vehicule?.photoCarteGriseRecto,
                                onTap: () => _showImageSourceDialog(true),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: _buildPhotoCard(
                                title: 'Verso',
                                photoFile: _photoVersoFile,
                                networkUrl: widget.vehicule?.photoCarteGriseVerso,
                                onTap: () => _showImageSourceDialog(false),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),
                    _buildSectionCard(
                      title: 'Informations d\'assurance',
                      icon: Icons.security,
                      children: [
                        _buildAutocompleteField(
                          controller: _compagnieAssuranceController,
                          label: 'Compagnie d\'assurance',
                          hint: 'Ex: STAR, GAT, COMAR...',
                          icon: Icons.business,
                          suggestions: _assurancesExemples,
                        ),
                        const SizedBox(height: 20),
                        _buildModernTextField(
                          controller: _numeroContratController,
                          label: 'Numéro de contrat',
                          hint: 'Ex: CT123456789',
                          icon: Icons.numbers,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Veuillez entrer le numéro de contrat';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 20),
                        _buildModernTextField(
                          controller: _quittanceController,
                          label: 'Quittance',
                          hint: 'Ex: QP2024N000042230',
                          icon: Icons.receipt,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Veuillez entrer la quittance';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 20),
                        _buildModernTextField(
                          controller: _agenceController,
                          label: 'Agence',
                          hint: 'Ex: Tunis Centre, Sfax...',
                          icon: Icons.location_city,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Veuillez entrer l\'agence';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 20),
                        Row(
                          children: [
                            Expanded(
                              child: _buildDateField(
                                label: 'Début de validité',
                                date: _dateDebutValidite,
                                onTap: () => _selectDate(context, true),
                                dateFormat: dateFormat,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: _buildDateField(
                                label: 'Fin de validité',
                                date: _dateFinValidite,
                                onTap: () => _selectDate(context, false),
                                dateFormat: dateFormat,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 32),
                    _buildSubmitButton(isEditing),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildLoadingState(VehiculeProvider vehiculeProvider) {
    final progress = vehiculeProvider.uploadProgress;
    final progressPercentage = (progress * 100).toInt();

    String statusMessage;
    if (progress < 0.2) {
      statusMessage = 'Compression des images...';
    } else if (progress < 0.8) {
      statusMessage = 'Téléchargement des photos...';
    } else if (progress < 0.9) {
      statusMessage = 'Enregistrement du véhicule...';
    } else {
      statusMessage = 'Finalisation...';
    }

    return Container(
      decoration: ,
      ),
      child: (1),
                      valueColor: const AlwaysStoppedAnimation<Color>(_primaryPastel),
                    ),
                  ),
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      ,
                      const SizedBox(height: 4),
                      (progressPercentage%',
                        style: ,
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 32),
              ,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              ,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              // Conseils pour l'utilisateur
              Container(
                padding: ,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _primaryPastel.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        ,
                        const SizedBox(width: 8),
                        ,
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    ,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              // Bouton d'annulation si le processus prend trop de temps
              if (progress > 0.1) // Afficher seulement après avoir commencé
                ElevatedButton(
                  onPressed: () {
                    vehiculeProvider.cancelVehiculeOperation();
                    vehiculeProvider.resetForNewOperation();
                    setState(() {
                      _isLoading = false;
                      _errorMessage = null;
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey.shade600,
                    foregroundColor: Colors.white,
                    padding: ,
                    ),
                  ),
                  child: const Text('Annuler et réessayer'),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: _cardPastel,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 20,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: (1),
                  decoration: BoxDecoration(
                    gradient: ,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ,
                ),
                const SizedBox(width: 16),
                ,
                ),
              ],
            ),
            const SizedBox(height: 24),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: const Icon(icon, color: _primaryPastel),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(15),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(15),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(15),
          borderSide: const BorderSide(color: _primaryPastel, width: 2),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
        labelStyle: ,
        hintStyle: TextStyle(color: Colors.grey.shade500),
      ),
    );
  }

  Widget _buildAutocompleteField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    required List<String> suggestions,
  }) {
    return Autocomplete<String>(
      optionsBuilder: (TextEditingValue textEditingValue) {
        if (textEditingValue.text.isEmpty) {
          return  {
          return option.toLowerCase().contains(textEditingValue.text.toLowerCase());
        });
      },
      onSelected: (String selection) {
        controller.text = selection;
      },
      fieldViewBuilder: (context, fieldController, focusNode, onFieldSubmitted) {
        if (controller.text.isNotEmpty && fieldController.text != controller.text) {
          fieldController.text = controller.text;
        }
        
        fieldController.addListener(() {
          if (controller.text != fieldController.text) {
            controller.text = fieldController.text;
          }
        });
        
        return TextFormField(
          controller: fieldController,
          focusNode: focusNode,
          decoration: InputDecoration(
            labelText: label,
            hintText: hint,
            prefixIcon: const Icon(icon, color: _primaryPastel),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: const BorderSide(color: _primaryPastel, width: 2),
            ),
            filled: true,
            fillColor: Colors.grey.shade50,
            labelStyle: ,
            hintStyle: TextStyle(color: Colors.grey.shade500),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Veuillez entrer $label';
            }
            return null;
          },
        );
      },
    );
  }

  Widget _buildPhotoCard({
    required String title,
    required File? photoFile,
    required String? networkUrl,
    required VoidCallback onTap,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        ),
        const SizedBox(height: 12),
        InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(15),
          child: Container(
            height: 160,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                color: _primaryPastel.withValues(alpha: 0.3),
                width: 2,
              ),
              gradient: LinearGradient(
                colors: [
                  _primaryPastel.withValues(alpha: 0.05),
                  _secondaryPastel.withValues(alpha: 0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: photoFile != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(13),
                    child: Image.file(
                      photoFile,
                      fit: BoxFit.cover,
                      width: double.infinity,
                    ),
                  )
                : networkUrl != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(13),
                        child: Image.network(
                          networkUrl,
                          fit: BoxFit.cover,
                          width: double.infinity,
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return const Center(
                              child: CircularProgressIndicator(color: _primaryPastel),
                            );
                          },
                          errorBuilder: (context, error, stackTrace) {
                            return ,
                            );
                          },
                        ),
                      )
                    : Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            padding: ,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: ,
                          ),
                          const SizedBox(height: 12),
                          ,
                          ),
                        ],
                      ),
          ),
        ),
      ],
    );
  }

  Widget _buildDateField({
    required String label,
    required DateTime? date,
    required VoidCallback onTap,
    required DateFormat dateFormat,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(15),
      child: Container(
        padding: ,
          borderRadius: BorderRadius.circular(15),
          color: Colors.grey.shade50,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                ,
                const SizedBox(width: 8),
                Expanded(
                  child: 
                        : 'Sélectionner une date
