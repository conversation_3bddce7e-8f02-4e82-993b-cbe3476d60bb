import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// 🏛️ Carte d'aperçu des agences de la compagnie
class AgencesOverviewCard extends StatefulWidget {
  final String compagnieId;

  ;

  @override
  State<AgencesOverviewCard> createState() => _AgencesOverviewCardState();
}

class _AgencesOverviewCardState extends State<AgencesOverviewCard> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  List<Map<String, dynamic>> _agences = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAgences();
  }

  /// 🏛️ Charger les agences
  Future<void> _loadAgences() async {
    try {
      setState(() => _isLoading = true);

      final query = await _firestore
          .collection('agences_assurance')
          .where('compagnie_id', isEqualTo: widget.compagnieId)
          .orderBy('created_at', descending: true)
          .limit(5)
          .get();

      final agences = query.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();

      setState(() {
        _agences = agences;
        _isLoading = false;
      });

    } catch (e) {
      debugPrint('[AGENCES_OVERVIEW] ❌ Erreur: $e');
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: ,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: .withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              ,
                ),
              ),
              ,
              if (_isLoading)
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              else
                TextButton.const Icon(
                  onPressed: () {
                    // TODO: Naviguer vers la liste complète des agences
                  },
                  icon: const Icon(Icons.info),
                  label: const Text('Voir tout'),
                  style: TextButton.styleFrom(
                    foregroundColor: const Color(0xFF3B82F6),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),
          
          if (_isLoading)
            _buildLoadingState()
          else if (_agences.isEmpty)
            _buildEmptyState()
          else
            _buildAgencesList(),
        ],
      ),
    );
  }

  /// 🔄 État de chargement
  Widget _buildLoadingState() {
    return ,
            const SizedBox(height: 8),
            ,
            ),
          ],
        ),
      ),
    );
  }

  /// 📭 État vide
  Widget _buildEmptyState() {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: ,
            const SizedBox(height: 8),
            ,
            ),
            const SizedBox(height: 4),
            ,
            ),
          ],
        ),
      ),
    );
  }

  /// 📋 Liste des agences
  Widget _buildAgencesList() {
    return Column(
      children: _agences.map((agence) => _buildAgenceItem(agence)).toList(),
    );
  }

  /// 🏛️ Élément d'agence
  Widget _buildAgenceItem(Map<String, dynamic> agence) {
    final status = agence['status']?.toString().toLowerCase() ?? 'actif';
    final isActive = status == 'actif';
    
    return Container(
      margin: ,
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isActive ? const Color(0xFF10B981).withValues(alpha: 0.2) : Colors.grey[300]!,
        ),
      ),
      child: Row(
        children: [
          // Icône et statut
          Container(
            padding: .withValues(alpha: 0.1)
                  : Colors.grey[300],
              borderRadius: BorderRadius.circular(8),
            ),
            child:  : Colors.grey[600],
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          
          // Informations
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ,
                  ),
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    ,
                    const SizedBox(width: 4),
                    Expanded(
                      child: ({agence['gouvernorat'] ?? 'Gouvernorat'}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Badge de statut
          Container(
            padding:  : Colors.grey[400],
              borderRadius: BorderRadius.circular(12),
            ),
            child: ,
            ),
          ),
          
          // Bouton d'action
          const SizedBox(width: 8),
          IconButton(
            icon: const Icon(Icons.info),
            onPressed: () {
              // TODO: Naviguer vers les détails de l'agence
            },
            tooltip: 'Voir détails
