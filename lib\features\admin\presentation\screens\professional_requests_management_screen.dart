import 'package:flutter/material.dart';
import '../../models/professional_request_model_final.dart';
import '../../services/professional_request_management_service.dart';
import '../widgets/professional_request_card.dart';
import '../widgets/request_details_modal.dart';
import '../../../../core/theme/modern_theme.dart';

/// 📋 Écran de gestion des demandes professionnelles pour Super Admin
class ProfessionalRequestsManagementScreen extends StatefulWidget {
  const Text(\;

  @override
  State<ProfessionalRequestsManagementScreen> createState() => _ProfessionalRequestsManagementScreenState();
}

class _ProfessionalRequestsManagementScreenState extends State<ProfessionalRequestsManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = 'Contenu';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ModernTheme.backgroundColor,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          // Barre de recherche
          _buildSearchBar(),
          
          // Onglets
          _buildTabBar(),
          
          // Contenu des onglets
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildRequestsList('en_attente'),
                _buildRequestsList('acceptee'),
                _buildRequestsList('rejetee'),
                _buildRequestsList(null), // Toutes les demandes
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: _buildStatsButton(),
    );
  }

  /// 🔝 AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text("Titre"),
      ),
      backgroundColor: ModernTheme.primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.info),
        onPressed: () {
          // Rediriger vers le dashboard Super Admin moderne
          Navigator.pushReplacementNamed(context, '/super-admin-dashboard');
        },
      ),
      actions: [
        IconButton(
          onPressed: () {
            Navigator.pushNamed(context, '/admin/users');
          },
          icon: const Icon(Icons.info),
          tooltip: 'Gestion des utilisateurs',
        ),
        IconButton(
          onPressed: _showFilterDialog,
          icon: const Icon(Icons.info),
          tooltip: 'Filtres',
        ),
        IconButton(
          onPressed: _refreshData,
          icon: const Icon(Icons.info),
          tooltip: 'Actualiser',
        ),
      ],
    );
  }

  /// 🔍 Barre de recherche
  Widget _buildSearchBar() {
    return Container(
      padding: ,
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    _searchController.clear();
                    setState(() => _searchQuery = 'Contenu');
                  },
                  icon: const Icon(Icons.info),
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: ModernTheme.primaryColor, width: 2),
          ),
        ),
        onChanged: (value) {
          setState(() => _searchQuery = value);
        },
      ),
    );
  }

  /// 📑 Barre d'onglets
  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        labelColor: ModernTheme.primaryColor,
        unselectedLabelColor: ModernTheme.textLight,
        indicatorColor: ModernTheme.primaryColor,
        tabs: const [
            text: 'En attente',
          ),
          Tab(
            icon: const Icon(Icons.info),
            text: 'Approuvées',
          ),
          Tab(
            icon: const Icon(Icons.info),
            text: 'Rejetées',
          ),
          Tab(
            icon: const Icon(Icons.info),
            text: 'Toutes',
          ),
        ],
      ),
    );
  }

  /// 📋 Liste des demandes
  Widget _buildRequestsList(String? status) {
    return StreamBuilder<List<ProfessionalRequestModel>>(
      stream: ProfessionalRequestManagementService.getAllRequests(status: status),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (snapshot.hasError) {
          // Debug: Afficher l'erreur complète
          debugPrint('Erreur Firestore: ${snapshot.error}');
          debugPrint('StackTrace: ${snapshot.stackTrace}');

          return ,
                const SizedBox(height: 16),
                ,
                ),
                const SizedBox(height: 8),
                (1),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: ModernTheme.textLight,
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                ElevatedButton.const Icon(
                  onPressed: _refreshData,
                  icon: const Icon(Icons.info),
                  label: const Text('Réessayer'),
                ),
              ],
            ),
          );
        }

        final requests = snapshot.data ?? [];
        
        // Filtrer par recherche si nécessaire
        final filteredRequests = _searchQuery.isEmpty
            ? requests
            : requests.where((request) {
                final query = _searchQuery.toLowerCase();
                return request.nomComplet.toLowerCase().contains(query) ||
                       request.email.toLowerCase().contains(query) ||
                       request.tel.contains(query) ||
                       request.cin.contains(query) ||
                       request.roleFormate.toLowerCase().contains(query);
              }).toList();

        if (filteredRequests.isEmpty) {
          return ,
                const SizedBox(height: 16),
                ,
                ),
                if (_searchQuery.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  ,
                  ),
                ],
              ],
            ),
          );
        }

        return ListView.builder(
          padding:  {
            final request = filteredRequests[index];
            return ProfessionalRequestCard(
              request: request,
              onApprove: request.status == 'en_attente'
                  ? () => _approveRequest(request)
                  : null,
              onReject: request.status == 'en_attente'
                  ? () => _rejectRequest(request)
                  : null,
              onViewDetails: () => _viewRequestDetails(request),
            );
          },
        );
      },
    );
  }

  /// 📊 Bouton des statistiques
  Widget _buildStatsButton() {
    return FloatingActionButton.extended(
      onPressed: _showStatsDialog,
      backgroundColor: ModernTheme.primaryColor,
      foregroundColor: Colors.white,
      icon: const Icon(Icons.info),
      label: const Text('Statistiques'),
    );
  }

  /// ✅ Approuver une demande
  Future<void> _approveRequest(ProfessionalRequestModel request) async {
    final confirmed = await _showApprovalDialog(request);
    if (!confirmed) return;

    try {
      final success = await ProfessionalRequestManagementService.approveRequest(
        requestId: request.id,
        adminId: 'super_admin', // TODO: Récupérer l'ID de l'admin connecté
        commentaire: 'Demande approuvée par le Super Admin',
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: ({request.nomComplet} approuvée avec succès'),
            backgroundColor: ModernTheme.successColor,
            action: SnackBarAction(
              label: 'OK',
              textColor: Colors.white,
              onPressed: () {},
            ),
          ),
        );
      } else if (mounted) {
        throw Exception('Erreur lors de l\'approbation');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: ({e.toString()}'),
            backgroundColor: ModernTheme.errorColor,
          ),
        );
      }
    } finally {
      // Nettoyage si nécessaire
    }
  }

  /// ❌ Rejeter une demande
  Future<void> _rejectRequest(ProfessionalRequestModel request) async {
    final result = await _showRejectionDialog(request);
    if (result == null) return;

    try {
      final success = await ProfessionalRequestManagementService.rejectRequest(
        requestId: request.id,
        adminId: 'super_admin', // TODO: Récupérer l'ID de l'admin connecté
        motifRejet: result['motif']!,
        commentaire: result['commentaire'],
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: ({request.nomComplet} rejetée'),
            backgroundColor: ModernTheme.errorColor,
            action: SnackBarAction(
              label: 'OK',
              textColor: Colors.white,
              onPressed: () {},
            ),
          ),
        );
      } else if (mounted) {
        throw Exception('Erreur lors du rejet');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: ({e.toString()}'),
            backgroundColor: ModernTheme.errorColor,
          ),
        );
      }
    } finally {
      // Nettoyage si nécessaire
    }
  }

  /// 👁️ Voir les détails d'une demande
  void _viewRequestDetails(ProfessionalRequestModel request) {
    showDialog(
      context: context,
      builder: (context) => RequestDetailsModal(
        request: request,
        onRequestUpdated: () {
          setState(() {}); // Rafraîchir la liste
        },
      ),
    );
  }

  /// 💬 Dialog d'approbation
  Future<bool> _showApprovalDialog(ProfessionalRequestModel request) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Approuver la demande'),
        content: ({request.nomComplet} ?\n\n'
          'Un compte sera créé et un email de confirmation sera envoyé.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: ModernTheme.successColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Approuver'),
          ),
        ],
      ),
    ) ?? false;
  }

  /// 💬 Dialog de rejet
  Future<Map<String, String>?> _showRejectionDialog(ProfessionalRequestModel request) async {
    final motifController = TextEditingController();
    final commentaireController = TextEditingController();

    return await showDialog<Map<String, String>>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Rejeter la demande'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ({request.nomComplet}'),
            const SizedBox(height: 16),
            TextField(
              controller: motifController,
              decoration: const InputDecoration(
                labelText: 'Motif du rejet *',
                hintText: 'Ex: Documents incomplets',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 12),
            TextField(
              controller: commentaireController,
              decoration: const InputDecoration(
                labelText: 'Commentaire (optionnel)',
                hintText: 'Commentaire interne...',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              if (motifController.text.trim().isNotEmpty) {
                Navigator.pop(context, {
                  'motif': motifController.text.trim(),
                  'commentaire': commentaireController.text.trim(),
                });
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: ModernTheme.errorColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Rejeter'),
          ),
        ],
      ),
    );
  }

  /// 🔄 Actualiser les données
  void _refreshData() {
    setState(() {});
  }

  /// 🔍 Dialog de filtres
  void _showFilterDialog() {
    // TODO: Implémenter les filtres avancés
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filtres'),
        content: const Text('Filtres avancés à implémenter'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  /// 📊 Dialog des statistiques
  void _showStatsDialog() {
    // TODO: Implémenter les statistiques
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Statistiques'),
        content: const Text('Statistiques à implémenter'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer
