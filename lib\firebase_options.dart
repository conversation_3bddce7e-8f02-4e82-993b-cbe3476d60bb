// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyD2vejYDavUL9Rifv2UgDb4l2AFhXF8c_s',
    appId: '1:324863789443:web:52bec42558c4193e78ceb6',
    messagingSenderId: '324863789443',
    projectId: 'assuranceaccident-2c2fa',
    authDomain: 'assuranceaccident-2c2fa.firebaseapp.com',
    storageBucket: 'assuranceaccident-2c2fa.firebasestorage.app',
    measurementId: 'G-8FGZE2HWR2',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDWW5CCR27i58JP5rGsJ7xceV2LHQya0_A',
    appId: '1:324863789443:android:e4422e0659add27578ceb6',
    messagingSenderId: '324863789443',
    projectId: 'assuranceaccident-2c2fa',
    storageBucket: 'assuranceaccident-2c2fa.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCaVUCpbeQ4E_5AjOi0t6ISo1SvTO9eQ2Q',
    appId: '1:324863789443:ios:4434955323e641ff78ceb6',
    messagingSenderId: '324863789443',
    projectId: 'assuranceaccident-2c2fa',
    storageBucket: 'assuranceaccident-2c2fa.firebasestorage.app',
    androidClientId: '324863789443-3h0bro6npj9egucma049d2c4m6d65t93.apps.googleusercontent.com',
    iosClientId: '324863789443-25572umk62og0s0mbo838tit7vlfvk52.apps.googleusercontent.com',
    iosBundleId: 'com.example.constatTunisie',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCaVUCpbeQ4E_5AjOi0t6ISo1SvTO9eQ2Q',
    appId: '1:324863789443:ios:4434955323e641ff78ceb6',
    messagingSenderId: '324863789443',
    projectId: 'assuranceaccident-2c2fa',
    storageBucket: 'assuranceaccident-2c2fa.firebasestorage.app',
    androidClientId: '324863789443-3h0bro6npj9egucma049d2c4m6d65t93.apps.googleusercontent.com',
    iosClientId: '324863789443-25572umk62og0s0mbo838tit7vlfvk52.apps.googleusercontent.com',
    iosBundleId: 'com.example.constatTunisie',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyD2vejYDavUL9Rifv2UgDb4l2AFhXF8c_s',
    appId: '1:324863789443:web:1b5c480de3e94cca78ceb6',
    messagingSenderId: '324863789443',
    projectId: 'assuranceaccident-2c2fa',
    authDomain: 'assuranceaccident-2c2fa.firebaseapp.com',
    storageBucket: 'assuranceaccident-2c2fa.firebasestorage.app',
    measurementId: 'G-PBRS4ZN1V4',
  );
}
