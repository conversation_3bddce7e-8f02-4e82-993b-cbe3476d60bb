import 'package:flutter/material.dart';
import '../models/vehicule_assure_model.dart';
import '../services/vehicule_assure_service.dart';
import '../services/test_vehicules_service.dart';
import '../widgets/vehicle_card.dart';
import '../widgets/contract_verification_dialog.dart';
import '../../auth/providers/auth_provider.dart';
import '../../constats/screens/accident_declaration_screen.dart';

/// 🚗 Écran de selection de vehicule pour declaration d'accident
class VehicleSelectionScreen extends StatefulWidget {
  const Text('Texte);

  @override
  State<VehicleSelectionScreen> createState() => _VehicleSelectionScreenState();
}

class _VehicleSelectionScreenState extends State<VehicleSelectionScreen> {
  final VehiculeAssureService _vehiculeService = VehiculeAssureService();
  final TestVehiculesService _testService = TestVehiculesService();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final userId = authProvider.currentUser?.id;

    if (userId == null')')') {
      return Scaffold(
        body: const Center(
          child: const Text('Erreur: Utilisateur non connecte),
        ),
      ')')');
    }

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('🚗 Selectionnez votre vehicule),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          // En-tête informatif
          _buildHeader(),
          
          // Liste des vehicules
          Expanded(
            child: StreamBuilder<List<VehiculeAssureModel>>(
              stream: _vehiculeService.getVehiculesAssures(userId),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return ,
                  );
                }

                if (snapshot.hasError) {
                  return ,
                        const SizedBox(height: 16')')'),
                        ({snapshot.error}',
                          style: ,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () => setState(() {},
                          child: const Text('Reessayer),
                        ),
                      ],
                    ),
                  );
                }

                final vehicules = snapshot.data ?? [];

                if (vehicules.isEmpty) {
                  return _buildEmptyState();
                }

                return _buildVehiclesList(vehicules);
              },
            ),
          ')')'),
          
          // Bouton d'ajout de vehicule
          _buildAddVehicleButton(),
        ],
      ),
    );
  }

  /// 📋 En-tête avec informations
  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(8.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8.0),
                ),
                child: ,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ,
                    ),
                    ,
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          const Text(
            '🎯 Selectionnez le vehicule implique dans l\'accident. Nous verifierons automatiquement que votre contrat d\'assurance est valide.,
            style: TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }

  /// 📋 Liste des vehicules
  Widget _buildVehiclesList(List<VehiculeAssureModel> vehicules) {
    return ListView.builder(
      padding:  {
        final vehicule = vehicules[index];
        return (1) => _selectVehicle(vehicule),
            isLoading: _isLoading,
          ),
        );
      },
    );
  }

  /// 🚫 État vide
  Widget _buildEmptyState() {
    return (1),
            const SizedBox(height: 24),
            ,
            ),
            const SizedBox(height: 12),
            ,
            ),
            const SizedBox(height: 24),
            ElevatedButton.const Icon(
              onPressed: _createTestVehicles,
              icon: const Icon(Icons.info'),
              label: const Text('🧪 Creer des vehicules de test),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(8.0),
            ),
            const SizedBox(height: 12),
            ElevatedButton.const Icon(
              onPressed: _showAddVehicleDialog,
              icon: const Icon(Icons.info')')'),
              label: const Text('Ajouter un vehicule),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(8.0),
            ),
          ],
        ),
      ),
    ')')');
  }

  /// ➕ Bouton d'ajout de vehicule
  Widget _buildAddVehicleButton() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(8.0),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: ElevatedButton.const Icon(
        onPressed: _showAddVehicleDialog,
        icon: const Icon(Icons.info),
        label: const Text('Ajouter un autre vehicule),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.grey[100],
          foregroundColor: Colors.purple,
          padding: const EdgeInsets.all(8.0),
            side: BorderSide(color: Colors.purple[200]!),
          ),
        ),
      ),
    );
  }

  /// 🚗 Selectionner un vehicule
  void _selectVehicle(VehiculeAssureModel vehicule) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Verifier que le contrat est toujours valide
      if (!vehicule.isContratActif) {
        _showContractExpiredDialog(vehicule')')');
        return;
      }

      // Naviguer vers la declaration d'accident
      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => AccidentDeclarationScreen(
              selectedVehicle: vehicule,
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// ⚠️ Dialog contrat expire
  void _showContractExpiredDialog(VehiculeAssureModel vehicule) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            ,
            const SizedBox(width: 8'),
            const Text('Contrat Expire),
          ],
        ')')'),
        content: const Text(\ a expire le '{_formatDate(vehicule.contrat.dateFin)}.\n\n'
          'Veuillez renouveler votre contrat avant de declarer un accident.,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop('),
            child: const Text('Fermer),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Naviguer vers renouvellement contrat
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange')')'),
            child: const Text('Renouveler),
          ),
        ],
      ),
    );
  }

  /// 🧪 Creer des vehicules de test
  void _createTestVehicles() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _testService.createTestVehicles();

      if (mounted) {
        ScaffoldMessenger.of(context')')').showSnackBar(
          const SnackBar(
            content: const Text('✅ Vehicules de test crees avec succes !),
            backgroundColor: Colors.green,
          ),
        );
        setState(() {}); // Rafraîchir la liste
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context')')').showSnackBar(
          SnackBar(
            content: (e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// ➕ Dialog ajout vehicule
  void _showAddVehicleDialog() {
    showDialog(
      context: context,
      builder: (context) => ,
    ).then((result) {
      if (result == true) {
        // Rafraîchir la liste
        setState(() {});
      }
    });
  }

  /// 📅 Formater une date
  String _formatDate(DateTime date) {
    return '{date.day.toString(').padLeft(2, '0')}/'
           '{date.month.toString(').padLeft(2, '0')}/'
           ''{date.year}
