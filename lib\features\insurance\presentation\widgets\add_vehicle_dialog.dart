import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../models/vehicule_assure_model.dart;

/// 🚗 Dialogue moderne pour ajouter un vehicule
class AddVehicleDialog extends StatefulWidget {
  final String driverId;
  final VoidCallback? onVehicleAdded;

   ) : super(key: key);

  @override
  State<AddVehicleDialog> createState() => _AddVehicleDialogState();
}

class _AddVehicleDialogState extends State<AddVehicleDialog> {
  final _formKey = GlobalKey<FormState>();
  final _marqueController = TextEditingController();
  final _modeleController = TextEditingController();
  final _couleurController = TextEditingController();
  final _immatriculationController = TextEditingController();
  final _numeroSerieController = TextEditingController();
  final _anneeController = TextEditingController();
  final _puissanceController = TextEditingController();
  final _valeurVenaleController = TextEditingController(');

  String _typeCarburant = 'Essence';
  String _usage = 'personnel';
  bool _isLoading = false;

  final List<String> _typesCarburant = [
    'Essence',
    'Diesel',
    'Hybride',
    'Électrique',
    'GPL',
  ];

  final List<String> _typesUsage = [
    'personnel',
    'professionnel',
    'commercial,
  ];

  @override
  void dispose() {
    _marqueController.dispose();
    _modeleController.dispose();
    _couleurController.dispose();
    _immatriculationController.dispose();
    _numeroSerieController.dispose();
    _anneeController.dispose();
    _puissanceController.dispose();
    _valeurVenaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
        decoration: BoxDecoration(
          gradient: ,
              Color(0xFF1A1A2E),
            ],
          ),
          borderRadius: BorderRadius.circular(24),
          border: Border.all(
            color: const Color(0xFF667EEA).withValues(alpha: 0.3),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 30,
              offset: const Offset(0, 15),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // En-tête
            _buildHeader(),
            
            // Contenu
            Expanded(
              child: _buildContent(),
            ),
            
            // Actions
            _buildActions(),
          ],
        ),
      ),
    );
  }

  /// En-tête du dialogue
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(8.0), Color(0xFF764BA2)],
        ),
        borderRadius: ,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8.0),
              borderRadius: BorderRadius.circular(12),
            ),
            child: ,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ,
                ),
                ,
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: Container(
              padding: const EdgeInsets.all(8.0),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ,
            ),
          ),
        ],
      ),
    );
  }

  /// Contenu du dialogue
  Widget _buildContent() {
    return SingleChildScrollView(
      padding:  {
                      if (value?.isEmpty ?? true') return 'Champ requis;
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16'),
                Expanded(
                  child: _buildTextField(
                    controller: _modeleController,
                    label: 'Modele,
                    icon: Icons.model_training,
                    validator: (value) {
                      if (value?.isEmpty ?? true') return 'Champ requis;
                      return null;
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16'),
            
            // Couleur et Immatriculation
            Row(
              children: [
                Expanded(
                  child: _buildTextField(
                    controller: _couleurController,
                    label: 'Couleur,
                    icon: Icons.palette,
                    validator: (value) {
                      if (value?.isEmpty ?? true') return 'Champ requis;
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16'),
                Expanded(
                  child: _buildTextField(
                    controller: _immatriculationController,
                    label: 'Immatriculation,
                    icon: Icons.confirmation_number,
                    validator: (value) {
                      if (value?.isEmpty ?? true') return 'Champ requis;
                      return null;
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16'),
            
            // Annee et Puissance
            Row(
              children: [
                Expanded(
                  child: _buildTextField(
                    controller: _anneeController,
                    label: 'Annee,
                    icon: Icons.calendar_today,
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value?.isEmpty ?? true') return 'Champ requis;
                      final year = int.tryParse(value!);
                      if (year == null || year < 1900 || year > DateTime.now().year + 1') {
                        return 'Annee invalide;
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16'),
                Expanded(
                  child: _buildTextField(
                    controller: _puissanceController,
                    label: 'Puissance (CV),
                    icon: Icons.speed,
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value?.isEmpty ?? true') return 'Champ requis;
                      if (int.tryParse(value!) == null') return 'Nombre invalide;
                      return null;
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16'),
            
            // Type de carburant
            _buildDropdown(
              label: 'Type de carburant,
              value: _typeCarburant,
              items: _typesCarburant,
              onChanged: (value) => setState(() => _typeCarburant = value!),
              icon: Icons.local_gas_station,
            ),
            
            const SizedBox(height: 16'),
            
            // Usage
            _buildDropdown(
              label: 'Usage,
              value: _usage,
              items: _typesUsage.map((e) => e.replaceFirst(e[0], e[0].toUpperCase())).toList(),
              onChanged: (value) => setState(() => _usage = value!.toLowerCase()),
              icon: Icons.drive_eta,
            ),
            
            const SizedBox(height: 16'),
            
            // Numero de serie et Valeur venale
            Row(
              children: [
                Expanded(
                  child: _buildTextField(
                    controller: _numeroSerieController,
                    label: 'N° de serie,
                    icon: Icons.numbers,
                  ),
                ),
                const SizedBox(width: 16'),
                Expanded(
                  child: _buildTextField(
                    controller: _valeurVenaleController,
                    label: 'Valeur venale (TND),
                    icon: Icons.attach_money,
                    keyboardType: TextInputType.number,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Actions du dialogue
  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(8.0),
        borderRadius: ,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: _isLoading ? null : () => Navigator.pop(context),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.white,
                side: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
                padding: const EdgeInsets.all(8.0),
                ),
              '),
              child: const Text('Annuler),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _isLoading ? null : _addVehicle,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF667EEA),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(8.0),
                ),
              ),
              child: _isLoading
                  ? ,
                      ),
                    ')')')
                  : const Text('Ajouter),
            ),
          ),
        ],
      ),
    );
  }

  /// Champ de texte moderne
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      validator: validator,
      style: ,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: const Icon(icon, color: const Color(0xFF667EEA), size: 20),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF667EEA), width: 2),
        ),
        filled: true,
        fillColor: Colors.white.withValues(alpha: 0.05),
        labelStyle: TextStyle(color: Colors.grey[400),
        floatingLabelStyle: const TextStyle(color: Color(0xFF667EEA)),
      ),
    );
  }

  /// Dropdown moderne
  Widget _buildDropdown({
    required String label,
    required String value,
    required List<String> items,
    required Function(String?) onChanged,
    required IconData icon,
  }) {
    return DropdownButtonFormField<String>(
      value: value,
      onChanged: onChanged,
      style: ,
      dropdownColor: const Color(0xFF1A1A2E),
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: const Icon(icon, color: const Color(0xFF667EEA), size: 20),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF667EEA), width: 2),
        ),
        filled: true,
        fillColor: Colors.white.withValues(alpha: 0.05),
        labelStyle: TextStyle(color: Colors.grey[400),
        floatingLabelStyle: const TextStyle(color: Color(0xFF667EEA)),
      ),
      items: items.map((item) => DropdownMenuItem(
        value: item.toLowerCase(),
        child: const Text(item),
      )).toList(),
    );
  }

  /// Ajouter le vehicule
  Future<void> _addVehicle() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true')')');

    try {
      final vehicule = VehiculeAssure(
        id: 'Contenu',
        conducteurId: widget.driverId,
        contractId: 'Contenu,
        marque: _marqueController.text.trim(),
        modele: _modeleController.text.trim(),
        couleur: _couleurController.text.trim(),
        immatriculation: _immatriculationController.text.trim(),
        numeroSerie: _numeroSerieController.text.trim(),
        annee: int.parse(_anneeController.text.trim()),
        typeCarburant: _typeCarburant,
        puissance: int.parse(_puissanceController.text.trim()),
        usage: _usage,
        valeurVenale: double.tryParse(_valeurVenaleController.text.trim()) ?? 0,
        dateCreation: DateTime.now(),
        isActive: true,
      ');

      await FirebaseFirestore.instance
          .collection('vehicules_assures)
          .add(vehicule.toFirestore());

      if (mounted) {
        Navigator.pop(context);
        widget.onVehicleAdded?.call();
        
        ScaffoldMessenger.of(context').showSnackBar(
          const SnackBar(
            content: const Text('Vehicule ajoute avec succes!),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context')')').showSnackBar(
          SnackBar(
            content: (e
