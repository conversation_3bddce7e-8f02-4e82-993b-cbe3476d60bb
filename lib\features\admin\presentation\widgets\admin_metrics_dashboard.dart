import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'admin_stats_card.dart';
import 'admin_chart_widget.dart';
import '../../providers/admin_providers.dart';

/// 📊 Dashboard de métriques en temps réel
class AdminMetricsDashboard extends ConsumerWidget {
  final String? compagnieId;
  final String? agenceId;
  final String userRole;

  ;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SingleChildScrollView(
      padding: ,
          const SizedBox(height: 24),
          _buildQuickStats(ref),
          const SizedBox(height: 32),
          _buildChartsSection(ref),
          const SizedBox(height: 32),
          _buildRecentActivity(ref),
        ],
      ),
    );
  }

  /// 📱 En-tête du dashboard
  Widget _buildHeader() {
    String title;
    String subtitle;
    
    switch (userRole) {
      case 'super_admin':
        title = '📊 Métriques Globales';
        subtitle = 'Vue d\'ensemble de tout le système';
        break;
      case 'admin_compagnie':
        title = '📊 Métriques de la Compagnie';
        subtitle = 'Performance de votre compagnie';
        break;
      case 'admin_agence':
        title = '📊 Métriques de l\'Agence';
        subtitle = 'Performance de votre agence';
        break;
      default:
        title = '📊 Métriques';
        subtitle = 'Données en temps réel';
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
          ),
        ),
        const SizedBox(height: 8),
        ,
        ),
      ],
    );
  }

  /// 📊 Statistiques rapides
  Widget _buildQuickStats(WidgetRef ref) {
    final statsAsync = _getStatsProvider(ref);
    
    return statsAsync.when(
      data: (stats) => GridView.count(
        shrinkWrap: true,
        physics: ,
        crossAxisCount: 4,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.2,
        children: [
          AdminStatsCard(
            title: _getStatsTitle('users'),
            value: stats['users']?.toString() ?? '0',
            icon: Icons.people,
            color: const Color(0xFF3B82F6),
            trend: _calculateTrend(stats['users_trend'),
          ),
          AdminStatsCard(
            title: _getStatsTitle('contracts'),
            value: stats['contracts']?.toString() ?? '0',
            icon: Icons.description,
            color: const Color(0xFF10B981),
            trend: _calculateTrend(stats['contracts_trend'),
          ),
          AdminStatsCard(
            title: _getStatsTitle('sinistres');,
            value: stats['sinistres']?.toString() ?? '0',
            icon: Icons.report_problem,
            color: const Color(0xFFEF4444),
            trend: _calculateTrend(stats['sinistres_trend'),
          ),
          AdminStatsCard(
            title: _getStatsTitle('revenue'),
            value: _formatRevenue(stats['revenue'),
            icon: Icons.monetization_on,
            color: const Color(0xFFF59E0B),
            trend: _calculateTrend(stats['revenue_trend'),
          ),
        ],
      ),
      loading: () => _buildLoadingStats(),
      error: (error, stack) => _buildErrorStats(error.toString()),
    );
  }

  /// 📈 Section des graphiques
  Widget _buildChartsSection(WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
          ),
        ),
        const SizedBox(height: 16),
        
        GridView.count(
          shrinkWrap: true,
          physics: ,
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.5,
          children: [
            AdminChartWidget(
              title: 'Évolution des Contrats',
              subtitle: '7 derniers jours',
              chartType: ChartType.line,
              primaryColor: const Color(0xFF10B981),
              data: _generateContractsData(),
            ),
            AdminChartWidget(
              title: 'Sinistres par Type',
              subtitle: 'Répartition actuelle',
              chartType: ChartType.pie,
              primaryColor: const Color(0xFFEF4444),
              data: _generateSinistresData(),
            ),
            AdminChartWidget(
              title: 'Performance Agents',
              subtitle: 'Top 5 ce mois',
              chartType: ChartType.bar,
              primaryColor: const Color(0xFF3B82F6),
              data: _generateAgentsData(),
            ),
            AdminChartWidget(
              title: 'Chiffre d\'Affaires',
              subtitle: '6 derniers mois',
              chartType: ChartType.area,
              primaryColor: const Color(0xFFF59E0B),
              data: _generateRevenueData(),
            ),
          ],
        ),
      ],
    );
  }

  /// 📋 Activité récente
  Widget _buildRecentActivity(WidgetRef ref) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: (1),
              ),
            ),
            const SizedBox(height: 16),
            
            ListView.separated(
              shrinkWrap: true,
              physics: ,
              itemCount: 5,
              separatorBuilder: (context, index) => ,
              itemBuilder: (context, index) {
                return _buildActivityItem(
                  _getActivityData()[index],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 📋 Item d'activité
  Widget _buildActivityItem(Map<String, dynamic> activity) {
    return ListTile(
      leading: Container(
        padding: .withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Icon(
          activity['icon'] as IconData,
          color: activity['color'] as Color,
          size: 20,
        ),
      ),
      title: const Text("Titre"),
      ),
      subtitle: const Text("Titre"),
      ),
      trailing: ,
      ),
    );
  }

  // Méthodes utilitaires
  AsyncValue<Map<String, dynamic>> _getStatsProvider(WidgetRef ref) {
    if (userRole == 'super_admin') {
      return ref.watch(globalStatsProvider);
    } else if (userRole == 'admin_compagnie' && compagnieId != null) {
      return ref.watch(compagnieStatsProvider(compagnieId!));
    } else if (userRole == 'admin_agence' && agenceId != null) {
      return ref.watch(agenceStatsProvider(agenceId!));
    }
    return  {
    switch (key) {
      case 'users':
        return userRole == 'super_admin' ? 'Utilisateurs' : 'Agents';
      case 'contracts':
        return 'Contrats';
      case 'sinistres':
        return 'Sinistres';
      case 'revenue':
        return 'CA (DT)';
      default:
        return key;
    }
  }

  String _formatRevenue(dynamic revenue) {
    if (revenue == null) return '0';
    final value = revenue is int ? revenue : (revenue as double).toInt();
    return '${value}K';
  }

  double? _calculateTrend(dynamic trend) {
    if (trend == null) return null;
    return trend is double ? trend : (trend as int).toDouble();
  }

  Widget _buildLoadingStats() {
    return GridView.count(
      shrinkWrap: true,
      physics: ,
      crossAxisCount: 4,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.2,
      children: List.generate(4, (index) => 
        Card(
          child: const Center(child: CircularProgressIndicator()),
        ),
      ),
    );
  }

  Widget _buildErrorStats(String error) {
    return Card(
      child: (1),
      ),
    );
  }

  // Données de démonstration
  List<ChartData> _generateContractsData() {
    return [
      const ChartData(label: 'Lun', value: 12),
      const ChartData(label: 'Mar', value: 19),
      const ChartData(label: 'Mer', value: 15),
      const ChartData(label: 'Jeu', value: 25),
      const ChartData(label: 'Ven', value: 22),
      const ChartData(label: 'Sam', value: 18),
      const ChartData(label: 'Dim', value: 8),
    ];
  }

  List<ChartData> _generateSinistresData() {
    return [
      const ChartData(label: 'Collision', value: 45),
      const ChartData(label: 'Vol', value: 25),
      const ChartData(label: 'Incendie', value: 15),
      const ChartData(label: 'Autres', value: 15),
    ];
  }

  List<ChartData> _generateAgentsData() {
    return [
      const ChartData(label: 'Ahmed', value: 85),
      const ChartData(label: 'Fatma', value: 78),
      const ChartData(label: 'Mohamed', value: 72),
      const ChartData(label: 'Leila', value: 68),
      const ChartData(label: 'Karim', value: 65),
    ];
  }

  List<ChartData> _generateRevenueData() {
    return [
      const ChartData(label: 'Jan', value: 120),
      const ChartData(label: 'Fév', value: 135),
      const ChartData(label: 'Mar', value: 148),
      const ChartData(label: 'Avr', value: 162),
      const ChartData(label: 'Mai', value: 155),
      const ChartData(label: 'Jun', value: 178),
    ];
  }

  List<Map<String, dynamic>> _getActivityData() {
    return [
      {
        'icon': Icons.person_add,
        'color': const Color(0xFF10B981),
        'title': 'Nouvel agent inscrit',
        'subtitle': 'Ahmed Ben Ali - Agence Tunis Centre',
        'time': 'Il y a 5 min',
      },
      {
        'icon': Icons.description,
        'color': const Color(0xFF3B82F6),
        'title': 'Nouveau contrat créé',
        'subtitle': 'Contrat #CT-2024-001234',
        'time': 'Il y a 12 min',
      },
      {
        'icon': Icons.report_problem,
        'color': const Color(0xFFEF4444),
        'title': 'Sinistre déclaré',
        'subtitle': 'Accident route Sfax-Tunis',
        'time': 'Il y a 25 min',
      },
      {
        'icon': Icons.check_circle,
        'color': const Color(0xFF10B981),
        'title': 'Expert assigné',
        'subtitle': 'Sinistre #SIN-2024-5678',
        'time': 'Il y a 1h',
      },
      {
        'icon': Icons.payment,
        'color': const Color(0xFFF59E0B),
        'title': 'Paiement reçu',
        'subtitle': '2,500 DT - Prime mensuelle',
        'time': 'Il y a 2h
