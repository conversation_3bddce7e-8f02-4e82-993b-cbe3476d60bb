import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// 📊 Résultat d'importation
class ImportResult {
  final bool success;
  final int totalRows;
  final int successCount;
  final int errorCount;
  final List<String> errors;
  final String dataType;
  final List<Map<String, dynamic>> createdData;

  ImportResult({
    required this.success,
    required this.totalRows,
    required this.successCount,
    required this.errorCount,
    required this.errors,
    required this.dataType,
    this.createdData = const [],
  });
}

/// 📊 Service CSV spécialisé pour les données d'assurance
class InsuranceCsvService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 🔍 Analyser et importer des données CSV d'assurance
  static Future<ImportResult> importInsuranceData(String csvContent) async {
    try {
      debugPrint('[INSURANCE_CSV] 🚀 Début importation données assurance...');

      // Parser le CSV
      final lines = csvContent.trim().split('\n');
      if (lines.isEmpty) {
        return ImportResult(
          success: false,
          totalRows: 0,
          successCount: 0,
          errorCount: 0,
          errors: ['Fichier CSV vide'],
          dataType: 'unknown',
        );
      }

      // Extraire les en-têtes
      final headers = lines[0].split(',').map((h) => h.trim().toLowerCase()).toList();
      final dataRows = lines.skip(1).map((line) => line.split(',')).toList();

      debugPrint('[INSURANCE_CSV] 📊 Headers détectés: $headers');
      debugPrint('[INSURANCE_CSV] 📊 ${dataRows.length} lignes de données');

      // Détecter le type de données
      final dataType = _detectDataType(headers);
      debugPrint('[INSURANCE_CSV] 🔍 Type détecté: $dataType');

      // Importer selon le type
      switch (dataType) {
        case 'compagnies':
          return await _importCompagnies(headers, dataRows);
        case 'agences':
          return await _importAgences(headers, dataRows);
        case 'agents':
          return await _importAgents(headers, dataRows);
        case 'vehicules':
          return await _importVehicules(headers, dataRows);
        case 'contrats':
          return await _importContrats(headers, dataRows);
        case 'sinistres':
          return await _importSinistres(headers, dataRows);
        default:
          return await _importGeneric(headers, dataRows, dataType);
      }

    } catch (e) {
      debugPrint('[INSURANCE_CSV] ❌ Erreur importation: $e');
      return ImportResult(
        success: false,
        totalRows: 0,
        successCount: 0,
        errorCount: 1,
        errors: ['Erreur générale: $e'],
        dataType: 'error',
      );
    }
  }

  /// 🔍 Détecter le type de données CSV
  static String _detectDataType(List<String> headers) {
    final headerStr = headers.join(' ').toLowerCase();

    // Compagnies d'assurance
    if (headerStr.contains('compagnie') || 
        headerStr.contains('assurance') ||
        (headerStr.contains('nom') && headerStr.contains('code'))) {
      return 'compagnies';
    }

    // Agences
    if (headerStr.contains('agence') || 
        (headerStr.contains('nom') && headerStr.contains('ville'))) {
      return 'agences';
    }

    // Agents
    if (headerStr.contains('agent') || 
        (headerStr.contains('prenom') && headerStr.contains('nom'))) {
      return 'agents';
    }

    // Véhicules
    if (headerStr.contains('vehicule') || 
        headerStr.contains('immatriculation') ||
        headerStr.contains('marque') ||
        headerStr.contains('modele')) {
      return 'vehicules';
    }

    // Contrats
    if (headerStr.contains('contrat') || 
        headerStr.contains('police') ||
        headerStr.contains('prime')) {
      return 'contrats';
    }

    // Sinistres
    if (headerStr.contains('sinistre') || 
        headerStr.contains('accident') ||
        headerStr.contains('constat')) {
      return 'sinistres';
    }

    return 'generic';
  }

  /// 🏢 Importer des compagnies d'assurance
  static Future<ImportResult> _importCompagnies(List<String> headers, List<List<dynamic>> dataRows) async {
    int successCount = 0;
    int errorCount = 0;
    List<String> errors = [];
    List<Map<String, dynamic>> createdData = [];

    for (int i = 0; i < dataRows.length; i++) {
      try {
        final row = dataRows[i];
        final data = _mapRowToData(headers, row);

        // Champs obligatoires pour compagnie
        if (!data.containsKey('nom') || data['nom'].toString().isEmpty) {
          errors.add('Ligne ${i + 2}: Nom de compagnie manquant');
          errorCount++;
          continue;
        }

        // Générer un code si manquant
        if (!data.containsKey('code') || data['code'].toString().isEmpty) {
          data['code'] = data['nom'].toString().toUpperCase().replaceAll(' ', 'Contenu').substring(0, 4);
        }

        final compagnieId = data['code'].toString().toUpperCase();
        final compagnieData = {
          'id': compagnieId,
          'nom': data['nom'],
          'code': compagnieId,
          'adresse': data['adresse'] ?? 'Contenu',
          'telephone': data['telephone'] ?? data['tel'] ?? 'Contenu',
          'email': data['email'] ?? 'Contenu',
          'ville': data['ville'] ?? 'Contenu',
          'pays': 'Tunisie',
          'status': 'actif',
          'created_at': FieldValue.serverTimestamp(),
          'imported_from': 'csv',
          'import_date': DateTime.now().toIso8601String(),
        };

        // Essayer plusieurs collections
        bool saved = await _saveToMultipleCollections(['companies', 'compagnies_assurance'], compagnieId, compagnieData);
        
        if (saved) {
          successCount++;
          createdData.add(compagnieData);
          debugPrint('[INSURANCE_CSV] ✅ Compagnie créée: ${compagnieData['nom']}');
        } else {
          errors.add('Ligne ${i + 2}: Impossible de sauvegarder ${data['nom']}');
          errorCount++;
        }

      } catch (e) {
        errors.add('Ligne ${i + 2}: Erreur - $e');
        errorCount++;
      }
    }

    return ImportResult(
      success: successCount > 0,
      totalRows: dataRows.length,
      successCount: successCount,
      errorCount: errorCount,
      errors: errors,
      dataType: 'compagnies',
      createdData: createdData,
    );
  }

  /// 🏪 Importer des agences
  static Future<ImportResult> _importAgences(List<String> headers, List<List<dynamic>> dataRows) async {
    int successCount = 0;
    int errorCount = 0;
    List<String> errors = [];
    List<Map<String, dynamic>> createdData = [];

    for (int i = 0; i < dataRows.length; i++) {
      try {
        final row = dataRows[i];
        final data = _mapRowToData(headers, row);

        // Champs obligatoires
        if (!data.containsKey('nom') || data['nom'].toString().isEmpty) {
          errors.add('Ligne ${i + 2}: Nom d\'agence manquant');
          errorCount++;
          continue;
        }

        final agenceId = 'agence_${DateTime.now().millisecondsSinceEpoch}_$i';
        final agenceData = {
          'id': agenceId,
          'nom': data['nom'],
          'compagnieId': data['compagnie'] ?? data['compagnieid'] ?? 'UNKNOWN',
          'adresse': data['adresse'] ?? 'Contenu',
          'ville': data['ville'] ?? 'Contenu',
          'telephone': data['telephone'] ?? data['tel'] ?? 'Contenu',
          'responsable': data['responsable'] ?? 'Contenu',
          'status': 'actif',
          'created_at': FieldValue.serverTimestamp(),
          'imported_from': 'csv',
          'import_date': DateTime.now().toIso8601String(),
        };

        bool saved = await _saveToMultipleCollections(['agencies', 'agences'], agenceId, agenceData);
        
        if (saved) {
          successCount++;
          createdData.add(agenceData);
          debugPrint('[INSURANCE_CSV] ✅ Agence créée: ${agenceData['nom']}');
        } else {
          errors.add('Ligne ${i + 2}: Impossible de sauvegarder ${data['nom']}');
          errorCount++;
        }

      } catch (e) {
        errors.add('Ligne ${i + 2}: Erreur - $e');
        errorCount++;
      }
    }

    return ImportResult(
      success: successCount > 0,
      totalRows: dataRows.length,
      successCount: successCount,
      errorCount: errorCount,
      errors: errors,
      dataType: 'agences',
      createdData: createdData,
    );
  }

  /// 📊 Importation générique
  static Future<ImportResult> _importGeneric(List<String> headers, List<List<dynamic>> dataRows, String dataType) async {
    int successCount = 0;
    int errorCount = 0;
    List<String> errors = [];
    List<Map<String, dynamic>> createdData = [];

    for (int i = 0; i < dataRows.length; i++) {
      try {
        final row = dataRows[i];
        final data = _mapRowToData(headers, row);

        final docId = '${dataType}_${DateTime.now().millisecondsSinceEpoch}_$i';
        final docData = {
          ...data,
          'id': docId,
          'data_type': dataType,
          'created_at': FieldValue.serverTimestamp(),
          'imported_from': 'csv',
          'import_date': DateTime.now().toIso8601String(),
        };

        bool saved = await _saveToMultipleCollections(['csv_imports', 'imported_data'], docId, docData);
        
        if (saved) {
          successCount++;
          createdData.add(docData);
        } else {
          errors.add('Ligne ${i + 2}: Impossible de sauvegarder');
          errorCount++;
        }

      } catch (e) {
        errors.add('Ligne ${i + 2}: Erreur - $e');
        errorCount++;
      }
    }

    return ImportResult(
      success: successCount > 0,
      totalRows: dataRows.length,
      successCount: successCount,
      errorCount: errorCount,
      errors: errors,
      dataType: dataType,
      createdData: createdData,
    );
  }

  /// 🔄 Mapper une ligne CSV vers un objet de données
  static Map<String, dynamic> _mapRowToData(List<String> headers, List<dynamic> row) {
    Map<String, dynamic> data = {};
    
    for (int i = 0; i < headers.length && i < row.length; i++) {
      final header = headers[i].toLowerCase().trim();
      final value = row[i]?.toString().trim() ?? 'Contenu';
      
      if (value.isNotEmpty) {
        data[header] = value;
      }
    }
    
    return data;
  }

  /// 💾 Sauvegarder dans plusieurs collections (fallback)
  static Future<bool> _saveToMultipleCollections(List<String> collections, String docId, Map<String, dynamic> data) async {
    for (String collection in collections) {
      try {
        await _firestore
            .collection(collection)
            .doc(docId)
            .set(data)
            .timeout(const Duration(seconds: 10));
        
        debugPrint('[INSURANCE_CSV] ✅ Sauvegardé dans collection: $collection');
        return true;
      } catch (e) {
        debugPrint('[INSURANCE_CSV] ❌ Échec collection $collection: $e');
        continue;
      }
    }
    return false;
  }

  // Méthodes d'importation spécialisées (à implémenter selon vos besoins)
  static Future<ImportResult> _importAgents(List<String> headers, List<List<dynamic>> dataRows) async {
    // TODO: Implémenter l'importation d'agents
    return _importGeneric(headers, dataRows, 'agents');
  }

  static Future<ImportResult> _importVehicules(List<String> headers, List<List<dynamic>> dataRows) async {
    // TODO: Implémenter l'importation de véhicules
    return _importGeneric(headers, dataRows, 'vehicules');
  }

  static Future<ImportResult> _importContrats(List<String> headers, List<List<dynamic>> dataRows) async {
    // TODO: Implémenter l'importation de contrats
    return _importGeneric(headers, dataRows, 'contrats');
  }

  static Future<ImportResult> _importSinistres(List<String> headers, List<List<dynamic>> dataRows) async {
    // TODO: Implémenter l'importation de sinistres
    return _importGeneric(headers, dataRows, 'sinistres');
  }
}
