import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_theme.dart';
import '../providers/dashboard_data_provider.dart';

/// 🕒 Widget pour afficher l'activité récente
class RecentActivityCard extends ConsumerWidget {
  const Text(\;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final recentActivity = ref.watch(recentActivityProvider);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: (1),
                ),
                TextButton(
                  onPressed: () {
                    // TODO: Naviguer vers la page complète d'activité
                  },
                  child: const Text('Voir tout'),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Contenu basé sur les données réelles
            recentActivity.when(
              data: (activities) {
                if (activities.isEmpty) {
                  return ,
                        const SizedBox(height: 8),
                        ,
                        ),
                      ],
                    ),
                  );
                }

                return Column(
                  children: activities.asMap().entries.map((entry) {
                    final index = entry.key;
                    final activity = entry.value;

                    return Column(
                      children: [
                        _buildActivityItem(
                          icon: activity['icon'] as IconData,
                          color: activity['color'] as Color,
                          title: activity['title'] as String,
                          subtitle: activity['subtitle'] as String,
                          time: activity['time'] as String,
                        ),
                        if (index < activities.length - 1)
                          const Divider(height: 24),
                      ],
                    );
                  }).toList(),
                );
              },
              loading: () => const Center(
                child: Column(
                  children: [
                    CircularProgressIndicator(),
                    const SizedBox(height: 8),
                    const Text('Chargement de l\'activité...'),
                  ],
                ),
              ),
              error: (error, stack) => ,
                    const SizedBox(height: 8),
                    ({error.toString()}
