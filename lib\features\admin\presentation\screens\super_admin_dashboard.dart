import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/admin_providers.dart';
import '../../models/admin_models.dart';
import '../widgets/admin_stats_card.dart';
import '../widgets/admin_data_table.dart';
import '../widgets/admin_action_button.dart';

/// 🔰 Super Admin Dashboard - Gestion complète du système
class SuperAdminDashboard extends ConsumerStatefulWidget {
  const Text(\;

  @override
  ConsumerState<SuperAdminDashboard> createState() => _SuperAdminDashboardState();
}

class _SuperAdminDashboardState extends ConsumerState<SuperAdminDashboard>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 7, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildHomeTab(),
                _buildCompagniesTab(),
                _buildAgencesTab(),
                _buildUsersTab(),
                _buildProfessionalRequestsTab(),
                _buildSinistresTab(),
                _buildSettingsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 📱 AppBar moderne
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: const Color(0xFF1E293B),
      title: Row(
        children: [
          ,
          const SizedBox(width: 12),
          ,
          ),
        ],
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.info),
          onPressed: () => _showNotifications(),
        ),
        IconButton(
          icon: const Icon(Icons.info),
          onPressed: () => _logout(),
        ),
        const SizedBox(width: 16),
      ],
    );
  }

  /// 📋 TabBar élégante
  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        indicatorColor: const Color(0xFF3B82F6),
        labelColor: const Color(0xFF3B82F6),
        unselectedLabelColor: const Color(0xFF64748B),
        labelStyle: ,
        tabs: const [ text: 'Accueil'),
          Tab(icon: const Icon(Icons.info), text: 'Compagnies'),
          Tab(icon: const Icon(Icons.info), text: 'Agences'),
          Tab(icon: const Icon(Icons.info), text: 'Utilisateurs'),
          Tab(icon: const Icon(Icons.info), text: 'Demandes'),
          Tab(icon: const Icon(Icons.info), text: 'Sinistres'),
          Tab(icon: const Icon(Icons.info), text: 'Paramètres'),
        ],
      ),
    );
  }

  /// 🏠 Onglet Accueil - Statistiques globales
  Widget _buildHomeTab() {
    final statsAsync = ref.watch(globalStatsProvider);
    
    return statsAsync.when(
      data: (stats) => SingleChildScrollView(
        padding: ,
              ),
            ),
            const SizedBox(height: 24),
            
            // Cartes de statistiques
            GridView.count(
              shrinkWrap: true,
              physics: ,
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 1.5,
              children: [
                AdminStatsCard(
                  title: 'Compagnies',
                  value: stats['compagnies']?.toString() ?? '0',
                  icon: Icons.business,
                  color: const Color(0xFF3B82F6),
                ),
                AdminStatsCard(
                  title: 'Agences',
                  value: stats['agences']?.toString() ?? '0',
                  icon: Icons.store,
                  color: const Color(0xFF10B981),
                ),
                AdminStatsCard(
                  title: 'Utilisateurs',
                  value: stats['users']?.toString() ?? '0',
                  icon: Icons.people,
                  color: const Color(0xFF8B5CF6),
                ),
                AdminStatsCard(
                  title: 'Contrats',
                  value: stats['contracts']?.toString() ?? '0',
                  icon: Icons.description,
                  color: const Color(0xFFF59E0B),
                ),
                AdminStatsCard(
                  title: 'Sinistres',
                  value: stats['sinistres']?.toString() ?? '0',
                  icon: Icons.report_problem,
                  color: const Color(0xFFEF4444),
                ),
                AdminStatsCard(
                  title: 'Experts',
                  value: '12', // TODO: Calculer depuis les users
                  icon: Icons.engineering,
                  color: const Color(0xFF06B6D4),
                ),
              ],
            ),
            
            const SizedBox(height: 32),
            
            // Actions  ,
              ),
            ),
            const SizedBox(height: 16),
            
            Wrap(
              spacing: 16,
              runSpacing: 16,
              children: [
                AdminActionButton(
                  title: 'Nouvelle Compagnie',
                  icon: Icons.add_business,
                  color: const Color(0xFF3B82F6),
                  onPressed: () => _createCompagnie(),
                ),
                AdminActionButton(
                  title: 'Nouvelle Agence',
                  icon: Icons.add_location,
                  color: const Color(0xFF10B981),
                  onPressed: () => _createAgence(),
                ),
                AdminActionButton(
                  title: 'Gérer Demandes',
                  icon: Icons.pending_actions,
                  color: const Color(0xFFF59E0B),
                  onPressed: () => _tabController.animateTo(4),
                ),
                AdminActionButton(
                  title: 'Rapport Global',
                  icon: Icons.analytics,
                  color: const Color(0xFF8B5CF6),
                  onPressed: () => _generateReport(),
                ),
              ],
            ),
          ],
        ),
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => (error'),
      ),
    );
  }

  /// 🏢 Onglet Compagnies
  Widget _buildCompagniesTab() {
    final compagniesAsync = ref.watch(compagniesProvider);
    
    return compagniesAsync.when(
      data: (compagnies) => (1),
                  ),
                ),
                ElevatedButton.const Icon(
                  onPressed: () => _createCompagnie(),
                  icon: const Icon(Icons.info),
                  label: const Text('Nouvelle Compagnie'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF3B82F6),
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            Expanded(
              child: AdminDataTable<CompagnieAssurance>(
                data: compagnies,
                columns: const [
                  'Nom',
                  'Code',
                  'Ville',
                  'Gouvernorat',
                  'Date Création',
                  'Actions',
                ],
                buildRow: (compagnie) => [
                  compagnie.nom,
                  compagnie.code,
                  compagnie.ville,
                  compagnie.gouvernorat,
                  _formatDate(compagnie.dateCreation),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: ),
                        onPressed: () => _editCompagnie(compagnie),
                      ),
                      IconButton(
                        icon: ),
                        onPressed: () => _deleteCompagnie(compagnie),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => (error')),
    );
  }

  /// 🏬 Onglet Agences
  Widget _buildAgencesTab() {
    final agencesAsync = ref.watch(agencesProvider);
    
    return agencesAsync.when(
      data: (agences) => (1),
                  ),
                ),
                ElevatedButton.const Icon(
                  onPressed: () => _createAgence(),
                  icon: const Icon(Icons.info),
                  label: const Text('Nouvelle Agence'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF10B981),
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            Expanded(
              child: AdminDataTable<AgenceAssurance>(
                data: agences,
                columns: const [
                  'Nom',
                  'Code',
                  'Compagnie',
                  'Ville',
                  'Responsable',
                  'Actions',
                ],
                buildRow: (agence) => [
                  agence.nom,
                  agence.code,
                  agence.compagnieId, // TODO: Afficher le nom de la compagnie
                  agence.ville,
                  agence.responsable ?? 'N/A',
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: ),
                        onPressed: () => _editAgence(agence),
                      ),
                      IconButton(
                        icon: ),
                        onPressed: () => _deleteAgence(agence),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => (error')),
    );
  }

  /// 👤 Onglet Utilisateurs
  Widget _buildUsersTab() {
    final usersAsync = ref.watch(usersProvider);

    return usersAsync.when(
      data: (users) => (1),
              ),
            ),
            const SizedBox(height: 24),

            Expanded(
              child: AdminDataTable<UserProfile>(
                data: users,
                columns: const [
                  'Nom',
                  'Email',
                  'Rôle',
                  'Compagnie',
                  'Agence',
                  'Statut',
                  'Actions',
                ],
                buildRow: (user) => [
                  user.fullName,
                  user.email,
                  _getRoleDisplayName(user.role),
                  user.compagnieId ?? 'N/A',
                  user.agenceId ?? 'N/A',
                  _buildStatusChip(user.status),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: ),
                        onPressed: () => _editUser(user),
                      ),
                      IconButton(
                        icon:  : const Color(0xFF10B981),
                        ),
                        onPressed: () => _toggleUserStatus(user),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => (error')),
    );
  }

  /// 📋 Onglet Demandes Professionnelles
  Widget _buildProfessionalRequestsTab() {
    return (1),
            ),
          ),
          const SizedBox(height: 24),

          Expanded(
            child: Card(
              child: (1),
                        const SizedBox(width: 8),
                        ,
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Expanded(
                      child: const Center(
                        child: const Text(
                          'Intégration avec le système de demandes professionnelles à implémenter',
                          style: TextStyle(color: Color(0xFF64748B)),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 🚧 Onglet Sinistres
  Widget _buildSinistresTab() {
    final sinistresAsync = ref.watch(sinistresProvider);

    return sinistresAsync.when(
      data: (sinistres) => (1),
              ),
            ),
            const SizedBox(height: 24),

            Expanded(
              child: AdminDataTable<SinistreAssurance>(
                data: sinistres,
                columns: const [
                  'N° Sinistre',
                  'Date Accident',
                  'Lieu',
                  'Conducteur A',
                  'Statut',
                  'Expert',
                  'Actions',
                ],
                buildRow: (sinistre) => [
                  sinistre.numeroSinistre,
                  _formatDate(sinistre.dateAccident),
                  sinistre.lieuAccident,
                  sinistre.conducteurAId,
                  _buildStatusChip(sinistre.statut),
                  sinistre.expertId ?? 'Non assigné',
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: ),
                        onPressed: () => _viewSinistre(sinistre),
                      ),
                      IconButton(
                        icon: ),
                        onPressed: () => _assignExpert(sinistre),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => (error')),
    );
  }

  /// ⚙️ Onglet Paramètres
  Widget _buildSettingsTab() {
    return (1),
            ),
          ),
          const SizedBox(height: 24),

          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 2,
              children: [
                _buildSettingCard(
                  'Sauvegardes',
                  'Gérer les sauvegardes automatiques',
                  Icons.backup,
                  const Color(0xFF3B82F6),
                  () => _manageBackups(),
                ),
                _buildSettingCard(
                  'Sécurité',
                  'Configurer les règles de sécurité',
                  Icons.security,
                  const Color(0xFFEF4444),
                  () => _manageSecurity(),
                ),
                _buildSettingCard(
                  'Notifications',
                  'Paramètres de notifications',
                  Icons.notifications,
                  const Color(0xFFF59E0B),
                  () => _manageNotifications(),
                ),
                _buildSettingCard(
                  'Rapports',
                  'Configuration des rapports',
                  Icons.analytics,
                  const Color(0xFF10B981),
                  () => _manageReports(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Actions
  void _showNotifications() {
    // TODO: Implémenter les notifications
  }

  void _logout() {
    // TODO: Implémenter la déconnexion
  }

  void _createCompagnie() {
    // TODO: Implémenter la création de compagnie
  }

  void _createAgence() {
    // TODO: Implémenter la création d'agence
  }

  void _editCompagnie(CompagnieAssurance compagnie) {
    // TODO: Implémenter l'édition de compagnie
  }

  void _deleteCompagnie(CompagnieAssurance compagnie) {
    // TODO: Implémenter la suppression de compagnie
  }

  void _editAgence(AgenceAssurance agence) {
    // TODO: Implémenter l'édition d'agence
  }

  void _deleteAgence(AgenceAssurance agence) {
    // TODO: Implémenter la suppression d'agence
  }

  void _generateReport() {
    // TODO: Implémenter la génération de rapport
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _getRoleDisplayName(String role) {
    switch (role) {
      case 'super_admin':
        return 'Super Admin';
      case 'admin_compagnie':
        return 'Admin Compagnie';
      case 'admin_agence':
        return 'Admin Agence';
      case 'agent_agence':
        return 'Agent';
      case 'expert_auto':
        return 'Expert Auto';
      case 'conducteur':
        return 'Conducteur';
      default:
        return role;
    }
  }

  Widget _buildStatusChip(String status) {
    Color color;
    String label;

    switch (status.toLowerCase()) {
      case 'active':
      case 'paye':
      case 'clos':
        color = const Color(0xFF10B981);
        label = status;
        break;
      case 'suspended':
      case 'impaye':
      case 'en_cours':
        color = const Color(0xFFF59E0B);
        label = status;
        break;
      case 'pending':
      case 'declare':
        color = const Color(0xFF3B82F6);
        label = status;
        break;
      default:
        color = const Color(0xFF64748B);
        label = status;
    }

    return Container(
      padding: ,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: ),
      ),
      child: ,
      ),
    );
  }

  Widget _buildSettingCard(String title, String subtitle, IconData icon, Color color, VoidCallback onTap) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: (1),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: ,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ,
                      ),
                    ),
                    const SizedBox(height: 4),
                    const Text(
                      subtitle,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF64748B),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Nouvelles méthodes d'action
  void _editUser(UserProfile user) {
    // TODO: Implémenter l'édition d'utilisateur
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: ({user.fullName} - À implémenter')),
    );
  }

  void _toggleUserStatus(UserProfile user) {
    // TODO: Implémenter le changement de statut
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: ({user.fullName} - À implémenter')),
    );
  }

  void _viewSinistre(SinistreAssurance sinistre) {
    // TODO: Implémenter la vue détaillée du sinistre
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: ({sinistre.numeroSinistre} - À implémenter')),
    );
  }

  void _assignExpert(SinistreAssurance sinistre) {
    // TODO: Implémenter l'assignation d'expert
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: ({sinistre.numeroSinistre} - À implémenter')),
    );
  }

  void _manageBackups() {
    // TODO: Implémenter la gestion des sauvegardes
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: const Text('Gestion des sauvegardes - À implémenter')),
    );
  }

  void _manageSecurity() {
    // TODO: Implémenter la gestion de la sécurité
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: const Text('Gestion de la sécurité - À implémenter')),
    );
  }

  void _manageNotifications() {
    // TODO: Implémenter la gestion des notifications
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: const Text('Gestion des notifications - À implémenter')),
    );
  }

  void _manageReports() {
    // TODO: Implémenter la gestion des rapports
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: const Text('Gestion des rapports - À implémenter
