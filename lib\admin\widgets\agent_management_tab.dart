import 'package:flutter/material.dart';
import '../../services/agent_management_service.dart';
import '../../services/admin_compagnie_auth_service.dart';
import '../../common/widgets/simple_credentials_dialog.dart';
import '../../services/agence_management_service.dart';

/// 👥 Onglet de gestion des agents
class AgentManagementTab extends StatefulWidget {
  final String compagnieId;
  final String compagnieNom;

   ) : super(key: key);

  @override
  State<AgentManagementTab> createState() => _AgentManagementTabState();
}

class _AgentManagementTabState extends State<AgentManagementTab> {
  List<Map<String, dynamic>> _agents = [];
  List<Map<String, dynamic>> _agences = [];
  bool _isLoading = true;
  String? _selectedAgenceFilter;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// 📋 Charger les données
  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    
    try {
      final futures = await Future.wait([
        AgentManagementService.getAdminAgencesByCompagnie(widget.compagnieId), // Charger les Admin Agence
        AgenceManagementService.getAgencesByCompagnie(widget.compagnieId),
      ]);

      setState(() {
        _agents = futures[0] as List<Map<String, dynamic>>;
        _agences = futures[1] as List<Map<String, dynamic>>;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: (e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  /// ➕ Créer un nouvel Admin Agence
  Future<void> _createAdminAgence() async {
    if (_agences.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: const Text('Créez d\'abord une agence avant d\'ajouter un Admin Agence'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => _CreateAdminAgenceDialog(
        compagnieId: widget.compagnieId,
        agences: _agences,
      ),
    );

    if (result != null && result['success'] == true) {
      _loadData(); // Recharger la liste des admins agence
      if (mounted) {
        // Afficher les identifiants avec le dialog moderne
        showSimpleCredentialsDialog(
          context: context,
          title: '🎉 Admin Agence créé avec succès',
          primaryColor: Colors.blue,
          credentials: {
            'nom': result['displayCredentials']['nom'],
            'email': result['displayCredentials']['email'],
            'password': result['displayCredentials']['password'],
            'agence': result['displayCredentials']['agence'],
            'role': 'Admin Agence',
          },
        );
      }
    }
  }

  /// 🔍 Filtrer les agents
  List<Map<String, dynamic>> get _filteredAgents {
    if (_selectedAgenceFilter == null) return _agents;
    return _agents.where((agent) => agent['agenceId'] == _selectedAgenceFilter).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // En-tête
          Container(
            padding: ,
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    ,
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ,
                          ),
                          ({_filteredAgents.length} admin(s) agence - ${widget.compagnieNom}',
                            style: ,
                          ),
                        ],
                      ),
                    ),
                    ElevatedButton.const Icon(
                      onPressed: _createAdminAgence,
                      icon: const Icon(Icons.info),
                      label: const Text('Nouvel Admin Agence'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: Colors.green.shade600,
                      ),
                    ),
                  ],
                ),
                
                // Filtre par agence
                if (_agences.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      ,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Container(
                          padding: ,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: DropdownButtonHideUnderline(
                            child: DropdownButton<String?>(
                              value: _selectedAgenceFilter,
                              dropdownColor: Colors.green.shade700,
                              style: ,
                              items: [
                                ),
                                ),
                                ..._agences.map((agence) => DropdownMenuItem<String?>(
                                  value: agence['id'],
                                  child: ),
                                )),
                              ],
                              onChanged: (value) {
                                setState(() {
                                  _selectedAgenceFilter = value;
                                });
                              },
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),

          // Liste des agents
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredAgents.isEmpty
                    ? _buildEmptyState()
                    : _buildAgentsList(),
          ),
        ],
      ),
    );
  }

  /// 📋 Liste des agents
  Widget _buildAgentsList() {
    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding:  {
          final agent = _filteredAgents[index];
          return _buildAgentCard(agent);
        },
      ),
    );
  }

  /// 👤 Carte d'agent
  Widget _buildAgentCard(Map<String, dynamic> agent) {
    final stats = agent['stats'] as Map<String, dynamic>? ?? {};
    final agence = _agences.firstWhere(
      (a) => a['id'] == agent['agenceId'],
      orElse: () => {'nom': 'Agence inconnue'},
    );
    
    return Card(
      margin: ),
      child: (1),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ({agent['nom']}',
                        style: ,
                      ),
                      ,
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: ,
                  ),
                  child: ,
                  ),
                ),
                PopupMenuButton(
                  itemBuilder: (context) => [
                    ,
                          const SizedBox(width: 8),
                          const Text('Modifier'),
                        ],
                      ),
                    ),
                    ,
                          const SizedBox(width: 8),
                          const Text('Reset mot de passe'),
                        ],
                      ),
                    ),
                    ,
                          const SizedBox(width: 8),
                          ),
                        ],
                      ),
                    ),
                  ],
                  onSelected: (value) => _handleAgentAction(value.toString(), agent),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // Informations de contact
            Row(
              children: [
                ,
                const SizedBox(width: 4),
                Expanded(child: const Text(agent['email'] ?? 'N/A')),
                const SizedBox(width: 16),
                ,
                const SizedBox(width: 4),
                const Text(agent['telephone'] ?? 'N/A'),
              ],
            ),
            const SizedBox(height: 12),
            
            // Statistiques
            Row(
              children: [
                _buildStatChip('Constats', stats['total_constats']?.toString() ?? '0', Colors.blue),
                const SizedBox(width: 8),
                _buildStatChip('Ce mois', stats['constats_ce_mois']?.toString() ?? '0', Colors.green),
                const SizedBox(width: 8),
                _buildStatChip('Score', stats['performance_score']?.toString() ?? '100', Colors.orange),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 📊 Chip de statistique
  Widget _buildStatChip(String label, String value, Color color) {
    return Container(
      padding: ,
        borderRadius: BorderRadius.circular(12),
      ),
      child: (value',
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 📭 État vide
  Widget _buildEmptyState() {
    return ,
          const SizedBox(height: 16),
          ,
          ),
          const SizedBox(height: 8),
          ,
          ),
          const SizedBox(height: 16),
          ElevatedButton.const Icon(
            onPressed: _createAdminAgence,
            icon: const Icon(Icons.info),
            label: const Text('Créer Admin Agence'),
          ),
        ],
      ),
    );
  }

  /// 🎯 Gérer les actions sur un agent
  void _handleAgentAction(String action, Map<String, dynamic> agent) {
    switch (action) {
      case 'edit':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: const Text('Modification bientôt disponible')),
        );
        break;
      case 'reset_password':
        _resetAgentPassword(agent);
        break;
      case 'deactivate':
        _confirmDeactivateAgent(agent);
        break;
    }
  }

  /// 🔐 Reset mot de passe
  void _resetAgentPassword(Map<String, dynamic> agent) async {
    final result = await AgentManagementService.resetAgentPassword(agent['id']);
    
    if (result['success'] && mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Mot de passe réinitialisé'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ({agent['nom']}'),
              ({agent['email']}'),
              ({result['newPassword']}'),
              const SizedBox(height: 8),
              ,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        ),
      );
    }
  }

  /// ⚠️ Confirmer la désactivation
  void _confirmDeactivateAgent(Map<String, dynamic> agent) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Désactiver l\'agent'),
        content: ({agent['nom']}" ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final result = await AgentManagementService.deactivateAgent(agent['id']);
              if (result['success']) {
                _loadData();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: const Text('Agent désactivé'), backgroundColor: Colors.orange),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: ),
          ),
        ],
      ),
    );
  }
}

/// 🏗️ Dialog de création d'agent
class _CreateAgentDialog extends StatefulWidget {
  final String compagnieId;
  final List<Map<String, dynamic>> agences;

  ;

  @override
  State<_CreateAgentDialog> createState() => _CreateAgentDialogState();
}

class _CreateAgentDialogState extends State<_CreateAgentDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nomController = TextEditingController();
  final _prenomController = TextEditingController();
  final _emailController = TextEditingController();
  final _telephoneController = TextEditingController();
  final _adresseController = TextEditingController();
  final _cinController = TextEditingController();
  String? _selectedAgenceId;
  bool _isCreating = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Nouvel Agent'),
      content: ,
                  ),
                  items: widget.agences.map((agence) => DropdownMenuItem<String>(
                    value: agence['id'],
                    child: const Text(agence['nom'),
                  )).toList(),
                  onChanged: (value) => setState(() => _selectedAgenceId = value),
                  validator: (value) => value == null ? 'Agence requise' : null,
                ),
                const SizedBox(height: 16),
                
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _prenomController,
                        decoration: ,
                        ),
                        validator: (value) => value?.isEmpty == true ? 'Prénom requis' : null,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _nomController,
                        decoration: ,
                        ),
                        validator: (value) => value?.isEmpty == true ? 'Nom requis' : null,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                
                TextFormField(
                  controller: _emailController,
                  decoration: ,
                  ),
                  validator: (value) {
                    if (value?.isEmpty == true) return 'Email requis';
                    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value!)) {
                      return 'Format email invalide';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                
                TextFormField(
                  controller: _telephoneController,
                  decoration: ,
                  ),
                  validator: (value) => value?.isEmpty == true ? 'Téléphone requis' : null,
                ),
                const SizedBox(height: 16),
                
                TextFormField(
                  controller: _adresseController,
                  decoration: ,
                  ),
                ),
                const SizedBox(height: 16),
                
                TextFormField(
                  controller: _cinController,
                  decoration: ,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isCreating ? null : () => Navigator.of(context).pop(),
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: _isCreating ? null : _createAgent,
          child: _isCreating
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Créer'),
        ),
      ],
    );
  }

  Future<void> _createAgent() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isCreating = true);

    try {
      final result = await AgentManagementService.createAgent(
        compagnieId: widget.compagnieId,
        agenceId: _selectedAgenceId!,
        nom: _nomController.text.trim(),
        prenom: _prenomController.text.trim(),
        email: _emailController.text.trim(),
        telephone: _telephoneController.text.trim(),
        adresse: _adresseController.text.trim(),
        cin: _cinController.text.trim(),
        createdBy: 'admin_compagnie',
      );

      if (mounted) {
        Navigator.of(context).pop(result);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: (e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isCreating = false);
      }
    }
  }

  @override
  void dispose() {
    _nomController.dispose();
    _prenomController.dispose();
    _emailController.dispose();
    _telephoneController.dispose();
    _adresseController.dispose();
    _cinController.dispose();
    super.dispose();
  }
}

/// 👤 Dialog de création d'Admin Agence (pour Admin Compagnie)
class _CreateAdminAgenceDialog extends StatefulWidget {
  final String compagnieId;
  final List<Map<String, dynamic>> agences;

  ;

  @override
  State<_CreateAdminAgenceDialog> createState() => _CreateAdminAgenceDialogState();
}

class _CreateAdminAgenceDialogState extends State<_CreateAdminAgenceDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nomController = TextEditingController();
  final _prenomController = TextEditingController();
  final _emailController = TextEditingController();
  final _telephoneController = TextEditingController();
  final _adresseController = TextEditingController();
  final _cinController = TextEditingController();
  String? _selectedAgenceId;
  bool _isCreating = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('👤 Nouvel Admin Agence'),
      content: ,
                  ),
                  items: widget.agences.map((agence) {
                    return DropdownMenuItem<String>(
                      value: agence['id'],
                      child: ({agence['ville']}'),
                    );
                  }).toList(),
                  onChanged: (value) => setState(() => _selectedAgenceId = value),
                  validator: (value) => value == null ? 'Sélectionnez une agence' : null,
                ),
                const SizedBox(height: 16),

                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _prenomController,
                        decoration: ,
                        ),
                        validator: (value) => value?.isEmpty == true ? 'Prénom requis' : null,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _nomController,
                        decoration: ,
                        ),
                        validator: (value) => value?.isEmpty == true ? 'Nom requis' : null,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                TextFormField(
                  controller: _emailController,
                  decoration: ,
                  ),
                  validator: (value) {
                    if (value?.isEmpty == true) return 'Email requis';
                    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value!)) {
                      return 'Format email invalide';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                TextFormField(
                  controller: _telephoneController,
                  decoration: ,
                  ),
                  validator: (value) => value?.isEmpty == true ? 'Téléphone requis' : null,
                ),
                const SizedBox(height: 16),

                TextFormField(
                  controller: _adresseController,
                  decoration: ,
                  ),
                ),
                const SizedBox(height: 16),

                TextFormField(
                  controller: _cinController,
                  decoration: ,
                  ),
                ),
                const SizedBox(height: 16),

                Container(
                  padding: ,
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          ,
                          const SizedBox(width: 8),
                          ,
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      ,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isCreating ? null : () => Navigator.of(context).pop(),
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: _isCreating ? null : _createAdminAgence,
          child: _isCreating
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Créer Admin'),
        ),
      ],
    );
  }

  Future<void> _createAdminAgence() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isCreating = true);

    try {
      // Importer le service nécessaire
      final result = await AdminCompagnieAuthService.createAdminAgence(
        compagnieId: widget.compagnieId,
        agenceId: _selectedAgenceId!,
        nom: _nomController.text.trim(),
        prenom: _prenomController.text.trim(),
        email: _emailController.text.trim(),
        telephone: _telephoneController.text.trim(),
        adresse: _adresseController.text.trim(),
        cin: _cinController.text.trim(),
        createdBy: 'admin_compagnie', // TODO: Récupérer l'ID de l'admin connecté
      );

      if (mounted) {
        Navigator.of(context).pop(result);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: (e
