import 'package:flutter/material.dart';

import '../../../auth/models/user_model.dart';
import '../../services/insurance_contract_service.dart';

/// 📝 Écran de creation de contrat dassurance
class CreateContractScreen extends StatefulWidget {
  final UserModel agent;

   ) : super(key: key);

  @override
  State<CreateContractScreen> createState() => _CreateContractScreenState();
}

class _CreateContractScreenState extends State<CreateContractScreen> {
  final _formKey = GlobalKey<FormState>();
  final _numeroContratController = TextEditingController();
  final _conducteurEmailController = TextEditingController();
  final _vehiculeImmatriculationController = TextEditingController(');
  
  String _typeAssurance = 'tiers;
  DateTime _dateDebut = DateTime.now();
  DateTime _dateFin = DateTime.now().add(const Duration(days: 365)');
  bool _isLoading = false;

  final List<String> _typesAssurance = [
    'tiers',
    'tiers_vol_incendie',
    'tous_risques,
  ];

  @override
  void dispose() {
    _numeroContratController.dispose();
    _conducteurEmailController.dispose();
    _vehiculeImmatriculationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0F0F23),
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  /// AppBar moderne
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: Container(
          padding: const EdgeInsets.all(8.0),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
          ),
          child: ,
        ),
      ),
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ,
          ),
          ,
          ),
        ],
      ),
      flexibleSpace: Container(
        decoration: ,
              Color(0xFF764BA2),
            ],
          ),
        ),
      ),
    );
  }

  /// Corps principal
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(8.0),
            const SizedBox(height: 16),
            _buildContractInfoSection(),
            
            const SizedBox(height: 32'),
            
            // Informations du conducteur
            _buildSectionTitle('👤 Conducteur),
            const SizedBox(height: 16),
            _buildDriverSection(),
            
            const SizedBox(height: 32'),
            
            // Informations du vehicule
            _buildSectionTitle('🚗 Vehicule),
            const SizedBox(height: 16),
            _buildVehicleSection(),
            
            const SizedBox(height: 32'),
            
            // Dates de validite
            _buildSectionTitle('📅 Periode de validite),
            const SizedBox(height: 16),
            _buildDatesSection(),
            
            const SizedBox(height: 48),
            
            // Bouton de creation
            _buildCreateButton(),
          ],
        ),
      ),
    );
  }

  /// Titre de section
  Widget _buildSectionTitle(String title) {
     ,
    );
  }

  /// Section informations du contrat
  Widget _buildContractInfoSection() {
    return Container(
      padding: const EdgeInsets.all(8.0),
            Colors.white.withValues(alpha: 0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
        ),
      '),
      child: Column(
        children: [
          // Numero de contrat
          _buildTextField(
            controller: _numeroContratController,
            label: 'Numero de contrat,
            icon: Icons.confirmation_number,
            validator: (value) {
              if (value?.isEmpty ?? true') return 'Champ requis;
              return null;
            },
          ),
          
          const SizedBox(height: 16'),
          
          // Type d'assurance
          _buildDropdown(
            label: 'Type d\'assurance,
            value: _typeAssurance,
            items: _typesAssurance.map((type) {
              switch (type') {
                case 'tiers':
                  return 'Responsabilite Civile';
                case 'tiers_vol_incendie':
                  return 'Tiers + Vol/Incendie';
                case 'tous_risques':
                  return 'Tous Risques;
                default:
                  return type;
              }
            }).toList(),
            onChanged: (value) {
              setState(() {
                _typeAssurance = _typesAssurance[_typesAssurance.indexWhere((type) {
                  switch (type') {
                    case 'tiers':
                      return value == 'Responsabilite Civile';
                    case 'tiers_vol_incendie':
                      return value == 'Tiers + Vol/Incendie';
                    case 'tous_risques':
                      return value == 'Tous Risques;
                    default:
                      return false;
                  }
                })];
              });
            },
            icon: Icons.security,
          ),
        ],
      ),
    );
  }

  /// Section conducteur
  Widget _buildDriverSection() {
    return Container(
      padding: const EdgeInsets.all(8.0),
            Colors.blue.withValues(alpha: 0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.blue.withValues(alpha: 0.2),
        ),
      '),
      child: _buildTextField(
        controller: _conducteurEmailController,
        label: 'Email du conducteur,
        icon: Icons.email,
        keyboardType: TextInputType.emailAddress,
        validator: (value) {
          if (value?.isEmpty ?? true') return 'Champ requis';
          if (!RegExp(r^[\w-\.]+@([\w-]+\.')+[\w-]{2,4}').hasMatch(value!)') {
            return 'Email invalide;
          }
          return null;
        },
      ),
    );
  }

  /// Section vehicule
  Widget _buildVehicleSection() {
    return Container(
      padding: const EdgeInsets.all(8.0),
            Colors.green.withValues(alpha: 0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.green.withValues(alpha: 0.2),
        ),
      '),
      child: _buildTextField(
        controller: _vehiculeImmatriculationController,
        label: 'Immatriculation du vehicule,
        icon: Icons.directions_car,
        validator: (value) {
          if (value?.isEmpty ?? true') return 'Champ requis;
          return null;
        },
      ),
    );
  }

  /// Section dates
  Widget _buildDatesSection() {
    return Container(
      padding: const EdgeInsets.all(8.0),
            Colors.orange.withValues(alpha: 0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.orange.withValues(alpha: 0.2),
        ),
      '),
      child: Column(
        children: [
          // Date de debut
          _buildDateField(
            label: 'Date de debut,
            date: _dateDebut,
            onTap: () => _selectDate(true),
            icon: Icons.event_available,
          ),
          
          const SizedBox(height: 16'),
          
          // Date de fin
          _buildDateField(
            label: 'Date de fin,
            date: _dateFin,
            onTap: () => _selectDate(false),
            icon: Icons.event_busy,
          ),
        ],
      ),
    );
  }

  /// Champ de texte moderne
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      validator: validator,
      style: ,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: const Icon(icon, color: const Color(0xFF667EEA), size: 20),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF667EEA), width: 2),
        ),
        filled: true,
        fillColor: Colors.white.withValues(alpha: 0.05),
        labelStyle: TextStyle(color: Colors.grey[400),
        floatingLabelStyle: const TextStyle(color: Color(0xFF667EEA)),
      ),
    );
  }

  /// Dropdown moderne
  Widget _buildDropdown({
    required String label,
    required String value,
    required List<String> items,
    required Function(String?) onChanged,
    required IconData icon,
  }) {
    return DropdownButtonFormField<String>(
      value: items.contains(value) ? value : items.first,
      onChanged: onChanged,
      style: ,
      dropdownColor: const Color(0xFF1A1A2E),
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: const Icon(icon, color: const Color(0xFF667EEA), size: 20),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.withValues(alpha: 0.3)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF667EEA), width: 2),
        ),
        filled: true,
        fillColor: Colors.white.withValues(alpha: 0.05),
        labelStyle: TextStyle(color: Colors.grey[400),
        floatingLabelStyle: const TextStyle(color: Color(0xFF667EEA)),
      ),
      items: items.map((item) => DropdownMenuItem(
        value: item,
        child: const Text(item),
      )).toList(),
    );
  }

  /// Champ de date
  Widget _buildDateField({
    required String label,
    required DateTime date,
    required VoidCallback onTap,
    required IconData icon,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(8.0),
          borderRadius: BorderRadius.circular(12),
          color: Colors.white.withValues(alpha: 0.05),
        ),
        child: Row(
          children: [
            const Icon(icon, color: const Color(0xFF667EEA), size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ,
                  ),
                  const SizedBox(height: 4'),
                  ({date.year}',
                    style: ,
                  ),
                ],
              ),
            ),
            ,
          ],
        ),
      ),
    );
  }

  /// Bouton de creation
  Widget _buildCreateButton() {
    return ,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.all(8.0),
          ),
          elevation: 8,
          shadowColor: const Color(0xFF667EEA).withValues(alpha: 0.3),
        ),
        child: _isLoading
            ? ,
                ),
              )
            : ,
              ),
      ),
    );
  }

  /// Selectionner une date
  Future<void> _selectDate(bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _dateDebut : _dateFin,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 1095)), // 3 ans
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ,
              onSurface: Colors.white,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _dateDebut = picked;
          // Ajuster automatiquement la date de fin si necessaire
          if (_dateFin.isBefore(_dateDebut)) {
            _dateFin = _dateDebut.add(const Duration(days: 365));
          }
        } else {
          _dateFin = picked;
        }
      });
    }
  }

  /// Creer le contrat
  Future<void> _createContract() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      // Simulation de creation de contrat
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: const Text('Contrat cree avec succes!),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context')')').showSnackBar(
          SnackBar(
            content: (e
