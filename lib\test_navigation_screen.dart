import 'package:flutter/material.dart';
import 'core/config/app_routes.dart';
import 'features/auth/presentation/screens/elegant_professional_request_screen.dart';

/// 🧪 Écran de test pour vérifier la navigation
class TestNavigationScreen extends StatelessWidget {
  const Text(\;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🧪 Test Navigation'),
        backgroundColor: Colors.red[600],
        foregroundColor: Colors.white,
      ),
      body: (1),
              const SizedBox(height: 24),
              ,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              
              // Test 1: Navigation par route
               {
                    print('🔥 TEST 1: Navigation par route');
                    Navigator.pushNamed(context, AppRoutes.professionalRequest);
                  },
                  icon: const Icon(Icons.info),
                  label: const Text('Test 1: Navigation par Route'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue[600],
                    foregroundColor: Colors.white,
                    padding: ,
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Test 2: Navigation directe
               {
                    print('🔥 TEST 2: Navigation directe');
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => ,
                      ),
                    );
                  },
                  icon: const Icon(Icons.info),
                  label: const Text('Test 2: Navigation Directe'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green[600],
                    foregroundColor: Colors.white,
                    padding: ,
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Test 3: Vérifier la route
               {
                    print('🔥 TEST 3: Vérification route');
                    print('Route: ${AppRoutes.professionalRequest}');
                    
                    // Afficher un dialog avec les infos
                    showDialog(
                      context: context,
                      builder: (context) => AlertDialog(
                        title: const Text('Info Route'),
                        content: const Text('Route: /professional-request'),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: const Text('OK'),
                          ),
                        ],
                      ),
                    );
                  },
                  icon: const Icon(Icons.info),
                  label: const Text('Test 3: Vérifier Route'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange[600],
                    foregroundColor: Colors.white,
                    padding: ,
                ),
              ),
              
              const SizedBox(height: 32),
              
              OutlinedButton.const Icon(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.info),
                label: const Text('Retour
