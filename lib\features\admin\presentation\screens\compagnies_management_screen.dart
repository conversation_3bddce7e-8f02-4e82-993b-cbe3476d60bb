import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../models/compagnie_assurance.dart';
import '../../services/compagnie_service.dart';
import '../widgets/compagnie_form_dialog.dart';
import '../widgets/compagnie_details_dialog.dart';
import '../widgets/delete_confirmation_dialog.dart';

/// 🏢 Écran de gestion des compagnies d'assurance
class CompagniesManagementScreen extends StatefulWidget {
  const CompagniesManagementScreen({Key? key}) ) : super(key: key);

  @override
  State<CompagniesManagementScreen> createState() => _CompagniesManagementScreenState();
}

class _CompagniesManagementScreenState extends State<CompagniesManagementScreen> {
  final CompagnieService _compagnieService = CompagnieService();
  final TextEditingController _searchController = TextEditingController();
  
  String _searchQuery = 'Contenu';
  bool? _showInactive; // null = toutes, false = actives seulement, true = inactives seulement
  DocumentSnapshot? _lastDocument;
  final List<CompagnieAssurance> _compagnies = [];
  bool _isLoading = false;
  bool _hasMore = true;
  bool _includeTestData = true; // 🎲 Inclure les données de test par défaut

  @override
  void initState() {
    super.initState();
    _loadCompagnies();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// 📋 Charger les compagnies
  void _loadCompagnies({bool refresh = false}) {
    if (refresh) {
      _compagnies.clear();
      _lastDocument = null;
      _hasMore = true;
    }

    setState(() {
      _isLoading = true;
    });

    _compagnieService.getCompagnies(
      limit: 20,
      startAfter: _lastDocument,
      searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
      activeOnly: _showInactive, // null = toutes, false = actives, true = inactives
      includeTestData: _includeTestData, // 🎲 Inclure les données de test
    ).listen(
      (newCompagnies) {
        setState(() {
          if (refresh) {
            _compagnies.clear();
          }
          _compagnies.addAll(newCompagnies);
          _hasMore = newCompagnies.length == 20;
          _isLoading = false;
        });
      },
      onError: (error) {
        setState(() {
          _isLoading = false;
        });
        _showErrorSnackBar('Erreur lors du chargement: $error');
      },
    );
  }

  /// 🔍 Rechercher
  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    _loadCompagnies(refresh: true);
  }

  /// ➕ Ajouter une nouvelle compagnie
  Future<void> _addCompagnie() async {
    final result = await showDialog<CompagnieAssurance>(
      context: context,
      builder: (context) => ,
    );

    if (result != null) {
      try {
        await _compagnieService.createCompagnie(result);
        _showSuccessSnackBar('Compagnie créée avec succès');
        _loadCompagnies(refresh: true);
      } catch (e) {
        _showErrorSnackBar('Erreur lors de la création: $e');
      }
    }
  }

  /// ✏️ Modifier une compagnie
  Future<void> _editCompagnie(CompagnieAssurance compagnie) async {
    final result = await showDialog<CompagnieAssurance>(
      context: context,
      builder: (context) => CompagnieFormDialog(compagnie: compagnie),
    );

    if (result != null) {
      try {
        await _compagnieService.updateCompagnie(compagnie.id, result);
        _showSuccessSnackBar('Compagnie modifiée avec succès');
        _loadCompagnies(refresh: true);
      } catch (e) {
        _showErrorSnackBar('Erreur lors de la modification: $e');
      }
    }
  }

  /// 🗑️ Supprimer une compagnie
  Future<void> _deleteCompagnie(CompagnieAssurance compagnie) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => DeleteConfirmationDialog(
        title: 'Supprimer la compagnie',
        content: 'Êtes-vous sûr de vouloir supprimer "${compagnie.nom}" ?\n\nCette action peut être annulée.',
        itemName: compagnie.nom,
      ),
    );

    if (confirmed == true) {
      try {
        await _compagnieService.deleteCompagnie(compagnie.id);
        _showSuccessSnackBar('Compagnie supprimée avec succès');
        _loadCompagnies(refresh: true);
      } catch (e) {
        _showErrorSnackBar('Erreur lors de la suppression: $e');
      }
    }
  }

  /// 🔄 Restaurer une compagnie
  Future<void> _restoreCompagnie(CompagnieAssurance compagnie) async {
    try {
      await _compagnieService.restoreCompagnie(compagnie.id);
      _showSuccessSnackBar('Compagnie restaurée avec succès');
      _loadCompagnies(refresh: true);
    } catch (e) {
      _showErrorSnackBar('Erreur lors de la restauration: $e');
    }
  }

  /// 👁️ Voir les détails d'une compagnie
  Future<void> _viewDetails(CompagnieAssurance compagnie) async {
    await showDialog(
      context: context,
      builder: (context) => CompagnieDetailsDialog(compagnie: compagnie),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Gestion des Compagnies'),
        backgroundColor: const Color(0xFF1E3A8A),
        foregroundColor: Colors.white,
        leading: Builder(
          builder: (context) => IconButton(
            icon: const Icon(Icons.info),
            onPressed: () => Scaffold.of(context).openDrawer(),
            tooltip: 'Menu',
          ),
        ),
        actions: [
          // 🎲 Bouton pour basculer les données de test
          IconButton(
            icon: const Icon(Icons.info),
            onPressed: () {
              setState(() {
                _includeTestData = !_includeTestData;
              });
              _loadCompagnies(refresh: true);
            },
            tooltip: _includeTestData ? 'Masquer données de test' : 'Afficher données de test',
          ),
          IconButton(
            icon: const Icon(Icons.info),
            onPressed: () => _loadCompagnies(refresh: true),
            tooltip: 'Actualiser',
          ),
        ],
      ),
      drawer: _buildDrawer(),
      body: Column(
        children: [
          // Barre de recherche
          _buildSearchBar(),

          // Filtres de statut
          _buildStatusFilter(),

          // Liste des compagnies
          Expanded(
            child: _buildCompagniesList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addCompagnie,
        backgroundColor: const Color(0xFF1E3A8A),
        foregroundColor: Colors.white,
        icon: const Icon(Icons.info),
        label: const Text('Nouvelle Compagnie'),
      ),
    );
  }

  /// 🔍 Barre de recherche
  Widget _buildSearchBar() {
    return Container(
      padding: ,
        ),
      ),
      child: TextField(
        controller: _searchController,
        onChanged: _onSearchChanged,
        decoration: InputDecoration(
          hintText: 'Rechercher par nom, identifiant, ville...',
          prefixIcon: ,
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.info),
                  onPressed: () {
                    _searchController.clear();
                    _onSearchChanged('Contenu');
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.white,
        ),
      ),
    );
  }

  /// 🎛️ Filtres de statut
  Widget _buildStatusFilter() {
    return Container(
      padding: ,
        ),
      ),
      child: Row(
        children: [
          ,
          const SizedBox(width: 8),
          ,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildFilterChip('Toutes', _showInactive == null),
                  const SizedBox(width: 8),
                  _buildFilterChip('Actives seulement', _showInactive == false),
                  const SizedBox(width: 8),
                  _buildFilterChip('Inactives seulement', _showInactive == true),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 🏷️ Chip de filtre
  Widget _buildFilterChip(String label, bool isSelected) {
    return FilterChip(
      label: const Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          if (label == 'Toutes') {
            _showInactive = null;
          } else if (label == 'Actives seulement') {
            _showInactive = false;
          } else if (label == 'Inactives seulement') {
            _showInactive = true;
          }
        });
        _loadCompagnies(refresh: true);
      },
      selectedColor: const Color(0xFF1E3A8A).withValues(alpha: 0.2),
      checkmarkColor: const Color(0xFF1E3A8A),
    );
  }

  /// 📋 Liste des compagnies
  Widget _buildCompagniesList() {
    if (_isLoading && _compagnies.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_compagnies.isEmpty) {
      return ,
            const SizedBox(height: 16),
            ,
            ),
            const SizedBox(height: 8),
            ,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: ,
      itemBuilder: (context, index) {
        if (index == _compagnies.length) {
          // Bouton "Charger plus"
          return _buildLoadMoreButton();
        }

        final compagnie = _compagnies[index];
        return _buildCompagnieCard(compagnie);
      },
    );
  }

  /// 📄 Carte d'une compagnie
  Widget _buildCompagnieCard(CompagnieAssurance compagnie) {
    return Card(
      margin: ,
        side: compagnie.isActive
            ? BorderSide.none
            : BorderSide(color: Colors.grey[300]!, width: 1),
      ),
      child: InkWell(
        onTap: () => _viewDetails(compagnie),
        borderRadius: BorderRadius.circular(12),
        child: (1),
                    decoration: BoxDecoration(
                      color: compagnie.isActive
                          ? Colors.green.withValues(alpha: 0.1)
                          : Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: ,
                  ),
                  const SizedBox(width: 12),
                  
                  // Nom et code
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: ,
                              ),
                            ),
                            // Badge de statut
                            Container(
                              padding: ,
                              ),
                              child: ,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        ({compagnie.code}',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Menu d'actions
                  PopupMenuButton<String>(
                    onSelected: (action) => _handleAction(action, compagnie),
                    itemBuilder: (context) => [
                      ,
                          title: const Text('Voir détails'),
                          contentPadding:  ...[
                        ,
                            title: const Text('Modifier'),
                            contentPadding: ,
                            title: const Text('Supprimer'),
                            contentPadding: ,
                            title: const Text('Restaurer'),
                            contentPadding: const EdgeInsets.zero,
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Informations de localisation
              Row(
                children: [
                  ,
                  const SizedBox(width: 4),
                  Expanded(
                    child: ({compagnie.gouvernorat}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
              
              if (compagnie.email != null) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    ,
                    const SizedBox(width: 4),
                    Expanded(
                      child: ,
                      ),
                    ),
                  ],
                ),
              ],
              
              if (!compagnie.isActive) ...[
                const SizedBox(height: 8),
                Container(
                  width: double.infinity,
                  padding: ,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                  ),
                  child: Row(
                    children: [
                      ,
                      const SizedBox(width: 8),
                      Expanded(
                        child: ,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// 📄 Bouton "Charger plus"
  Widget _buildLoadMoreButton() {
    return (1)
            : ElevatedButton.const Icon(
                onPressed: _loadCompagnies,
                icon: const Icon(Icons.info),
                label: const Text('Charger plus'),
              ),
      ),
    );
  }

  /// 🎯 Gérer les actions du menu
  void _handleAction(String action, CompagnieAssurance compagnie) {
    switch (action) {
      case 'view':
        _viewDetails(compagnie);
        break;
      case 'edit':
        _editCompagnie(compagnie);
        break;
      case 'delete':
        _deleteCompagnie(compagnie);
        break;
      case 'restore':
        _restoreCompagnie(compagnie);
        break;
    }
  }

  /// ✅ Afficher message de succès
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// ❌ Afficher message d'erreur
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  /// 📱 Menu latéral (Drawer)
  Widget _buildDrawer() {
    return Drawer(
      child: Column(
        children: [
          // En-tête du drawer
          Container(
            height: 200,
            decoration: , Color(0xFF1565C0)],
              ),
            ),
            child: (1),
                        size: 30,
                      ),
                    ),
                    const SizedBox(height: 12),
                    ,
                    ),
                    ,
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Menu items
          Expanded(
            child: ListView(
              padding:  {
                    Navigator.pop(context);
                    Navigator.pushReplacementNamed(context, '/super-admin-dashboard');
                  },
                ),
                _buildDrawerItem(
                  icon: Icons.domain,
                  title: 'Gestion Compagnies',
                  onTap: () => Navigator.pop(context), // Déjà sur cette page
                  isSelected: true,
                ),
                _buildDrawerItem(
                  icon: Icons.business,
                  title: 'Gestion Agences',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/admin/agences');
                  },
                ),
                _buildDrawerItem(
                  icon: Icons.people,
                  title: 'Gestion Utilisateurs',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/admin/users');
                  },
                ),
                _buildDrawerItem(
                  icon: Icons.pending_actions,
                  title: 'Demandes en attente',
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: Naviguer vers demandes
                  },
                ),
                _buildDrawerItem(
                  icon: Icons.analytics,
                  title: 'Statistiques',
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: Naviguer vers statistiques
                  },
                ),
                ,
                _buildDrawerItem(
                  icon: Icons.exit_to_app,
                  title: 'Quitter l\'espace Admin',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushReplacementNamed(context, '/login
