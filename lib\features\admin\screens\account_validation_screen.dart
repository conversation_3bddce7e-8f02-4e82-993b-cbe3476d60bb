import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../auth/models/notification_model.dart';
import '../../auth/services/notification_service.dart';
import '../../auth/models/user_model.dart';

/// 🔍 Écran de validation des comptes professionnels
class AccountValidationScreen extends ConsumerStatefulWidget {
  const AccountValidationScreen({Key? key}) ) : super(key: key);

  @override
  ConsumerState<AccountValidationScreen> createState() => _AccountValidationScreenState();
}

class _AccountValidationScreenState extends ConsumerState<AccountValidationScreen> {
  String _selectedFilter = 'pending';
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Validation des Comptes',
      ),
      body: Column(
        children: [
          // Filtres
          _buildFilterTabs(),
          
          // Liste des demandes
          Expanded(
            child: _buildRequestsList(),
          ),
        ],
      ),
    );
  }

  /// 📊 Onglets de filtrage
  Widget _buildFilterTabs() {
    return Container(
      padding: ,
          const SizedBox(width: 8),
          _buildFilterChip('approved', 'Approuvés', Colors.green),
          const SizedBox(width: 8),
          _buildFilterChip('rejected', 'Rejetés', Colors.red),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label, Color color) {
    final isSelected = _selectedFilter == value;
    return FilterChip(
      label: const Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedFilter = value;
        });
      },
      backgroundColor: Colors.grey[100],
      selectedColor: color.withValues(alpha: ,
      checkmarkColor: color,
      labelStyle: TextStyle(
        color: isSelected ? color : Colors.grey[700],
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      ),
    );
  }

  /// 📋 Liste des demandes
  Widget _buildRequestsList() {
    return StreamBuilder<List<ProfessionalAccountRequest>>(
      stream: _selectedFilter == 'pending' 
          ? ProfessionalAccountService.getPendingRequests()
          : _getAllRequestsByStatus(_selectedFilter),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return ,
                const SizedBox(height: 16),
                ({snapshot.error}'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => setState(() {},
                  child: const Text('Réessayer'),
                ),
              ],
            ),
          );
        }

        final requests = snapshot.data ?? [];

        if (requests.isEmpty) {
          return ,
                const SizedBox(height: 16),
                ({_getStatusLabel(_selectedFilter)}',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding:  {
            final request = requests[index];
            return _buildRequestCard(request);
          },
        );
      },
    );
  }

  /// 🃏 Carte de demande
  Widget _buildRequestCard(ProfessionalAccountRequest request) {
    return Card(
      margin:  => _showRequestDetails(request),
        borderRadius: BorderRadius.circular(8),
        child: (1),
                    child: ,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ({request.nom}',
                          style: ,
                        ),
                        ,
                        ),
                      ],
                    ),
                  ),
                  _buildStatusBadge(request.status),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Informations professionnelles
              if (request.userType == 'assureur') ...[
                _buildInfoRow(Icons.business, 'Compagnie', request.compagnie ?? 'Contenu'),
                _buildInfoRow(Icons.badge, 'Matricule', request.matricule ?? 'Contenu'),
                _buildInfoRow(Icons.location_city, 'Gouvernorat', request.gouvernorat ?? 'Contenu'),
              ],
              
              if (request.userType == 'expert') ...[
                _buildInfoRow(Icons.business_center, 'Cabinet', request.cabinet ?? 'Contenu'),
                _buildInfoRow(Icons.verified, 'Agrément', request.agrement ?? 'Contenu'),
              ],
              
              const SizedBox(height: 8),
              
              // Date et actions
              Row(
                children: [
                  ,
                  const SizedBox(width: 4),
                  ,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                  ,
                  if (request.status == AccountStatus.pending) ...[
                    TextButton.const Icon(
                      onPressed: () => _rejectRequest(request),
                      icon: const Icon(Icons.info),
                      label: const Text('Rejeter'),
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.red,
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton.const Icon(
                      onPressed: () => _approveRequest(request),
                      icon: const Icon(Icons.info),
                      label: const Text('Approuver'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return (1),
          const SizedBox(width: 8),
          (label: ',
            style: ,
          ),
          Expanded(
            child: ,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBadge(AccountStatus status) {
    Color color;
    String text;
    
    switch (status) {
      case AccountStatus.pending:
        color = Colors.orange;
        text = 'En attente';
        break;
      case AccountStatus.approved:
        color = Colors.green;
        text = 'Approuvé';
        break;
      case AccountStatus.rejected:
        color = Colors.red;
        text = 'Rejeté';
        break;
      default:
        color = Colors.grey;
        text = 'Inconnu';
    }
    
    return Container(
      padding: ,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: ),
      ),
      child: ,
      ),
    );
  }

  Color _getTypeColor(String userType) {
    switch (userType) {
      case 'assureur':
        return Colors.blue;
      case 'expert':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getTypeconst Icon(String userType) {
    switch (userType) {
      case 'assureur':
        return Icons.business;
      case 'expert':
        return Icons.assignment_ind;
      default:
        return Icons.person;
    }
  }

  String _getStatusLabel(String status) {
    switch (status) {
      case 'pending':
        return 'en attente';
      case 'approved':
        return 'approuvées';
      case 'rejected':
        return 'rejetées';
      default:
        return 'Contenu';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} à ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  /// 📄 Afficher les détails d'une demande
  void _showRequestDetails(ProfessionalAccountRequest request) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          constraints: const BoxConstraints(maxHeight: 600),
          child: Column(
            children: [
              // En-tête
              Container(
                padding: ,
                  borderRadius: ,
                  ),
                ),
                child: Row(
                  children: [
                    ,
                      color: Colors.white,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ({request.nom}',
                            style: ,
                          ),
                          ,
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.info),
                    ),
                  ],
                ),
              ),

              // Contenu
              Expanded(
                child: SingleChildScrollView(
                  padding: ,
                        _buildDetailRow('Téléphone', request.telephone),
                        _buildDetailRow('Adresse', request.adresse ?? 'Non renseignée'),
                      ),

                      const SizedBox(height: 20),

                      if (request.userType == 'assureur')
                        _buildDetailSection('🏢 Informations Assurance', [
                          _buildDetailRow('Compagnie', request.compagnie ?? 'Contenu'),
                          _buildDetailRow('Matricule', request.matricule ?? 'Contenu'),
                          _buildDetailRow('Gouvernorat', request.gouvernorat ?? 'Contenu'),
                          _buildDetailRow('Agence préférée', request.agencePreferee ?? 'Aucune préférence'),
                        ),

                      if (request.userType == 'expert')
                        _buildDetailSection('🔍 Informations Expert', [
                          _buildDetailRow('Cabinet', request.cabinet ?? 'Contenu'),
                          _buildDetailRow('Agrément', request.agrement ?? 'Contenu'),
                          _buildDetailRow('Spécialités', request.specialites ?? 'Non renseignées'),
                        ),

                      const SizedBox(height: 20),

                      if (request.motivationLetter != null && request.motivationLetter!.isNotEmpty)
                        _buildDetailSection('💬 Lettre de motivation', [
                          Container(
                            width: double.infinity,
                            padding: ,
                              border: Border.all(color: Colors.grey[300]!),
                            ),
                            child: ,
                            ),
                          ),
                        ),

                      const SizedBox(height: 20),

                      _buildDetailSection('📊 Statut', [
                        Row(
                          children: [
                            _buildStatusBadge(request.status),
                            ,
                            ({_formatDate(request.createdAt)}',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Actions
              if (request.status == AccountStatus.pending)
                Container(
                  padding: ,
                      bottomRight: Radius.circular(8),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.const Icon(
                          onPressed: () {
                            Navigator.pop(context);
                            _rejectRequest(request);
                          },
                          icon: const Icon(Icons.info),
                          label: const Text('Rejeter'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.red,
                            side: ,
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton.const Icon(
                          onPressed: () {
                            Navigator.pop(context);
                            _approveRequest(request);
                          },
                          icon: const Icon(Icons.info),
                          label: const Text('Approuver'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
          ),
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return (1),
            ),
          ),
          Expanded(
            child: ,
            ),
          ),
        ],
      ),
    );
  }

  /// ✅ Approuver une demande
  Future<void> _approveRequest(ProfessionalAccountRequest request) async {
    try {
      // Afficher dialog de confirmation
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Approuver la demande'),
          content: ({request.nom} ?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context, true),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
              child: const Text('Approuver'),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        // Approuver la demande
        await ProfessionalAccountService.approveRequest(
          requestId: request.id,
          approvedBy: 'admin', // TODO: Récupérer l'ID de l'admin connecté
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: const Text('Demande approuvée avec succès'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// ❌ Rejeter une demande
  Future<void> _rejectRequest(ProfessionalAccountRequest request) async {
    final reasonController = TextEditingController();

    try {
      // Afficher dialog pour saisir la raison
      final result = await showDialog<Map<String, dynamic>>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Rejeter la demande'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ({request.nom}'),
              const SizedBox(height: 16),
              ,
              ),
              const SizedBox(height: 8),
              TextField(
                controller: reasonController,
                maxLines: 3,
                decoration: const InputDecoration(
                  hintText: 'Expliquez pourquoi cette demande est rejetée...',
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () {
                if (reasonController.text.trim().isNotEmpty) {
                  Navigator.pop(context, {
                    'confirmed': true,
                    'reason': reasonController.text.trim(),
                  });
                }
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('Rejeter'),
            ),
          ],
        ),
      );

      if (result != null && result['confirmed'] == true) {
        // Rejeter la demande
        await ProfessionalAccountService.rejectRequest(
          requestId: request.id,
          rejectedBy: 'admin', // TODO: Récupérer l'ID de l'admin connecté
          reason: result['reason'],
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: const Text('Demande rejetée'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: (e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      reasonController.dispose();
    }
  }

  /// 📊 Obtenir les demandes par statut (pour les filtres autres que pending)
  Stream<List<ProfessionalAccountRequest>> _getAllRequestsByStatus(String status) {
    // TODO: Implémenter la récupération des demandes par statut
    // Pour l
