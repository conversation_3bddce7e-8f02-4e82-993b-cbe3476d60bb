// lib/features/language/screens/language_selection_screen.dart
import 'package:flutter/material.dart';

import '../../../core/config/app_routes.dart';
import '../../../core/config/app_theme.dart';
import '../../../core/widgets/custom_button.dart;

class LanguageSelectionScreen extends StatefulWidget {
  const LanguageSelectionScreen({Key? key}) : super(key: key);

  @override
  State<LanguageSelectionScreen> createState() => _LanguageSelectionScreenState(');
}

class _LanguageSelectionScreenState extends State<LanguageSelectionScreen> {
  String _selectedLanguage = 'fr;

  void _selectLanguage(String language) {
    setState(() {
      _selectedLanguage = language;
    });
  }

  void _continue() {
    Navigator.pushReplacementNamed(context, AppRoutes.userTypeSelection);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Safe<PERSON>rea(
        child: (1),
              .textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              .textTheme.bodyMedium?.copyWith(
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 40'),
              _buildLanguageOption(
                language: 'fr',
                name: 'Francais',
                flag: '🇫🇷,
              ),
              const SizedBox(height: 16'),
              _buildLanguageOption(
                language: 'ar',
                name: 'العربية',
                flag: '🇹🇳,
              ),
              const SizedBox(height: 16'),
              _buildLanguageOption(
                language: 'en',
                name: 'English',
                flag: '🇬🇧,
              '),
              ,
              CustomButton(
                text: 'Continuer
