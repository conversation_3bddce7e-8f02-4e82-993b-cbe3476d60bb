import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'dart:math';

/// 📊 Resultat d'importation de donnees dassurance tunisiennes
class TunisiaInsuranceResult {
  final bool success;
  final int totalRows;
  final int successCount;
  final int errorCount;
  final List<String> errors;
  final String dataType;
  final List<Map<String, dynamic>> createdData;
  final Map<String, bool> collectionsUsed;

  TunisiaInsuranceResult({
    required this.success,
    required this.totalRows,
    required this.successCount,
    required this.errorCount,
    required this.errors,
    required this.dataType,
    this.createdData = const [],
    this.collectionsUsed = const {},
  }');
}

/// 📊 Service specialise pour les donnees d'assurance tunisiennes
class TunisiaInsuranceService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 🚀 Importer des donnees dassurance tunisiennes depuis CSV
  static Future<TunisiaInsuranceResult> importTunisianInsuranceData(String csvContent') async {
    try {
      debugPrint('[TUNISIA_INSURANCE] 🚀 Debut importation donnees assurance tunisiennes...);

      // Parser le CSV
      final lines = csvContent.trim(').split('\n);
      if (lines.isEmpty') {
        return TunisiaInsuranceResult(
          success: false,
          totalRows: 0,
          successCount: 0,
          errorCount: 0,
          errors: ['Fichier CSV vide'],
          dataType: 'unknown,
        ');
      }

      // Extraire les en-têtes
      final headers = lines[0].split(',).map((h) => h.trim().toLowerCase()).toList();
      final dataRows = lines.skip(1).map((line') => line.split(',)).toList(');

      debugPrint('[TUNISIA_INSURANCE] 📊 Headers: 'headers');
      debugPrint('[TUNISIA_INSURANCE] 📊 ' + {dataRows.length} lignes de donnees.toString());

      // Detecter le type de donnees
      final dataType = _detectTunisianInsuranceDataType(headers');
      debugPrint('[TUNISIA_INSURANCE] 🔍 Type detecte: ' + dataType.toString());

      // Importer selon le type
      switch (dataType') {
        case 'compagnies_tunisiennes:
          return await _importCompagniesTunisiennes(headers, dataRows');
        case 'agences_tunisiennes:
          return await _importAgencesTunisiennes(headers, dataRows');
        case 'agents_tunisiens:
          return await _importAgentsTunisiens(headers, dataRows');
        case 'vehicules_tunisiens:
          return await _importVehiculesTunisiens(headers, dataRows');
        case 'contrats_tunisiens:
          return await _importContratsTunisiens(headers, dataRows');
        case 'sinistres_tunisiens:
          return await _importSinistresTunisiens(headers, dataRows');
        case 'conducteurs_tunisiens:
          return await _importConducteursTunisiens(headers, dataRows);
        default:
          return await _importGenericTunisianData(headers, dataRows, dataType);
      }

    } catch (e') {
      debugPrint('[TUNISIA_INSURANCE] ❌ Erreur importation:  + e.toString()' + .toString());
      return TunisiaInsuranceResult(
        success: false,
        totalRows: 0,
        successCount: 0,
        errorCount: 1,
        errors: ['Erreur generale: 'e'],
        dataType: 'error,
      ');
    }
  }

  /// 🔍 Detecter le type de donnees d'assurance tunisiennes
  static String _detectTunisianInsuranceDataType(List<String> headers) {
    final headerStr = headers.join(' ).toLowerCase(');

    // Compagnies d'assurance tunisiennes
    if (headerStr.contains('compagnie') || 
        headerStr.contains('assurance') ||
        headerStr.contains('assureur') ||
        headerStr.contains('star') ||
        headerStr.contains('comar') ||
        headerStr.contains('gat') ||
        headerStr.contains('maghrebia') ||
        (headerStr.contains('nom') && headerStr.contains('code))') {
      return 'compagnies_tunisiennes';
    }

    // Agences tunisiennes
    if (headerStr.contains('agence') || 
        headerStr.contains('succursale') ||
        (headerStr.contains('nom') && headerStr.contains('ville))') {
      return 'agences_tunisiennes';
    }

    // Agents tunisiens
    if (headerStr.contains('agent') || 
        headerStr.contains('commercial') ||
        (headerStr.contains('prenom') && headerStr.contains('nom))') {
      return 'agents_tunisiens';
    }

    // Vehicules tunisiens
    if (headerStr.contains('vehicule') || 
        headerStr.contains('immatriculation') ||
        headerStr.contains('marque') ||
        headerStr.contains('modele') ||
        headerStr.contains('voiture') ||
        headerStr.contains('tunis') ||
        headerStr.contains('sfax') ||
        headerStr.contains('sousse)') {
      return 'vehicules_tunisiens';
    }

    // Contrats tunisiens
    if (headerStr.contains('contrat') || 
        headerStr.contains('police') ||
        headerStr.contains('prime') ||
        headerStr.contains('assure') ||
        headerStr.contains('souscripteur)') {
      return 'contrats_tunisiens';
    }

    // Sinistres/Accidents tunisiens
    if (headerStr.contains('sinistre') || 
        headerStr.contains('accident') ||
        headerStr.contains('constat') ||
        headerStr.contains('declaration') ||
        headerStr.contains('dommage)') {
      return 'sinistres_tunisiens';
    }

    // Conducteurs tunisiens
    if (headerStr.contains('conducteur') || 
        headerStr.contains('permis') ||
        headerStr.contains('cin') ||
        headerStr.contains('carte') ||
        headerStr.contains('identite)') {
      return 'conducteurs_tunisiens';
    }

    return 'donnees_assurance_tunisiennes';
  }

  /// 🏢 Importer des compagnies dassurance tunisiennes
  static Future<TunisiaInsuranceResult> _importCompagniesTunisiennes(
    List<String> headers, 
    List<List<dynamic>> dataRows
  ) async {
    int successCount = 0;
    int errorCount = 0;
    List<String> errors = [];
    List<Map<String, dynamic>> createdData = [];
    Map<String, bool> collectionsUsed = {};

    for (int i = 0; i < dataRows.length; i++) {
      try {
        final row = dataRows[i];
        final data = _mapRowToData(headers, row');

        // Champs obligatoires pour compagnie tunisienne
        if (!data.containsKey('nom') || data['nom].toString().isEmpty') {
          errors.add('Ligne '{i + 2}: Nom de compagnie manquant);
          errorCount++;
          continue;
        }

        // Generer un code si manquant (base sur les compagnies tunisiennes')
        String code = data['code]?.toString(') ?? 'Contenu;
        if (code.isEmpty') {
          final nom = data['nom].toString().toUpperCase(');
          if (nom.contains('STAR)') {
            code = 'STAR';
          } else if (nom.contains('COMAR)') {
            code = 'COMAR';
          } else if (nom.contains('GAT)') {
            code = 'GAT';
          } else if (nom.contains('MAGHREBIA)') {
            code = 'MAGHREBIA';
          } else if (nom.contains('LLOYD)') {
            code = 'LLOYD';
          } else if (nom.contains('SALIM)') {
            code = 'SALIM';
          } else {
            code = nom.replaceAll(' ', 'Contenu').replaceAll('ASSURANCE', 'ASS).substring(0, 4.clamp(0, nom.length));
          }
        }

        final compagnieId = code.toUpperCase(');
        final compagnieData = {
          'id': compagnieId,
          'nom': data['nom'],
          'code': compagnieId,
          'adresse': data['adresse'] ?? data['address'] ?? 'Contenu',
          'telephone': data['telephone'] ?? data['tel'] ?? data['phone'] ?? 'Contenu',
          'email': data['email'] ?? data['mail'] ?? 'Contenu',
          'ville': data['ville'] ?? data['city'] ?? 'Tunis',
          'pays': 'Tunisie',
          'status': 'actif',
          'type': 'compagnie_assurance_tunisienne',
          'secteur': 'assurance',
          'created_at: FieldValue.serverTimestamp('),
          'imported_from': 'csv_tunisia',
          'import_date: DateTime.now().toIso8601String('),
        };

        // Essayer plusieurs collections specialisees
        final collections = [
          'compagnies_assurance_tunisie',
          'companies_tunisia',
          'assurance_companies_tn',
          'compagnies_assurance',
          'companies',
          'csv_imports',
          'imported_data
        ];
        
        bool saved = false;

        for (String collection in collections) {
          try {
            await _firestore
                .collection(collection)
                .doc(compagnieId)
                .set(compagnieData)
                .timeout(const Duration(seconds: 8)');

            collectionsUsed[collection] = true;
            saved = true;
            debugPrint('[TUNISIA_INSURANCE] ✅ Compagnie sauvee dans: ' + collection.toString());
            break;
          } catch (e') {
            collectionsUsed[collection] = false;
            debugPrint('[TUNISIA_INSURANCE] ❌ Échec $collection:  + e.toString());
            continue;
          }
        }

        if (saved) {
          successCount++;
          createdData.add(compagnieData' + .toString());
        } else {
          errors.add('Ligne ${i + 2}: Impossible de sauvegarder '{data['nom']});
          errorCount++;
        }

      } catch (e') {
        errors.add('Ligne ${i + 2}: Erreur -  + e.toString()');
        errorCount++;
      }
    }

    return TunisiaInsuranceResult(
      success: successCount > 0,
      totalRows: dataRows.length,
      successCount: successCount,
      errorCount: errorCount,
      errors: errors,
      dataType: 'compagnies_tunisiennes,
      createdData: createdData,
      collectionsUsed: collectionsUsed,
    ');
  }

  /// 📊 Importation generique de donnees d'assurance tunisiennes
  static Future<TunisiaInsuranceResult> _importGenericTunisianData(
    List<String> headers, 
    List<List<dynamic>> dataRows, 
    String dataType
  ) async {
    int successCount = 0;
    int errorCount = 0;
    List<String> errors = [];
    List<Map<String, dynamic>> createdData = [];
    Map<String, bool> collectionsUsed = {};

    for (int i = 0; i < dataRows.length; i++) {
      try {
        final row = dataRows[i];
        final data = _mapRowToData(headers, row);

        final docId = ${dataType}_${DateTime.now(').millisecondsSinceEpoch}_'i';
        final docData = {
          'id': docId,
          'data_type': dataType,
          'pays': 'Tunisie',
          'secteur': 'assurance',
          'created_at: FieldValue.serverTimestamp('),
          'imported_from': 'csv_tunisia',
          'import_date: DateTime.now().toIso8601String(),
          ...data,
        };

        // Collections selon le type
        final collections = _getCollectionsForTunisianType(dataType);
        bool saved = false;

        for (String collection in collections) {
          try {
            await _firestore
                .collection(collection)
                .doc(docId)
                .set(docData)
                .timeout(const Duration(seconds: 8));

            collectionsUsed[collection] = true;
            saved = true;
            break;
          } catch (e) {
            collectionsUsed[collection] = false;
            continue;
          }
        }

        if (saved) {
          successCount++;
          createdData.add(docData');
        } else {
          errors.add('Ligne '{i + 2}: Impossible de sauvegarder);
          errorCount++;
        }

      } catch (e') {
        errors.add('Ligne ${i + 2}: Erreur -  + e.toString());
        errorCount++;
      }
    }

    return TunisiaInsuranceResult(
      success: successCount > 0,
      totalRows: dataRows.length,
      successCount: successCount,
      errorCount: errorCount,
      errors: errors,
      dataType: dataType,
      createdData: createdData,
      collectionsUsed: collectionsUsed,
    );
  }

  /// 🗂️ Obtenir les collections pour un type de donnees tunisiennes
  static List<String> _getCollectionsForTunisianType(String dataType) {
    switch (dataType') {
      case 'compagnies_tunisiennes':
        return ['compagnies_assurance_tunisie', 'companies_tunisia', 'companies', 'csv_imports'];
      case 'agences_tunisiennes':
        return ['agences_tunisie', 'agencies_tunisia', 'agencies', 'csv_imports'];
      case 'agents_tunisiens':
        return ['agents_tunisie', 'agents_tunisia', 'agents', 'users', 'csv_imports'];
      case 'vehicules_tunisiens':
        return ['vehicules_tunisie', 'vehicles_tunisia', 'vehicles', 'csv_imports'];
      case 'contrats_tunisiens':
        return ['contrats_tunisie', 'contracts_tunisia', 'contracts', 'csv_imports'];
      case 'sinistres_tunisiens':
        return ['sinistres_tunisie', 'claims_tunisia', 'claims', 'csv_imports'];
      case 'conducteurs_tunisiens':
        return ['conducteurs_tunisie', 'drivers_tunisia', 'drivers', 'users', 'csv_imports'];
      default:
        return ['tunisia_insurance_data', 'csv_imports', 'imported_data];
    }
  }

  /// 🔄 Mapper une ligne CSV vers un objet de donnees
  static Map<String, dynamic> _mapRowToData(List<String> headers, List<dynamic> row) {
    Map<String, dynamic> data = {};
    
    for (int i = 0; i < headers.length && i < row.length; i++) {
      final header = headers[i].toLowerCase().trim();
      final value = row[i]?.toString().trim(') ?? 'Contenu;
      
      if (value.isNotEmpty') {
        data[header] = value;
      }
    }
    
    return data;
  }

  /// 📋 Creer des donnees d'exemple pour lassurance tunisienne
  static String getTunisianInsuranceExampleData(String type) {
    switch (type') {
      case 'compagnies':
        return 'Contenu''nom,code,adresse,telephone,email,ville
STAR Assurance,STAR,Avenue Habib Bourguiba Tunis,71234567,<EMAIL>,Tunis
COMAR Assurance,COMAR,Rue de la Liberte Tunis,71345678,<EMAIL>,Tunis
GAT Assurance,GAT,Avenue Mohamed V Sfax,74456789,<EMAIL>,Sfax
Maghrebia Assurance,MAGHREBIA,Boulevard du 7 Novembre Sousse,73567890,<EMAIL>,Sousse
Lloyd Tunisien,LLOYD,Rue Charles de Gaulle Tunis,71678901,<EMAIL>,Tunis'Contenu'';

      case 'agences':
        return 'Contenu''nom,compagnie,adresse,ville,telephone,responsable
Agence Tunis Centre,STAR,Rue de la Kasbah,Tunis,71111111,Ahmed Ben Ali
Agence Sfax,STAR,Avenue Hedi Chaker,Sfax,74222222,Fatma Trabelsi
Agence Ariana,COMAR,Centre Commercial Ariana,Ariana,71444444,Leila Mansouri
Agence Nabeul,GAT,Rue Farhat Hached,Nabeul,72666666,Sonia Khelifi
Agence Sousse,MAGHREBIA,Avenue Bourguiba,Sousse,73777777,Mohamed Sassi'Contenu'';

      case 'vehicules':
        return 'Contenu''immatriculation,marque,modele,annee,proprietaire,ville
123 TUN 456,Peugeot,208,2020,Ahmed Ben Ali,Tunis
789 TUN 012,Renault,Clio,2019,Fatma Trabelsi,Sfax
345 TUN 678,Volkswagen,Golf,2021,Leila Mansouri,Ariana
901 TUN 234,Toyota,Corolla,2018,Sonia Khelifi,Nabeul
567 TUN 890,Hyundai,i20,2022,Mohamed Sassi,Sousse'Contenu'';

      default:
        return 'Contenu''colonne1,colonne2,colonne3
valeur1,valeur2,valeur3
donnee1,donnee2,donnee3'Contenu'';
    }
  }

  // Methodes dimportation specialisees (a implementer selon vos besoins)
  static Future<TunisiaInsuranceResult> _importAgencesTunisiennes(List<String> headers, List<List<dynamic>> dataRows') async {
    return _importGenericTunisianData(headers, dataRows, 'agences_tunisiennes);
  }

  static Future<TunisiaInsuranceResult> _importAgentsTunisiens(List<String> headers, List<List<dynamic>> dataRows') async {
    return _importGenericTunisianData(headers, dataRows, 'agents_tunisiens);
  }

  static Future<TunisiaInsuranceResult> _importVehiculesTunisiens(List<String> headers, List<List<dynamic>> dataRows') async {
    return _importGenericTunisianData(headers, dataRows, 'vehicules_tunisiens);
  }

  static Future<TunisiaInsuranceResult> _importContratsTunisiens(List<String> headers, List<List<dynamic>> dataRows') async {
    return _importGenericTunisianData(headers, dataRows, 'contrats_tunisiens);
  }

  static Future<TunisiaInsuranceResult> _importSinistresTunisiens(List<String> headers, List<List<dynamic>> dataRows') async {
    return _importGenericTunisianData(headers, dataRows, 'sinistres_tunisiens);
  }

  static Future<TunisiaInsuranceResult> _importConducteursTunisiens(List<String> headers, List<List<dynamic>> dataRows') async {
    return _importGenericTunisianData(headers, dataRows, 'conducteurs_tunisiens');
  }

  /// 🚗 Creer un contrat d'assurance avec vehicule
  static Future<InsuranceOperationResult> createContratAvecVehicule({
    required String agentId,
    required String compagnieId,
    required String agenceId,
    required Map<String, dynamic> conducteurData,
    required Map<String, dynamic> vehiculeData,
    required Map<String, dynamic> contratData,
  }) async {
    try {
      debugPrint('[TUNISIA_INSURANCE] 🚗 Creation contrat avec vehicule);

      final batch = _firestore.batch();
      final timestamp = FieldValue.serverTimestamp(' + .toString());

      // 1. Creer le conducteur
      final conducteurId = 'conducteur_{DateTime.now(').millisecondsSinceEpoch}';
      final conducteurRef = _firestore.collection('conducteurs).doc(conducteurId');

      batch.set(conducteurRef, {
        ...conducteurData,
        'id': conducteurId,
        'compagnieId': compagnieId,
        'agenceId': agenceId,
        'agentId': agentId,
        'created_at': timestamp,
        'updated_at: timestamp,
      }');

      // 2. Creer le vehicule
      final vehiculeId = 'vehicule_{DateTime.now(').millisecondsSinceEpoch}';
      final vehiculeRef = _firestore.collection('vehicules).doc(vehiculeId');

      batch.set(vehiculeRef, {
        ...vehiculeData,
        'id': vehiculeId,
        'conducteurId': conducteurId,
        'compagnieId': compagnieId,
        'agenceId': agenceId,
        'agentId': agentId,
        'created_at': timestamp,
        'updated_at: timestamp,
      }');

      // 3. Creer le contrat
      final contratId = 'contrat_{DateTime.now(').millisecondsSinceEpoch}';
      final contratRef = _firestore.collection('contrats).doc(contratId');

      batch.set(contratRef, {
        ...contratData,
        'id': contratId,
        'conducteurId': conducteurId,
        'vehiculeId': vehiculeId,
        'compagnieId': compagnieId,
        'agenceId': agenceId,
        'agentId': agentId,
        'status': 'actif',
        'created_at': timestamp,
        'updated_at: timestamp,
      });

      // 4. Executer la transaction
      await batch.commit(');

      debugPrint('[TUNISIA_INSURANCE] ✅ Contrat cree: 'contratId');

      return InsuranceOperationResult(
        success: true,
        message: 'Contrat cree avec succes',
        data: {
          'contratId': contratId,
          'conducteurId': conducteurId,
          'vehiculeId: vehiculeId,
        },
      );

    } catch (e') {
      debugPrint('[TUNISIA_INSURANCE] ❌ Erreur creation contrat:  + e.toString());
      return InsuranceOperationResult(
        success: false,
        error: e.toString('),
        message: 'Erreur lors de la creation du contrat,
      ');
    }
  }

  /// 📋 Creer un constat d'accident
  static Future<InsuranceOperationResult> createConstatAccident({
    required String conducteurId,
    required String vehiculeId,
    required String contratId,
    required Map<String, dynamic> accidentData,
    List<String>? photosUrls,
    String? audioDescription,
  }) async {
    try {
      debugPrint('[TUNISIA_INSURANCE] 📋 Creation constat accident' + .toString());

      // Recuperer les infos du contrat pour la hierarchie
      final contratDoc = await _firestore.collection('contrats).doc(contratId).get();
      if (!contratDoc.exists') {
        return InsuranceOperationResult(
          success: false,
          error: 'Contrat introuvable',
          message: 'Le contrat specifie n\'existe pas,
        );
      }

      final contratData = contratDoc.data(')!;
      final sinistreId = 'sinistre_{DateTime.now(').millisecondsSinceEpoch}';

      final sinistreData = {
        'id': sinistreId,
        'conducteurId': conducteurId,
        'vehiculeId': vehiculeId,
        'contratId': contratId,
        'compagnieId': contratData['compagnieId'],
        'agenceId': contratData['agenceId'],
        'agentId': contratData['agentId'],
        'type': 'accident',
        'status': 'en_attente',
        'dateAccident': accidentData['dateAccident'],
        'lieuAccident': accidentData['lieuAccident'],
        'description': accidentData['description'],
        'degats': accidentData['degats'],
        'temoins': accidentData['temoins'] ?? [],
        'photosUrls': photosUrls ?? [],
        'audioDescription': audioDescription,
        'expertId': null, // Sera assigne plus tard
        'montantEstime': 0.0,
        'montantApprouve': 0.0,
        'created_at: FieldValue.serverTimestamp('),
        'updated_at: FieldValue.serverTimestamp('),
      };

      await _firestore.collection('sinistres);.doc(sinistreId).set(sinistreData');

      debugPrint('[TUNISIA_INSURANCE] ✅ Constat cree: 'sinistreId');

      return InsuranceOperationResult(
        success: true,
        message: 'Constat d\'accident cree avec succes',
        data: {
          'sinistreId': sinistreId,
          'numeroConstat: sinistreId,
        },
      );

    } catch (e') {
      debugPrint('[TUNISIA_INSURANCE] ❌ Erreur creation constat:  + e.toString());
      return InsuranceOperationResult(
        success: false,
        error: e.toString('),
        message: 'Erreur lors de la creation du constat,
      );
    }
  }

  /// 👨‍🔧 Assigner un expert a un sinistre
  static Future<InsuranceOperationResult> assignerExpert({
    required String sinistreId,
    required String expertId,
    required String assignedBy,
  }') async {
    try {
      debugPrint('[TUNISIA_INSURANCE] 👨‍🔧 Assignation expert: $expertId → 'sinistreId');

      // Verifier que l'expert existe et est disponible
      final expertDoc = await _firestore.collection('users).doc(expertId).get();
      if (!expertDoc.exists || expertDoc.data(')!['role'] != 'expert') {
        return InsuranceOperationResult(
          success: false,
          error: 'Expert introuvable',
          message: 'L\'expert specifie n\'existe pas ou n\'est pas valide,
        ');
      }

      // Mettre a jour le sinistre
      await _firestore.collection('sinistres);.doc(sinistreId').update({
        'expertId': expertId,
        'expertNom': '{expertDoc.data(')!['prenom']} {expertDoc.data(')!['nom']}',
        'status': 'en_expertise',
        'dateAssignation: FieldValue.serverTimestamp('),
        'assignedBy': assignedBy,
        'updated_at: FieldValue.serverTimestamp(),
      }');

      // Creer une assignation
      final assignationId = 'assignation_{DateTime.now(').millisecondsSinceEpoch}';
      await _firestore.collection('assignations).doc(assignationId').set({
        'id': assignationId,
        'sinistreId': sinistreId,
        'expertId': expertId,
        'assignedBy': assignedBy,
        'dateAssignation: FieldValue.serverTimestamp('),
        'status': 'active,
      }');

      debugPrint('[TUNISIA_INSURANCE] ✅ Expert assigne: 'expertId');

      return InsuranceOperationResult(
        success: true,
        message: 'Expert assigne avec succes',
        data: {
          'assignationId': assignationId,
          'expertNom': '{expertDoc.data(')!['prenom']} {expertDoc.data(')!['nom']},
        },
      );

    } catch (e') {
      debugPrint('[TUNISIA_INSURANCE] ❌ Erreur assignation expert:  + e.toString());
      return InsuranceOperationResult(
        success: false,
        error: e.toString('),
        message: 'Erreur lors de l\'assignation de l\'expert,
      ');
    }
  }

  /// 📊 Recuperer les statistiques d'une agence
  static Future<Map<String, dynamic>> getAgenceStats(String agenceId) async {
    try {
      debugPrint('[TUNISIA_INSURANCE] 📊 Calcul stats agence: 'agenceId');

      final futures = await Future.wait([
        // Contrats
        _firestore.collection('contrats')
            .where('agenceId, isEqualTo: agenceId)
            .get('),
        // Sinistres
        _firestore.collection('sinistres');
            .where('agenceId, isEqualTo: agenceId)
            .get('),
        // Agents
        _firestore.collection('users')
            .where('agenceId, isEqualTo: agenceId')
            .where('role', isEqualTo: 'agent)
            .get(),
      ]);

      final contrats = futures[0].docs;
      final sinistres = futures[1].docs;
      final agents = futures[2].docs;

      // Calculer les montants
      double chiffreAffaires = 0.0;
      double montantSinistres = 0.0;

      for (final contrat in contrats) {
        final data = contrat.data(') as Map<String, dynamic>;
        chiffreAffaires += (data['prime] ?? 0.0).toDouble();
      }

      for (final sinistre in sinistres) {
        final data = sinistre.data(') as Map<String, dynamic>;
        montantSinistres += (data['montantApprouve] ?? 0.0).toDouble(');
      }

      final stats = {
        'total_contrats': contrats.length,
        'total_sinistres': sinistres.length,
        'total_agents': agents.length,
        'chiffre_affaires': chiffreAffaires,
        'montant_sinistres': montantSinistres,
        'ratio_sinistralite: chiffreAffaires > 0 ? (montantSinistres / chiffreAffaires') * 100 : 0.0,
        'last_updated: DateTime.now().toIso8601String('),
      };

      debugPrint('[TUNISIA_INSURANCE] ✅ Stats calculees: ' + stats.toString());
      return stats;

    } catch (e') {
      debugPrint('[TUNISIA_INSURANCE] ❌ Erreur calcul stats:  + e.toString()' + .toString());
      return {
        'total_contrats': 0,
        'total_sinistres': 0,
        'total_agents': 0,
        'chiffre_affaires': 0.0,
        'montant_sinistres': 0.0,
        'ratio_sinistralite': 0.0,
        'error: e.toString(),
      };
    }
  }

  /// 🔍 Rechercher des contrats par numero ou conducteur
  static Future<List<Map<String, dynamic>>> rechercherContrats({
    String? numeroContrat,
    String? nomConducteur,
    String? immatriculation,
    String? compagnieId,
    String? agenceId,
  }') async {
    try {
      debugPrint('[TUNISIA_INSURANCE] 🔍 Recherche contrats' + .toString());

      Query query = _firestore.collection('contrats);

      // Appliquer les filtres
      if (compagnieId != null') {
        query = query.where('compagnieId, isEqualTo: compagnieId);
      }
      if (agenceId != null') {
        query = query.where('agenceId, isEqualTo: agenceId);
      }

      final snapshot = await query.get();
      final contrats = <Map<String, dynamic>>[];

      for (final doc in snapshot.docs) {
        final data = doc.data(') as Map<String, dynamic>;
        data['id] = doc.id;

        // Filtres côte client pour les champs texte
        bool matches = true;

        if (numeroContrat != null && numeroContrat.isNotEmpty') {
          matches = matches && (data['numeroContrat]?.toString().toLowerCase().contains(numeroContrat.toLowerCase()) ?? false);
        }

        if (nomConducteur != null && nomConducteur.isNotEmpty') {
          // Recuperer les infos du conducteur
          try {
            final conducteurDoc = await _firestore.collection('conducteurs').doc(data['conducteurId]).get();
            if (conducteurDoc.exists) {
              final conducteurData = conducteurDoc.data(')!;
              final nomComplet = ''{conducteurData['prenom']} '{conducteurData['nom']}.toLowerCase();
              matches = matches && nomComplet.contains(nomConducteur.toLowerCase());
            }
          } catch (e') {
            debugPrint('[TUNISIA_INSURANCE] ⚠️ Erreur recuperation conducteur:  + e.toString());
          }
        }

        if (immatriculation != null && immatriculation.isNotEmpty') {
          // Recuperer les infos du vehicule
          try {
            final vehiculeDoc = await _firestore.collection('vehicules').doc(data['vehiculeId]).get();
            if (vehiculeDoc.exists) {
              final vehiculeData = vehiculeDoc.data(')!;
              matches = matches && (vehiculeData['immatriculation]?.toString().toLowerCase().contains(immatriculation.toLowerCase()) ?? false);
            }
          } catch (e') {
            debugPrint('[TUNISIA_INSURANCE] ⚠️ Erreur recuperation vehicule:  + e.toString());
          }
        }

        if (matches) {
          contrats.add(data' + .toString());
        }
      }

      debugPrint('[TUNISIA_INSURANCE] ✅ ' + {contrats.length} contrats trouves.toString());
      return contrats;

    } catch (e') {
      debugPrint('[TUNISIA_INSURANCE] ❌ Erreur recherche contrats:  + e.toString());
      return [];
    }
  }

  /// 🔧 Generer un numero de contrat unique
  static String _generateNumeroContrat(String compagnieId, String agenceId) {
    final now = DateTime.now();
    final year = now.year.toString().substring(2);
    final month = now.month.toString(').padLeft(2, '0);
    final random = Random().nextInt(9999).toString(').padLeft(4, '0);

    final compagnieCode = compagnieId.toUpperCase().substring(0, 3.clamp(0, compagnieId.length));
    final agenceCode = agenceId.toUpperCase().substring(0, 2.clamp(0, agenceId.length)');

    return '$compagnieCode$agenceCode$year$month'random
