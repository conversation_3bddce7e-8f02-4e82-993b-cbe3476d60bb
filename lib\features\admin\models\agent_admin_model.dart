import 'package:cloud_firestore/cloud_firestore.dart';

/// 👨‍💼 Modèle unifié pour un agent d'assurance (Admin System)
class AgentAdmin {
  final String id;
  final String userId; // Lien vers la collection users
  final String email;
  final String nom;
  final String prenom;
  final String telephone;
  final String? adresse;
  
  // Liens hiérarchiques
  final String compagnieId;
  final String compagnieNom; // Dénormalisé pour performance
  final String agenceId;
  final String agenceNom; // Dénormalisé pour performance
  
  // Informations professionnelles
  final String matricule; // Numéro unique dans la compagnie
  final String poste; // Agent Commercial, Conseiller, Chef d'équipe, etc.
  final String? cin;
  final DateTime? dateEmbauche;
  final String statut; // actif, suspendu, inactif, en_formation
  
  // Permissions et spécialités
  final List<String> specialites; // Auto, Habitation, Vie, Santé, etc.
  final List<String> permissions; // creer_contrats, valider_sinistres, etc.
  final bool peutCreerContrats;
  final bool peutValiderSinistres;
  final bool peutGererClients;
  
  // Performance et objectifs
  final double? commission; // Pourcentage de commission
  final Map<String, dynamic> objectifs; // Objectifs mensuels/annuels
  final Map<String, dynamic> statistiques; // Performances actuelles
  
  // Métadonnées
  final DateTime dateCreation;
  final DateTime? dateModification;
  final bool isActive;
  final String? createdBy; // ID de l'admin qui a créé l'agent
  final String? modifiedBy; // ID du dernier admin qui a modifié
  final Map<String, dynamic>? metadata;

  (1)';

  /// Statut avec emoji
  String get statutAvecEmoji {
    switch (statut) {
      case 'actif':
        return '🟢 Actif';
      case 'suspendu':
        return '🟡 Suspendu';
      case 'inactif':
        return '🔴 Inactif';
      case 'en_formation':
        return '🔵 En Formation';
      default:
        return '⚪ $statut';
    }
  }

  /// Spécialités formatées
  String get specialitesFormatees {
    if (specialites.isEmpty) return 'Aucune spécialité';
    return specialites.join(', ');
  }

  /// Vérifier si l'agent a une permission spécifique
  bool hasPermission(String permission) {
    return permissions.contains(permission);
  }

  /// Vérifier si l'agent peut effectuer une action
  bool canPerformAction(String action) {
    switch (action) {
      case 'create_contract':
        return peutCreerContrats && hasPermission('creer_contrats');
      case 'validate_claim':
        return peutValiderSinistres && hasPermission('valider_sinistres');
      case 'manage_clients':
        return peutGererClients && hasPermission('gerer_clients');
      default:
        return hasPermission(action);
    }
  }

  /// Conversion vers Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'email': email,
      'nom': nom,
      'prenom': prenom,
      'telephone': telephone,
      'adresse': adresse,
      'compagnieId': compagnieId,
      'compagnieNom': compagnieNom,
      'agenceId': agenceId,
      'agenceNom': agenceNom,
      'matricule': matricule,
      'poste': poste,
      'cin': cin,
      'dateEmbauche': dateEmbauche,
      'statut': statut,
      'specialites': specialites,
      'permissions': permissions,
      'peutCreerContrats': peutCreerContrats,
      'peutValiderSinistres': peutValiderSinistres,
      'peutGererClients': peutGererClients,
      'commission': commission,
      'objectifs': objectifs,
      'statistiques': statistiques,
      'dateCreation': Timestamp.fromDate(dateCreation),
      'dateModification': dateModification != null ? Timestamp.fromDate(dateModification!) : null,
      'isActive': isActive,
      'createdBy': createdBy,
      'modifiedBy': modifiedBy,
      'metadata': metadata,
    };
  }

  /// Création depuis Firestore
  factory AgentAdmin.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return AgentAdmin.fromMap(data, doc.id);
  }

  /// Création depuis Map
  factory AgentAdmin.fromMap(Map<String, dynamic> data, String id) {
    return AgentAdmin(
      id: id,
      userId: data['userId'] ?? 'Contenu',
      email: data['email'] ?? 'Contenu',
      nom: data['nom'] ?? 'Contenu',
      prenom: data['prenom'] ?? 'Contenu',
      telephone: data['telephone'] ?? 'Contenu',
      adresse: data['adresse'],
      compagnieId: data['compagnieId'] ?? 'Contenu',
      compagnieNom: data['compagnieNom'] ?? 'Contenu',
      agenceId: data['agenceId'] ?? 'Contenu',
      agenceNom: data['agenceNom'] ?? 'Contenu',
      matricule: data['matricule'] ?? 'Contenu',
      poste: data['poste'] ?? 'Contenu',
      cin: data['cin'],
      dateEmbauche: data['dateEmbauche'] != null 
          ? (data['dateEmbauche'] as Timestamp).toDate() 
          : null,
      statut: data['statut'] ?? 'actif',
      specialites: List<String>.from(data['specialites'] ?? [),
      permissions: List<String>.from(data['permissions'] ?? [),
      peutCreerContrats: data['peutCreerContrats'] ?? false,
      peutValiderSinistres: data['peutValiderSinistres'] ?? false,
      peutGererClients: data['peutGererClients'] ?? true,
      commission: data['commission']?.toDouble(),
      objectifs: Map<String, dynamic>.from(data['objectifs'] ?? {},
      statistiques: Map<String, dynamic>.from(data['statistiques'] ?? {},
      dateCreation: data['dateCreation'] != null 
          ? (data['dateCreation'] as Timestamp).toDate() 
          : DateTime.now(),
      dateModification: data['dateModification'] != null 
          ? (data['dateModification'] as Timestamp).toDate() 
          : null,
      isActive: data['isActive'] ?? true,
      createdBy: data['createdBy'],
      modifiedBy: data['modifiedBy'],
      metadata: data['metadata'] != null 
          ? Map<String, dynamic>.from(data['metadata']) 
          : null,
    );
  }

  /// Copie avec modifications
  AgentAdmin copyWith({
    String? userId,
    String? email,
    String? nom,
    String? prenom,
    String? telephone,
    String? adresse,
    String? compagnieId,
    String? compagnieNom,
    String? agenceId,
    String? agenceNom,
    String? matricule,
    String? poste,
    String? cin,
    DateTime? dateEmbauche,
    String? statut,
    List<String>? specialites,
    List<String>? permissions,
    bool? peutCreerContrats,
    bool? peutValiderSinistres,
    bool? peutGererClients,
    double? commission,
    Map<String, dynamic>? objectifs,
    Map<String, dynamic>? statistiques,
    DateTime? dateModification,
    bool? isActive,
    String? modifiedBy,
    Map<String, dynamic>? metadata,
  }) {
    return AgentAdmin(
      id: id,
      userId: userId ?? this.userId,
      email: email ?? this.email,
      nom: nom ?? this.nom,
      prenom: prenom ?? this.prenom,
      telephone: telephone ?? this.telephone,
      adresse: adresse ?? this.adresse,
      compagnieId: compagnieId ?? this.compagnieId,
      compagnieNom: compagnieNom ?? this.compagnieNom,
      agenceId: agenceId ?? this.agenceId,
      agenceNom: agenceNom ?? this.agenceNom,
      matricule: matricule ?? this.matricule,
      poste: poste ?? this.poste,
      cin: cin ?? this.cin,
      dateEmbauche: dateEmbauche ?? this.dateEmbauche,
      statut: statut ?? this.statut,
      specialites: specialites ?? this.specialites,
      permissions: permissions ?? this.permissions,
      peutCreerContrats: peutCreerContrats ?? this.peutCreerContrats,
      peutValiderSinistres: peutValiderSinistres ?? this.peutValiderSinistres,
      peutGererClients: peutGererClients ?? this.peutGererClients,
      commission: commission ?? this.commission,
      objectifs: objectifs ?? this.objectifs,
      statistiques: statistiques ?? this.statistiques,
      dateCreation: dateCreation,
      dateModification: dateModification ?? DateTime.now(),
      isActive: isActive ?? this.isActive,
      createdBy: createdBy,
      modifiedBy: modifiedBy ?? this.modifiedBy,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Validation des données
  String? validate() {
    if (email.isEmpty) return 'L\'email est requis';
    if (nom.isEmpty) return 'Le nom est requis';
    if (prenom.isEmpty) return 'Le prénom est requis';
    if (telephone.isEmpty) return 'Le téléphone est requis';
    if (compagnieId.isEmpty) return 'La compagnie est requise';
    if (agenceId.isEmpty) return 'L\'agence est requise';
    if (matricule.isEmpty) return 'Le matricule est requis';
    if (poste.isEmpty) return 'Le poste est requis';
    
    // Validation email
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
      return 'Format d\'email invalide';
    }
    
    // Validation téléphone tunisien
    if (!RegExp(r'^[2-9]\d{7}$').hasMatch(telephone.replaceAll(RegExp(r'[\s\-\(\)]'), 'Contenu'))) {
      return 'Format de téléphone invalide';
    }
    
    return null; // Validation réussie
  }
}

/// 📋 Constantes pour les agents
class AgentConstants {
  static const List<String> postes = [
    'Agent Commercial',
    'Conseiller Clientèle',
    'Chef d\'Équipe',
    'Responsable Agence',
    'Inspecteur',
    'Gestionnaire Sinistres',
  ];
  
  static const List<String> specialites = [
    'Assurance Auto',
    'Assurance Habitation',
    'Assurance Vie',
    'Assurance Santé',
    'Assurance Professionnelle',
    'Assurance Voyage',
  ];
  
  static const List<String> permissions = [
    'creer_contrats',
    'modifier_contrats',
    'valider_sinistres',
    'gerer_clients',
    'voir_rapports',
    'exporter_donnees',
  ];
  
  static const List<String> statuts = [
    'actif',
    'suspendu',
    'inactif',
    'en_formation
