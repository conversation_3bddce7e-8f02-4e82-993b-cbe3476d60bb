import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../../core/theme/modern_theme.dart';
import '../../models/agence_assurance.dart';
import '../../services/agence_service.dart';

/// 🏪 Dialog de détails d'une agence
class AgenceDetailsDialog extends ConsumerStatefulWidget {
  final AgenceAssurance agence;

  ;

  @override
  ConsumerState<AgenceDetailsDialog> createState() => _AgenceDetailsDialogState();
}

class _AgenceDetailsDialogState extends ConsumerState<AgenceDetailsDialog> {
  final AgenceService _agenceService = AgenceService();
  Map<String, dynamic>? _stats;
  bool _isLoadingStats = true;

  @override
  void initState() {
    super.initState();
    _loadStats();
  }

  /// 📊 Charger les statistiques de l'agence
  Future<void> _loadStats() async {
    try {
      final stats = await _agenceService.getAgenceStats(widget.agence.id);
      if (mounted) {
        setState(() {
          _stats = stats;
          _isLoadingStats = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingStats = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(ModernTheme.radiusLarge),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxWidth: 600, maxHeight: 700),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            Expanded(
              child: _buildContent(),
            ),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  /// 📋 En-tête du dialog
  Widget _buildHeader() {
    return Container(
      padding: ,
        borderRadius: ,
        ),
        boxShadow: ModernTheme.cardShadow,
      ),
      child: Row(
        children: [
          ,
          ,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ,
                ),
                const SizedBox(height: 2),
                ,
                ),
              ],
            ),
          ),
          // Statut
          Container(
            padding: 
                  : Colors.red.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
            ),
            child: ,
            ),
          ),
          ,
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.info),
          ),
        ],
      ),
    );
  }

  /// 📄 Contenu du dialog
  Widget _buildContent() {
    return SingleChildScrollView(
      padding: 
            (1),
              ),
            )
          else
            _buildStatsSection(),

          ,

          // Informations générales
          _buildInfoSection(),

          ,

          // Informations de contact
          _buildContactSection(),

          ,

          // Informations système
          _buildSystemSection(),
        ],
      ),
    );
  }

  /// 📊 Section des statistiques
  Widget _buildStatsSection() {
    if (_stats == null) return ,
        ),
        ,
        Container(
          padding: ,
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'Agents',
                      _stats!['totalAgents'].toString(),
                      Icons.people_outline,
                      Colors.blue,
                      subtitle: '${_stats!['agentsActifs']} actifs',
                    ),
                  ),
                  ,
                  Expanded(
                    child: _buildStatCard(
                      'Contrats',
                      _stats!['totalContrats'].toString(),
                      Icons.description_outlined,
                      Colors.green,
                      subtitle: '${_stats!['contratsActifs']} actifs',
                    ),
                  ),
                ],
              ),
              ,
              _buildStatCard(
                'Constats',
                _stats!['totalConstats'].toString(),
                Icons.assignment_outlined,
                Colors.orange,
                isFullWidth: true,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 📊 Carte de statistique
  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    String? subtitle,
    bool isFullWidth = false,
  }) {
    return Container(
      padding: ,
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: isFullWidth
          ? Row(
              children: [
                const Icon(icon, color: color, size: 24),
                ,
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ,
                      ),
                      ,
                      ),
                    ],
                  ),
                ),
              ],
            )
          : Column(
              children: [
                const Icon(icon, color: color, size: 24),
                ,
                ,
                ),
                ,
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 2),
                  ,
                  ),
                ],
              ],
            ),
    );
  }

  /// ℹ️ Section des informations générales
  Widget _buildInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        ),
        ,
        _buildInfoTile(
          'Compagnie',
          widget.agence.compagnieNom,
          Icons.domain_rounded,
        ),
        _buildInfoTile(
          'Localisation',
          '${widget.agence.ville}, ${widget.agence.gouvernorat}',
          Icons.location_on_outlined,
        ),
        _buildInfoTile(
          'Adresse',
          widget.agence.adresse,
          Icons.home_outlined,
        ),
        if (widget.agence.responsable != null)
          _buildInfoTile(
            'Responsable',
            widget.agence.responsable!,
            Icons.person_outlined,
          ),
        if (widget.agence.zone != null)
          _buildInfoTile(
            'Zone couverte',
            widget.agence.zone!,
            Icons.map_outlined,
          ),
      ],
    );
  }

  /// 📞 Section des informations de contact
  Widget _buildContactSection() {
    final hasContact = widget.agence.telephone != null || widget.agence.email != null;
    
    if (!hasContact) return ,
        ),
        ,
        if (widget.agence.telephone != null)
          _buildInfoTile(
            'Téléphone',
            widget.agence.telephone!,
            Icons.phone_outlined,
          ),
        if (widget.agence.email != null)
          _buildInfoTile(
            'Email',
            widget.agence.email!,
            Icons.email_outlined,
          ),
      ],
    );
  }

  /// ⚙️ Section des informations système
  Widget _buildSystemSection() {
    final dateFormat = DateFormat('dd/MM/yyyy à HH:mm');
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ,
        ),
        ,
        _buildInfoTile(
          'Date de création',
          dateFormat.format(widget.agence.dateCreation),
          Icons.calendar_today_outlined,
        ),
        if (widget.agence.dateModification != null)
          _buildInfoTile(
            'Dernière modification',
            dateFormat.format(widget.agence.dateModification!),
            Icons.update_outlined,
          ),
        _buildInfoTile(
          'ID système',
          widget.agence.id,
          Icons.fingerprint_outlined,
        ),
      ],
    );
  }

  /// 📝 Tuile d'information
  Widget _buildInfoTile(String label, String value, IconData icon) {
    return (1),
          ,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ,
                ),
                const SizedBox(height: 2),
                ,
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 🎬 Actions du dialog
  Widget _buildActions() {
    return Container(
      padding: ,
          bottomRight: Radius.circular(ModernTheme.radiusLarge),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton.const Icon(
            onPressed: _loadStats,
            icon: const Icon(Icons.info),
            label: const Text('Actualiser'),
          ),
          ,
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: ModernTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Fermer
