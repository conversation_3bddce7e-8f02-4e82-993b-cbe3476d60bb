import 'package:flutter/material.dart';
import '../models/hierarchical_structure.dart';
import '../services/hierarchical_admin_service.dart';

/// 📋 Écran de gestion des demandes filtré par admin
class CleanDemandesScreen extends StatefulWidget {
  final AdminUser admin;

  ;

  @override
  State<CleanDemandesScreen> createState() => _CleanDemandesScreenState();
}

class _CleanDemandesScreenState extends State<CleanDemandesScreen> {
  StatutDemande? _filtreStatut;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('📋 Demandes d\'Inscription'),
        backgroundColor: _getAdminColor(),
        foregroundColor: Colors.white,
        actions: [
          PopupMenuButton<StatutDemande?>(
            icon: const Icon(Icons.info),
            onSelected: (statut) => setState(() => _filtreStatut = statut),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: null,
                child: const Text('Toutes les demandes'),
              ),
              ,
              ),
              ,
              ),
              ,
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          _buildInfoHeader(),
          Expanded(
            child: StreamBuilder<List<DemandeAgent>>(
              stream: HierarchicalAdminService.getDemandesForAdmin(widget.admin),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (snapshot.hasError) {
                  return ,
                        const SizedBox(height: 16),
                        ({snapshot.error}'),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () => setState(() {},
                          child: const Text('Réessayer'),
                        ),
                      ],
                    ),
                  );
                }

                final demandes = snapshot.data ?? [];
                final demandesFiltrees = _filtreStatut == null
                    ? demandes
                    : demandes.where((d) => d.statut == _filtreStatut).toList();

                if (demandesFiltrees.isEmpty) {
                  return ,
                        const SizedBox(height: 16),
                        ({_getStatutconst Text(_filtreStatut!)}',
                          style: ,
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  padding:  {
                    return _buildDemandeCard(demandesFiltrees[index]);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoHeader() {
    return Container(
      margin: ,
      decoration: BoxDecoration(
        color: _getAdminColor().withValues(alpha: ,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: _getAdminColor().withValues(alpha: ),
      ),
      child: Row(
        children: [
          (1), color: _getAdminColor()),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  _getAdminScope(),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _getAdminColor(),
                  ),
                ),
                ({widget.admin.type == AdminType.agence ? 'agence' : 'compagnie'}',
                  style: ,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDemandeCard(DemandeAgent demande) {
    return Card(
      margin: ),
      child: (1).withValues(alpha: ,
                  child: ({demande.nom[0]}',
                    style: TextStyle(
                      color: _getStatutColor(demande.statut),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ({demande.nom}',
                        style: ,
                      ),
                      ,
                      ),
                    ],
                  ),
                ),
                _buildStatutChip(demande.statut),
              ],
            ),
            const SizedBox(height: 12),
            _buildInfoRow(Icons.phone, demande.telephone),
            _buildInfoRow(Icons.credit_card, 'CIN: ${demande.cin}'),
            _buildInfoRow(Icons.access_time, _formatDate(demande.dateCreation)),
            if (demande.statut == StatutDemande.enAttente) ...[
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.const Icon(
                      onPressed: () => _approuverDemande(demande),
                      icon: const Icon(Icons.info),
                      label: const Text('Approuver'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.const Icon(
                      onPressed: () => _rejeterDemande(demande),
                      icon: const Icon(Icons.info),
                      label: const Text('Rejeter'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ],
            if (demande.commentaire != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: ,
                ),
                child: Row(
                  children: [
                    ,
                    const SizedBox(width: 8),
                    Expanded(
                      child: ,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String text) {
    return (1),
          const SizedBox(width: 8),
          ,
          ),
        ],
      ),
    );
  }

  Widget _buildStatutChip(StatutDemande statut) {
    return Container(
      padding: .withValues(alpha: ,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: _getStatutColor(statut).withValues(alpha: ),
      ),
      child: const Text(
        _getStatutconst Text(statut),
        style: TextStyle(
          color: _getStatutColor(statut),
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Future<void> _approuverDemande(DemandeAgent demande) async {
    final success = await HierarchicalAdminService.approuverDemande(
      demande.id,
      widget.admin,
      commentaire: 'Demande approuvée par ${widget.admin.prenom} ${widget.admin.nom}',
    );

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text(success ? '✅ Demande approuvée' : '❌ Erreur lors de l\'approbation'),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }

  Future<void> _rejeterDemande(DemandeAgent demande) async {
    final raison = await _showRejectDialog();
    if (raison == null) return;

    final success = await HierarchicalAdminService.rejeterDemande(
      demande.id,
      widget.admin,
      raison,
    );

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text(success ? '❌ Demande rejetée' : '❌ Erreur lors du rejet'),
          backgroundColor: success ? Colors.orange : Colors.red,
        ),
      );
    }
  }

  Future<String?> _showRejectDialog() async {
    final controller = TextEditingController();
    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Rejeter la demande'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'Raison du rejet',
            hintText: 'Expliquez pourquoi vous rejetez cette demande...',
          ),
          maxLines: 3,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, controller.text.trim()),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Rejeter'),
          ),
        ],
      ),
    );
  }

  Color _getAdminColor() {
    switch (widget.admin.type) {
      case AdminType.superAdmin:
        return Colors.red;
      case AdminType.compagnie:
        return Colors.blue;
      case AdminType.agence:
        return Colors.green;
    }
  }

  IconData _getAdmin {
    switch (widget.admin.type) {
      case AdminType.superAdmin:
        return Icons.admin_panel_settings;
      case AdminType.compagnie:
        return Icons.business;
      case AdminType.agence:
        return Icons.store;
    }
  }

  String _getAdminScope() {
    switch (widget.admin.type) {
      case AdminType.superAdmin:
        return 'Toutes les demandes du système';
      case AdminType.compagnie:
        return 'Demandes de votre compagnie';
      case AdminType.agence:
        return 'Demandes de votre agence';
    }
  }

  Color _getStatutColor(StatutDemande statut) {
    switch (statut) {
      case StatutDemande.enAttente:
        return Colors.orange;
      case StatutDemande.approuvee:
        return Colors.green;
      case StatutDemande.rejetee:
        return Colors.red;
    }
  }

  String _getStatutconst Text(StatutDemande statut) {
    switch (statut) {
      case StatutDemande.enAttente:
        return 'En attente';
      case StatutDemande.approuvee:
        return 'Approuvée';
      case StatutDemande.rejetee:
        return 'Rejetée';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} à ${date.hour}:${date.minute.toString().padLeft(2, '0')}
